{"name": "miniwork", "version": "1.0.0", "private": true, "description": "小程序研发平台", "repository": {"type": "git", "url": "http://gitlab.alibaba-inc.com/func/miniwork"}, "author": {"name": "南麓", "email": "<EMAIL>"}, "scripts": {"dev": "midway-bin dev --ts", "test": "midway-bin test --ts"}, "resolutions": {"cluster-client": "3.6.0"}, "dependencies": {"@ali/configclient": "^7.6.0", "@ali/crypto": "^1.0.4", "@ali/def-open-client": "^0.2.6", "@ali/keycenter": "^2.1.1", "@ali/midway-buc-client": "^3.2.8", "@ali/midway-buc-login": "^3.0.0", "@ali/midway-fc-starter": "^1.3.3", "@ali/midway-rsocket-adapter": "^1.0.0", "@alicloud/openapi-client": "^0.4.6", "@alicloud/pop-core": "^1.7.13", "@alicloud/tea-util": "^1.4.7", "@alipay/basement": "^3.5.0", "@midwayjs/axios": "^3.0.0", "@midwayjs/core": "^3.0.0", "@midwayjs/decorator": "^3.0.0", "@midwayjs/faas": "^3.0.0", "@midwayjs/logger": "^3.4.2", "@midwayjs/session": "^3.19.0", "@midwayjs/validate": "^3.19.0", "acorn": "^8.12.1", "alipay-sdk": "^3.2.0", "aliyun-sdk": "^1.12.4", "axios": "^0.24.0", "chalk": "^4.1.2", "cheerio": "1.0.0-rc.12", "co": "^4.6.0", "compressing": "^1.5.1", "cookie": "^1.0.2", "crypto": "^1.0.1", "dayjs": "^1.11.2", "ejs": "^3.1.6", "form-data": "^4.0.0", "fs-extra": "^11.2.0", "hsf-client": "^9.8.1", "jimp": "^0.16.1", "jsqr": "^1.4.0", "lodash": "^4.17.21", "md5": "^2.3.0", "mkdirp": "^1.0.4", "mysql2": "^2.1.0", "qrcode-reader": "^1.0.4", "semver": "^7.3.5", "sequelize": "^6.20.1", "sequelize-typescript": "^2.0.0", "simple-git": "^2.45.1", "urllib": "^2.38.0", "yarn": "^1.22.22"}, "devDependencies": {"@ali/midway-faas-typings": "^2.0.0", "@midwayjs/mock": "^3.12.0", "@types/bluebird": "^3.5.30", "@types/ejs": "^3.0.1", "@types/fs-extra": "^9.0.1", "@types/jest": "29", "@types/node": "16", "@types/qs": "^6.5.3", "@types/validator": "^12.0.1", "cross-env": "^7.0.3", "jest": "29", "mwts": "^1.3.0", "ts-jest": "29", "ts-node": "^10.9.1", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "typescript": "~5.1.0"}, "engines": {"node": ">=16.0.0"}, "midway-cli": {"plugins": ["@ali/midway-cli-plugin-ali"]}}