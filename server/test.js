const fse = require('fs-extra');
const urllib = require('urllib');
const path = require('path');
const Basement = require('@alipay/basement');

/**
 * 上传文件
 */
const basement = new Basement({
  appId: '61c428ded57f620493e0ba10',
  masterKey: '2cfvgCuQY9YGFIpwfOEojBWt',
  urllib,
  endpoint: 'https://basement-gzone.alipay.com'
});
async function uploadToBasement() {
  const file = await fse.readFile(path.join('/Users/<USER>/Desktop', 'bootstrapvalidator.js'), { encoding: 'utf-8' });
  return basement.file.upload('bootstrapvalidator.0.4.5.js', new Buffer(file))
}

uploadToBasement().then(res => console.log(res));