import { homedir, tmpdir } from 'os';
import { exec } from 'child_process';
import * as fse from 'fs-extra';
import * as path from 'path';
import * as mkdirp from 'mkdirp';

export const config = {
  alipay: {
    // <EMAIL> 账号的秘钥
    authentication: {
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      toolId: '3aa12bb270674ab6a789fcdb4862e219'
    }
  }
}

export async function init() {
  const installSuccess = await new Promise((resolve) => {
    console.time('安装小程序构建的依赖包');
    exec('tnpm install yarn @ali/yarn @ali/clam @ali/mini@3.3.0-beta', {
      cwd: tmpdir()
    }, err => {
      console.timeEnd('安装小程序构建的依赖包');
      if (err) {
        console.error(`安装小程序构建的依赖包 >>> 失败：${err.message}`)
        resolve(false);
      } else {
        console.log('安装小程序构建的依赖包 >>> 成功')
        resolve(true)
      }
    })
  })

  if (installSuccess) {
    // 模拟登录
    await login();
  }
}

// 模拟登录
export async function login() {
  console.time('小程序构建环境的模拟登录');
  const dirPath = path.join(homedir(), '.minidev');
  const filePath = path.join(dirPath, 'config.json');
  let initConfig;

  mkdirp.sync(dirPath);

  try {
    const data = await fse.readFile(filePath, {
      encoding: 'utf8',
    });
    console.log('minidev 配置已存在');
    initConfig = JSON.parse(data);
  } catch (err: any) {
    initConfig = {};
  }

  // 写入鉴权配置
  const data = JSON.stringify(Object.assign(initConfig, config));
  await fse.writeFile(filePath, data, {
    encoding: 'utf8',
  });

  console.timeEnd('小程序构建环境的模拟登录');
}