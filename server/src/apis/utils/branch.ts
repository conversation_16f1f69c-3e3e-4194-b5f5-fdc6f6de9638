import { IDevBranch } from "../interface/dev-branch";
import { IFreeDevBranch } from "../interface/free-dev-branch";
import { EDevStatus } from '@/apis/const/dev-branch';
import { IUser } from "../interface/user";
import { EChecked } from "../interface/iter-branch";

/** 获取通知迭代员工号 */
export function getNoticeMemberIds(list: Array<IDevBranch | IFreeDevBranch >, checked?: EChecked, iterQaList?: IUser[]) {
  const memberIds: string[] = [];
  list.forEach(item => {
    const { creatorWorkid, modifierWorkid, qaList = [], checked, status } = item;
    // 已确认回归或者未就绪不通知
    if (checked || status !== EDevStatus.READY) return;
    // 创建人
    if (creatorWorkid) memberIds.push(creatorWorkid);
    // 修改人
    if (modifierWorkid) memberIds.push(modifierWorkid);
    // qa
    memberIds.push(...qaList.map(user => user.workid));
  });

  // 迭代测试
  if (iterQaList && iterQaList.length && !checked) {
    iterQaList.forEach(qa => {
      memberIds.push(qa.workid);
    });
  }
  return Array.from(new Set(memberIds));
}