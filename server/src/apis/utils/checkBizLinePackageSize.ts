import { GROUP_SIZE } from '@/apis/const';
import { formatFileSize } from './file';

export function checkBizLinePackageSize(packageResult, bizLine): {
  isWarning: boolean;
  warningList: {
    isExceed: boolean;
    group: string;
    groupName: string;
    maxSize: string;
    currentSize: string;
    bufferSize: string;
    bizLines: any[];
  }[];
} {
  if (!packageResult || !bizLine) {
    return {
      isWarning: false,
      warningList: [],
    }
  };

  const {
    buildSize = 0, // 构建完成后总包大小
    subPackage = [], // 各个分包大小
  } = packageResult;

  // 按业务类型归类
  const subPackageMap = {};
  subPackage.forEach(item => {
    const itemBuildSize = item.buildSize;
    const itemBizLine = item.bizLine;
    const groupItem = GROUP_SIZE.find(gItem => gItem.bizLines.includes(itemBizLine)) || { group: 'unknown', name: '未知', size: 0 };
    const {
      group,
      name: groupName,
      size: groupMaxSize,
    } = groupItem;

    if (subPackageMap[group]) {
      subPackageMap[group]['buildSize'] += itemBuildSize;
      subPackageMap[group]['bizLines'].push(item);
    } else {
      subPackageMap[group] = {
        group,
        groupName,
        buildSize: itemBuildSize,
        maxSize: groupMaxSize,
        bizLines: [item],
      }
    }
  });

  /**
   * 如果是rc构建，则校验全部的业务类型，有达到预警水位的业务，则告警。
   * 如果是游离开发分支或开发分支，则只校验当前业务类型的大小
   * 
   * 目前暂定水位告警为95%
   * */ 
  const warningPercent = 0.95;
  const warningList: {
    isExceed: boolean;
    group: string;
    groupName: string;
    maxSize: string;
    currentSize: string;
    bufferSize: string;
    bizLines: any[];
  }[] = [];
  if (bizLine === 'all') { 
    const allPackageMaxSize = 20 * 1024 * 1024;
    const currentPackageSize = buildSize;
    const allIsExceed = (currentPackageSize - allPackageMaxSize) > 0;
    const allBufferSize = Math.abs(currentPackageSize - allPackageMaxSize);

    if ((currentPackageSize / allPackageMaxSize) > warningPercent) {
      warningList.push({
        group: 'all',
        groupName: '总包',
        isExceed: allIsExceed,
        bufferSize: formatFileSize(allBufferSize),
        maxSize: formatFileSize(allPackageMaxSize),
        currentSize: formatFileSize(currentPackageSize),
        bizLines: [],
      });
    }

    Object.keys(subPackageMap).forEach(key => {
      const item = subPackageMap[key];
      const {
        groupName,
        buildSize: itemBuildSize,
        maxSize: itemMaxSize,
        bizLines,
      } = item;
      const itemIsExceed = (itemBuildSize - itemMaxSize) > 0;
      const itemBufferSize = Math.abs(itemBuildSize - itemMaxSize);

      if ((itemBuildSize / itemMaxSize) > warningPercent) {
        warningList.push({
          group: key,
          groupName: groupName,
          isExceed: itemIsExceed,
          bufferSize: formatFileSize(itemBufferSize),
          maxSize: formatFileSize(itemMaxSize),
          currentSize: formatFileSize(itemBuildSize),
          bizLines,
        });
      }
    });

    return {
      isWarning: warningList.length > 0,
      warningList,
    }
  } else {
    const { group } = GROUP_SIZE.find(gItem => gItem.bizLines.includes(bizLine)) || { group: 'unknown', name: '未知', size: 0 };
    const {
      groupName,
      buildSize,
      maxSize,
      bizLines,
    } = subPackageMap[group];
    const isExceed = (buildSize - maxSize) > 0;
    const bufferSize = Math.abs(buildSize - maxSize);

    return {
      isWarning: (buildSize / maxSize) > warningPercent,
      warningList: [{
        group,
        groupName,
        isExceed,
        bufferSize: formatFileSize(bufferSize),
        maxSize: formatFileSize(maxSize),
        currentSize: formatFileSize(buildSize),
        bizLines,
      }],
    }
  }
}