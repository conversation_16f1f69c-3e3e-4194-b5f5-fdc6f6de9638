import * as fs from 'fs-extra';
import * as path from 'path';
import * as acorn from 'acorn';
import { getFolderSize } from './file';

const CHAIN_LINK_STR = ' --> ';
let resolvedFiles: string[] = [];

const USER_PACKAGE = {
  MAIN: 'main',
  HOME: 'home',
}

const BIZ_MAP = {
  USER: 'user', // 用户
  TRAFFICX: 'trafficx', // 交通公共
  HOTEL: 'hotel', // 交通酒店
  FLIGHT: 'flight', // 机票
  TRAIN: 'train', // 火车票
  BUS: 'bus', // 汽车票
  VEHICLE: 'vehicle', // 度假
  TICKET: 'ticket', // 门票
  COMMON: 'common', // 公共
  TRAVEL: 'travel', // 旅游度假
};

/** 异步分包归属业务 */
const PACKAGE_BIZ_MAP = {
  main: BIZ_MAP.USER,
  home: BIZ_MAP.USER,
  async: BIZ_MAP.COMMON,
  flight2: BIZ_MAP.FLIGHT,
  flight: BIZ_MAP.FLIGHT,
  'train-search': BIZ_MAP.TRAIN,
  'rent-car': BIZ_MAP.VEHICLE,
  ticket: BIZ_MAP.TICKET,
}

const HEAD_NPM_BIZ_MAP = {
  '@ali/rxpi-mitan-miniapp': BIZ_MAP.COMMON,
  '@ali/rxpi-crm-loader4miniapp': BIZ_MAP.COMMON,
  '@ali/rxpi-miniwork-tool': BIZ_MAP.COMMON,
  '@ali/trafficx-traffic-search': BIZ_MAP.TRAFFICX,
  '@ali/rxpi-hotel-search-new': BIZ_MAP.HOTEL,
  '@ali/rxpi-snapshot-mock-ui-miniapp': BIZ_MAP.HOTEL,
  '@ali/trafficx-flight-search': BIZ_MAP.FLIGHT,
  '@ali/trafficx-train-search': BIZ_MAP.TRAIN,
  '@ali/trafficx-bus-combination-search': BIZ_MAP.BUS,
  '@ali/mono-vehicle-components-wx-rent-car-search-v2': BIZ_MAP.VEHICLE,
  '@ali/mono-vehicle-components-rent-car-list-activity': BIZ_MAP.VEHICLE,
  '@ali/mono-vehicle-components-rent-car-list-activity-wrapper': BIZ_MAP.VEHICLE,
  '@ali/rxpi-ticket-mix-list': BIZ_MAP.TICKET,
  '@ali/rxpi-location-change': BIZ_MAP.TICKET,
  '@ali/rxpi-travel-search-list': BIZ_MAP.TICKET,
  '@ali/rxpi-ticket-search-card2mini': BIZ_MAP.TICKET,
  '@ali/rxpi-main-channel-mini': BIZ_MAP.TRAVEL,
  '@ali/rxpi-landing-recommend-mini': BIZ_MAP.TRAVEL,
};

const bizNpmList = Object.keys(HEAD_NPM_BIZ_MAP);
const IMPORT_TYPE = 'ImportDeclaration';
const REQUIRE_TYPE = 'VariableDeclaration';

/** 获取文件AST中的import 和 require 的值，并做相应的处理 */
function getRequireOrImportPathFromJs(parseContent, fn) {
  parseContent.body.forEach((item) => {
    if (item.type === REQUIRE_TYPE) {
      item.declarations.forEach((val) => {
        if (val.type === 'VariableDeclarator' && val.init && val.init.arguments && val.init.arguments[0]) {
          const { value, arguments: _argus, callee } = val.init.arguments[0];
          if (value && val.init.callee.name === 'require') {
            fn(value);
          } else if (_argus && _argus[0] && _argus[0].value && callee.name === 'require') {
            fn(_argus[0].value);
          }
        }
      });
    } else if (item.type === IMPORT_TYPE) {
      const { value } = item.source;
      fn(value, true);
    }
  });
}

function getSubpackageNpmPath(isTnpm10?: boolean) {
  return isTnpm10 ? 'npm' : 'miniapp-compiled/npm';
}

/** 通过路径来获取当前组件是在哪个分包 */
function getSubpackageName(cpnPath: string, fullPath: string, dirPath: string) {
  // 以pages开头场景，直接取： "mitan-watermark": "/pages/async/miniapp-compiled/npm/__ali_rxpi-mitan-miniapp_1.0.4__ali/rxpi-mitan-miniapp/lib/miniapp-wechat/index"
  if (cpnPath.startsWith('/pages/')) {
    return cpnPath.split('/')[2];
  }
  const cpnRealtivePath = path.relative(path.join(dirPath, 'pages'), fullPath);
  const pathArray = cpnRealtivePath.split(path.sep);
  return pathArray[0];
}

/** 
 * 通过路径获取组件名
 * @param cpnPath json文件中的相对路径
 * @param fullPath 完整路径
 * @param npmDirPath 存放组件的目录路径
 * */
function getCpnNameByPath(cpnPath, fullPath, npmDirPath, isTnpm10?: boolean) {
  const cpnRealtivePath = path.relative(npmDirPath, fullPath);
  const [firstName, secondName] = cpnRealtivePath.split(path.sep);
  if (isTnpm10 && firstName.startsWith('_')) {
    return `${firstName}/${secondName}`;
  }
  return firstName;
}

function formatNpmName(pathName, isTnpm10?: boolean) {
  // tnpm10的包名规则 @ali/rxpi-weixin-login -> _ali/rxpi-weixin-login
  if (isTnpm10) {
    return pathName.replace('@', '_');
  }
  // @ali/rxpi-weixin-login -> __ali_rxpi-weixin-login
  // url-parse -> _url-parse
  return '_' + pathName.replace('@ali/', '_ali_');
}

/** 根据format后的npm找到原npm名 */
function getNpmNameInfo(npmName, isTnpm10?: boolean) {
  const originName = npmName ? bizNpmList.find(name => npmName.includes(formatNpmName(name, isTnpm10))) : '';
  if (originName) {
    return {
      originName,
      npmName,
    };
  }
  return null;
}

/** 获取依赖链的头个npm包 */
function getHeadNpm(prevChain, cpnName, isTnpm10?: boolean) {
  // 优先判断自己
  const cpnNameRes = getNpmNameInfo(cpnName, isTnpm10);
  if (cpnNameRes || !prevChain) {
    return cpnNameRes || { npmName: cpnName, originName: '' };
  }

  const [firstNpm, secondNpm, thidNpm] = prevChain.split(CHAIN_LINK_STR);
  const thirdRes = getNpmNameInfo(thidNpm, isTnpm10);
  if (thirdRes) {
    return thirdRes;
  }
  const secondRes = getNpmNameInfo(secondNpm, isTnpm10);
  if (secondRes) {
    return secondRes;
  }
  return {
    npmName: firstNpm,
    originName: '',
  };
}

function handleFilePath(cpnPath, curFilePath, data, prevChain) {
  const cpnFullPath = cpnPath.startsWith('/pages/') ? path.join(data.dirPath, cpnPath) : path.join(path.dirname(curFilePath), cpnPath);

  // 如果是公共组件，不计入分析
  if (
    !cpnFullPath.includes(path.join(data.dirPath, 'pages')) ||
    cpnPath.startsWith('/miniapp-native/') ||
    cpnPath.startsWith('/common-compiled/npm/') ||
    cpnPath.includes('/common-npm/') ||
    cpnPath.includes('jsx2mp-runtime') ||
    cpnPath.startsWith('plugin://') // 忽略插件
  ) {
    return;
  }
  // 取分包名，递归收集依赖
  // const cpnFullPath = cpnPath.startsWith('/pages/') ? path.join(BUILD_DIR, cpnPath) : path.join(path.dirname(curFilePath), cpnPath);
  const cpnFrom = getSubpackageName(cpnPath, cpnFullPath, data.dirPath);
  const subpkgNpmDirPath =  path.resolve(data.dirPath, `pages/${cpnFrom}/${getSubpackageNpmPath(data.isTnpm10)}`);
  const cpnName = getCpnNameByPath(cpnPath, cpnFullPath, subpkgNpmDirPath, data.isTnpm10);
  if (!cpnFullPath.includes(subpkgNpmDirPath)) {
    return {
      nextChain: prevChain,
      cpnFullPath,
    };
  }

  // 是否当前依赖链的最后一个npm包
  const isLastNpm = prevChain && prevChain.endsWith(cpnName);
  // 找到当前npm包的首个包信息
  const headNpmInfo = getHeadNpm(prevChain, cpnName, data.isTnpm10);
  const { npmName: headNpm, originName } = headNpmInfo;

  if (!data.packageInfo[cpnFrom]) {
    data.packageInfo[cpnFrom] = { depList: [] };
  }
  // 分包依赖，去重
  if (data.packageInfo[cpnFrom].depList.indexOf(cpnName) === -1) {
    data.packageInfo[cpnFrom].depList.push(cpnName);
  }

  // 收集业务npm依赖及依赖链
  if (!prevChain) {
    if (!data.bizNpmMap[cpnName]) {
      data.bizNpmMap[cpnName] = [];
      data.headNpmPkgMap[cpnName] = cpnFrom;
    }
  } else if (!isLastNpm) {
    const cpnItem = `${cpnName}:${cpnFrom}`;
    // 存储一份用于找到来源的分包
    if (cpnName === headNpm) {
      data.headNpmPkgMap[headNpm] = cpnFrom;
    }
    // 存储业务入口npm包对应的子依赖列表
    if (!data.bizNpmMap[headNpm]) {
      data.bizNpmMap[headNpm] = [];
    }
  
    if (cpnName !== headNpm  && data.bizNpmMap[headNpm].indexOf(cpnItem) === -1) {
      data.bizNpmMap[headNpm].push(cpnItem);
    }
  }

  // 存储一份npm名字对应关系
  if (originName) {
    data.originNameMap[headNpm] = originName;
  }

  // 依赖链
  const nextChain = isLastNpm ? prevChain : (prevChain ? `${prevChain}${CHAIN_LINK_STR}${cpnName}` : cpnName);
  if (!isLastNpm) {
    // 0-有后续链路 1-无后续依赖
    if (data.depMap[prevChain]) {
      data.depMap[prevChain] = 0;
    }
    data.depMap[nextChain] = 1;
  }

  return {
    nextChain,
    cpnFullPath,
  }
}

function getJsFilePathWithSuffix(jsPath) {
  if (jsPath.endsWith('.js')) {
    return jsPath;
  }
  return jsPath + '.js';
}

/** 收集 js 依赖 */
function collectJsDeps(jsPath: string, res: Record<string, any>, prevChain?: string) {
  if (resolvedFiles.indexOf(jsPath) > -1) {
    return;
  }
  resolvedFiles.push(jsPath);
  const jsContent = fs.readFileSync(jsPath, { encoding: 'utf-8' });
  if (!jsContent.includes('require(') && !jsContent.includes('import')) {
    return;
  }

  const parseJson = acorn.parse(jsContent, { ecmaVersion: 2022, sourceType: 'module' });
  getRequireOrImportPathFromJs(parseJson, (requriePathValue) => {
    const nextInfo = handleFilePath(requriePathValue, jsPath, res, prevChain);
    if (nextInfo) {
      collectJsDeps(getJsFilePathWithSuffix(nextInfo.cpnFullPath), res, nextInfo.nextChain);
    }
  });
}

/** 收集 json 依赖 */
function collectJsonDeps(jsonPath: string, res: Record<string, any>, prevChain?: string) {
  if (resolvedFiles.indexOf(jsonPath) > -1) {
    return;
  }
  resolvedFiles.push(jsonPath);
  const jsonContent = fs.readJSONSync(jsonPath);
  if (jsonContent.usingComponents) {
    Object.values(jsonContent.usingComponents).forEach(cpnPath => {
      // 如果是公共组件，不计入分析
      const nextInfo = handleFilePath(cpnPath, jsonPath, res, prevChain);
      if (nextInfo) {
        collectDeps(nextInfo.cpnFullPath, res, nextInfo.nextChain);
      }
    });
  }
}

/** 收集依赖 */
function collectDeps(filePath: string, res: Record<string, any>, prevChain?: string) {
  const jsonPath = filePath + '.json';
  const jsPath = filePath + '.js';

  collectJsonDeps(jsonPath, res, prevChain);
  collectJsDeps(jsPath, res, prevChain);
}

/** 获取平台分包npm信息 */
function getUserNpmInfo(res, npmSizeMap) {
  const npmInfo = {};
  const bizNpmSummary = {};
  const { bizNpmMap, originNameMap, packageInfo, headNpmPkgMap } = res;

  const setNpmInfoBiz = (npmName, biz, pkgName) => {
    if (!Object.values(USER_PACKAGE).includes(pkgName)) {
      return;
    }
    if (!npmInfo[pkgName]) {
      npmInfo[pkgName] = {};
    }
    const originData = npmInfo[pkgName][npmName] || {};

    if (originData.bizList && originData.bizList.indexOf(biz) > -1) {
      return;
    }
  
    npmInfo[pkgName][npmName] = {
      ...originData,
      bizList: [...(originData.bizList || []), biz],
    };
  };

  const getPkgName = (npmName) => {
    if (packageInfo.main.depList.some(name => npmName === name)) {
      return USER_PACKAGE.MAIN;
    }
    if (packageInfo.home.depList.some(name => npmName === name)) {
      return USER_PACKAGE.HOME;
    }
    return 'other';
  };

  const setBizNpmSummary = (npmName, biz, pkgName) => {
    if (!bizNpmSummary[biz]) {
      bizNpmSummary[biz] = [];
    }
    bizNpmSummary[biz].push({
      npmName,
      fromPkg: pkgName,
      size: npmSizeMap[npmName],
    });
  }
  
  // 处理npm所属行业
  Object.keys(bizNpmMap).forEach(headNpm => {
    const originName = originNameMap[headNpm] || headNpm;
    const biz = HEAD_NPM_BIZ_MAP[originName] || BIZ_MAP.USER;
    const cpnFrom = headNpmPkgMap[headNpm];
    setNpmInfoBiz(headNpm, biz, getPkgName(headNpm));
    setBizNpmSummary(headNpm, biz, cpnFrom);

    bizNpmMap[headNpm].forEach(item => {
      const [npmName, pkgName] = item.split(':');
      setNpmInfoBiz(npmName, biz, pkgName);
      setBizNpmSummary(npmName, biz, pkgName);
    });
  });
  return {
    npmInfo,
    bizNpmSummary,
  };
}

/** 对平台分包的npm包进行分类 */
function sortDepList(depList, npmInfo, packageName) {
  return depList.reduce((prev, npmName) => {
    const { bizList } = npmInfo[packageName][npmName];
    if (bizList.length === 1) {
      const bizName = bizList[0];
      prev[bizName] = [...(prev[bizName] || []), npmName];
    } else if (
      !bizList.includes(BIZ_MAP.USER) &&
      !bizList.includes(BIZ_MAP.HOTEL) &&
      !bizList.includes(BIZ_MAP.TICKET) &&
      !bizList.includes(BIZ_MAP.VEHICLE) &&
      !bizList.includes(BIZ_MAP.COMMON)
    ) {
      // 交通公共
      prev[BIZ_MAP.TRAFFICX] = [...(prev[BIZ_MAP.TRAFFICX] || []), npmName];
    } else {
      // 公共
      prev[BIZ_MAP.COMMON] = [...(prev[BIZ_MAP.COMMON] || []), npmName];
    }
    return prev;
  }, {});
}

interface INpmItem {
  biz: string,
  npmName: string;
  size: number
}

function formatSizeInfo(res, npmInfo, npmSizeMap: Record<string, number>, uploadSize) {
  const { packageInfo } = res;
  const pkgSizeInfo: Record<string, number> = {};
  const corePkgSizeInfo: Record<string, Record<string, number>> = {};
  const bizSizeInfo: Record<string, number> = {};
  const pkgDepSummary: Record<string, INpmItem[]> = {};

  function setPkgDepSummary(pkg, npmName, biz,) {
    if (!pkgDepSummary[pkg]) {
      pkgDepSummary[pkg] = [];
    }
    pkgDepSummary[pkg].push({
      npmName,
      biz,
      size: npmSizeMap[npmName],
    });
  }

  Object.keys(packageInfo).forEach(pkgName => {
    const { depList } = packageInfo[pkgName];
    // 对平台的分包进行细化分析：行业包占比
    if (Object.values(USER_PACKAGE).includes(pkgName)) {
      // 分包内行业的依赖信息
      const bizDepMap = sortDepList(depList, npmInfo, pkgName);
      // 计算行业体积信息
      const { total, ...sizeInfo } = Object.keys(bizDepMap).reduce((prev, biz) => {
        const bizSize = bizDepMap[biz].reduce((prevNum, npmName) => {
          setPkgDepSummary(pkgName, npmName, biz);
          return prevNum + npmSizeMap[npmName];
        }, 0);
        prev[biz] = bizSize;
        bizSizeInfo[biz] = (bizSizeInfo[biz] || 0) + bizSize;
        prev.total += bizSize;
        return prev;
      }, { total: 0 });
      corePkgSizeInfo[pkgName] = sizeInfo as Record<string, number>;
      pkgSizeInfo[pkgName] = total;
      Object.assign(packageInfo[pkgName], { bizDepMap });
    } else {
      // 行业异步分包的依赖体积计算
      const bizName = PACKAGE_BIZ_MAP[pkgName];
      const total = depList.reduce((prevNum, npmName) => {
        setPkgDepSummary(pkgName, npmName, bizName);
        return prevNum + npmSizeMap[npmName];
      }, 0);
      // 非核心包按分包来计算体积
      bizSizeInfo[bizName] = (bizSizeInfo[bizName] || 0) + total;
      pkgSizeInfo[pkgName] = total;
    }
    Object.assign(packageInfo[pkgName]);
  });

  const mainSize = uploadSize.find(v => v.subPackageName === 'main');
  const totalSizeInfo = uploadSize.find(v => v.subPackageName === 'total');
  const mainHomeSize = pkgSizeInfo.main;
  const homeSize = Object.values(pkgSizeInfo).reduce((prev: number, cur: number) => (prev + cur), 0);

  return {
    // 总数据
    allSizeInfo: {
      totalSize: totalSizeInfo?.buildSize,
      mainSize: mainSize?.buildSize,
      homeSize,
      mainHomeSize,
    },
    // 核心包体积信息
    corePkgSizeInfo,
    // 各分包体积
    pkgSizeInfo,
    // 按业务维度的体积
    bizSizeInfo,
    // 分包维度依赖明细
    pkgDepSummary,
  }
}

function getNpmSizeMap(dirPath, packageInfo: Record<string, { depList: string[] }>, isTnpm10?: boolean) {
  const npmDir = getSubpackageNpmPath(isTnpm10);
  return Object.keys(packageInfo).reduce((prev, pkgName: string) => {
    packageInfo[pkgName].depList.forEach(npmName => {
      if (!prev[npmName]) {
        prev[npmName] = getFolderSize(path.join(dirPath, `pages/${pkgName}/${npmDir}/${npmName}`));
      }
      
    });
    return prev;
  }, {} as Record<string, number>);
}

interface IMainSizeItem {
  size: number;
  prevSize?: number;
  name?: string;
  biz?: string;
  differSize?: number;
}

interface IPrevIterInfo {
  prevMainSize?: Record<string, IMainSizeItem>; // 上个迭代的主包体积信息
  pkgDepSummary?: Record<string, INpmItem[]>; // 上个迭代的包信息汇总
  prevVersion?: string; // 上个迭代版本
  version?: string; // 当前版本
}

// 还原npm包名及版本号
function splitNameAndVersion(val: string) {
  const regex = /\d+\.\d+\.\d+(-beta\.\d+)*/;

  const match = val.match(regex);
  const version = match ? match[0] : '';
  let name = val;
  if (version) {
    name = val.split(`_${version}`)[0].replace('__', '@').replace(/^_/, '').replace('_', '/');
  } else {
    name = val.replace(/^_/, '@');
  }
  return {
    name,
    version,
  };
}

/** 主包体积变更明细 */
function diffMainPkg(
  mainPkgSize: Record<string, IMainSizeItem>,
  prevIter: IPrevIterInfo = {},
  mainNpmList: INpmItem[] = [],
) {
  const { prevMainSize, prevVersion, pkgDepSummary, version } = prevIter || {};
  // 没有上个迭代的信息，直接返回
  if (!prevMainSize || JSON.stringify(prevMainSize) === '{}') {
    return { list: [], total: 0, bizSizeMap: {} };
  }
  // 收集npm包的业务关系
  const npmBizMap = mainNpmList.reduce((prev: any, cur: any) => {
    prev[cur.npmName] = cur.biz;
    return prev;
  }, {});
  const prevNpmBizMap = (pkgDepSummary?.main || []).reduce((prev: any, cur: any) => {
    prev[cur.npmName] = cur.biz;
    return prev;
  }, {});
  // 获取当前版本的体积信息
  const diffMap = Object.keys(mainPkgSize).reduce((prev, filename) => {
    const { version, name } = splitNameAndVersion(filename);
    prev[name] = {
      ...(mainPkgSize[filename]),
      version,
      biz: npmBizMap[filename]
    };
    return prev;
  }, {} as any);
  // 与上个版本对比
  Object.keys(prevMainSize).forEach((filename) => {
    const { version: prevVersion, name } = splitNameAndVersion(filename);
    const { size: prevSize = 0, ...prevInfo } = prevMainSize[filename] || {};
    const { size = 0, ...info } = diffMap[name] || {};
    const prevBiz = prevNpmBizMap[filename]; // 兼容npm包删除场景
    const biz = info.biz || prevBiz || BIZ_MAP.COMMON;
    // 记录前后版本npm包所属的业务不同的数据
    const extra = (info.biz && prevBiz && info.biz !== prevBiz) ? { bizList: [prevBiz, info.biz] } : {};
    diffMap[name] = {
      ...prevInfo,
      ...info,
      extra,
      biz,
      size,
      prevVersion,
      prevSize: prevSize,
      differSize: size - prevSize,
    };
  });
  // 业务统计
  let total = 0;
  const bizSizeMap = {};
   // 过滤有变更的文件或者npm包
  const list = Object.keys(diffMap)
    .reduce((prev, name) => {
      const info = diffMap[name];
      if (info.differSize !== 0 && (info.prevSize || info.size)) {
        // 新增场景
        const biz = info.biz || BIZ_MAP.COMMON;
        const differSize = !info.prevSize ? info.size : info.differSize;
        const prevBizVal = bizSizeMap[biz] || 0;
        total += differSize;
        bizSizeMap[biz] = prevBizVal + differSize;
        prev.push({
          ...info,
          name,
          differSize,
          biz
        });
      }
      return prev;
    }, [] as any)
    .sort((a: any, b: any) => {
      return Math.abs(b.differSize || 0) - Math.abs(a.differSize || 0);
    });
  return {
    total, // 变更总体积
    list, // 有变更的文件列表
    bizSizeMap, // 行业变更统计
    prevVersion,
    version,
  }
}

/** 依赖分析 */
export function analyzeDeps(dirPath: string, uploadSize: Array<{subPackageName: string; buildSize: number;}> = [], prevIter?: any) {
  // 清空缓存数据
  resolvedFiles = [];
  const entryPath = path.resolve(dirPath, 'pages/main/home');
  const isTnpm10 = fs.existsSync(path.join(dirPath, 'common-npm'));
  const initData = { dirPath, packageInfo: {}, headNpmPkgMap: {}, depMap: {}, bizNpmMap: {}, originNameMap: {}, isTnpm10 };
  collectDeps(entryPath, initData);
  const chainList: string[] = Object.keys(initData.depMap).reduce((prev, chain) => {
    if (initData.depMap[chain] && prev.indexOf(chain) === -1) {
      prev.push(chain);
    }
    return prev;
  }, [] as string[]);
  const npmSizeMap = getNpmSizeMap(dirPath, initData.packageInfo, isTnpm10);
  const { npmInfo, bizNpmSummary } = getUserNpmInfo(initData, npmSizeMap);
  const res = formatSizeInfo(initData, npmInfo, npmSizeMap, uploadSize);
  const mainPkgSize = getMainPkgFileSize(dirPath, isTnpm10);
  const mainSizeDetail = diffMainPkg(mainPkgSize, prevIter, res.pkgDepSummary.main);
  return {
    ...res,
    // 行业维度依赖明细
    bizNpmSummary,
    // 依赖链
    chainList,
    // 主包体积变更
    mainSizeDetail,
    // 主包体积明细
    mainPkgSize,
    isTnpm10,
    prevIter,
  };
}

// 设置文件或者文件夹体积信息
function setFileOrNpmSize(res: any, dirPath: string, rootPath, ignoreDir?: boolean, isTnpm10?: boolean) {
  const fileList = fs.readdirSync(dirPath);
  fileList.forEach(fileName => {
    const filePath = path.join(dirPath, fileName);
    const stats = fs.statSync(`${filePath}`);
    const dirname = dirPath.replace(rootPath, '');
    if (stats.isFile()) {
      const resName = res[fileName] ? `${dirname}/${fileName}` : fileName;
      Object.assign(res, {
        [resName]: { size: stats.size, dirname },
      });
    } else if (stats.isDirectory() && !ignoreDir) {
      // tnpm 10的结构是 _ali/rxpi-utils、_ali/rxpi-user
      if (isTnpm10 && fileName.startsWith('_')) {
        const subFileList = fs.readdirSync(filePath);
        subFileList.forEach(subFileName => {
          const subFilePath = path.join(filePath, subFileName);
          Object.assign(res, {
            [`${fileName}/${subFileName}`]: { size: getFolderSize(subFilePath), dirname: `${dirname}/${fileName}` },
          });
        });
      } else {
        Object.assign(res, {
          [fileName]: { size: getFolderSize(filePath), dirname },
        });
      }
    }
  });
}

/** 获取主包体积信息 */
export function getMainPkgFileSize(dirPath: string, isTnpm10?: boolean) {
  const res: any = {};
  const files = fs.readdirSync(dirPath);
  files.forEach(file => {
    const filePath = path.join(dirPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      // 公共npm
      if (file === 'common-compiled') {
        setFileOrNpmSize(res, `${filePath}/npm`, dirPath);
        return;
      }
      if (file === 'common-npm') {
        setFileOrNpmSize(res, filePath, dirPath, false, isTnpm10);
        return;
      }
      // 分包：只分析主包
      if (file === 'pages') {
        const mainFilePath = `${filePath}/main`;
        setFileOrNpmSize(res, mainFilePath, dirPath, true, isTnpm10);
        // 页面入口文件
        if (!isTnpm10) {
          setFileOrNpmSize(res, `${mainFilePath}/miniapp-compiled`, dirPath, true, isTnpm10);
        }
        // npm包
        setFileOrNpmSize(res, `${mainFilePath}/${getSubpackageNpmPath(isTnpm10)}`, dirPath, false, isTnpm10);
        return;
      }
      // 其他
      Object.assign(res, {
        [file]: { size: getFolderSize(filePath) },
      });
      return;
    }
    if (stats.isFile()) {
      Object.assign(res, {
        [file]: { size: stats.size },
      });
      return;
    }
  });
  return res;
}