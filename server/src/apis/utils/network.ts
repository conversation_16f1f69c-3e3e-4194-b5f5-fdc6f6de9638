import Axios from 'axios';
import * as fse from 'fs-extra';
import * as path from 'path';

// 漏洞修复：https://s.alibaba-inc.com/?spm=defwork.home.0.0.a1f35c4b8uVNRc#/securityDefect/778416174
const HOST_REGEX = /(aliyun-inc\.com|alibaba-inc\.com)$/;

/**
 * 下载文件
 * @param fileUrl 文件地址
 * @param dirPath 下载到本地的目录
 * @param fileName 下载后的文件名
 * @returns 
 */
export async function downloadFile(fileUrl: string, dirPath: string, fileName?: string): Promise<IDownloadFileReturn> {
  console.time(`下载 ${fileUrl}`);
  if (!fileUrl || !dirPath) throw Error('入参必须包含文件地址及下载到本地的目录');

  const { host } = new URL(fileUrl);

  if (!HOST_REGEX.test(host)) throw Error(`域名${host}不合法`);

  // 如果指定下载到的本地目录还不存在，则先行创建
  if (!fse.existsSync(dirPath)) fse.mkdirSync(dirPath);


  // 如果没有传 fileName，则尝试从文件地址中自动解析出文件名
  if (!fileName) {
    const regRes = fileUrl.match(/\/([^\/]+)$/);
    fileName = regRes && regRes[1] || String(Date.now());
  }

  // 创建写入对象
  const filePath = path.resolve(dirPath, fileName);
  const writer = fse.createWriteStream(filePath);

  // 以二进制数据流格式请求文件
  const response = await Axios({
    method: 'get',
    url: fileUrl,
    responseType: 'stream',
  })

  // 通过管道实现流的写入
  response.data.pipe(writer);

  return new Promise((resolve, reject) => {
    writer.on('finish', () => {
      console.timeEnd(`下载 ${fileUrl}`);
      resolve({
        dirPath,
        filePath,
        // @ts-ignore
        fileName,
      })
    });
    writer.on('error', reject);
  });
}

interface IDownloadFileReturn {
  /** 文件所处目录 */
  dirPath: string;
  /** 文件地址 */
  filePath: string;
  /** 文件名 */
  fileName: string;
}