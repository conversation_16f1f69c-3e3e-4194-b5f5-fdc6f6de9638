import { IIterBranch } from '@/apis/interface/iter-branch';
import { IterStatus } from '@/apis/const/iter-branch';
import { get } from 'lodash';

enum PERF_TYPE {
  LAUNCH = 'launch',
  WEBVIEW = 'webview',
  NETWORK = 'network',
}

// 同步登录态
const SYNC_LOGIN_PATH = '%2Fskip_sessionfilter%2Flogin_token_login.htm';
const PID_CONFIG: Record<string, string> = {
  weixin: 'haa3j9plpi@ff139ef9b51871b', // 微信
  miniapp: 'haa3j9plpi@1c8365874528b74', // 支付宝
};

const PAGES_MAP = {
  weixin: {
    home: 'pages%2Fmain%2Fhome',
    webview: 'pages%2Fmain%2Fwebview',
  },
  miniapp: {
    home: 'pages%2Findex%2Findex',
    webview: 'pages%2Fwebview%2Findex',
  }
}

// 性能键值
const PERF_KEY_MAP = {
  [PERF_TYPE.LAUNCH]: ['launch_time', 'home_launch_time', 'webview_launch_time'],
  [PERF_TYPE.WEBVIEW]: ['webview_time', 'webview_nologin_time'],
  [PERF_TYPE.NETWORK]: ['wait_time', 'request_time'],
};

const PERF_NAME_MAP = {
  [PERF_TYPE.LAUNCH]: '启动性能',
  [PERF_TYPE.WEBVIEW]: '套壳性能',
  [PERF_TYPE.NETWORK]: '网络性能',
}

const PERF_KEY_NAME_MAP = {
  launch_time: '启动耗时',
  home_launch_time: '首页启动耗时',
  webview_launch_time: '套壳启动耗时',
  webview_time: '套壳容器耗时',
  webview_nologin_time: '套壳容器耗时（不含同步登录态）',
  wait_time: '请求等待耗时',
  request_time: '请求耗时',
};

/** 样本量 */
const PERF_KEY_COUNT_MAP = {
  launch_time: 'total',
  home_launch_time: 'home_count',
  webview_launch_time: 'webview_count',
  webview_time: 'total',
  webview_nologin_time: 'webview_nologin_count',
  wait_time: 'total',
  request_time: 'total',
}

const PLATFORM_MAP = {
  weixin: 'wechat',
  alipay: 'miniapp',
};

function getPerfQuerys(version: string, platform: string) {
  const versionSql = `and release: ${version}`;
  const pageConfig = PAGES_MAP[platform];
  const launchPageSql = Object.keys(pageConfig)
    .map(pageName => `avg(CASE WHEN c3 = '${pageConfig[pageName]}' THEN t4 END) as ${pageName}_launch_time, SUM(IF(c3 = '${pageConfig[pageName]}', 1, 0)) as ${pageName}_count`)
    .join(',');
  const webviewAvgSql = `avg(t6) as webview_time, avg(CASE WHEN c2 != '${SYNC_LOGIN_PATH}' THEN t6 END) as webview_nologin_time, SUM(IF(c2 != '${SYNC_LOGIN_PATH}', 1, 0)) as webview_nologin_count`;
  return [
    // 启动性能
    `t:perf and c1: launch ${versionSql} and t4 >=0 and t4 <= 60000 | select count(*) as total, avg(t4) as launch_time, ${launchPageSql}`,
    // 套壳容器性能
    `t:perf and c1 not launch and t6 >= 0 and t10 >= 0 and t6 <= 6000 ${versionSql} | select count(*) as total, ${webviewAvgSql} `,
    // 网络性能
    `c1: weixin-mtop-controller ${versionSql} | select count(*) as total, avg(CAST(c2 AS INTEGER)) as wait_time, avg(time) as request_time `,
  ];
}

function getEndTime(iterBranch: IIterBranch, deliverStartTime?: string | null) {
  // 开始投放的时间
  if (deliverStartTime) {
    return new Date(deliverStartTime).getTime();
  }
  // 集成中
  if (iterBranch.status === IterStatus.MERGE) {
    return new Date().getTime();
  }
  // 兜底发布日
  return new Date(`${iterBranch.publishDay} 23:59:59`).getTime();
}

/** 获取性能日志的请求参数 */
export function getPerfLogParams(iterBranch: IIterBranch, platform: string, deliverStartTime?: string | null) {
  const { version, publishDay } = iterBranch;
  return getPerfQuerys(version, platform).map(query => ({
    startTime: new Date(`${publishDay} 00:00:00`).getTime(),
    endTime: getEndTime(iterBranch, deliverStartTime),
    pid: PID_CONFIG[platform],
    query,
  }));
}

function getPerfData(res: any) {
  const launch = get(res, 'data[0].value.0', {});
  const webview = get(res, 'data[1].value.0', {});
  const network = get(res, 'data[2].value.0', {});
  const invalid = (formatValue(launch.total) + formatValue(webview.total) + formatValue(network.total)) === 0;
  return {
    launch,
    webview,
    network,
    invalid,
  };
}

function formatValue(data) {
  if (data === 'null' || !data) {
    return 0;
  }
  return +data;
}

function getDiffInfo(data: any, prevData: any, perfKeys: string[]) {
  return perfKeys.reduce((prev, keyName) => {
    const prevPerfVal = formatValue(prevData?.[keyName]);
    if (prevPerfVal === 0) return prev;
    return {
      ...prev,
      [keyName]: formatValue(data?.[keyName]) - formatValue(prevData?.[keyName]),
    }
  }, {});
}

/** 处理性能数据 */
export function formatPerfLog(curData: any, prevData: any) {
  const perfData = getPerfData(curData);
  const prevPerfData = getPerfData(prevData);
  return {
    perfData,
    prevPerfData,
    diffInfo: Object.values(PERF_TYPE).reduce((prev, perfName) => ({
      ...prev,
      [perfName]: getDiffInfo(perfData[perfName], prevPerfData[perfName], PERF_KEY_MAP[perfName]),
    }), {}),
  };
}

function getPerfText(name: string, value: number, diffValue: number, count: number, prevCount?: string) {
  const diffAbsValue = Math.ceil(Math.abs(formatValue(diffValue)));
  const showValue = Math.ceil(formatValue(value));
  const valueText = `${name}${showValue}ms`;
  const curCount = formatValue(count);
  const prevCountText = formatValue(prevCount) ? ` VS ${prevCount}` : '';
  const countText = curCount ? `，样本量${count}${prevCountText}` : '';
  
  if (!showValue) {
    return `${name}（暂无数据）`;
  }

  if (diffValue === 0 || !diffValue) {
    return `${valueText}${countText}`;
  }
  if (diffValue < 0) {
    return `${valueText}（<font color='#52c41a'>**↓**</font>${diffAbsValue}ms）${countText}`
  } 
  return `${valueText}（<font color='#cf1322'>**↑**</font>${diffAbsValue}ms）${countText}`
}

export function getPublishReportMsg(res, platform: string, versionList: string[]) {
  if (!res || !res.diffInfo || !res.perfData || res.perfData.invalid) {
    return {
      text: '',
      atUserIds: [],
      actions: [],
    };
  }
  const { diffInfo, perfData, prevPerfData } = res;
  const textList = [
      `#### <font color='#1890ff' size=7>**迭代性能**</font> `,
      ...Object.keys(PERF_KEY_MAP).map((perfName) => {
        const data = perfData[perfName];
        const perfText = PERF_KEY_MAP[perfName].map(key => {
          const countKey = PERF_KEY_COUNT_MAP[key];
          const count = data[countKey];
          const prevCount = prevPerfData[perfName][countKey];
          return getPerfText(PERF_KEY_NAME_MAP[key], data[key], diffInfo[perfName][key], count, prevCount);
        }).join('；');
        return `**${PERF_NAME_MAP[perfName]}** ${perfText}`;
      }),      
      `<font color='#999999'>说明：数据来源于体验码访问日志，网络性能中的指标仅包含被阻塞请求</font>`,
    ];
  return {
    text: textList.join('\n\n'),
    atUserIds: [],
    // 暂时不展示明细
    actions: [],
    // actions: [{
    //    title: '查看性能详情',
    //    actionURL: `https://fl-bz.fc.alibaba-inc.com/#/special/MiniappPerf/${PLATFORM_MAP[platform]}?versionList=${encodeURIComponent(JSON.stringify(versionList))}`
    // }]
  };
}
