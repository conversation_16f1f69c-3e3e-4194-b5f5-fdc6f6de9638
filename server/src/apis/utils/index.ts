import * as path from 'path';
const lodashString = require('lodash/string');

function convertsCollection(collection, convertsMethod, depth: number = Infinity) {
  if (depth === 0) return collection;

  if (Array.isArray(collection)) {
    return collection.map(item => convertsCollection(item, convertsMethod, depth - 1))
  } else if (Object.prototype.toString.call(collection) === '[object Object]') {
    return Object.keys(collection).reduce((pre, key) => {
      pre[convertsMethod(key)] = convertsCollection(collection[key], convertsMethod, depth - 1)
      return pre;
    }, {})
  } else {
    return collection;
  }
}

export function convertsCollectionToSnake(collection, depth: number = Infinity) {
  return convertsCollection(collection, lodashString.snakeCase, depth)
}

export function convertsCollectionToCamel(collection, depth: number = Infinity) {
  return convertsCollection(collection, lodashString.camelCase, depth)
}

/** 过滤掉 null 值 */
export function filterOutNull(collection) {
  return Object.keys(collection).reduce((pre, key) => {
    if (collection[key] !== null) pre[key] = collection[key];
    return pre;
  }, {})
}

/** 过滤掉 undefined 值 */
export function filterOutUndefined(collection) {
  return Object.keys(collection).reduce((pre, key) => {
    if (collection[key] !== undefined) pre[key] = collection[key];
    return pre;
  }, {})
}

export function formatDate(date) {
  if (typeof date === 'string') {
    date = new Date(date);
  }

  const formatter = new Intl.DateTimeFormat('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
  return formatter.format(date).replaceAll('/', '-');
}

// 环境变量
const env = process.env.MIDWAY_SERVER_ENV;
export const isDaily = env === 'local' || env === 'daily';
export const isPre = env === 'pre';
export const isProd = env === 'prod';
export const isServer = path.resolve() === '/code'; // server 环境


// 工号处理，兼容 001234，WB1234这种工号
export function removeWorkidPrefixZero(workid: string) {
  return String(+workid || workid); // 兼容 001234，WB1234这种工号
}

// 加密字符串，中间部分使用*替代
export function maskString(str: string) {
  const len = str.length;
  let partLen = 0; // 头尾各自保留的字数

  switch (true) {
    case len > 10: partLen = 3; break;
    case len > 6: partLen = 2; break;
    case len > 4: partLen = 1; break;
    default: partLen = 0;
  }

  const start = str.substring(0, partLen);
  const end = str.substring(len - partLen);
  return start + '******' + end;
}

export async function sleepFunc(delay = 10000) {
  return new Promise((res, rej) => {
    setTimeout(() => {
      res(true);
    }, delay);
  });
} 

// 预加载的key值获取逻辑

export const genQueryStr = (query:any={}, whiteList = [], blackList = []) => {
  // '_fz_web_base_cache_key',
  // const whiteList = ['_fz_from_wv', 'webviewTransparentTitle', 'titleBarHidden', 'disableNav', '_fz_cli_cache_key'];
  let queryList: any = [];
  if (whiteList.length > 0) {
    queryList = [].concat(whiteList);
  } else if (query._preKeyParams) {
    queryList = [].concat(decodeURIComponent(query._preKeyParams).split(',') as any);
  } else {
    queryList = Object.keys(query).sort().filter((item: any) => {
      const itemNew:any = item.toLocaleLowerCase()
      return !(blackList as any).includes(itemNew);
    })
  }
  return queryList.reduce((pre, cur, idx) => {
    return `${pre}${idx !== 0 ? '&' : ''}${cur}=${query[cur] || ''}`;
  }, '')
}