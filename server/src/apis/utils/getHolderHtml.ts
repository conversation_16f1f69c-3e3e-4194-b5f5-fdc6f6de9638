import * as cheerio from 'cheerio';
import Axios from 'axios';

function compressHTML(html) {
  // 去除注释
  html = html.replace(/<!--[\s\S]*?-->/g, '');
  // 去除多余空白
  html = html.replace(/\s+/g, ' ');
  // 去除标签之间空格
  html = html.replace(/>\s+</g, '><');
  return html.trim();
}

export async function getHolderHtml(opt: any) {
  const {
    group,
    version,
    projectName,
    pageName,
    env,
  } = opt;
  try {
    const htmlUrl = `https://${env === 'pre' ? 'dev.g' : 'g'}.alicdn.com/${group}/${projectName}/${version}/pages/${(pageName || '').replace('/index.html', '')}/index.html`;
    const html = await Axios({
      method: 'get',
      url: htmlUrl,
      responseType: 'document',
    }).then(res => {
      return res.data || '';
    }).catch(err => {
      return '';
    });

    if (html) {
      const htmlObj = cheerio.load(html);
      htmlObj('#ssr-er-holder').removeAttr('style');
      const holderDiv = htmlObj.html('#ssr-er-holder');
      // 压缩字符串
      const compressHolderHTML = compressHTML(holderDiv);
  
      return compressHolderHTML;  
    }

    return '';
  } catch (err: any) {
    return '';
  }
}