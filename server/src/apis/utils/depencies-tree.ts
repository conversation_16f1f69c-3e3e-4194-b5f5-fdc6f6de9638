import { IShrinkwrap, IShrinkwrapJson } from '@/apis/interface/helper';

const BLACK_LIST = ['@ali/clam', '@ali/uniapi'];

const setDepTreeRecord = (record: Record<string, any>, { name, version, parent }) => {
  const title = `${name}@${version}`;
  // 生成当前依赖的依赖链
  const currentChain = parent ? `${parent} -> ${title}` : `${title}`;
  // 记录当前依赖的版本号和依赖链
  if (record[name]) {
    // 如果是相同版本依赖，则更新依赖链，否则插入新版本
    let sameVersion = false;
    record[name].forEach(depend => {
      // 如果该版本已存在，则判断依赖链
      if (depend.version === version) {
        sameVersion = true;
        const dependIndex = depend.chain.findIndex((c: string) => c.includes(currentChain) || currentChain.includes(c));
        if (dependIndex >= 0) {
          // 如果是子母依赖链，取最长的依赖链版本
          depend.chain[dependIndex] = depend.chain[dependIndex].length > currentChain.length ? depend.chain[dependIndex] : currentChain;
        } else {
          // 否则认为是全新依赖链
          depend.chain.push(currentChain);
        }
      }
    });
    // 如果不是相同版本，则认为是新增依赖
    if (!sameVersion) {
      record[name].push({ version: version, chain: [currentChain] });
    }
  } else {
    record[name] = [{ version: version, chain: [currentChain] }];
  }
  return currentChain
}

 const traverseDependencies = (depends: IShrinkwrap['dependencies'], parent: string | null, record: Record<string, any>) => {
  if (!depends) return record;
  Object.entries(depends).forEach(([name, item]) => {        // 如果 npm 包以 “_” 开头，或者没有 version，或者 version 不是合法地址，则直接返回
    if (name.charAt(0) === '_' || item.optional || !item.version || /^http/.test(item.version)) {
      return;
    }
    const currentChain = setDepTreeRecord(record, { name, version: item.version, parent });
    // 深度遍历子依赖
    traverseDependencies(item.dependencies, currentChain, record);
  });
  return record;
};

const getDepName = (packages: IShrinkwrap, name: string, depChainList: string[]) => {
  const listLen = depChainList.length;
  for (let i = 0; i < listLen; i++) {
    const chainDepName = depChainList.slice(i, listLen).join('/node_modules/');
    const depName = `node_modules/${chainDepName}${chainDepName ? '/node_modules/' : ''}${name}`;
    if (packages[depName]) {
      return depName;
    }
  }
  return `node_modules/${name}`;
};

const getLock3DepTree = (packages: IShrinkwrap, deps: IShrinkwrap['dependencies'], record: Record<string, any>, parent: string | null, depChainList: string[], visited: Set<string> = new Set()) => {
  if (!deps || !Object.keys(deps).length) return;
  Object.keys(deps).forEach((name) => {
    const depName = getDepName(packages, name, depChainList);
    const item = packages[depName] || {};
    const { version, optional } = item;

    // 创建唯一标识符，包含包名、版本和依赖链路径
    const uniqueKey = `${name}@${version}:${depChainList.join('->')}`;

    console.log(depChainList.join(' -> '));

    // 增强的循环检测：检查是否已经访问过相同的包+版本+路径组合
    if (!version || /^http/.test(version) || optional || BLACK_LIST.includes(name) || visited.has(uniqueKey)) {
      return;
    }

    // 检查是否在当前依赖链中形成循环
    if (depChainList.includes(name)) {
      console.warn(`Circular dependency detected: ${depChainList.join(' -> ')} -> ${name}`);
      return;
    }

    // 标记当前节点为已访问
    visited.add(uniqueKey);

    const currentChain = setDepTreeRecord(record, { name, version, parent });
    getLock3DepTree(packages, item.dependencies, record, currentChain, [...depChainList, name], visited);

    // 递归完成后移除标记，允许在其他路径中访问相同的包
    visited.delete(uniqueKey);
  });
}

/**
 * 查找多版本依赖
 * @param dependencies 依赖树
 * @param parent 父依赖链
 * @param record 依赖记录
 * @returns 多版本依赖
 */
export const getMultiVersionDependencies = (shrinkwrapJson?: IShrinkwrapJson) => {
  if (!shrinkwrapJson) return {};
  const record: Record<string, any> = {};
  if (shrinkwrapJson.lockfileVersion === 3) {
    const packages = shrinkwrapJson.packages || {} as IShrinkwrap;
    const rootDeps = packages['']?.dependencies || {};
    getLock3DepTree(packages, rootDeps, record, null, []);
  } else {
    traverseDependencies(shrinkwrapJson.dependencies as IShrinkwrap['dependencies'], null, record);
  }

  console.log(22222, record);
  // 如果只有1个版本，则删除记录
  Object.keys(record).forEach((dependName) => {
    const versions = record[dependName];
    if (versions.length <= 1) {
      delete record[dependName];
    }
  });
  return record;
}

/** 获取json文件中的依赖信息 */
function getJsonDependenciesInfo(shrinkwrapJson?: IShrinkwrapJson) {
  const packages = shrinkwrapJson?.packages || {} as IShrinkwrap;
  const rootDeps = packages['']?.dependencies || {};
  const isLock3 = shrinkwrapJson?.lockfileVersion === 3;
  return {
    packages,
    rootDeps,
    dependencies: isLock3 ? rootDeps : shrinkwrapJson?.dependencies || {},
    isLock3,
  }
}

/** diff 依赖 */
const diffDependenciesRecursively = (shrinkwrapJson: IShrinkwrapJson | undefined, ctrlShrinkwrapJson: IShrinkwrapJson | undefined) => {
  let addCount = 0;
  let deleteCount = 0;
  let modifyCount = 0;
  let key = 0;
  // 1. dependencies 为空，则 diff 结果也为空
  if (!shrinkwrapJson) {
    return {
      diffTree: [],
      addCount,
      deleteCount,
      modifyCount,
    };
  }
  const dependenciesInfo = getJsonDependenciesInfo(shrinkwrapJson);
  const ctrlDependenciesInfo = getJsonDependenciesInfo(ctrlShrinkwrapJson);

  const getDiffTree = (depends, ctrlDepends) => {
    // 1. dependencies 为空，则 diff 结果也为空
    if (!depends) return [];
    // 2. 先 diff 出“新增”、“修改”
    const dependenciesList = Object.entries(depends).map(([name, _item]) => {
      const depName = dependenciesInfo.isLock3 ? getDepName(dependenciesInfo.packages, name, []) : '';
      const item = dependenciesInfo.isLock3 ? dependenciesInfo.packages[depName] : _item;
      // 如果 npm 包以 “_” 开头，或者没有 version，或者 version 不是合法地址，则直接返回
      if (name.charAt(0) === '_' || item.optional || !item.version || /^http/.test(item.version)) {
        return;
      }

      // 删除对照组同名依赖，以便执行第3步
      const ctrlItem = ctrlDepends && ctrlDepends[name];
      if (ctrlDepends) delete ctrlDepends[name];

      // 深度遍历
      const childrenDiffRes = getDiffTree(item.dependencies, ctrlItem?.dependencies);

      // 与参照组进行 diff
      let diffType;
      let diffVersion;
      if (ctrlItem) {
        if (ctrlItem.version !== item.version) { // 如果版本号不同
          diffType = 'modify';
          modifyCount++;
          diffVersion = ctrlItem.version;
        } else if (childrenDiffRes.length === 0) { // 如果版本号相同，切子节点无 diff，则返回空
          return;
        }
      } else { // 参照组无同名依赖，则认为是新增的
        diffType = 'add';
        addCount++;
      }

      return {
        key: `${++key}-${name}`,
        title: `${name}@${item.version}`,
        isLeaf: childrenDiffRes.length === 0,
        name,
        version: item.version,
        diffType,
        diffVersion,
        children: childrenDiffRes
      }
    });
    // 3. 再 diff 出“删除”
    if (!ctrlDepends) return dependenciesList.filter(item => !!item);
    const ctrlDependenciesList = Object.entries(ctrlDepends).map(([name, _ctrlItem]) => {
      const depName = ctrlDependenciesInfo.isLock3 ? getDepName(ctrlDependenciesInfo.packages, name, []) : '';
      const ctrlItem = ctrlDependenciesInfo.isLock3 ? ctrlDependenciesInfo.packages[depName] : _ctrlItem;
      // 如果 npm 包以 “_” 开头，或者没有 version，或者 version 不是合法地址，则直接返回
      if (name.charAt(0) === '_' || ctrlItem.optional || !ctrlItem.version || /^http/.test(ctrlItem.version)) {
        return;
      }

      deleteCount++;
      return {
        key: `${++key}-${name}`,
        title: `${name}@${ctrlItem.version}`,
        isLeaf: true,
        name: name,
        version: ctrlItem.version,
        diffType: 'delete',
      }
    })
    return dependenciesList.concat(ctrlDependenciesList).filter(item => !!item);
  };

  return {
    diffTree: getDiffTree(dependenciesInfo.dependencies, ctrlDependenciesInfo.dependencies),
    addCount,
    deleteCount,
    modifyCount,
  };
}

/** 获取依赖树 */
export const getDependenciesDiffTree = (shrinkwrapJson: IShrinkwrapJson, ctrlShrinkwrapJson?: IShrinkwrapJson) => {
  if (!shrinkwrapJson) {
    return {
      diffTree: [],
      multiVersionTree: [],
      addCount: 0,
      deleteCount: 0,
      modifyCount: 0,
      multiVersionCount: 0,
      multiVersionPackageCount: 0,
    };
  }
  // 重复依赖（多版本）
  const multiVersionTree:any[] = [];
  const duplicatedVersions = getMultiVersionDependencies(shrinkwrapJson);
  console.log(211132423)
  const ctrlDuplicatedVersions = getMultiVersionDependencies(ctrlShrinkwrapJson);
  console.log(54444444, duplicatedVersions,ctrlDuplicatedVersions)
  Object.entries(duplicatedVersions).forEach(([dependName, dependVersions]) => {
    // 对比的依赖
    const ctrlVersions = ctrlDuplicatedVersions[dependName];
    const getChildDiffType = (depend: any, isCtrl: boolean) => {
      if (!isCtrl) {
        return {
          diffType: 'delete',
        };
      }
      if (!ctrlVersions || !ctrlVersions.length) {
        return {};
      }
      const ctrlVersionIndex = ctrlVersions.findIndex(v => depend.version === v.version);
      if (ctrlVersionIndex < 0) {
        return {
          diffType: 'modify',
          childrenDiffType: 'add',
        };
      }
      ctrlVersions.splice(ctrlVersionIndex, 1);
      return {
        diffType: 'modify',
        childrenDiffType: 'add',
      }
    }
    const getChildren = (versionList: any[], isCtrl = false) => versionList.map((depend) => {
      const { diffType, childrenDiffType } = getChildDiffType(depend, isCtrl);
      return {
        diffType, 
        isLeaf: false,
        name: depend.version,
        title: depend.version,
        key: `${dependName}-${depend.version}`,
        isVersion: true,
        children: depend.chain.map((chain, index) => ({
          isLeaf: true,
          name: chain,
          title: chain,
          childrenDiffType,
          key: `${dependName}-${depend.version}-${index}`,
          isChain: true,
        })),
      };
    });
    multiVersionTree.push({
       isLeaf: false,
      name: dependName,
      title: dependName,
      key: dependName,
      diffType: ctrlVersions && ctrlVersions.length ? 'modify' : 'add',
      children: getChildren(dependVersions).concat(getChildren(ctrlVersions, true)),
    });
  });

  const multiVersionPackageCount = multiVersionTree.length;
  const multiVersionCount = multiVersionTree.reduce((prev, current) => prev + (current && current.children && current.children.length || 0), 0);

  return {
    ...diffDependenciesRecursively(shrinkwrapJson, ctrlShrinkwrapJson),
    multiVersionPackageCount,
    multiVersionCount,
    //// 对多版本依赖进行排序
    multiVersionTree: multiVersionTree.sort((a, b) => {
      if(a.isReal && !b.isReal) {
        return -1;
      } else if (!a.isReal && b.isReal) {
        return 1;
      } else {
        return b.children.length - a.children.length;
      }
    }),
  }
}