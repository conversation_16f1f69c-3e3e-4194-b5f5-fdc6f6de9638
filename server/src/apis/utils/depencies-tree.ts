import { IShrinkwrap, IShrinkwrapJson } from '@/apis/interface/helper';

const BLACK_LIST = ['@ali/clam', '@ali/uni-api'];

export const isInValidDep = (name: string, item: { version: string; optional?: boolean }, parent?: string | null) => {
  const { version, optional } = item || {};
  return name.charAt(0) === '_' || !version || optional || (parent && parent.indexOf(`${name}@${version}`) > -1) || BLACK_LIST.includes(name);
}

export functio

const setDepTreeRecord = (record: Record<string, any>, { name, version, parent }) => {
  const title = `${name}@${version}`;
  // 生成当前依赖的依赖链
  const currentChain = parent ? `${parent} -> ${title}` : `${title}`;
  const oldInfo = record[name] || [];
  // 如果是相同版本依赖，则更新依赖链，否则插入新版本
  if (oldInfo.findIndex((depend) => depend.version === version) > -1) {
    record[name] = oldInfo.map((v) => {
      if (v.version === version && v.chain.findIndex((c: string) => c.includes(currentChain)) === -1) {
        v.chain.push(currentChain);
      }
      return v;
    });
  } else {
    // 记录当前依赖的版本号和依赖链
    record[name] = [...oldInfo, { version, chain: [currentChain] }];
  }
  return currentChain
}

const traverseDependencies = (depends: IShrinkwrap['dependencies'], parent: string | null, record: Record<string, any>) => {
  if (!depends) return record;
  Object.entries(depends).forEach(([name, item]) => {
    // 如果 npm 包以 “_” 开头，或者没有 version，或者 version 不是合法地址，则直接返回
    if (name === '@ali/flight-oasis-home-passenger-tips') {
      console.log(1111, item, parent, isInValidDep(name, item, parent), item.version);
    }
    if (!isInValidDep(name, item, parent)) {
      return;
    }
    
    const currentChain = setDepTreeRecord(record, { name, version: item.version, parent });
    // 深度遍历子依赖
    traverseDependencies(item.dependencies, currentChain, record);
  });
  return record;
};

const getDepName = (packages: IShrinkwrap, name: string, depChainList: string[]) => {
  const listLen = depChainList.length;
  for (let i = 0; i < listLen; i++) {
    const chainDepName = depChainList.slice(i, listLen).join('/node_modules/');
    const depName = `node_modules/${chainDepName}${chainDepName ? '/node_modules/' : ''}${name}`;
    if (packages[depName]) {
      return depName;
    }
  }
  return `node_modules/${name}`;
};

const getLock3DepTree = (packages: IShrinkwrap, deps: IShrinkwrap['dependencies'], record: Record<string, any>, parent: string | null, depChainList: string[]) => {
  if (!deps || !Object.keys(deps).length) {
    return record;
  };
  Object.keys(deps).forEach((name) => {
    const depName = getDepName(packages, name, depChainList);
    const item = packages[depName] || {};
    const { version } = item;
    const prevInfo = record[name];
 
    const isSame = prevInfo && prevInfo.find((depend) => depend.version === version);
    if (isInValidDep(name, item, parent) || isSame) return;
    const currentChain = setDepTreeRecord(record, { name, version, parent });
    if (isSame) return;
    getLock3DepTree(packages, item.dependencies, record, currentChain, [...depChainList, name]);
  });
  return record;
}

/**
 * 查找多版本依赖
 * @param dependencies 依赖树
 * @param parent 父依赖链
 * @param record 依赖记录
 * @returns 多版本依赖
 */
export const getMultiVersionDependencies = (shrinkwrapJson?: IShrinkwrapJson) => {
  if (!shrinkwrapJson) return {};
  const record: Record<string, any> = {};
  if (shrinkwrapJson.lockfileVersion === 3) {
    const packages = shrinkwrapJson.packages || {} as IShrinkwrap;
    const rootDeps = packages['']?.dependencies || {};
    getLock3DepTree(packages, rootDeps, record, null, []);
  } else {
    traverseDependencies(shrinkwrapJson.dependencies as IShrinkwrap['dependencies'], null, record);
  }
  // 过滤出多版本的数据
  return Object.keys(record).reduce((prev, dependName) => {
    const versions = record[dependName];
    if (versions.length > 1) {
      prev[dependName] = versions;
    }
    return prev;
  }, {} as Record<string, any>);
}

/** 获取json文件中的依赖信息 */
function getJsonDependenciesInfo(shrinkwrapJson?: IShrinkwrapJson) {
  const packages = shrinkwrapJson?.packages || {} as IShrinkwrap;
  const rootDeps = packages['']?.dependencies || {};
  const isLock3 = shrinkwrapJson?.lockfileVersion === 3;
  return {
    packages,
    rootDeps,
    dependencies: isLock3 ? rootDeps : shrinkwrapJson?.dependencies || {},
    isLock3,
  }
}

/** diff 依赖 */
const diffDependenciesRecursively = (shrinkwrapJson: IShrinkwrapJson | undefined, ctrlShrinkwrapJson: IShrinkwrapJson | undefined) => {
  let addCount = 0;
  let deleteCount = 0;
  let modifyCount = 0;
  let totalCount = 0;
  let realCount = 0;
  let key = 0;
  // 1. dependencies 为空，则 diff 结果也为空
  if (!shrinkwrapJson) {
    return {
      diffTree: [],
      addCount,
      deleteCount,
      modifyCount,
    };
  }
  const dependenciesInfo = getJsonDependenciesInfo(shrinkwrapJson);
  const ctrlDependenciesInfo = getJsonDependenciesInfo(ctrlShrinkwrapJson);
  const hasCtrlShrinkwrapJson = ctrlShrinkwrapJson && Object.keys(ctrlShrinkwrapJson).length;
  const cacheMap = {};

  const getDiffTree = (depends, ctrlDepends, parent = '') => {
    // 1. dependencies 为空，则 diff 结果也为空
    if (!depends) return [];
    // 2. 先 diff 出“新增”、“修改”
    const dependenciesList = Object.entries(depends).map(([name, _item]) => {
      const depName = dependenciesInfo.isLock3 ? getDepName(dependenciesInfo.packages, name, []) : '';
      const item = dependenciesInfo.isLock3 ? dependenciesInfo.packages[depName] : _item;
      const { version } = item || {};
      // 如果 npm 包以 “_” 开头，或者没有 version，或者 version 不是合法地址，则直接返回
      if (isInValidDep(name, item, parent) || (dependenciesInfo.isLock3 && parent && depName === `node_modules/${name}` && dependenciesInfo.rootDeps[name])) {
        if (/^http/.test(version)) {
          realCount++;
        }
        return;
      }

      // 删除对照组同名依赖，以便执行第3步
      const ctrlItem = ctrlDepends && ctrlDepends[name];
      if (ctrlItem) delete ctrlDepends[name];

      const currentChain = `${name}@${version}`;
      let childrenDiffRes;

      if (cacheMap[currentChain]) {
        childrenDiffRes = cacheMap[currentChain];
      } else {
        childrenDiffRes = parent.indexOf(currentChain) > - 1 ? [] : getDiffTree(item.dependencies, ctrlItem?.dependencies, `${parent}${parent ? ' -> ' : ''}${currentChain}`);
        cacheMap[currentChain] = childrenDiffRes;
      }

      // 与参照组进行 diff
      let diffType;
      let diffVersion;
      if (ctrlItem && ctrlItem.version === item.version && childrenDiffRes.length === 0) {
        return;
      }
      if (hasCtrlShrinkwrapJson && !ctrlItem) {
        diffType = 'add';
        addCount++;
      } else if (hasCtrlShrinkwrapJson && ctrlItem.version !== item.version) {
        diffType = 'modify';
        modifyCount++;
        diffVersion = ctrlItem.version;
      }

      totalCount++;

      return {
        key: `${++key}-${name}`,
        title: `${name}@${item.version}`,
        isLeaf: childrenDiffRes.length === 0,
        name,
        version: item.version,
        diffType,
        diffVersion,
        children: childrenDiffRes
      }
    });
    // 3. 再 diff 出“删除”
    const ctrlDependenciesList = Object.entries(ctrlDepends || {}).map(([name, _ctrlItem]) => {
      const depName = ctrlDependenciesInfo.isLock3 ? getDepName(ctrlDependenciesInfo.packages, name, []) : '';
      const ctrlItem = ctrlDependenciesInfo.isLock3 ? ctrlDependenciesInfo.packages[depName] : _ctrlItem;
      // 如果 npm 包以 “_” 开头，或者没有 version，或者 version 不是合法地址，则直接返回
      if (isInValidDep(name, ctrlItem, parent) || (ctrlDependenciesInfo.isLock3 && parent && depName === `node_modules/${name}` && ctrlDependenciesInfo.rootDeps[name])) {
        return;
      }

      deleteCount++;
      return {
        key: `${++key}-${name}`,
        title: `${name}@${ctrlItem.version}`,
        isLeaf: true,
        name: name,
        version: ctrlItem.version,
        diffType: 'delete',
      }
    }) as any[];
    return dependenciesList.concat(ctrlDependenciesList).filter(item => !!item);
  };

  return {
    // diffTree: getDiffTree(dependenciesInfo.dependencies, ctrlDependenciesInfo.dependencies),
    totalCount,
    realCount,
    addCount,
    deleteCount,
    modifyCount,
  };
}

/** 获取多版本依赖树 */
const getMultiVersionInfo = (shrinkwrapJson: IShrinkwrapJson, ctrlShrinkwrapJson?: IShrinkwrapJson) => {
  const duplicatedVersions = getMultiVersionDependencies(shrinkwrapJson);
  const ctrlDuplicatedVersions = getMultiVersionDependencies(ctrlShrinkwrapJson);
  const hasCtrlShrinkwrapJson = !!(ctrlShrinkwrapJson && Object.keys(ctrlShrinkwrapJson).length);
  const getChildDiffType = (depend: any, hasCtrl: boolean, ctrlVersions?: any[]) => {
    if (!hasCtrl) return {};
    if (!ctrlVersions || !ctrlVersions.length) {
      return {
        diffType: 'add'
      };
    }
    if (!ctrlVersions.some(v => depend.version === v.version)) {
      return {
        diffType: 'modify',
        childrenDiffType: 'add',
      };
    }
    return {};
  };
  const getChildrenItem = (depend: { version: string; chain: string[] }, dependName: string, hasCtrl: boolean, ctrlVersions?: any[]) => {
    const { diffType, childrenDiffType } = getChildDiffType(depend, hasCtrl, ctrlVersions);
    const { version, chain } = depend;
    return {
      diffType, 
      isLeaf: false,
      name: version,
      title: version,
      key: `${dependName}-${version}`,
      isVersion: true,
      children: chain.map((chain, index) => ({
        isLeaf: true,
        name: chain,
        title: chain,
        diffType: childrenDiffType,
        key: `${dependName}-${version}-${index}`,
        isChain: true,
      })),
    };
  };

  const multiVersionTree = Object.entries(duplicatedVersions).map(([dependName, dependVersions]) => {
    // 对比的依赖
    const ctrlVersions = ctrlDuplicatedVersions[dependName];
    const currentChildren = (dependVersions || []).map(depend => getChildrenItem(depend, dependName, hasCtrlShrinkwrapJson, ctrlVersions));
    const deleteChildren = (ctrlVersions || []).reduce((prev, depend) => {
      if (!dependVersions.some(v => v.version === depend.version)) {
        prev.push({
          ...getChildrenItem(depend, dependName, false),
          diffType: 'delete',
        });
      }
      return prev;
    }, []);
    const modifyType = currentChildren.some(v => !!v.diffType) ? 'modify' : '';
    const diffType = !hasCtrlShrinkwrapJson ? '' : (ctrlVersions && ctrlVersions.length ? modifyType : 'add');
    return {
      isLeaf: false,
      name: dependName,
      title: dependName,
      key: dependName,
      diffType,
      children: [...currentChildren, ...deleteChildren],
    };
  }).sort((a, b) => b.children.length - a.children.length);

  const multiVersionPackageCount = multiVersionTree.length;
  const multiVersionCount = multiVersionTree.reduce((prev, current) => prev + (current && current.children && current.children.length || 0), 0);

  return {
    multiVersionPackageCount,
    multiVersionCount,
    // 对多版本依赖进行排序
    multiVersionTree,
  }
}

/** 获取依赖树 */
export const getDependenciesDiffTree = (shrinkwrapJson: IShrinkwrapJson, ctrlShrinkwrapJson?: IShrinkwrapJson) => {
  if (!shrinkwrapJson) {
    return {
      diffTree: [],
      multiVersionTree: [],
      addCount: 0,
      deleteCount: 0,
      modifyCount: 0,
      multiVersionCount: 0,
      multiVersionPackageCount: 0,
    };
  }


  const treeInfo = diffDependenciesRecursively(shrinkwrapJson, ctrlShrinkwrapJson);
  const multiVersionInfo = getMultiVersionInfo(shrinkwrapJson, ctrlShrinkwrapJson);

  return {
    ...treeInfo,
    ...multiVersionInfo,
  }
}