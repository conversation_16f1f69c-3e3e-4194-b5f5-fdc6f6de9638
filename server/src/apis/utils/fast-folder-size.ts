import * as path from 'path';
import { exec, ExecException } from 'child_process';

const commands = {
  // windows
  win32: `"${path.join(
    __dirname,
    'bin',
    'du.exe'
  )}" -nobanner -accepteula -q -c .`,

  // macos
  darwin: `du -sk .`,

  // any linux
  linux: `du -sb .`,
}

const processOutput = {
  // windows
  win32(stdout: string) {
    // query stats indexes from the end since path can contain commas as well
    const stats = stdout.split('\n')[1].split(',');
    const bytes = +stats.slice(-2)[0];

    return bytes;
  },

  // macos
  darwin(stdout: string) {
    const match = /^(\d+)/.exec(stdout);
    const bytes = Number(match && match[1]) * 1024;

    return bytes;
  },

  // any linux
  linux(stdout: string) {
    const match = /^(\d+)/.exec(stdout);
    const bytes = Number(match && match[1]);

    return bytes;
  },
}

export default function fastFolderSize(target: string, cb: (err: ExecException | null, bytes?: number) => void) {
  const command = commands[process.platform] || commands['linux'];

  return exec(command, { cwd: target }, (err, stdout) => {
    if (err) return cb(err);

    const processFn = processOutput[process.platform] || processOutput['linux'];
    const bytes = processFn(stdout);

    cb(null, bytes);
  })
}
