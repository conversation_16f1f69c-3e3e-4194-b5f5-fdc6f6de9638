import { EClient, BUNDLE_ID_CONFIG } from '@/apis/const/iter-deliver';

/**
 * 获取原投放平台地址
 * @param miniAppId 小程序id
 * @param clientName 端名称
 * @returns 
 */
export function getOriginalPlatform(miniAppId: string, clientName: EClient) {
  if (clientName === EClient.ALIPAY) {
    return `https://openhome.alipay.com/mini/dev/sub/dev-manage?appId=${miniAppId}&bundleId=${BUNDLE_ID_CONFIG[clientName]}`
  } else if (clientName === EClient.WEIXIN) {
    return 'https://mp.weixin.qq.com'
  } else if ([EClient.DOUYIN, EClient.DOUYIN_LITE, EClient.TOUTIAO, EClient.TT_LITE].includes(clientName)) {
    return `https://microapp.bytedance.com/app/${miniAppId}/publish`;
  }
  
  return '';
}
