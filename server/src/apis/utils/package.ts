import * as path from 'path';
import * as fse from 'fs-extra';
import { IAppStructure, IPackageInfo, IPackageJSON, IAppJSON, IBuildJSON, IPageInfo } from '@/apis/interface/package';
import { IGitRepoInfo } from '@/apis/interface/git-opts';
import { BIZ_TITLE, BIZ_LINE } from '@/apis/const';
import { EPackageType, EBuildType } from '@/apis/const/package';
import { continueIfPathExists } from '@/apis/utils/file';

/**
 * 获取app结构
 * @returns 
 */
export async function getAppStructure({
  projectTmpDirPath,
  gitRepoInfo,
  branchName = 'master',
  withPages = true
}: {
  projectTmpDirPath: string
  branchName: string,
  gitRepoInfo: IGitRepoInfo,
  withPages?: boolean
}) {
  const [appJSON, appDevJSON, buildJSON] = await Promise.all([
    fse.readJSON(path.join(projectTmpDirPath, 'src/app.json')) as Promise<IAppJSON>,
    continueIfPathExists<IAppJSON>(path.join(projectTmpDirPath, 'src/app.dev.json'), (filePath) => {
      return fse.readJSON(filePath) as Promise<IAppJSON>;
    }),
    fse.readJSON(path.join(projectTmpDirPath, 'build.json')) as Promise<IBuildJSON>,
  ])

  // 解析包信息
  const [packages, minifyBuildPackages] = await Promise.all([
    parsePackageInfo({
      appJSON,
      buildJSON,
      projectTmpDirPath,
      gitRepoInfo,
      branchName,
      withPages
    }),
    appDevJSON ? parsePackageInfo({
      appJSON: appDevJSON,
      buildJSON,
      projectTmpDirPath,
      gitRepoInfo,
      branchName,
      withPages: false
    }) : undefined
  ]);

  return {
    packages,
    minifyBuildPackages
  } as IAppStructure
}

/**
 * 解析包信息
 */
async function parsePackageInfo({
  appJSON,
  buildJSON,
  projectTmpDirPath,
  gitRepoInfo,
  branchName,
  withPages
}: {
  appJSON: IAppJSON,
  buildJSON: IBuildJSON,
  projectTmpDirPath: string,
  gitRepoInfo: IGitRepoInfo,
  branchName: string,
  withPages: boolean
}) {
  const buildType = buildJSON[buildJSON.targets[0]].buildType || EBuildType.CompileTime;
  const packages = await Promise.all(appJSON.routes.map(async (packageRoute) => {
    let { source, miniappMain, targets, independent, bizType } = packageRoute;
    // source格式：支付宝中是 hotel/app，微信是 pages/home/<USER>
    const temp = source.split('/');
    const packageName = temp[temp.length - 2]; // 倒数第二个是包名
    const packagePath = temp.slice(0, -1).join('/') // 去除最后一个是包路径

    // 判断包类型
    let packageTypeName;
    let packageTypeTitle;
    if (miniappMain) {
      packageTypeName = EPackageType.Main;
      packageTypeTitle = '主包';
    } else if (independent) {
      packageTypeName = EPackageType.IndependentSubPackage;
      packageTypeTitle = '独立分包';
    } else {
      packageTypeName = EPackageType.SubPackage;
      packageTypeTitle = '分包';
    }

    // 判断包构建类型
    let buildTypeName = EBuildType.CompileTime;
    let buildTypeTitle = '编译时';
    if (buildType === EBuildType.Runtime && !targets) {
      buildTypeName = EBuildType.Runtime;
      buildTypeTitle = '运行时';
    }

    // gitlab 地址
    let gitlabHref;
    if (gitRepoInfo && packageName) {
      gitlabHref = `${gitRepoInfo.url}/tree/${branchName}/src/${packagePath}`;
    }

    // 包信息
    const packageInfo = {
      packageName,
      gitlabHref,
      packageType: {
        name: packageTypeName,
        title: packageTypeTitle
      },
      buildType: {
        name: buildTypeName,
        title: buildTypeTitle
      },
      bizType: bizType ? {
        name: bizType,
        title: BIZ_TITLE[bizType]
      } : undefined,
    } as IPackageInfo

    // 是否进一步解析page信息
    if (withPages) {
      // 获取包配置
      const packageJSON: IPackageJSON = await fse.readJSON(path.join(projectTmpDirPath, `src/${packagePath}/app.json`));
      packageInfo.pages = parsePageInfo({
        packageJSON,
        packageInfo,
        packagePath,
        gitRepoInfo,
        branchName
      })
    }

    return packageInfo;
  }))

  // 按字母大小正序排列，主包会提到最前
  const packageBySort = packages.sort((a, b) => {
    if (a.packageType.name === EPackageType.Main || b.packageType.name === EPackageType.Main) {
      if (a.packageType.name === EPackageType.Main) return -1;
      if (b.packageType.name === EPackageType.Main) return 1;
      return 0;
    } else {
      return a.packageName < b.packageName ? -1 : 1;
    }
  })

  return packageBySort;
}

/**
 * 解析页面信息
 * @returns 
 */
function parsePageInfo({
  packageJSON,
  packageInfo,
  packagePath,
  gitRepoInfo,
  branchName
}: {
  packageJSON: IPackageJSON;
  packageInfo: IPackageInfo;
  packagePath: string;
  gitRepoInfo: IGitRepoInfo;
  branchName: string;
}) {
  if (!packageJSON.routes) return [];
  return packageJSON.routes.map(({ source, miniappPreloadRule, window, bizType }) => {
    const pageName = source.replace(/^\//, '');
    const pagePath = pageName.replace(/(|\/)[\w-_]+$/, '');
    let returnBizType;
    if (bizType) { // 优先取页面的 bizType
      returnBizType = {
        name: bizType,
        title: BIZ_TITLE[bizType]
      };
    } else if (packageInfo.bizType) { // 没有则取所属分包的 bizType
      returnBizType = packageInfo.bizType
    }

    // 返回的页面信息
    return {
      pageName,
      pageTitle: window?.title,
      gitlabHref: `${gitRepoInfo.url}/tree/${branchName}/src/${packagePath}/${pagePath}`,
      source: `${packagePath}/${pageName}`,
      preloadRule: miniappPreloadRule,
      bizType: returnBizType
    } as IPageInfo
  })
}


/**
 * 修改 app 的版本号
 * @param version 待修改版本号
 * @param projectTmpDirPath 项目路径
 */
export async function modifyAppVersion(version: string, projectTmpDirPath: string, gitRepoInfo: IGitRepoInfo) {
  let appJSPath;
  if (gitRepoInfo.project === 'rx-fliggy-allinone') {
    appJSPath = 'src/pages/app.js'
  } else if (gitRepoInfo.project === 'rx-fliggy-weixin' || gitRepoInfo.project === 'rx-miniwork-test') {
    appJSPath = 'src/miniapp-native/app.js';
  }
  if (appJSPath) {
    appJSPath = path.join(projectTmpDirPath, appJSPath);
    let appJSFile = await fse.readFile(appJSPath, { encoding: 'utf-8' });
    if (/^const version =/.test(appJSFile)) {
      appJSFile = appJSFile.replace(/^(const version = ')([\w\.-]+)(';)/, (_match, p1, _p2, p3) => p1 + version + p3)
    } else {
      appJSFile = `const version = '${version}';\n${appJSFile}`;
    }
    await fse.writeFile(appJSPath, appJSFile);
  }
}

/**
 * 修改 package.json 的版本号
 * @param version 待修改版本号
 * @param projectTmpDirPath 项目路径
 */
export async function modifyPackageVersion(version: string, projectTmpDirPath: string) {
  const pkgJSONPath = path.join(projectTmpDirPath, 'package.json');
  const pkgJSONFile = await fse.readFile(pkgJSONPath, { encoding: 'utf-8' });
  await fse.writeFile(pkgJSONPath, pkgJSONFile.replace(/("version":\s*")(.+)(")/, (_match, p1, _p2, p3) => p1 + version + p3));
}

/**
 * 根据行业线，生成按需构建的app.dev.json
 * @param projectTmpDirPath 项目路径
 * @param bizLine 行业线
 */
export async function minifyAppJson(projectTmpDirPath: string, bizLine: BIZ_LINE): Promise<IAppJSON> {
  // 读取app.json
  const appJSONPath = path.join(projectTmpDirPath, './src/app.json');
  const appJSONFile = await fse.readJson(appJSONPath) as IAppJSON;
  const rawRoutes = appJSONFile.routes;
  // 根据行业线，过滤构建的分包
  const finalRoutes = rawRoutes.filter((route) => {
    // 必须要带上主包
    if (route.miniappMain) return true;
    if (!route || !route.devBizType || !route.devBizType.length) return false;
    return route.devBizType.find((devBiz) => devBiz === bizLine);
  });
  return { ...appJSONFile, routes: finalRoutes };
}