import * as urllib from 'urllib';
import * as KeyCenterClient from '@ali/keycenter';
import AlipaySdk from 'alipay-sdk';
import * as crypto from '@ali/crypto';
import * as chalk from 'chalk';
import { MINIAPP_CONFIG } from '@/apis/const/iter-deliver';
import { isDaily } from '@/apis/utils';
// const isDaily = false

const OPEN_API_URL = 'https://openapi.alipay.com/gateway.do';
// const OPEN_API_URL_PRE = 'https://openapipre.alipay.com/gateway.do'; // 预发地址

export default class AlipaySdkManager {
  // sdk 缓存
  alipaySdkCache = {} as { [key: string]: AlipaySdk };

  // 初始化 KeyCenter
  keyCenterClient = new KeyCenterClient({
    // app发放码，需要到控制台申请，流程参加：https://yuque.antfin-inc.com/mkf08w/cvdlr6/aone-encrypt-scene
    appNum: isDaily ? '156886d161164223991d93aa43d8660f' : 'c4c35aeebdbb48ec88f66cb8fc81d405',
    // 和keycenter服务器的信道是否加密,默认加密
    encryptCommunication: true,
    urllib,
    protocol: 'http',
    // 详情参考：https://yuque.antfin-inc.com/mkf08w/cvdlr6/environment
    url: isDaily ? 'http://daily.keycenter.alibaba.net/keycenter' : 'http://keycenter-service.alibaba-inc.com/keycenter',
  });

  async createAlipaySdk(miniAppId: string) {
    const miniAppConfig = MINIAPP_CONFIG[miniAppId];

    if (!miniAppConfig) throw Error(`不支持appId 为 ${miniAppId} 的小程序`);

    console.time(chalk.yellow('从KC换取支付宝平台秘钥 >>> 耗时'))
    const [privateKey, alipayPublicKey] = await Promise.all([
      this.keyCenterClient.getKey(miniAppConfig.privateKeyName),
      this.keyCenterClient.getKey(miniAppConfig.alipayPublicKeyName)
    ]);
    console.timeEnd(chalk.yellow('从KC换取支付宝平台秘钥 >>> 耗时'))

    const alipaySdk = new AlipaySdk({
      appId: miniAppId,
      gateway: OPEN_API_URL,
      keyType: 'PKCS8',
      privateKey: crypto.Utils.pemPriKey(privateKey.content),
      alipayPublicKey: crypto.Utils.pemPubKey(alipayPublicKey.content)
    });

    this.alipaySdkCache[miniAppId] = alipaySdk;

    return alipaySdk;
  }

  async getAlipaySdk(miniAppId: string) {
    if (this.alipaySdkCache[miniAppId]) return this.alipaySdkCache[miniAppId];

    return this.createAlipaySdk(miniAppId);
  }
}