import { IGitRepoInfo } from '@/apis/interface/git-opts';
import { INpm } from '@/apis/interface/npm';
import { BIZ_LINE } from '@/apis/const';
import { EProjectType } from '@/apis/const/project';
import { useGit, useTmpDir } from '@/apis/middleware/hooks';
import { modifyAppVersion, modifyPackageVersion, minifyAppJson } from '@/apis/utils/package';
import { IProject } from '@/apis/interface/project';
import * as path from 'path';
import * as fse from 'fs-extra';

const COMMIT_PREFIX = '【一体化研发平台】';

export function parseGitRepoInfo(repoUrl: string): IGitRepoInfo | null {
  let matches;
  if (/^http/.test(repoUrl)) {
    // http://gitlab.alibaba-inc.com/trip/rx-miniwork-test.git
    const regExp = /^http(?:s)?:\/\/([^/]*)\/([^/]*)\/([^/.]*)/i;
    matches = repoUrl.match(regExp);
  }

  if (/^git@/.test(repoUrl)) {
    // **************************:trip/rx-miniwork-test.git
    const regExp = /^git@([^:]*):([^/]*)\/([^/.]*)/i;
    matches = repoUrl.match(regExp);
  }

  if (matches) {
    const host = matches[1] || '';
    const group = matches[2] || '';
    const project = matches[3] || '';
    return {
      url: `http://${host}/${group}/${project}`,
      httpUrl: `http://${host}/${group}/${project}.git`,
      sshUrl: `git@${host}:${group}/${project}.git`,
      host,
      group,
      project
    };
  } else {
    console.error('>> parse git repo info fail, url: ' + repoUrl);
    return null;
  }
}

export function getBranchUrl(repoUrl: string, branch: string): string {
  // http://gitlab.alibaba-inc.com/trip/rx-miniwork-test/tree/0.0.1
  const { url } = parseGitRepoInfo(repoUrl) || {};
  return url ? `${url}/tree/${branch}` : '';
}

export async function mergeBranch(branchName: string, branchUrl: string, targetBranchName: string) {
  const { tmpDirPath, removeTmpDir } = useTmpDir();
  const { gitHandler, projectTmpDirPath, gitRepoInfo } = await useGit(tmpDirPath, { branchUrl })

  // 1. 检查分支是否存在
  const branchIsExist = await gitHandler.listRemote(['--heads', gitRepoInfo.sshUrl, branchName]);
  if (!branchIsExist) throw Error(`代码分支 ${branchName} 不存在`)

  // 2. 检查目标分支是否存在
  const targetBranchIsExist = await gitHandler.listRemote(['--heads', gitRepoInfo.sshUrl, targetBranchName]);
  if (!targetBranchIsExist) throw Error(`目标代码分支 ${targetBranchName} 不存在`)

  // 3. 先 checkout 到目标分支，获取版本号
  const pkgJSONPath = path.join(projectTmpDirPath, 'package.json');
  await gitHandler.checkout([targetBranchName])
  const targetBranchPkgJSON = await fse.readJSON(pkgJSONPath);
  const targetBranchVersion = targetBranchPkgJSON.version;

  // 4. 再 checkout 到源分支，临时修改版本号，防止 merge 时冲突
  await gitHandler.checkout([branchName])
  const sourceBranchPkgJSON = await fse.readJSON(pkgJSONPath);
  const sourceBranchVersion = sourceBranchPkgJSON.version;
  if (targetBranchVersion !== sourceBranchVersion) {
    await modifyPackageVersion(targetBranchVersion, projectTmpDirPath);
    await modifyAppVersion(targetBranchVersion, projectTmpDirPath, gitRepoInfo);
    await gitHandler.add('-A').commit(`${COMMIT_PREFIX}临时修改版本号，防止 merge 时冲突`);
  }

  // 5. merge 到目标分支
  const mergeCommitMessage = `${COMMIT_PREFIX}Merge branch '${targetBranchName}' into ${branchName}`;
  try {
    await gitHandler.merge([targetBranchName, '--allow-unrelated-histories', '-m', mergeCommitMessage]);
  } catch (err) {
    // 如果 merge 失败，则删除 shrinkwrap.json 再试
    const shrinkwrapJSONPath = path.join(projectTmpDirPath, 'shrinkwrap.json');
    if (fse.existsSync(shrinkwrapJSONPath)) {
      fse.unlinkSync(shrinkwrapJSONPath);
      await gitHandler.reset(['--hard']).add('-A').commit(`${COMMIT_PREFIX}临时删除 shrinkwrap.json，防止 merge 时冲突`);
      await gitHandler.merge([targetBranchName, '-m', mergeCommitMessage]);
    } else {
      throw err;
    }
  }

  // 6. 改回原版本号
  if (targetBranchVersion !== sourceBranchVersion) {
    await modifyPackageVersion(sourceBranchVersion, projectTmpDirPath);
    await modifyAppVersion(sourceBranchVersion, projectTmpDirPath, gitRepoInfo);
  }

  // 7. commit & push
  const statusRes = await gitHandler.status(['-s']);
  if (!statusRes.isClean()) {
    await gitHandler
      .add('-A')
      .commit(`${COMMIT_PREFIX}${branchName} merge ${targetBranchName} 成功`);
  }
  await gitHandler.push(['-u', 'origin', branchName]);

  // 8. 删除临时文件
  removeTmpDir()
}

// 新建（游离）开发分支
export async function createDevBranch(branchName: string, branchUrl: string, {
  npmList = [],
  npmResolutionList = [],
  baseBranchName = 'master',
  bizLine = 'all',
  project,
  isComponentProjectWithPkgVer = false,
  pkgVersion = ''
}: {
  npmList?: INpm[];
  npmResolutionList?: INpm[];
  baseBranchName?: string;
  bizLine?: BIZ_LINE | 'all';
  project?: IProject;
  isComponentProjectWithPkgVer: boolean;
  pkgVersion?: string;
}) {
  const { tmpDirPath, removeTmpDir } = useTmpDir()
  const { gitHandler, projectTmpDirPath, gitRepoInfo } = await useGit(tmpDirPath, {
    branchUrl,
    branchName: baseBranchName,
    cloneSingleBranch: true
  })

  // 检查游离分支是否已存在
  const isBranchExist = await gitHandler.listRemote(['--heads', gitRepoInfo.sshUrl, branchName]);
  if (isBranchExist) {
    throw new Error(`Git: 分支 ${branchName} 已存在`);
  }

  // 创建分支
  await gitHandler.checkoutBranch(branchName, 'HEAD');

  // 修改组件package.json（如依赖、version版本）
  if (npmList.length > 0 || npmResolutionList.length > 0 || isComponentProjectWithPkgVer) {
    // 读取 package.json
    const pkgJSONPath = path.join(projectTmpDirPath, './package.json');
    const pkgJSON = await fse.readJSON(pkgJSONPath);

    if (isComponentProjectWithPkgVer) {
      // 更新npm version(一般组件集成时需要)
      pkgJSON.version = pkgVersion;
    } else {
      // 更新依赖（集成模式为更新依赖时）
      const pkgDeps = pkgJSON.dependencies || {};
      const pkgResolutions = pkgJSON.resolutions || {};

      // 写入新的 npm 依赖
      npmList.forEach(({ name, value }) => {
        pkgDeps[name] = value;
      });
      npmResolutionList.forEach(({ name, value }) => {
        pkgResolutions[name] = value;
      });

      // 更新依赖
      pkgJSON.dependencies = pkgDeps;
      pkgJSON.resolutions = pkgResolutions;
    }

    await fse.writeJSON(pkgJSONPath, pkgJSON, { spaces: 2 });

    // 提交日志
    await gitHandler
      .add('-A')
      .commit(isComponentProjectWithPkgVer ? `${COMMIT_PREFIX}更新package.json版本号` : `${COMMIT_PREFIX}更新依赖`)
  }

  // 根据业务线，对app.json进行优化，实现按需构建
  if (bizLine !== 'all' && project?.type === EProjectType.ALIPAY) {
    // 获取按需构建后的app.json
    const appJSON = await minifyAppJson(projectTmpDirPath, bizLine);
    // 写入新的按需构建内容进入app.dev.json
    const devAppJSONPath = path.join(projectTmpDirPath, './src/app.dev.json');
    await fse.writeJSON(devAppJSONPath, appJSON, { spaces: 2 });

    // 提交日志
    await gitHandler
      .add('-A')
      .commit(`${COMMIT_PREFIX}更新按需构建文件`);
  }

  // 推送分支
  await gitHandler.push(['-u', 'origin', branchName]);

  // 删除临时文件
  removeTmpDir();
}

export async function deleteBranch(branchName: string, branchUrl: string) {
  const { tmpDirPath, removeTmpDir } = useTmpDir()

  try {
    const { gitHandler, gitRepoInfo } = await useGit(tmpDirPath, { branchName, branchUrl, cloneSingleBranch: true })
    await gitHandler.push([gitRepoInfo.sshUrl, '-d', branchName]);
  } catch (err: any) {
    const errInfo = err.message.split('::');
    if (errInfo.length > 1 && errInfo[0] === 'BRANCH_DOES_NOT_EXIT') {
      // 如果错误是“分支不存在”，则不抛错
    } else {
      throw err;
    }
  }

  // 删除临时文件
  removeTmpDir();
}

export async function updateBranchDeps(
  branchName: string, branchUrl: string,
  npmList: INpm[] = [], oldNpmList: INpm[] = [],
  npmResolutionList: INpm[] = [], oldNpmResolutionList: INpm[] = []
) {
  // 如果新旧 npm 相同，则不做变更
  if ((oldNpmList.length === npmList.length) && (oldNpmResolutionList.length === npmResolutionList.length)) { // 首先过滤数量上相同的
    const isNpmSame = npmList.every(item => oldNpmList.find(oldItem => oldItem.name === item.name && oldItem.value === item.value))
    const isNpmResolutionSame = npmResolutionList.every(item => oldNpmResolutionList.find(oldItem => oldItem.name === item.name && oldItem.value === item.value))
    if (isNpmSame && isNpmResolutionSame) return;
  }

  const { tmpDirPath, removeTmpDir } = useTmpDir()
  const { gitHandler, projectTmpDirPath } = await useGit(tmpDirPath, {
    branchUrl,
    branchName,
    cloneSingleBranch: true
  })

  // 读取 package.json
  const pkgJSONPath = path.join(projectTmpDirPath, 'package.json');
  const pkgJSON = await fse.readJSON(pkgJSONPath)
  const pkgDeps = pkgJSON.dependencies || {};
  const pkgResolutions = pkgJSON.resolutions || {};

  // 删除旧的 npm 依赖
  oldNpmList.forEach(({ name }) => {
    delete pkgDeps[name];
  });
  oldNpmResolutionList.forEach(({ name }) => {
    delete pkgResolutions[name];
  });

  // 写入新的 npm 依赖
  npmList.forEach(({ name, value }) => {
    pkgDeps[name] = value;
  });
  npmResolutionList.forEach(({ name, value }) => {
    pkgResolutions[name] = value;
  });

  // 更新依赖
  pkgJSON.dependencies = pkgDeps;
  pkgJSON.resolutions = pkgResolutions;
  await fse.writeJSON(pkgJSONPath, pkgJSON, { spaces: 2 });

  // 提交日志 & 推送分支
  await gitHandler
    .add('-A')
    .commit(`${COMMIT_PREFIX}更新依赖`)
    .push(['-u', 'origin', branchName]);

  // 删除临时文件
  removeTmpDir();
}

/**
 * 查询某分支的具体依赖、锁版本依赖的信息
 * @param branchName 分支名
 * @param branchUrl git带分支的地址（如 http://gitlab.alibaba-inc.com/trip/rx-buy/tree/daily/1.0.0）
 * @param npmNameList 需要查询的分支名数组
 * @param npmResolutionNameList 需要查询的锁版本依赖列表
 *
 * @returns { findNpmList: INpm[]; findResolutionList: INpm[]; }
*/
export async function getBranchDeps(branchName: string, branchUrl: string, npmNameList?: string[], npmResolutionNameList?: string[]) {
  if(!npmNameList?.length && !npmResolutionNameList?.length) {
    return {
      findNpmList: [],
      findResolutionList: [],
    };
  }

  const { tmpDirPath, removeTmpDir } = useTmpDir()
  const { projectTmpDirPath } = await useGit(tmpDirPath, {
    branchUrl,
    branchName,
    cloneSingleBranch: true
  })

  // 读取 package.json
  const pkgJSONPath = path.join(projectTmpDirPath, 'package.json');
  const pkgJSON = await fse.readJSON(pkgJSONPath)
  const pkgDeps: { [key: string]: string } = pkgJSON.dependencies || {};
  const pkgResolutions: { [key: string]: string } = pkgJSON.resolutions || {};
  let findNpmList: INpm[] = [];
  let findResolutionList: INpm[] = [];

  // 查询依赖
  if(npmNameList?.length) {
    Object.entries(pkgDeps).forEach((item) => {
      if (!item) return;

      if (npmNameList.indexOf(item[0]) !== -1) {
        findNpmList.push({ name: item[0], value: item[1]})
      }
    })
  }
  if (npmResolutionNameList?.length) {
    Object.entries(pkgResolutions).forEach(item => {
      if (!item) return;

      if (npmResolutionNameList.indexOf(item[0]) !== -1) {
        findResolutionList.push({ name: item[0], value: item[1]})
      }
    })
  }

  // 删除临时文件
  removeTmpDir();

  return {
    findNpmList,
    findResolutionList
  }
}

/** 创建投放项目的git分支 */
export async function createDeliverBranch(branchName: string, branchUrl: string, {
  baseBranchName = 'master',
  npmList = [],
  pkgVersion = ''
}: {
  baseBranchName?: string;
  npmList?: INpm[];
  pkgVersion?: string;
}) {
  const { tmpDirPath, removeTmpDir } = useTmpDir()
  const { gitHandler, projectTmpDirPath, gitRepoInfo } = await useGit(tmpDirPath, {
    branchUrl,
    branchName: baseBranchName,
    cloneSingleBranch: true
  })

  // 检查游离分支是否已存在
  const isBranchExist = await gitHandler.listRemote(['--heads', gitRepoInfo.sshUrl, branchName]);
  if (isBranchExist) {
    throw new Error(`Git: 分支 ${branchName} 已存在`);
  }

  // 创建分支
  await gitHandler.checkoutBranch(branchName, 'HEAD');

  // 修改组件package.json（如依赖、version版本）
  if (npmList.length > 0 || pkgVersion) {
    // 读取 package.json
    const pkgJSONPath = path.join(projectTmpDirPath, './package.json');
    const pkgJSON = await fse.readJSON(pkgJSONPath);

    if (pkgVersion) {
      // 更新npm version(一般组件集成时需要)
      pkgJSON.version = pkgVersion;
    }
    if (npmList.length > 0) {
      // 更新依赖
      const pkgDeps = pkgJSON.dependencies || {};
      // 写入新的 npm 依赖
      npmList.forEach(({ name, value }) => {
        pkgDeps[name] = value;
      });

      // 更新依赖
      pkgJSON.dependencies = pkgDeps;
    }

    await fse.writeJSON(pkgJSONPath, pkgJSON, { spaces: 2 });

    // 提交日志
    await gitHandler
      .add('-A')
      .commit(`${COMMIT_PREFIX}更新package.json版本号和依赖`)
  }

  // 推送分支
  await gitHandler.push(['-u', 'origin', branchName]);

  // 删除临时文件
  removeTmpDir();
}


// 得到最大的版本号
export function getBiggestVersion(A): number[] {
  var a: number[][] = [];
  var r: number[][] = [];
  if (!A) {
    return [0, 0, 0];
  }
  for (var i = 0; i < A.length; i++) {
    if (A[i].match(/^\d+\.\d+\.\d+$/)) {
      var sp = A[i].split('.');
      a.push([
        Number(sp[0]), Number(sp[1]), Number(sp[2])
      ]);
    }
  }

  var r = findMax(findMax(findMax(a, 0), 1), 2);
  return r[0];
}

// a：二维数组，index，比较第几个
// return：返回保留比较后的结果组成的二维数组
export function findMax(a: number[][], index) {
  var t: number[] = [];
  var b: number[] = [];
  var r: number[][] = [];
  for (var i = 0; i < a.length; i++) {
    t.push(Number(a[i][index]));
  }
  var max = Math.max(...t);
  for (var i = 0; i < a.length; i++) {
    if (a[i][index] === max) {
      b.push(i);
    }
  }
  for (var i = 0; i < b.length; i++) {
    r.push(a[b[i]]);
  }
  return r;
}
