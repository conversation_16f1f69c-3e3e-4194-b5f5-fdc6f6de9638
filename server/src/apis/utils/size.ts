import { PROD_URL } from '@/apis/const';
import { formatFileSize } from './file';

const NAME_MAP = {
  '__FULL__': 'total', // 总包
  '__APP__': 'main' // 主包
};

// 警告限制300KB
const PKG_WARN_SIZE = 1024 * 300;
const PKG_NICE_SIZE = 1024 * 30;

// 总包强制限制700KB
const LIMIT_TYPE = {
  // 总包强制限制700KB
  total: 1024 * 700,
  // 主包限制30KB
  main: 1024 * 30,
  // 首页分包100KB
  home: 1024 * 100,
};

const PKG_NAME_STR = {
  total: '总包',
  main: '主包',
  home: '首页分包',
}

const PROJECT_NAME_MAP = {
  'fliggy-weixin': '飞猪微信小程序',
};

export function getSubPkgName(name: string) {
  if (NAME_MAP[name]) {
    return NAME_MAP[name];
  }
  return name.split('/')[2];
}

export function formatSubpackageSize(subpackageInfo: Array<{ name: string, size: number }>) {
  return subpackageInfo.map(item => {
    const { name, size } = item;

    return {
      subPackageName: getSubPkgName(name),
      buildSize: size,
      buildHumanSize: formatFileSize(size),
    }
  });
}

const diffSize = (obj, size, prevSize) => {
  if (typeof prevSize === 'undefined') return;
  Object.assign(obj, { diff: size - prevSize });
};

export function getPackageDiffInfo(prevSizeInfo, curSizeInfo, prevIterVersion: string, version: string, isIter?: boolean) {
  // 收集体积信息
  const packageInfo = {};
  const setSizeMap = (item, curVersion: string, preVersion?: string) => {
    // 兼容王守义打码以及spaceo2构建的产物
    const pkgName = item.subPackageName || getSubPkgName(item.name);
    const size = item.buildSize || item.size;
    const subSize = {
      [curVersion]: size,
    };
    if (!packageInfo[pkgName]) {
      packageInfo[pkgName] = {};
    }
    diffSize(subSize || 0, size || 0, preVersion && packageInfo[pkgName][preVersion]);
    Object.assign(packageInfo[pkgName], subSize);
  };

  const setBranchSizeInfo = (subs, v1: string, v2?: string) => {
    if (!subs) return;
    // spaceo2构建得到的体积信息
    if (isIter) {
      subs.subPackageInfo.forEach((item) => setSizeMap(item, v1, v2))
      return;
    }
    // 王守义打码的体积信息
    const { subPackage, ...totalPkg } = subs;
    setSizeMap(totalPkg, v1, v2);
    subPackage.forEach((item) => setSizeMap(item, v1, v2));
  }
  // 参照分支
  setBranchSizeInfo(prevSizeInfo, prevIterVersion);
  // 当前分支
  setBranchSizeInfo(curSizeInfo, version, prevIterVersion);
  return packageInfo;
}

function formatSize(size: number, unit: boolean =  true) {
  if (size < 1024) {
    return `${size}B`;
  }
  if (size / 1024 > 2048) {
    return `${(size / 1024 / 1024).toFixed(2)}${unit ? 'MB' : ''}`;
  }
  return `${(size / 1024).toFixed(2)}${unit ? 'KB' : ''}`
}

function checkSize(pkg: any, limitSize: number, version?: string): { limit: boolean; size: number; formatedSize: string, nice: boolean } {
  const size = pkg?.diff || 0;
  const formatedSize = formatSize(size);
  return {
    ...(version ? { fullSize: pkg?.[version] } : {}),
    limit: size > limitSize,
    nice: size < 0 && Math.abs(size) >= PKG_NICE_SIZE,
    size,
    formatedSize,
  };
}

/** 获取体积限制 */
export function getSizeLimitInfo(packageInfo, version?: string) {
  const limitMsg: string[] = [];
  const warnMsg: string[] = [];
  const warnDetail: any = {};
  const niceDetail: any = {};
  let limit = false;
  const limitDetail = Object.keys(LIMIT_TYPE).reduce((prev, pkgName) => {
    const limitRes = checkSize(packageInfo[pkgName], LIMIT_TYPE[pkgName], version);
    if (limitRes.limit) {
      limitMsg.push(`${PKG_NAME_STR[pkgName]} +${limitRes.formatedSize}`);
    }
    limit = limit || limitRes.limit;
    return {
      ...prev,
      [pkgName]: limitRes,
    };
  }, {} as any);

  Object.keys(packageInfo).forEach(pkgName => {
    // 超限了就不警告
    if (limitDetail[pkgName] && limitDetail[pkgName].limit) return;
    const limitRes = checkSize(packageInfo[pkgName], PKG_WARN_SIZE, version);
    if (limitRes.limit) {
      warnMsg.push(`${PKG_NAME_STR[pkgName] || pkgName} +${limitRes.formatedSize}`);
      warnDetail[pkgName] = limitRes;
    }
    // 优化的体积
    if (limitRes.nice) {
      niceDetail[pkgName] = limitRes;
    }
  });

  return {
    limit,
    limitMsg,
    limitDetail,
    warnMsg,
    warnDetail,
    niceDetail,
  };
}

// 钉钉通知
export function getBranchLimitNotice(limitInfo, projectName: string, branchName: string) {
  const { limit, limitMsg, warnMsg } = limitInfo;
  const branchInfo = `【${PROJECT_NAME_MAP[projectName] || projectName}】分支 ${branchName}`;
  if (limit) {
    const warnText = warnMsg.length ? `。此外以下分包体积增加过多：${warnMsg.join('、')}，请注意优化~` : '';
    return `❗️❗️ ${branchInfo}体积增长超出限制：<font color='#ff4d4f'>${limitMsg.join('、')}</font>，请及时优化，否则无法就绪参与集成${warnText}`;
  }
  return `⚠️⚠️ ${branchInfo}体积增加过多：${warnMsg.join('、')}，请注意优化~`;
}

function getDiffSizeText(diffSize: number) {
  const sizeValue = formatSize(Math.abs(diffSize));
  if (diffSize === 0) return '';
  // 体积减少
  if (diffSize < 0) {
    return `（<font color='#52c41a'>**↓**</font>${sizeValue}）`
  }
  // 增加超过30KB
  if (diffSize > 30) {
    return `（<font color='#cf1322'>**↑**</font>${sizeValue}）`
  }
  return `（↑${sizeValue}）`; // 增加不多
}

function getItemSizeText(label: string, data, omitList: string[] = []) {
  const sizeText = Object.keys(data).map((pkgName: string) => {
    if (omitList.includes(pkgName)) return '';
    const info = data[pkgName];
    const name = PKG_NAME_STR[pkgName] || pkgName;
    const fullSizeValue = info?.fullSize ? formatSize(info.fullSize) : '';
    return `${name} ${fullSizeValue}${getDiffSizeText(info.size)}`;
  }).join('，');
  return sizeText ? `**${label}：** ${sizeText}` : '';
}

const WEIXIN_WORKID = '414026';

/** 获取发版报告内容 */
export function getSizeReportMsgContent(limitInfo, iterId: number, noticeId: string) {
  if (!limitInfo) return {
    text: '',
    atUserIds: [],
    actions: [],
  };
  const { limit, limitDetail, warnDetail, niceDetail } = limitInfo;
  const workid = noticeId || WEIXIN_WORKID;
  const atUserIds: string[] = [];
  // 超限场景
  limit && atUserIds.push(workid);
  const textList = [
    `#### <font color='#1890ff' size=7>**体积变更**</font> `,
    getItemSizeText('核心数据', limitDetail),
    getItemSizeText('重点关注', warnDetail, Object.keys(LIMIT_TYPE)),
    getItemSizeText('点赞分包', niceDetail, Object.keys(LIMIT_TYPE)),
    limit ? `‼️包体积增长超过限制，请及时优化@${workid}` : '',
  ];
  return {
    text: textList.filter(v => !!v).join('\n\n'),
    atUserIds,
    actions: [
      {
        title: '查看体积详情',
        actionURL: `${PROD_URL}/#/iter/detail?iterId=${iterId}`
      }
    ]
  };
}