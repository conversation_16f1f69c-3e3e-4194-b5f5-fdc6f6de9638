import { isProd } from '@/apis/utils';
import { PRE_URL, PROD_URL } from '@/apis/const';
import { formatFileSize } from './file';

const NAME_MAP = {
  '__FULL__': 'total', // 总包
  '__APP__': 'main' // 主包
};

// 总包警告限制300KB
const PKG_WARN_SIZE = 1024 * 300;
const PKG_NICE_SIZE = 1024 * 30;

// 总包强制限制700KB
const LIMIT_TYPE = {
  // 总包强制限制700KB
  total: 1024 * 700,
  // 主包限制30KB
  main: 1024 * 30,
  // 首页分包100KB
  home: 1024 * 100,
};

const PKG_NAME_STR = {
  total: '总包',
  main: '主包',
  home: '首页分包',
}

export function getSubPkgName(name: string) {
  if (NAME_MAP[name]) {
    return NAME_MAP[name];
  }
  return name.split('/')[2];
}

export function formatSubpackageSize(subpackageInfo: Array<{ name: string, size: number }>) {
  return subpackageInfo.map(item => {
    const { name, size } = item;

    return {
      subPackageName: getSubPkgName(name),
      buildSize: size,
      buildHumanSize: formatFileSize(size),
    }
  });
}

const diffSize = (obj, size, prevSize) => {
  if (typeof prevSize === 'undefined') return;
  Object.assign(obj, { diff: size - prevSize });
};

export function getPackageDiffInfo(prevSizeInfo, curSizeInfo, prevIterVersion: string, version: string, isIter?: boolean) {
  // 收集体积信息
  const packageInfo = {};
  const setSizeMap = (item, curVersion: string, preVersion?: string) => {
    // 兼容王守义打码以及spaceo2构建的产物
    const pkgName = item.subPackageName || getSubPkgName(item.name);
    const size = item.buildSize || item.size;
    const subSize = {
      [curVersion]: size,
    };
    if (!packageInfo[pkgName]) {
      packageInfo[pkgName] = {};
    }
    diffSize(subSize || 0, size || 0, preVersion && packageInfo[pkgName][preVersion]);
    Object.assign(packageInfo[pkgName], subSize);
  };

  const setBranchSizeInfo = (subs, v1: string, v2?: string) => {
    if (!subs) return;
    // spaceo2构建得到的体积信息
    if (isIter) {
      console.log(44444444, subs.subPackageInfo)
      subs.subPackageInfo.forEach((item) => setSizeMap(item, v1, v2))
      return;
    }
    // 王守义打码的体积信息
    const { subPackage, ...totalPkg } = subs;
    setSizeMap(totalPkg, v1, v2);
    subPackage.forEach((item) => setSizeMap(item, v1, v2));
  }
  // 参照分支
  setBranchSizeInfo(prevSizeInfo, prevIterVersion);
  // 当前分支
  setBranchSizeInfo(curSizeInfo, version, prevIterVersion);
  return packageInfo;
}

function formatSize(size: number, unit: boolean =  true) {
  return `${(size / 1024).toFixed(2)}${unit ? 'KB' : ''}`
}

function checkSize(pkg: any, limitSize: number): { limit: boolean; size: number; formatedSize: string, nice: boolean } {
  const size = pkg?.diff || 0;
  const formatedSize = formatSize(size);
  return {
    limit: size > limitSize,
    nice: size < 0 && Math.abs(size) >= PKG_NICE_SIZE,
    size,
    formatedSize,
  };
}

/** 获取体积限制 */
export function getSizeLimitInfo(packageInfo) {
  const limitMsg: string[] = [];
  const warnMsg: string[] = [];
  const warnDetail: any = {};
  const niceDetail: any = {};
  let limit = false;
  const limitDetail = Object.keys(LIMIT_TYPE).reduce((prev, pkgName) => {
    const limitRes = checkSize(packageInfo[pkgName], LIMIT_TYPE[pkgName]);
    if (limitRes.limit) {
      limitMsg.push(`${PKG_NAME_STR[pkgName]} ${limitRes.formatedSize}`);
    }
    limit = limit || limitRes.limit;
    return {
      ...prev,
      [pkgName]: limitRes,
    };
  }, {} as any);

  Object.keys(packageInfo).forEach(pkgName => {
    // 超限了就不警告
    if (limitDetail[pkgName] && limitDetail[pkgName].limit) return;
    const limitRes = checkSize(packageInfo[pkgName], PKG_WARN_SIZE);
    if (limitRes.limit) {
      warnMsg.push(`${PKG_NAME_STR[pkgName] || pkgName} ${limitRes.formatedSize}`);
      warnDetail[pkgName] = limitRes;
    }
    // 优化的体积
    if (limitRes.nice) {
      niceDetail[pkgName] = limitRes;
    }
  });

  return {
    limit,
    limitMsg,
    limitDetail,
    warnMsg,
    warnDetail,
  };
}

function getDetailStr(color: string, size: number, pkgName: string) {
  const sizeStr = `${size > 0 ? '+' : ''}${formatSize(size, false)}`;
  return `<font color='#999999'>${pkgName}：</font><font color='${color}'>**${sizeStr}**</font>`;
}

function getColor(info, warnInfo) {
  const { size, limit } = info;
  if (limit || warnInfo) return '#ff4d4f'; // 超过限制
  if (size < 0) return '#52c41a'; // 体积减少
  return '#faad14'; // 增加不多
}

export function getSizeReportMsgContent(limitInfo, iterId: string, workid: string) {
  if (!limitInfo) return {
    text: '',
    atUserIds: [],
    actions: [],
  };
  const { limit, limitDetail, warnDetail, niceDetail } = limitInfo;
  const atUserIds: string[] = [];
  const mainList = Object.keys(limitDetail).map((pkgName: string) => {
    const info = limitDetail[pkgName];
    const { size } = info;
    return getDetailStr(getColor(info, warnDetail[pkgName]), size, PKG_NAME_STR[pkgName]);
  });
  const warnList = Object.keys(warnDetail).filter(pkgName => !limitDetail[pkgName]).map((pkgName: string) => {
    const { size } = warnDetail[pkgName];
    return getDetailStr('#faad14', size, pkgName);
  });
  const niceList = Object.keys(warnDetail).filter(pkgName => !limitDetail[pkgName]).map((pkgName: string) => {
    const { size } = niceDetail[pkgName];
    return getDetailStr('#52c41a', size, pkgName);
  });
  // 超限场景
  limit && atUserIds.push(workid);
  const textList = [
    `<font size=5>**体积变更（KB）**</font> `,
    `- ${mainList.join(' &nbsp;&nbsp;')}`,
    warnList.length ? `- ${warnList.join(' &nbsp;&nbsp;')}` : '',
    niceList.length ? `- ${niceList.join(' &nbsp;&nbsp;')}` : '',
    limit ? `‼️包体积增长超过限制，请及时优化@${workid}` : '',
  ];
  return {
    text: textList.join('\n\n'),
    atUserIds,
    actions: [
      {
        title: '查看体积详情',
        actionURL: `${isProd ? PROD_URL : PRE_URL}/#/iter/detail?iterId=${iterId}`
      }
    ]
  };
}