import { round } from 'lodash';

interface GetSunfireSqlOptions {
  projectName: string,
  pageName: string,
  from: number,
  to: number,
}

// 不同的时间区间长度需要以不同的单位聚合
// 1小时内按分钟维度统计，6小时内按10分钟维度统计，1天内按照30分钟维度统计，7天内按小时维度统计，30天内按照天维度统计
const getTimewindow = (duration: number): string => {
  if (duration <= 3600000) {
    return '1m';
  }
  if (duration <= 21600000) {
    return '10m';
  }
  if (duration <= 86400000) {
    return '30m';
  }
  if (duration <= 604800000) {
    return '1h';
  }
  return '1d';
};

const getTimewindowSeconds = (duration: number): number => {
  // 统一按照分钟计算
  return 60;
  // const timewindowSplits = getTimewindow(duration).match(/^(\d+)(\w)$/);
  // if (!timewindowSplits) {
  //   return -1;
  // }
  // return Number(timewindowSplits[1]) * DURATION_UNIT_SECONDS_MAP[timewindowSplits[2]];
};

// const DURATION_UNIT_SECONDS_MAP = {
//   m: 60,
//   h: 3600,
//   d: 86400
// };

export const ALARM_CONFIG = {
  6750: {
     // 查询sunfire监控数据的sql
     getSunfireSql(options: GetSunfireSqlOptions) {
      const {
        projectName,
        pageName,
        from,
        to,
      } = options;
      const queryStr = `select tw_avg(value) as count from sunfire.59_multiMinute_6750 where __topic__="${projectName}" and __source__="${pageName}" since ${from},${to} timewindow ${getTimewindow(to - from)}`;
      return queryStr;
    },
    parseSunfireSqlResult(result, options: GetSunfireSqlOptions) {
      const {
        from,
        to,
      } = options;
      return result.map((item) => {
        return {
          ...item,
          count: round(item.count / getTimewindowSeconds(to - from), 2)
        }
      });
    },
  },
  6748: {
    getSunfireSql(options: GetSunfireSqlOptions) {
      const {
        projectName,
        pageName,
        from,
        to
      } = options;
      const queryStr = `select tw_avg(value) as duration from sunfire.59_multiMinute_6748 where __topic__="${projectName}" and __source__="${pageName}" since ${from},${to} timewindow ${getTimewindow(to - from)}`;
      return queryStr;
    },
    parseSunfireSqlResult(result) {
      return result;
    },
  },
  11033: {
    getSunfireSql(options: GetSunfireSqlOptions) {
      const {
        projectName,
        pageName,
        from,
        to
      } = options;
      // P50
      const queryStr = `select tw_avg(value) as duration from sunfire.59_multiMinute_11033 where __topic__="${projectName}" and __source__="${pageName}" since ${from},${to} timewindow ${getTimewindow(to - from)}`;
      return queryStr;
    },
    parseSunfireSqlResult(result) {
      return result;
    },
  },
  11034: {
    getSunfireSql(options: GetSunfireSqlOptions) {
      const {
        projectName,
        pageName,
        from,
        to
      } = options;
      // P75
      const queryStr = `select tw_avg(value) as duration from sunfire.59_multiMinute_11034 where __topic__="${projectName}" and __source__="${pageName}" since ${from},${to} timewindow ${getTimewindow(to - from)}`;
      return queryStr;
    },
    parseSunfireSqlResult(result) {
      return result;
    },
  },
  11032: {
    getSunfireSql(options: GetSunfireSqlOptions) {
      const {
        projectName,
        pageName,
        from,
        to
      } = options;
      // P90
      const queryStr = `select tw_avg(value) as duration from sunfire.59_multiMinute_11032 where __topic__="${projectName}" and __source__="${pageName}" since ${from},${to} timewindow ${getTimewindow(to - from)}`;
      return queryStr;
    },
    parseSunfireSqlResult(result) {
      return result;
    },
  },
  11031: {
    getSunfireSql(options: GetSunfireSqlOptions) {
      const {
        projectName,
        pageName,
        from,
        to
      } = options;
      // P95
      const queryStr = `select tw_avg(value) as duration from sunfire.59_multiMinute_11031 where __topic__="${projectName}" and __source__="${pageName}" since ${from},${to} timewindow ${getTimewindow(to - from)}`;
      return queryStr;
    },
    parseSunfireSqlResult(result) {
      return result;
    },
  },
  11041: {
    getSunfireSql(options: GetSunfireSqlOptions) {
      const {
        projectName,
        pageName,
        from,
        to
      } = options;
      // ER耗时均值
      const queryStr = `select tw_avg(value) as duration from sunfire.59_multiMinute_11041 where __topic__="${projectName}" and __source__="${pageName}" since ${from},${to} timewindow ${getTimewindow(to - from)}`;
      return queryStr;
    },
    parseSunfireSqlResult(result) {
      return result;
    },
  },
  11066: {
    getSunfireSql(options: GetSunfireSqlOptions) {
      const {
        projectName,
        pageName,
        from,
        to
      } = options;
      // ER耗时均值
      const queryStr = `select tw_avg(value) as duration from sunfire.59_multiMinute_11066 where __topic__="${projectName}" and __source__="${pageName}" since ${from},${to} timewindow ${getTimewindow(to - from)}`;
      return queryStr;
    },
    parseSunfireSqlResult(result) {
      return result;
    },
  },
  11065: {
    getSunfireSql(options: GetSunfireSqlOptions) {
      const {
        projectName,
        pageName,
        from,
        to
      } = options;
      // ER耗时均值
      const queryStr = `select tw_avg(value) as duration from sunfire.59_multiMinute_11065 where __topic__="${projectName}" and __source__="${pageName}" since ${from},${to} timewindow ${getTimewindow(to - from)}`;
      return queryStr;
    },
    parseSunfireSqlResult(result) {
      return result;
    },
  },
  15064: {
    getSunfireSql(options: GetSunfireSqlOptions) {
      const {
        projectName,
        pageName,
        from,
        to
      } = options;
      const queryStr = `select * from sunfire.59_spm_15064 where __topic__="${projectName}" and __source__="${pageName}" since ${from},${to} timewindow ${getTimewindow(to - from)}`;
      return queryStr;
    },
    parseSunfireSqlResult(result, options: GetSunfireSqlOptions) {
      const {
        from,
        to,
      } = options;
      return (result || []).map((item) => {
        return {
          ...item,
          count: typeof item['总量'] === 'number' ? round(item['总量'] / getTimewindowSeconds(to - from), 2) : 0,
          duration: typeof item['平均耗时'] === 'number' ? item['平均耗时'] : 0,
          rate: typeof item['成功率'] === 'number' ? item['成功率'] / 100 : 1
        };
      });
    },
  }
}