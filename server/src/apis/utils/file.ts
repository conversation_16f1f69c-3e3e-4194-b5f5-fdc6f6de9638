import * as path from 'path';
import * as fse from 'fs-extra';
import * as compressing from 'compressing';
import * as urllib from 'urllib';
import * as Basement from '@alipay/basement';
import fastFolderSize from '@/apis/utils/fast-folder-size';

/**
 * 解压
 * @param filePath 文件地址
 * @returns
 */
export async function unCompress(filePath) {
  // 解析文件
  let { name, ext } = path.parse(filePath);

  // 解压到路径
  if (ext === '.gz' && /\.tar$/.test(name)) { // .tar.gz 格式
    ext = '.tar' + ext;
    name = name.slice(0, -4);
  }
  const unzipFileName = name;
  const unzipFilePath = path.resolve(filePath, '../' + unzipFileName);

  // 获取解压方法
  let unCompressFn;
  switch (ext) {
    case '.tar': unCompressFn = 'tar'; break;
    case '.zip': unCompressFn = 'zip'; break;
    case '.tgz': case '.tar.gz': unCompressFn = 'tgz'; break;
    case '.gz': unCompressFn = 'gz'; break;
  }

  if (!unCompressFn) throw Error('只支持解压 .tar|.zip|.tgz|.tar.gz|.gz 格式文件');

  // 解压
  await compressing[unCompressFn].uncompress(filePath, unzipFilePath);

  return {
    unzipFileName,
    unzipFilePath
  }
}

/**
 * 将文件大小转成可读格式
 * @param bytes 文件大小
 * @returns
 */
export function formatFileSize(bytes: number) {
  const thresh = 1024;
  const units = ['K', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y'];

  if (bytes < thresh) return bytes + 'B';

  let u = -1;
  do {
    bytes /= thresh;
    ++u;
  } while (bytes >= thresh && u < units.length - 1);

  return bytes.toFixed(1) + units[u];
}

/**
 * 计算文件大小，如果是文件夹会计算整体大小
 * @param filePath 文件地址
 * @returns
 */
export async function calcFileSize(filePath: string): Promise<number> {
  return new Promise((resolve, reject) => {
    fastFolderSize(filePath, (err, bytes?: number) => {
      if (bytes) return resolve(bytes);

      reject(err)
    })
  })
}

/**
 * 上传文件
 */
const basement = new Basement({
  appId: '61c428ded57f620493e0ba10',
  masterKey: '2cfvgCuQY9YGFIpwfOEojBWt',
  urllib,
  endpoint: 'https://basement-gzone.alipay.com'
});
export async function uploadToBasement(fileName: string, file: string): Promise<IUploadToBasementRes> {
  return basement.file.upload(fileName, new Buffer(file));
}
export interface IUploadToBasementRes {
  ETag: string;
  name: string;
  size: number;
  type: string;
  url: string;
}

export async function continueIfPathExists<T>(filePath: string, callback: (filePath: string) => Promise<T>) {
  if (await fse.pathExists(filePath)) {
    return callback(filePath)
  } else {
    return;
  }
}

/** 递归获取文件夹体积 */
export function getFolderSize(folderPath) {
  let totalSize = 0;

  const files = fse.readdirSync(folderPath);

  files.forEach(file => {
    const filePath = path.join(folderPath, file);
    const stats = fse.statSync(filePath);

    if (stats.isFile()) {
      totalSize += stats.size;
    } else if (stats.isDirectory()) {
      totalSize += getFolderSize(filePath);
    }
  });

  return totalSize;
}