import { IShrinkwrap } from '@/apis/interface/helper';
import { isProd } from '@/apis/utils';
import { PRE_URL, PROD_URL } from '@/apis/const';
import PROJECT_CONFIG from '@/apis/const/project';

interface IParams {
  deps: IShrinkwrap,
  key: string,
  depChainStr: string,
  diffKey?: string
}

interface IDepParams {
  name: string;
  key: string;
  version: string;
  depChainStr: string;
  diffKey?: string;
}
interface IPureObj {
  [key: string]: any;
}

enum Type {
  UNCHANGED = 0, // 未变更
  DELELTE = 1, // 删除
  ADD = 2, // 新增
  MODIFY = 3, // 更新
}

const NOTICE_TYPE_STR_MAP = {
  [Type.UNCHANGED]: '依赖变更',
  [Type.ADD]: '新增',
  [Type.DELELTE]: '删除',
  [Type.MODIFY]: '版本变更'
};

const NOTICE_COLOR = {
  [Type.UNCHANGED]: '#999999',
  [Type.ADD]: '#52c41a',
  [Type.DELELTE]: '#ff4d4f',
  [Type.MODIFY]: '#faad14'
};


/** 设置依赖信息：版本、依赖链 */
const setDepInfo = (obj: IPureObj, params: IDepParams) => {
  const { name, key, version, depChainStr } = params;
  const val = obj[name];
  const depArrName = `${key}-${version}`;

  // 依赖 - 版本不存在
  if (!val || !val[key]) {
    // 不增加额外字段
    const depsObj = depChainStr ? { [depArrName]: [depChainStr] } : {};
    obj[name] = {
      ...(val || {}),
      ...depsObj,
      [key]: [version],
    }
    return;
  }
  // 依赖 - 不同版本
  if (val[key].indexOf(version) === -1) {
    obj[name][key].push(version);
  }
  
  // 增加依赖关系
  const depList = obj[name][depArrName] || [];
  if (depChainStr && depList.indexOf(depChainStr) === -1)  {
    obj[name][depArrName] = [...depList, depChainStr];
  }
};
  
// 设置diff 类型：1-删除 2-新增 3-修改
const setDiffType = (obj: IPureObj, params: IDepParams) => {
  const { name, version, diffKey } = params;
  if (!diffKey) return;
  const diffVal = obj[name][diffKey];
  if (!diffVal) {
    // 删除项
    obj[name].type = Type.DELELTE;
  } else if (diffVal.indexOf(version) === -1) {
    // 修改项
    obj[name].type = Type.MODIFY;
  }
};

export const getNoticePkgMap = (noticeConfig?: IPureObj) => {
  if (!noticeConfig) return {};

  return Object.entries(noticeConfig).reduce((prev, [bizType, config]) => {
    return Object.entries(config).reduce((res, [pkg, noticeUser]) => {
      res[pkg] = `${bizType}-${noticeUser}`;
      return res;
    }, prev);
  }, {});
};

/** 获取依赖链首页包名 */
const getHeadPkgName = (str) => {
  if (!str) return '';
  const pkgStr = str.split(' -> ').shift() || '';
  return pkgStr.split('@').slice(0, -1).join('@');
}

const getNoticeInfo = (noticePkgMap, item, key, res): IPureObj => {
  const versions = item[key];
  if (!versions) {
    return res;
  };

  // 收集影响的包
  return versions.reduce((prev, v) => {
    const versionDepKey = `${key}-${v}`;
    const depsList = item[versionDepKey] || [];
    depsList.forEach((depStr) => {
      const pkgName = getHeadPkgName(depStr) || '';
      if (noticePkgMap[pkgName]) {
        const oldData = prev[pkgName] || {};
        // 依赖过滤 - 仅保留与需要通知的包有关的依赖链
        prev[pkgName] = {
          ...oldData,
          [versionDepKey]: depsList.filter(dep => getHeadPkgName(dep) === pkgName)
        };
      }
    });
    return prev;
  }, res);
}

/** 收集新增场景的通知包信息 */
/** 收集通知包信息 */
const collectNoticePkg = (
  noticeInfo,
  noticePkgMap,
  obj,
  item,
) => {
  const { name, type, version, prevVersion } = item;
  if (!type) return;
  // 页面npm包变更
  if (noticePkgMap[name]) {
    const [bizType, noticeUser] = noticePkgMap[name].split('-');
    const oldData = noticeInfo[name];
    noticeInfo[name] = {
      ...item,
      ...oldData,
      bizType,
      noticeUser,
    };
    return;
  }
  // 如果有依赖的依赖变更，也放置到通知信息
  const vRes = getNoticeInfo(noticePkgMap, item, 'version', {});
  const finalRes = getNoticeInfo(noticePkgMap, item, 'prevVersion', vRes);

  // 设置通知内容
  Object.entries(finalRes).forEach(([pkgName, depObj]) => {
    const noticeConfigInfo = noticePkgMap[pkgName];
    const [bizType, noticeUser] = noticeConfigInfo.split('-');
    const oldData = noticeInfo[pkgName] || obj[pkgName] || {};
    const deps = oldData.deps || [];
    noticeInfo[pkgName] = {
      ...oldData,
      bizType,
      noticeUser,
      deps: [...deps, {
        name,
        type,
        version,
        prevVersion,
        ...depObj,
      }],
    };
  });
};
  
/** 依赖收集 */
export const collectDeps = (
  obj: IPureObj,
  params: IParams,
) => {
  const { deps, key, depChainStr, diffKey } = params;
  if (!deps) return;
  Object.entries(deps).forEach(([name, item]) => {
    const { version } = item;
    const inValidVersion = !version || /^http/.test(version);
    if (item.dependencies) {
      const linkStr = depChainStr ? ' -> ' : '';
      const depChain = inValidVersion ? depChainStr : `${depChainStr}${linkStr}${name}@${version}`;
      // 递归收集
      collectDeps(obj, {
        deps: item.dependencies,
        key,
        depChainStr: depChain,
        diffKey,
      });
    }
    // 非合法版本不处理
    if (inValidVersion) return;

    const depParams = {
      name,
      key,
      version,
      depChainStr,
      diffKey,
    };

    setDepInfo(obj, depParams);
    setDiffType(obj, depParams);
  });
};

/** 获取 diff 结果 */
export const getDiffInfo = (
  obj: IPureObj,
  noticeInfo: IPureObj,
  noticePkgMap: IPureObj
) => {
  let addCount = 0; // 新增
  let modifyCount = 0;  // 修改
  let deleteCount = 0;  // 删除

  const list =  Object.entries(obj)
    .map(([name, item]) => {
      // 新增依赖场景
      let type = item.type || Type.UNCHANGED;
      if (!item.prevVersion) {
        type = Type.ADD;
        addCount += 1;
      } else if (type === Type.DELELTE) {
        deleteCount += 1;
      } else if (type === Type.MODIFY) {
        modifyCount += 1;
      }
      const res = { ...item, name, type };
      // 收集依赖通知信息
      collectNoticePkg(noticeInfo, noticePkgMap, obj, res);
      return res;
    })
    .filter(v => !!v.type)
    .sort((a, b) => b.type - a.type);

  return {
    list,
    addCount,
    deleteCount,
    modifyCount,
  };
}

function getCountStr(color: string, count: number, label: string) {
  return `${label}总数：<font color='${color}'>**${count}**</font>`;
}

/** 获取通知信息 */
export const getNoticeMsgContent = (diffRes, iterId, version, project) => {
  const { noticeInfo, addCount, modifyCount, deleteCount } = diffRes;
  let atUserIds = [];
  // 变更明细
  const noticeList = Object.entries(noticeInfo).filter(([_, item]) => {
    // TODO 优化依赖变更通知
    // const { type = Type.UNCHANGED } = item as any;
    // return type !== Type.UNCHANGED;
    return true;
  }).map(([pkgName, item]) => {
    const { noticeUser, type = Type.UNCHANGED } = item as any;
    atUserIds = atUserIds.concat(noticeUser.split(','));
    const tag = `<font color='${NOTICE_COLOR[type]}'>[${NOTICE_TYPE_STR_MAP[type]}]</font>`
    return `- ${tag}${pkgName} <font color='#1890ff'>@${noticeUser.replace(/,/g, '@')}</font>`;
  });
  const title = `【${project.cnName}】迭代 v${version} 依赖变更通知`;
  const singleURL = `${isProd ? PROD_URL : PRE_URL}/#/package/dependencies?iterId=${iterId}`;
  const countList = [
    getCountStr(NOTICE_COLOR[Type.ADD], addCount, '新增'),
    getCountStr(NOTICE_COLOR[Type.DELELTE], deleteCount, '删除'),
    getCountStr(NOTICE_COLOR[Type.MODIFY], modifyCount, '更新'),
  ]
  const textList = [
    `![bg](${PROJECT_CONFIG[project.type].dingtalkBigLogo})`,
    `**${title}** \n`,
    `> ${countList.join(' &nbsp;&nbsp;')}`,
    `${noticeList.join('\n')} \n`,
    '以上业务一级依赖包有变更，辛苦相关负责人关注下。[送花花][送花花][送花花]'
  ];

  return {
    title,
    atUserIds,
    isAtAll: false,
    text: textList.join(' \n '),
    singleTitle: '查看详情',
    singleURL,
  };
}

/** 新版通知报告信息 */
export const getDepsPublishReportMsg = (diffRes, iterId: string) => {
  if (!diffRes) return {
    text: '',
    atUserIds: [],
    actions: [],
  };
  const { noticeInfo, addCount, modifyCount, deleteCount } = diffRes;
  let atUserIds: string[] = [];

  const countList = [
    getCountStr(NOTICE_COLOR[Type.ADD], addCount, '新增'),
    getCountStr(NOTICE_COLOR[Type.DELELTE], deleteCount, '删除'),
    getCountStr(NOTICE_COLOR[Type.MODIFY], modifyCount, '更新'),
  ];
  const noticeList = Object.entries(noticeInfo).filter(([_, item]) => {
    const { type = Type.UNCHANGED } = item as any;
    return type !== Type.UNCHANGED;
  }).map(([pkgName, item]) => {
    const { noticeUser, type = Type.UNCHANGED } = item as any;
    atUserIds = atUserIds.concat(noticeUser.split(','));
    const tag = `<font color='${NOTICE_COLOR[type]}'>[${NOTICE_TYPE_STR_MAP[type]}]</font>`;
    const name = `<font color='#999999'>${pkgName}</font>`;
    return `- ${tag}${name} <font color='#1890ff'>@${noticeUser.replace(/,/g, '@')}</font>`;
  });
  const textList = [
    `<font size=5>**依赖变更**</font>\n\n `,
    `&nbsp;&nbsp;&nbsp;&nbsp;${countList.join(' &nbsp;&nbsp;')} \n\n `,
    `${noticeList.join('\n')}  `,
    `以上业务一级依赖包有变更，辛苦相关负责人关注下。[送花花][送花花][送花花]`,
  ];
  return {
    text: textList.join('\n\n'),
    atUserIds,
    actions: [
      {
        title: '查看依赖明细',
        actionURL: `${isProd ? PROD_URL : PRE_URL}/#/package/dependencies?iterId=${iterId}`
      }
    ]
  }
}