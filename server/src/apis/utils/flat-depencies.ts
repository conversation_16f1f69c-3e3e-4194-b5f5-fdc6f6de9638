import { IShrinkwrap } from '@/apis/interface/helper';
import { PROD_URL } from '@/apis/const';
import { isInValidDep } from './depencies-tree';

interface IParams {
  deps: IShrinkwrap;
  key: string;
  depChainStr: string;
  depChainList?: string[];
  diffKey?: string;
  rootDeps?: Record<string, any>;
}

interface IDepParams {
  name: string;
  key: string;
  version: string;
  depChainStr: string;
  diffKey?: string;
}
interface IPureObj {
  [key: string]: any;
}

enum Type {
  UNCHANGED = 0, // 未变更
  DELELTE = 1, // 删除
  ADD = 2, // 新增
  MODIFY = 3, // 更新
}

const NOTICE_TYPE_PUBLISH_MAP = {
  [Type.UNCHANGED]: '依赖变更',
  [Type.ADD]: '新增的依赖',
  [Type.DELELTE]: '删除的依赖',
  [Type.MODIFY]: '版本变更'
};
const NOTICE_TYPE_STR_MAP = {
  [Type.UNCHANGED]: '依赖变更',
  [Type.ADD]: '新增',
  [Type.DELELTE]: '删除',
  [Type.MODIFY]: '版本变更'
};

const NOTICE_COLOR = {
  [Type.UNCHANGED]: '#999999',
  [Type.ADD]: '#52c41a',
  [Type.DELELTE]: '#ff4d4f',
  [Type.MODIFY]: '#faad14'
};


/** 设置依赖信息：版本、依赖链 */
const setDepInfo = (obj: IPureObj, params: IDepParams) => {
  const { name, key, version, depChainStr } = params;
  const val = obj[name];
  const depArrName = `${key}-${version}`;

  // 依赖 - 版本不存在
  if (!val || !val[key]) {
    // 不增加额外字段
    const depsObj = depChainStr ? { [depArrName]: [depChainStr] } : {};
    obj[name] = {
      ...(val || {}),
      ...depsObj,
      [key]: [version],
    }
    return;
  }
  // 依赖 - 不同版本
  if (val[key].indexOf(version) === -1) {
    obj[name][key].push(version);
  }
  
  // 增加依赖关系
  const depList = obj[name][depArrName] || [];
  if (depChainStr && depList.indexOf(depChainStr) === -1)  {
    obj[name][depArrName] = [...depList, depChainStr];
  }
};
  
// 设置diff 类型：1-删除 2-新增 3-修改
const setDiffType = (obj: IPureObj, params: IDepParams) => {
  const { name, version, diffKey } = params;
  if (!diffKey) return;
  const diffVal = obj[name][diffKey];
  if (!diffVal) {
    // 删除项
    obj[name].type = Type.DELELTE;
  } else if (diffVal.indexOf(version) === -1) {
    // 修改项
    obj[name].type = Type.MODIFY;
  }
};

export const getNoticePkgMap = (noticeConfig?: IPureObj) => {
  if (!noticeConfig) return {};

  return Object.entries(noticeConfig).reduce((prev, [bizType, config]) => {
    return Object.entries(config).reduce((res, [pkg, noticeUser]) => {
      res[pkg] = `${bizType}-${noticeUser}`;
      return res;
    }, prev);
  }, {});
};

/** 获取依赖链首页包名 */
const getHeadPkgName = (str) => {
  if (!str) return '';
  const pkgStr = str.split(' -> ').shift() || '';
  return pkgStr.split('@').slice(0, -1).join('@');
}

const getNoticeInfo = (noticePkgMap, item, key, res): IPureObj => {
  const versions = item[key];
  if (!versions) {
    return res;
  };

  // 收集影响的包
  return versions.reduce((prev, v) => {
    const versionDepKey = `${key}-${v}`;
    const depsList = item[versionDepKey] || [];
    depsList.forEach((depStr) => {
      const pkgName = getHeadPkgName(depStr) || '';
      if (noticePkgMap[pkgName]) {
        const oldData = prev[pkgName] || {};
        // 依赖过滤 - 仅保留与需要通知的包有关的依赖链
        prev[pkgName] = {
          ...oldData,
          [versionDepKey]: depsList.filter(dep => getHeadPkgName(dep) === pkgName)
        };
      }
    });
    return prev;
  }, res);
}

/** 收集新增场景的通知包信息 */
/** 收集通知包信息 */
const collectNoticePkg = (
  noticeInfo,
  noticePkgMap,
  obj,
  item,
) => {
  const { name, type, version, prevVersion } = item;
  if (!type) return;
  // 页面npm包变更
  if (noticePkgMap[name]) {
    const [bizType, noticeUser] = noticePkgMap[name].split('-');
    const oldData = noticeInfo[name];
    noticeInfo[name] = {
      ...item,
      ...oldData,
      bizType,
      noticeUser,
    };
    return;
  }
  // 如果有依赖的依赖变更，也放置到通知信息
  const vRes = getNoticeInfo(noticePkgMap, item, 'version', {});
  const finalRes = getNoticeInfo(noticePkgMap, item, 'prevVersion', vRes);

  // 设置通知内容
  Object.entries(finalRes).forEach(([pkgName, depObj]) => {
    const noticeConfigInfo = noticePkgMap[pkgName];
    const [bizType, noticeUser] = noticeConfigInfo.split('-');
    const oldData = noticeInfo[pkgName] || obj[pkgName] || {};
    const deps = oldData.deps || [];
    noticeInfo[pkgName] = {
      ...oldData,
      bizType,
      noticeUser,
      deps: [...deps, {
        name,
        type,
        version,
        prevVersion,
        ...depObj,
      }],
    };
  });
};
  
/** 依赖收集 */
export const collectDeps = (
  obj: IPureObj,
  params: IParams,
) => {
  const { deps, key, depChainStr, diffKey } = params;
  if (!deps) return;
  Object.entries(deps).forEach(([name, item]) => {
    const { version } = item;
    // const inValidVersion = isInValidDep(name, item, depChainStr);
    const inValidVersion = !version || /^http/.test(version);
    if (diffKey && name === '@ali/rxpi-hotel-city-pad') {
      console.log(name, inValidVersion, version, depChainStr);
    }
    if (item.dependencies) {
      const linkStr = depChainStr ? ' -> ' : '';
      const depChain = inValidVersion ? depChainStr : `${depChainStr}${linkStr}${name}@${version}`;
      // 递归收集
      collectDeps(obj, {
        deps: item.dependencies,
        key,
        depChainStr: depChain,
        diffKey,
      });
    }
    // 非合法版本不处理
    if (inValidVersion) return;

    const depParams = {
      name,
      key,
      version,
      depChainStr,
      diffKey,
    };

    setDepInfo(obj, depParams);
    setDiffType(obj, depParams);
  });
};

const getDepName = (packages: IShrinkwrap, name: string, depChainList: string[]) => {
  const listLen = depChainList.length;
  for (let i = 0; i < listLen; i++) {
    const chainDepName = depChainList.slice(i, listLen).join('/node_modules/');
    const depName = `node_modules/${chainDepName}${chainDepName ? '/node_modules/' : ''}${name}`;
    if (packages[depName]) {
      return depName;
    }
  }
  return `node_modules/${name}`;
};

const collectLock3Deps = (packages: IShrinkwrap, obj: IPureObj, params: IParams, cacheMap: IPureObj) => {
  const { deps, key, depChainStr, diffKey, depChainList = [], rootDeps } = params;
  if (!deps || !Object.keys(deps).length) return;
  Object.keys(deps).forEach((name) => {
    const depName = getDepName(packages, name, depChainList);
    const item = packages[depName];
    const { version } = item;
    if (isInValidDep(name, item, depChainStr) || (depChainStr && depName === `node_modules/${name}` && rootDeps && rootDeps[name])) return;
    const depParams = {
      ...params,
      name,
      version,
    };
    const currentChain = `${name}@${version}`;
    setDepInfo(obj, depParams);
    setDiffType(obj, depParams);

    if (cacheMap[currentChain]) {
      return;
    }
    cacheMap[currentChain] = true;
    collectLock3Deps(packages, obj, {
      deps: item.dependencies,
      key,
      diffKey,
      depChainStr: `${depChainStr}${depChainStr ? ' -> ' : ''}${currentChain}`,
      depChainList: [...depChainList, name], 
      rootDeps,
    }, cacheMap);
  });
}

export const handleShrinkwrap = (
  obj: IPureObj,
  params: { key: string; depChainStr: string; diffKey?: string },
  shrinkwrapJson: { lockfileVersion: number; dependencies?: IShrinkwrap; packages?: IShrinkwrap }
) => {
  if (!shrinkwrapJson) return;
  // lockfileVersion 3 为 npm 8 产物
  if (shrinkwrapJson.lockfileVersion === 3) {
    const packages = shrinkwrapJson.packages || {} as IShrinkwrap;
    const rootDeps = packages['']?.dependencies || {};
    const cacheMap = {};
    collectLock3Deps(packages, obj, {
      ...params,
      deps: rootDeps,
      rootDeps: rootDeps,
    }, cacheMap);
    return;
  }
  // lockfileVersion 1 为 npm 产物
  collectDeps(obj, {
    ...params,
    deps: shrinkwrapJson.dependencies as IShrinkwrap,
  });
}

/** 获取 diff 结果 */
export const getDiffInfo = (
  obj: IPureObj,
  noticeInfo: IPureObj,
  noticePkgMap: IPureObj
) => {
  let addCount = 0; // 新增
  let modifyCount = 0;  // 修改
  let deleteCount = 0;  // 删除

  const list =  Object.entries(obj)
    .map(([name, item]) => {
      // 新增依赖场景
      let type = item.type || Type.UNCHANGED;
      if (!item.prevVersion) {
        type = Type.ADD;
        addCount += 1;
      } else if (type === Type.DELELTE) {
        deleteCount += 1;
      } else if (type === Type.MODIFY) {
        modifyCount += 1;
      }
      const res = { ...item, name, type };
      // 收集依赖通知信息
      collectNoticePkg(noticeInfo, noticePkgMap, obj, res);
      return res;
    })
    .filter(v => !!v.type)
    .sort((a, b) => b.type - a.type);

  return {
    list,
    addCount,
    deleteCount,
    modifyCount,
  };
}

function getCountStr(color: string, count: number, label: string) {
  return `**${label}总数：**<font color='${color}'>**${count}**</font>`;
}

/** 新版通知报告信息 */
export const getDepsPublishReportMsg = (diffRes, iterId: number) => {
  if (!diffRes) return {
    text: '',
    atUserIds: [],
    actions: [],
  };
  const { noticeInfo, addCount, modifyCount, deleteCount } = diffRes;
  let atUserIds: string[] = [];

  const countList = [
    getCountStr(NOTICE_COLOR[Type.ADD], addCount, '新增'),
    getCountStr(NOTICE_COLOR[Type.DELELTE], deleteCount, '删除'),
    getCountStr(NOTICE_COLOR[Type.MODIFY], modifyCount, '更新'),
  ];
  const noticeMap = Object.entries(noticeInfo).filter(([_, item]) => {
    const { type = Type.UNCHANGED } = item as any;
    return type !== Type.UNCHANGED;
  }).reduce((prev, [pkgName, item]) => {
    const { noticeUser, type = Type.UNCHANGED } = item as any;
    atUserIds = atUserIds.concat(noticeUser.split(','));
    const name = `${pkgName}`;
    const oldData = prev[type] || [];
    return {
      ...prev,
      [type]: [
        ...oldData,
        `- ${name} <font color='#1890ff'>@${noticeUser.replace(/,/g, '@')}</font>`
      ]
    }
  }, {});
  const noticeList = Object.keys(noticeMap).map((type) => {
    const list = noticeMap[type];
    return `**${NOTICE_TYPE_PUBLISH_MAP[type]}**\n${list.join('\n')}`;
  });
  const textList = [
    `#### <font color='#1890ff' size=7>**依赖变更**</font>\n\n `,
    `${countList.join(' &nbsp;&nbsp;')} \n\n `,
    `${noticeList.join('\n\n')}  `,
    noticeList.length ? `<font color='#999999'>以上业务一级依赖包有变更，辛苦相关负责人关注下。</font>[送花花][送花花][送花花]` : '',
  ];
  return {
    text: textList.filter(v => !!v).join('\n\n'),
    atUserIds,
    actions: [
      {
        title: '查看依赖明细',
        actionURL: `${PROD_URL}/#/package/dependencies?iterId=${iterId}`
      }
    ]
  }
}

function getVersionAndTagText(item) {
  const { type = Type.UNCHANGED, version = [], prevVersion = [] } = item;
  // 版本变更展示版本信息
  const versionText = type !== Type.MODIFY ? '' : `${prevVersion.join(' ')} → ${version.join(' ')}`;
  // 版本变更不展示标签
  const tagText = type !== Type.MODIFY && type !== Type.UNCHANGED ? ` <font color='${NOTICE_COLOR[type]}'>**${NOTICE_TYPE_STR_MAP[type]}**</font>` : '';
  return {
    versionText,
    tagText,
  }
}

function getChildDepsText(deps) {
  if (!deps || !deps.length) return '';
  const count = deps.length;
  const moreText = count > 3 ? ` \n\n - 其他......` : '';
  // 最多展示3个
  const detailText = deps.slice(0, 3).map((dep) => {
    const { versionText, tagText } = getVersionAndTagText(dep);
    return `- ${dep.name} ${versionText}${tagText}`;
  }).join(' \n\n ');
  return `${detailText}${moreText}`
}

// 以个人为维度的通知
export function getDepsChangeNoticeInfo(diffRes, iterId: number) {
  if (!diffRes || !diffRes.noticeInfo) return null;
  const { noticeInfo } = diffRes;
  const title = '你负责的核心npm包有变更👇🏻';
  const tips = `辛苦关注下~ 👉[查看详情](${PROD_URL}/#/package/dependencies?iterId=${iterId})`;
  const userMap = Object.entries(noticeInfo).reduce((prev, [pkgName, item]) => {
    const { noticeUser, deps } = item as any;
    const { versionText: _versionText, tagText } = getVersionAndTagText(item);
    const depsText = getChildDepsText(deps);
    const sign = tagText || _versionText ? '；' : '';
    const versionText = _versionText ? `版本变更：${_versionText}` : '';
    const totalDepsText = depsText ? `${sign}共有<font color='#ff4d4f'>**${deps.length}**</font>个子依赖发生变更：` : '';
    const content = `<font color='#1890ff'>**${pkgName}**</font> ${versionText}${tagText}${totalDepsText} \n\n ${depsText}`;

    noticeUser.split(',').forEach((user) => {
      const oldData = prev[user] || [];
      prev[user] = [...oldData, content];
    });
    return prev;
  }, {});
  return Object.keys(userMap).map(user => {
    return {
      userList: [user],
      content: [
        title,
        ...userMap[user],
        tips,
      ].join(' \n\n ')
    };
  });
}