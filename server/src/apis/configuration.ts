// midway3的时区被调整成0时区，这里将时区重置为东八区
process.env.TZ = "Asia/Shanghai";

import { join } from "path";
import * as faas from "@midwayjs/faas";
import { ILifeCycle } from "@midwayjs/core";
import { Config, Configuration, App } from "@midwayjs/core";
import * as bucLogin from "@ali/midway-buc-login";
import * as session from "@midwayjs/session";
import { DB } from "./model/db";
import * as axios from "@midwayjs/axios";
import { installTnpm } from './utils/mini-build';

@Configuration({
  importConfigs: [join(__dirname, "./config/")],
  imports: [faas, session, bucLogin, axios]
})
export class ContainerConfiguration implements ILifeCycle {
  @Config("sequelize") sequelizeConfig: any;

  @App() app;

  // @ts-ignore
  async onReady(): Promise<void> {
    console.log("进入ready>>>>>>>>");

    // 初始化小程序构建环境
    // await miniBuild.init();

    // 建立数据库连接
    await DB.init(this.sequelizeConfig);

    // 安装tnpm 
    installTnpm();
  }
}
