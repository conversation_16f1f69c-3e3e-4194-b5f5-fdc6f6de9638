import { EClient } from '@/apis/const/iter-deliver';
import { EProjectType } from '@/apis/const/project';
import { IBucSimpleUser } from './buc';
import { EBuildPlatform } from '../const/build';

/** 项目信息 */
export interface IProject {
  /** 项目id */
  id: number;
  /** 项目名称 */
  name: string;
  /** 项目中文名 */
  cnName: string;
  /** 项目类型 */
  type: EProjectType;
  /** 项目图标 */
  icon: string;
  /** 代码仓库地址 */
  gitRepo: string;
  /** 代码仓库划分 - 组 */
  group: string;
  /** 代码仓库划分 - 项目 */
  project: string;
  /** 是否是管理员 */
  isAdmin: boolean;
  /** 管理员工号列表 */
  adminWorkidList: string[];
  /** 管理员BUC信息 */
  adminsBucInfo: IBucSimpleUser[];
  /** 投放端 */
  clientList: IClient[];
  /** 钉钉机器人 */
  dingtalkRobots?: IDingtalkRobot[];
  /** 发布信息 */
  publishInfo: {
    /** 发布步骤 */
    steps: { title: string; }[];
    /** 发布平台 */
    platform: string;
  };
  /** package.json */
  pkjJSON?: {
    branch: string;
    json: {
      version: string;
    }
  };
  /** 项目是组件类型时，投放的配置信息 */
  deliverConfig?: { projectName: string; moduleName: string; gitRepo: string; gitProjectId: number; defProjectId: number; deliverList: any };
  /** 打码配置 */
  wsyConfig?: IWsyConfig;
}

export interface IClient {
  /** 小程序id */
  miniAppId: string;
  /** 端名称 */
  clientName: EClient;
}

export interface IWsyConfig {
  /** 场景值 */
  scene: string;
  /** 秘钥 */
  accessKey: string;
  /** 支持平台 */
  platformList: EBuildPlatform[];
}

export interface IDingtalkRobot {
  /** 钉钉webhook地址 */
  robotHook: string; 
  /** 秘钥 */
  signCode: string;
}