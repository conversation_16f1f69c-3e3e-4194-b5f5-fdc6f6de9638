import { INpm } from './npm';
import { IDevBranch } from './dev-branch';

export interface IGitRepo {
  gitRepoUrl: string;   // git仓库地址
}

export interface IGitBranch extends IGitRepo {
  gitBranch: string;
  desc?: string;
}

export interface IGitRepoInfo {
  httpUrl: string;
  sshUrl: string;
  url: string;     // 仓库浏览器访问地址
  host: string;    // gitlab或github域名，常用比如：gitlab.alibaba-inc.com
  group: string;   // git分组
  project: string; // git仓库basename
}

export interface IGitIterCreateParams extends IGitRepo {
  gitBranch: string;    // git分支
  description?: string; // 描述
}

export interface IGitAlterCreateParams extends IGitIterCreateParams {
  baseBranch: string; // 变更基于的分支，目前都基于迭代的stable分支创建
  npmList?: INpm[];   // 页面引用的npm包和其版本
  npmResolutionList?: INpm[];   // 页面引用的npm包resolution和其版本
}

export interface IGitAlterUpdateParams extends IGitIterCreateParams {
  npmList?: INpm[];    // 页面引用的npm包和其版本
  oldNpmList?: INpm[]; // 旧的npm包和其版本列表，用于更新时做diff
  npmResolutionList?: INpm[]; // 旧页面引用的npm包resolution和其版本
  oldNpmResolutionList?: INpm[];   // 旧的npm包resolution和其版本列表，用于更新时做diff
}

interface IGitAlterBranchInfo extends Pick<IDevBranch, 'mergeCode' | 'npmList' | 'npmResolutionList'> {
  branch: string;
}

export interface IGitIterAltersMergeParams {
  gitRepoInfo: IGitRepoInfo;
  projectTmpDirPath: string;
  iterBranchName: string; // 迭代分支
  rcBranchName: string;   // 集成分支
  alterBranchList: IGitAlterBranchInfo[];
  isComponentProject?: boolean;
  version: string;
}

interface INpmJSON {
  [name: string]: string;
}

export interface IPkgJSONParams {
  version?: string;
  dependencies?: INpmJSON
  resolutions?: INpmJSON
}

export interface IGitOptResult {
  success: boolean;      // git操作状态
  errorCode?: string;    // 统一错误码
  errorMessage?: string; // 错误提示信息
  branch?: string; // 操作分支名，集成时候用
}

export interface IGitRepoGetResult extends IGitOptResult {
  json?: any;
}

interface IGitOptErrRes {
  branch?: string;
}

interface IGitOptType {
  type: string;
  errRes: IGitOptErrRes
}

export type IGitOptTypeConf = string | IGitOptType;
