import { EClient } from "@/apis/const/iter-deliver";

/** 体验码详细数据 */
export interface IIterExperience {
  id: number;
  iterId: number;
  clientName: string;
  miniAppId: string;
  creator: string;
  gmtCreate: any;
  status: number;
  commitId: string;
  gmtModified?: any;
  dist?: string;
  miniAppVersion?: string;
}

export interface IIterExperienceGetReq {
  iterId: number;
  clientName: EClient;
  miniAppId: string;
  commitId?: string;
  rcGitName?: string;
  rcGitUrl?: string;
}

export interface IIterExperienceCreateReq {
  iterId: number;
  clientName: EClient;
  miniAppId: string;
  branchName: string;
  branchUrl: string;
  reBuild?: boolean;
}
