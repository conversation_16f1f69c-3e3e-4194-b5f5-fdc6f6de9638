import { INpm } from '@/apis/interface/npm';
import { IAone } from '@/apis/interface/aone';
import { IGitBranch } from '@/apis/interface/git-branch';
import { IGitRepoInfo } from '@/apis/interface/git-opts';
import { IUser } from '@/apis/interface/user';
import { IIterBranch } from '@/apis/interface/iter-branch';
import { BIZ_LINE } from '@/apis/const';
import { EDevStatus, EMergeCode, EChecked, EBranchType } from '@/apis/const/dev-branch';
import { EDefBuildStatus } from '@/apis/const/def';

/** 开发分支详情 */
export interface IBaseDevBranch {
  /** 开发分支id */
  devId: number;
  /** 分支类型（项目类型：app、组件类型：component） */
  branchType?: EBranchType;
  /** 分支名称 */
  branchName: string;
  /** 分支描述 */
  description: string;
  /** 创建人 */
  creator: string;
  /** 创建人工号 */
  creatorWorkid?: string;
  /** 创建时间 */
  gmtCreate: string;
  /** 修改人 */
  modifier?: string;
  /** 修改人工号 */
  modifierWorkid?: string;
  /** 修改时间 */
  gmtModified?: string;
  /** 状态：-1 - 废弃、0 - 开发中、1 - 准备就绪 */
  status: EDevStatus;
  /** 代码分支 */
  gitBranch: IGitBranch;
  /** 关联的aone */
  aoneList: IAone[];
  /** 是否合并代码：0 - 不合并、1 - 合并 */
  mergeCode: EMergeCode;
  /** 需要更新的dependencies npm包 */
  npmList: INpm[];
  /** 需要更新的resolutions npm包 */
  npmResolutionList: INpm[];
  /** 业务线 */
  bizLine?: BIZ_LINE;
  /** 测试负责人 */
  qaList?: IUser[];
  /** 塔台id，自动化测试用 */
  towerId?: string;
  /** shrinkwrap.json地址 */
  shrinkwrap?: string;
  /** 构建产物地址 */
  dist?: string;
  /** report_analyzed.json地址 */
  reportAnalyzed?: string;
  /** 是否已测试回归 */
  checked?: EChecked;

  /** 分支状态文案 */
  statusText: string;
  /** Git仓库 */
  gitRepo: IGitRepoInfo;
  /** 挂载到的迭代分支 */
  iterBranch?: IIterBranch;

  /** 迭代类型是组件类型时，新建git分支时，npm包的原始版本号 */
  pkgInitialVersion?: string;
  /** 迭代类型是组件类型时，npm包发布后的版本号 */
  pkgPublishVersion?: string;
  /** 迭代类型是组件类型时，def迭代id */
  defIterId?: number;
  /** 迭代类型是组件类型时，def构建任务id */
  defTaskId?: number;
  /** 迭代类型是组件类型时，构建状态（1：未构建、2：构建中、3：构建成功、4：构建失败） */
  defBuildStatus?: EDefBuildStatus;
  /** 迭代类型是组件类型时，def构建分支branchId（用于绑定） */
  defBranchId?: number;
}
