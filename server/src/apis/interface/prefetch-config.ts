/** prefetch配置信息 */ 
export interface IPrefetchConfig {
  /** id */
  id: number;
  /** 所属项目名称 */
  projectName: string;
  /** 是否停用 */
  isStop: number;
  /** 页面path */
  path: string;
  /** 是否需要登陆 */
  needLogin?: number;
  /** mtopApi */
  mtopApi?: string; 
  /** mtopVersion */
  mtopVersion?: string;
  /** 强匹配参数 */
  verifyKeys?: string;
  /** 获取数据时静态参数 */
  staticParams?: string;
  /** 动态参数 */
  dynamicParams?: string;
  /** 过期时间(ms) */ 
  expiryTimeMs?: number;
  /** 获取mtop时的超时时间(ms) */ 
  mtopTimeOutMs?: string;
  /** 是否需要定位信息 */
  needGeolocation?: number;
  /** 是否获取完就删除 */
  isNeedGetThenRemove?: number;
  /** 是否有冷启动场景 */
  hasColdStart?: number;
  /** hsfId */
  hsfId?: string;
  /** hsfMethod */
  hsfMethod?: string;
  /** hsfParameterTypes */ 
  hsfParameterTypes?: string;
  /** hsfParamsObj */ 
  hsfParamsObj?: string;
  /** 是否灰度 */
  isGray?: boolean;
  /** 灰度进度 */
  grayNumber?: number;
  /** 不开启冷启动*/
  notColdPrefetch?: number;
}