import { EPackageType, EBuildTarget, EBuildType } from '@/apis/const/package';

export interface IAppStructure {
  /** 包信息 */
  packages: IPackageInfo[],
  /** 参与按需构建的包，undefined代表没有按需构建 */
  minifyBuildPackages?: IPackageInfo[],
}

export interface IPackageInfo {
  /** 包名 */
  packageName: string;
  /** gitlab链接 */
  gitlabHref: string;
  /** 包类型 */
  packageType: {
    /** 名称 */
    name: EPackageType;
    /** 展示标题 */
    title: string;
  };
  /** 构建类型 */
  buildType: {
    /** 名称 */
    name: string;
    /** 展示标题 */
    title: string;
  }
  /** 业务类型 */
  bizType?: {
    /** 名称 */
    name: string;
    /** 展示标题 */
    title: string;
  };
  pages?: IPageInfo[]
}

export interface IPageInfo {
  /** 页面名 */
  pageName: string;
  /** gitlab链接 */
  gitlabHref: string;
  /** 页面标题 */
  pageTitle?: string;
  /** 路径 */
  source: string;
  /** 预加载规则 */
  preloadRule?: {
    network: string;
    packages: string[];
  };
  /** 业务类型 */
  bizType?: {
    /** 名称 */
    name: string;
    /** 展示标题 */
    title: string;
  };
}

export interface IAppJSON {
  routes: {
    source: string;
    name: string;
    miniappMain?: boolean;
    independent?: boolean;
    targets?: string[]
    bizType?: string;
    devBizType?: string[];
  }[]
}

export interface IPackageJSON {
  routes: {
    source: string;
    miniappPreloadRule?: {
      network: string;
      packages: string[];
    };
    window?: {
      title: string;
    }
    bizType?: string;
  }[]
}

export interface IBuildJSON {
  targets: EBuildTarget[];
  [EBuildTarget.ALIPAY]: {
    buildType: EBuildType
  }
  [EBuildTarget.WEIXIN]: {
    buildType: EBuildType
  }
  [EBuildTarget.BYTEDANCE]: {
    buildType: EBuildType
  }
}

export interface IPackageSize {
  /** 源码体积（小程序源码） */
  sourceSize: number;
  /** 人可读的源码体积 */
  sourceHumanSize: string;
  /** 构建产物体积（不含插件） */
  buildSize: number;
  /** 人可读的构建产物体积 */
  buildHumanSize: string;
  /** 分包信息 */
  subPackage: ISubPackageSize[];
}

export interface ISubPackageSize {
  /** 构建产物体积（不含插件） */
  buildSize: number;
  /** 人可读的构建产物体积 */
  buildHumanSize: string;
  /** 包名 */
  subPackageName: string;
}

export interface IPackageParams {
  branchName: string;
  branchUrl: string;
}