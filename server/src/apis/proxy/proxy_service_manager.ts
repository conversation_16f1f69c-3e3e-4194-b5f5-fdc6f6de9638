
      import { Provide, Inject, App } from '@midwayjs/decorator';
      // @ts-ignore
      import { RPC_RESULT_FAILED, RPC_RESULT_SUCCESS, TYPE_CUSTOM_HSF_CLIENT } from '@ali/eagleeye-tracer';
      
          interface MockRidsSidsVidsDebugDeviceParamsSourceUseridPagenumsExtparamsPagesizesPressureuserid {
            mock?: boolean;
rids?: string;
sids?: string;
vids?: string;
debug?: boolean;
device?: string;
params?: string;
source?: string;
userId?: string;
pageNums?: string;
extParams?: string;
pageSizes?: string;
pressureUserId?: string;
          }
        

          interface ModelobjHeadersMsgcodeMsginfoSuccessBizextmapMappingcodeHttpstatuscode {
            model?: any;
headers?: any;
msgCode?: string;
msgInfo?: string;
success?: boolean;
bizExtMap?: any;
mappingCode?: string;
httpStatusCode?: number;
          }
        

          interface LonglinkNeedforever {
            longLink?: string;
needForever?: boolean;
          }
        

          interface CodeDataobjMessageSuccess {
            code?: string;
data?: any;
message?: string;
success?: boolean;
          }
        

      @Provide('serviceManager')
      export class ServiceManager {
        _envCache: any;

        @Inject()
        ctx: any;

        @App()
        app: any;

        get env() {
          if (this.ctx.aliEnv) {
            return this.ctx.aliEnv;
          }
          if (!this._envCache) {
            this._envCache = this.app.getEnv();
          }
          return this._envCache;
        }

        tbTripfcecoreService = this._get_tbTripfcecoreService();
        _get_tbTripfcecoreService() {
          const _this = this;
          if (false) { console.log(_this); }
          return {
            async invoke(param_first?: MockRidsSidsVidsDebugDeviceParamsSourceUseridPagenumsExtparamsPagesizesPressureuserid, ...$$$othArgs: any[]):Promise<ModelobjHeadersMsgcodeMsginfoSuccessBizextmapMappingcodeHttpstatuscode> {
              const $$args: any = [param_first, ...$$$othArgs];
              if (false) { console.log($$args); }
              return _this.tbTripfcecoreService._getData(...$$args);
            },
            async _getData(...$$args: any[]):Promise<ModelobjHeadersMsgcodeMsginfoSuccessBizextmapMappingcodeHttpstatuscode> {
          
          // 预发环境
          if (_this.env == 'pre') {
            return _this.tbTripfcecoreService._getData_pre(...$$args);
          }
          
          // 日常环境
          if (!_this.env || _this.env == 'daily' || _this.env == 'local') {
            return _this.tbTripfcecoreService._getData_daily(...$$args);
          }
          
      const [param_first] = $$args;
      return await _this.ctx.hsfClient.invoke({
      id: 'com.fliggy.fcecore.service.CoreMgetService:1.0.0',
      group: 'HSF',
      method: 'getData',
      args: [param_first || {"mock":false,"rids":"","sids":"","vids":"","debug":false,"device":"","params":"","source":"","userId":"","pageNums":"","extParams":"","pageSizes":"","pressureUserId":""}],
      parameterTypes: ['com.fliggy.fcecore.domain.MultiMtopRequest'],
      
    })
    
        },
async _getData_pre(...$$args: any[]):Promise<ModelobjHeadersMsgcodeMsginfoSuccessBizextmapMappingcodeHttpstatuscode> {
          
          
          
      const [param_first] = $$args;
      return await _this.ctx.hsfClient.invoke({
      id: 'com.fliggy.fcecore.service.CoreMgetService:1.0.0',
      group: 'HSF',
      method: 'getData',
      args: [param_first || {"mock":false,"rids":"","sids":"","vids":"","debug":false,"device":"","params":"","source":"","userId":"","pageNums":"","extParams":"","pageSizes":"","pressureUserId":""}],
      parameterTypes: ['com.fliggy.fcecore.domain.MultiMtopRequest'],
      
    })
    
        },
async _getData_daily(...$$args: any[]):Promise<ModelobjHeadersMsgcodeMsginfoSuccessBizextmapMappingcodeHttpstatuscode> {
          
          
          
      const [param_first] = $$args;
      return await _this.ctx.hsfClient.invoke({
      id: 'com.fliggy.fcecore.service.CoreMgetService:1.0.0.daily',
      group: 'HSF',
      method: 'getData',
      args: [param_first || {"mock":false,"rids":"","sids":"","vids":"","debug":false,"device":"","params":"","source":"","userId":"","pageNums":"","extParams":"","pageSizes":"","pressureUserId":""}],
      parameterTypes: ['com.fliggy.fcecore.domain.MultiMtopRequest'],
      
    })
    
        }
          };
        }
        
fliggyFcopenapiService = this._get_fliggyFcopenapiService();
        _get_fliggyFcopenapiService() {
          const _this = this;
          if (false) { console.log(_this); }
          return {
            async invoke(accountID?: string, serviceName?: string, functionName?: string, params?: any, version?: string, ...$$$othArgs: any[]):Promise<any> {
              const $$args: any = [accountID, serviceName, functionName, params, version, ...$$$othArgs];
              if (false) { console.log($$args); }
              return _this.fliggyFcopenapiService._invoke(...$$args);
            },
            async _invoke(...$$args: any[]):Promise<any> {
          
          // 预发环境
          if (_this.env == 'pre') {
            return _this.fliggyFcopenapiService._invoke_pre(...$$args);
          }
          
          // 日常环境
          if (!_this.env || _this.env == 'daily' || _this.env == 'local') {
            return _this.fliggyFcopenapiService._invoke_daily(...$$args);
          }
          
      const [accountID, serviceName, functionName, params, version] = $$args;
      return await _this.ctx.hsfClient.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      group: 'HSF',
      method: 'invoke',
      args: [accountID || "", serviceName || "", functionName || "", params || {"":""}, version || ""],
      parameterTypes: ['java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String'],
      
    })
    
        },
async _invoke_pre(...$$args: any[]):Promise<any> {
          
          
          
      const [accountID, serviceName, functionName, params, version] = $$args;
      return await _this.ctx.hsfClient.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      group: 'HSF',
      method: 'invoke',
      args: [accountID || "", serviceName || "", functionName || "", params || {"":""}, version || ""],
      parameterTypes: ['java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String'],
      
    })
    
        },
async _invoke_daily(...$$args: any[]):Promise<any> {
          
          
          
      const [accountID, serviceName, functionName, params, version] = $$args;
      return await _this.ctx.hsfClient.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0.daily',
      group: 'HSF',
      method: 'invoke',
      args: [accountID || "", serviceName || "", functionName || "", params || {"":""}, version || ""],
      parameterTypes: ['java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String'],
      
    })
    
        }
          };
        }
        
fuFurlServiceCreateFurlService = this._get_fuFurlServiceCreateFurlService();
        _get_fuFurlServiceCreateFurlService() {
          const _this = this;
          if (false) { console.log(_this); }
          return {
            async invoke(param_first?: LonglinkNeedforever, ...$$$othArgs: any[]):Promise<CodeDataobjMessageSuccess> {
              const $$args: any = [param_first, ...$$$othArgs];
              if (false) { console.log($$args); }
              return _this.fuFurlServiceCreateFurlService._createFurl(...$$args);
            },
            async _createFurl(...$$args: any[]):Promise<CodeDataobjMessageSuccess> {
          
          // 预发环境
          if (_this.env == 'pre') {
            return _this.fuFurlServiceCreateFurlService._createFurl_pre(...$$args);
          }
          
          // 日常环境
          if (!_this.env || _this.env == 'daily' || _this.env == 'local') {
            return _this.fuFurlServiceCreateFurlService._createFurl_daily(...$$args);
          }
          
      const [param_first] = $$args;
      return await _this.ctx.hsfClient.invoke({
      id: 'com.alibaba.fliggy.furl.service.FurlService:1.0.0',
      group: 'HSF',
      method: 'createFurl',
      args: [param_first || {"longLink":"","needForever":false}],
      parameterTypes: ['com.alibaba.fliggy.furl.model.FurlCreateModel'],
      
    })
    
        },
async _createFurl_pre(...$$args: any[]):Promise<CodeDataobjMessageSuccess> {
          
          
          
      const [param_first] = $$args;
      return await _this.ctx.hsfClient.invoke({
      id: 'com.alibaba.fliggy.furl.service.FurlService:1.0.0',
      group: 'HSF',
      method: 'createFurl',
      args: [param_first || {"longLink":"","needForever":false}],
      parameterTypes: ['com.alibaba.fliggy.furl.model.FurlCreateModel'],
      
    })
    
        },
async _createFurl_daily(...$$args: any[]):Promise<CodeDataobjMessageSuccess> {
          
          
          
      const [param_first] = $$args;
      return await _this.ctx.hsfClient.invoke({
      id: 'com.alibaba.fliggy.furl.service.FurlService:1.0.0.daily',
      group: 'HSF',
      method: 'createFurl',
      args: [param_first || {"longLink":"","needForever":false}],
      parameterTypes: ['com.alibaba.fliggy.furl.model.FurlCreateModel'],
      
    })
    
        }
          };
        }
        
      }
    