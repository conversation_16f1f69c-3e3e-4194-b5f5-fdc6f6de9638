import { providerWrapper } from '@midwayjs/core';
import {
  AutoIncrement,
  Column,
  DataType,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';

const { BIGINT, CHAR, DATE } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => MiniAppVersionInfoModel;
providerWrapper([
  {
    id: 'MiniAppVersionInfoModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IMiniAppVersionInfoModel = typeof MiniAppVersionInfoModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_miniapp_version_info',
})
export class MiniAppVersionInfoModel extends Model<MiniAppVersionInfoModel> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: BIGINT.UNSIGNED,
    comment: '主键',
    allowNull: false,
  })
  id!: number;

  @Column({
    type: CHAR(20),
    comment: '投放端，适用多端单独投放',
    allowNull: true,
  })
  client_name?: string;

  @Column({
    type: CHAR(20),
    comment: '小程序id',
    allowNull: false,
  })
  mini_app_id!: string;

  @Column({
    type: CHAR(20),
    comment: '小程序上传版本',
    allowNull: false,
  })
  mini_app_version!: string;

  @Column({
    type: CHAR(20),
    comment: '小程序版本状态',
    allowNull: false,
  })
  status!: string;

  @Column({
    type: DATE,
    comment: '版本创建时间',
    allowNull: false,
  })
  create_time!: Date;

  @Column({
    type: DATE,
    comment: '版本灰度时间',
    allowNull: true,
  })
  gray_start_time?: Date;

  @Column({
    type: DATE,
    comment: '版本回滚时间',
    allowNull: true,
  })
  rollback_time?: Date;

  @Column({
    type: DATE,
    comment: '版本上架时间',
    allowNull: true,
  })
  shelf_time?: Date;

  @Column({
    type: DATE,
    comment: '版本下架时间',
    allowNull: true,
  })
  offline_time?: Date;

  @Column({
    type: CHAR(100),
    comment: '灰度策略',
    allowNull: true,
  })
  gray_strategy?: string;

  @Column({
    type: CHAR(100),
    comment: '投放端列表，适用多端统一投放，多个以逗号隔开',
    allowNull: true,
  })
  client_list?: string;
}
