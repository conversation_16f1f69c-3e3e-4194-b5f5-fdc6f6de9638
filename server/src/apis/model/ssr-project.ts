import { providerWrapper } from '@midwayjs/core';
import {
  Column,
  DataType,
  Model,
  PrimaryKey,
  AutoIncrement,
  Table,
} from 'sequelize-typescript';

const { INTEGER, CHAR, TEXT, DATE, TINYINT } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => SsrProjectModel;
providerWrapper([
  {
    id: 'SsrProjectModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type ISsrProjectModel = typeof SsrProjectModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_ssr_project',
})
export class SsrProjectModel extends Model<SsrProjectModel> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: INTEGER.UNSIGNED,
    comment: '主键',
    allowNull: false,
  })
  id!: number;

  @Column({
    type: DATE,
    comment: '创建时间',
    allowNull: false,
  })
  gmt_create!: Date;

  @Column({
    type: DATE,
    comment: '修改时间',
    allowNull: true,
  })
  gmt_modified?: Date;

  @Column({
    type: CHAR(255),
    comment: '项目分组',
    allowNull: true,
  })
  project_group?: string;

  @Column({
    type: CHAR(255),
    comment: '项目名称',
    allowNull: false,
  })
  project_name!: string;

  @Column({
    type: CHAR(255),
    comment: '最近操作人',
    allowNull: true,
  })
  last_operator?: string;

  @Column({
    type: TEXT,
    comment: '管理员列表',
    allowNull: true,
  })
  admin_list?: string;

  @Column({
    type: CHAR(255),
    comment: '项目所属行业',
    allowNull: true,
  })
  project_business?: string;

  @Column({
    type: CHAR(255),
    comment: '项目所属行业',
    allowNull: true,
  })
  pid?: string;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '是否关联DEF发布',
    allowNull: true,
  })
  is_associated_def?: number;
}
