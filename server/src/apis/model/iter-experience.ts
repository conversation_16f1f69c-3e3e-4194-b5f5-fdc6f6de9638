import { providerWrapper } from '@midwayjs/core';
import {
  AutoIncrement,
  Column,
  DataType,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';

const { BIGINT, CHAR, TEXT, DATE, TINYINT } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => IterExperienceModel;
providerWrapper([
  {
    id: 'IterExperienceModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IIterExperienceModel = typeof IterExperienceModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_experience_detail',
})
export class IterExperienceModel extends Model<IterExperienceModel> {
  @PrimaryKey@AutoIncrement@Column({type:BIGINT.UNSIGNED,comment:'主键，打体验码任务id',allowNull:false,})experience_id!:number;
  @Column({type:DATE,comment:'创建时间',allowNull:false,})gmt_create!:Date;
  @Column({type:DATE,comment:'修改时间',allowNull:true,})gmt_modified?:Date;
  @Column({type:TINYINT,comment:'分支状态（-1：生码失败、0：初始化、1：进行中、2 已完成）',allowNull:false,})status!:number;
  @Column({type:BIGINT.UNSIGNED,comment:'所属迭代分支id',allowNull:false,})iter_id!:number;
  @Column({type:TEXT,comment:'构建产物地址',allowNull:true,})dist?:string;
  @Column({type:CHAR(20),comment:'小程序上传版本',allowNull:true,})mini_app_version!:string;
  @Column({type:CHAR(20),comment:'小程序id',allowNull:false,})mini_app_id!:string;
  @Column({type:TEXT,comment:'rc分支commitId',allowNull:false,})commit_id!:string;
  @Column({type:CHAR(20),comment:'投放端',allowNull:false,})client_name!:string;
  @Column({type:CHAR(20),comment:'创建人',allowNull:false,})creator!:string;
  @Column({type:CHAR(10),comment:'创建人工号',allowNull:false,})creator_workid!:string;
}