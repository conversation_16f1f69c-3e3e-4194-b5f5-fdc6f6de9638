import { providerWrapper } from '@midwayjs/core';
import {
  AutoIncrement,
  Column,
  DataType,
  Model,
  PrimaryKey,
  Table
} from 'sequelize-typescript';

const { BIGINT, TINYINT, CHAR, DATE, TEXT } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => IterBranchDetailModel;
providerWrapper([
  {
    id: 'IterBranchDetailModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IIterBranchDetailModel = typeof IterBranchDetailModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_iter_branch_detail',
})
export class IterBranchDetailModel extends Model<IterBranchDetailModel> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: BIGINT,
    comment: '主键，迭代id',
    allowNull: false,
  })
  iter_id!: number;

  @Column({
    type: CHAR(50),
    comment: '分支版本',
    allowNull: false,
  })
  version!: string;

  @Column({
    type: TEXT({ length: 'tiny' }),
    comment: '分支描述',
    allowNull: false,
  })
  description!: string;

  @Column({
    type: TINYINT,
    comment: '分支状态（-1：废弃、0：计划发布、1：集成回归、2：发布中、3：已发布）',
    allowNull: false,
  })
  status!: number;

  @Column({
    type: CHAR(20),
    comment: '灰度状态',
    allowNull: true,
  })
  gray_status?: string;

  @Column({
    type: CHAR(20),
    comment: '创建人',
    allowNull: false,
  })
  creator!: string;

  @Column({
    type: DATE,
    comment: '创建时间',
    allowNull: false,
  })
  gmt_create!: Date;

  @Column({
    type: CHAR(20),
    comment: '修改人',
    allowNull: true,
  })
  modifier?: string;

  @Column({
    type: DATE,
    comment: '修改时间',
    allowNull: true,
  })
  gmt_modified?: Date;

  @Column({
    type: CHAR(10),
    comment: '发布日期',
    allowNull: false,
  })
  publish_day!: string;

  @Column({
    type: TEXT,
    comment: '分支代码，json格式的对象', // { name, url }
    allowNull: false,
  })
  git_branch!: string;

  @Column({
    type: TEXT,
    comment: '开发分支列表，多个用逗号隔开',
    allowNull: true,
  })
  dev_branch_list?: string;

  @Column({
    type: CHAR(30),
    comment: '所属项目名称',
    allowNull: false,
  })
  project_name!: string;

  @Column({
    type: CHAR(10),
    comment: '自动化测试任务id',
    allowNull: true,
  })
  tower_id?: string;

  @Column({
    type: TEXT,
    comment: 'wsy的构建产物地址',
    allowNull: true,
  })
  dist?: string;

  @Column({
    type: TEXT,
    comment: 'report_analyzed.json地址',
    allowNull: true,
  })
  report_analyzed?: string;

  @Column({
    type: TEXT,
    comment: 'shrinkwrap.json地址',
    allowNull: true,
  })
  shrinkwrap?: string;

  @Column({
    type: TEXT,
    comment: '游离开发分支列表，多个用逗号隔开',
    allowNull: true,
  })
  free_dev_branch_list?: string;

  @Column({
    type: TEXT,
    comment: '投放端列表，json格式的数组',
    allowNull: true,
  })
  deliver_client_list?: string;

  @Column({
    type: CHAR(30),
    comment: '迭代类型是组件类型时，新建git分支时 npm包的原始版本号',
    allowNull: true,
  })
  pkg_initial_version?: string;

  @Column({
    type: CHAR(30),
    comment: '迭代类型是组件类型时，npm包发布后的版本号',
    allowNull: true,
  })
  pkg_publish_version?: string;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: '迭代类型是组件类型时，def迭代id',
    allowNull: true,
  })
  def_iter_id?: number;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: '迭代类型是组件类型时，def构建任务id',
    allowNull: true,
  })
  def_task_id?: number;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '迭代类型是组件类型时，def构建状态（1：未构建、2：构建中、3：构建成功、4：构建失败）',
    allowNull: true,
  })
  def_build_status?: number;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: '迭代类型是组件类型时，def构建分支的branchId（用于绑定）',
    allowNull: true,
  })
  def_branch_id?: number;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '迭代类型是组件类型时，def构建类型（1：预发、2：线上）',
    allowNull: true,
  })
  def_env_type?: number;

  @Column({
    type: CHAR(20),
    comment: '迭代类型（项目类型：app、组件类型：component）',
    allowNull: true,
  })
  branch_type?: string;

  @Column({
    type: TINYINT,
    comment: '冻结集成后，主动通知状态（0：未通知，1：已通知）',
    allowNull: true,
  })
  auto_notice_status?: number;

  @Column({
    type: TEXT,
    comment: 'def构建产物',
    allowNull: true,
  })
  def_dist?: string;

  @Column({
    type: TEXT,
    comment: 'def小程序构建上传的包体积信息（目前仅微信有）',
    allowNull: true,
  })
  def_upload_pkg_res?: string;

  @Column({
    type: TEXT,
    comment: '迭代测试负责人',
    allowNull: true,
  })
  qa_list?: string;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '代测试负责人是否回归完成（0：未回归、1：回归完成）',
    allowNull: true,
  })
  checked?: number;
}
