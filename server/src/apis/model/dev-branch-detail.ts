import { providerWrapper } from '@midwayjs/core';
import {
  Column,
  DataType,
  Table,
  Model,
  PrimaryKey,
  AutoIncrement
} from 'sequelize-typescript';

const { BIGINT, TINYINT, CHAR, TEXT, DATE } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => DevBranchDetailModel;
providerWrapper([
  {
    id: 'DevBranchDetailModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IDevBranchDetailModel = typeof DevBranchDetailModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_dev_branch_detail',
})
export class DevBranchDetailModel extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: BIGINT.UNSIGNED,
    comment: '主键，开发分支id',
    allowNull: false,
  })
  dev_id!: number;

  @Column({
    type: CHAR(50),
    comment: '分支版本（逐步废弃）',
    allowNull: true,
  })
  version?: string;

  @Column({
    type: TEXT({ length: 'tiny' }),
    comment: '分支描述',
    allowNull: false,
  })
  description!: string;

  @Column({
    type: TINYINT,
    comment: '分支状态（-1：废弃、0：开发中、1：准备就绪）',
    allowNull: false,
  })
  status!: number;

  @Column({
    type: CHAR(20),
    comment: '创建人',
    allowNull: false,
  })
  creator!: string;

  @Column({
    type: DATE,
    comment: '创建时间',
    allowNull: false,
  })
  gmt_create!: Date;

  @Column({
    type: CHAR(20),
    comment: '修改人',
    allowNull: true,
  })
  modifier?: string;

  @Column({
    type: DATE,
    comment: '修改时间',
    allowNull: true,
  })
  gmt_modified?: Date;

  @Column({
    type: TEXT,
    comment: '关联的aone，json格式的数组或者逗号隔开的字符串',
    allowNull: true,
  })
  aone_list?: string;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '是否合并代码（0：不合并、1：合并）',
    allowNull: false,
  })
  merge_code!: number;

  @Column({
    type: TEXT,
    comment: '需要更新的npm包，json格式的数组',
    allowNull: true,
  })
  npm_list?: string;

  @Column({
    type: TEXT,
    comment: '需要更新的npm resolutions，json格式的数组',
    allowNull: true,
  })
  npm_resolution_list?: string;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: '所属迭代分支id ',
    allowNull: false,
  })
  iter_id!: number;

  @Column({
    type: TEXT,
    comment: '分支代码，json格式的对象', // { name, url }
    allowNull: false,
  })
  git_branch!: string;

  @Column({
    type: CHAR(10),
    comment: '创建人工号',
    allowNull: true,
  })
  creator_workid?: string;

  @Column({
    type: CHAR(10),
    comment: '修改人工号',
    allowNull: true,
  })
  modifier_workid?: string;

  @Column({
    type: CHAR(50),
    comment: '业务线',
    allowNull: true,
  })
  biz_line?: string;

  @Column({
    type: TEXT,
    comment: '测试负责人',
    allowNull: true,
  })
  qa_list?: string;

  @Column({
    type: CHAR(10),
    comment: '自动化测试任务id',
    allowNull: true,
  })
  tower_id?: string;

  @Column({
    type: TEXT,
    comment: '构建产物地址',
    allowNull: true,
  })
  dist?: string;

  @Column({
    type: TEXT,
    comment: 'report_analyzed.json地址',
    allowNull: true,
  })
  report_analyzed?: string;

  @Column({
    type: TEXT,
    comment: 'shrinkwrap.json地址',
    allowNull: true,
  })
  shrinkwrap?: string;

  @Column({
    type: CHAR(50),
    comment: '分支名称',
    allowNull: true,
  })
  branch_name?: string;

  @Column({
    type: CHAR(30),
    comment: '所属项目名称',
    allowNull: true,
  })
  project_name?: string;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '是否已通过回归测试（0：否、1：是）',
    allowNull: true,
  })
  checked?: number;

  @Column({
    type: CHAR(30),
    comment: '迭代类型是组件类型时，新建git分支时 npm包的原始版本号',
    allowNull: true,
  })
  pkg_initial_version?: string;

  @Column({
    type: CHAR(30),
    comment: '迭代类型是组件类型时，npm包发布后的版本号',
    allowNull: true,
  })
  pkg_publish_version?: string;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: '迭代类型是组件类型时，def迭代id',
    allowNull: true,
  })
  def_iter_id?: number;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: '迭代类型是组件类型时，def构建任务id',
    allowNull: true,
  })
  def_task_id?: number;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '迭代类型是组件类型时，def构建状态（1：未构建、2：构建中、3：构建成功、4：构建失败）',
    allowNull: true,
  })
  def_build_status?: number;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: '迭代类型是组件类型时，def构建分支的branchId（用于绑定）',
    allowNull: true,
  })
  def_branch_id?: number;

  @Column({
    type: CHAR(20),
    comment: '分支类型（项目类型：app、组件类型：component）',
    allowNull: true,
  })
  branch_type?: string;
}
