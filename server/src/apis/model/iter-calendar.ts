import { providerWrapper } from '@midwayjs/core';
import {
  AutoIncrement,
  Column,
  DataType,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';

const { BIGINT, TINYINT, CHAR, SMALLINT } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => IterCalendarModel;
providerWrapper([
  {
    id: 'IterCalendarModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IIterCalendarModel = typeof IterCalendarModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_iter_calendar',
})
export class IterCalendarModel extends Model<IterCalendarModel> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: BIGINT.UNSIGNED,
    comment: '主键，迭代id',
    allowNull: false,
  })
  iter_id!: number;

  @Column({
    type: SMALLINT.UNSIGNED,
    comment: '年',
    allowNull: false,
  })
  year!: number;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '月',
    allowNull: false,
  })
  month!: number;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '日',
    allowNull: false,
  })
  day!: number;

  @Column({
    type: CHAR(30),
    comment: '所属项目名称',
    allowNull: false,
  })
  project_name!: string;
}
