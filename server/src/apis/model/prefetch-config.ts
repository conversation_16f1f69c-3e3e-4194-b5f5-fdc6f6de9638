import { providerWrapper } from '@midwayjs/core';
import {
  AutoIncrement,
  Column,
  DataType,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';

const { INTEGER, CHAR, TINYINT, BIGINT, STRING } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => PrefetchConfigModel;
providerWrapper([
  {
    id: 'PrefetchConfigModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IPrefetchConfigModel = typeof PrefetchConfigModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_prefetch_config_new',
})
export class PrefetchConfigModel extends Model<PrefetchConfigModel> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: INTEGER.UNSIGNED,
    comment: '主键，项目id',
    allowNull: false,
  })
  id!: number;

  @Column({
    type: CHAR(30),
    comment: '所属项目名称',
    allowNull: false,
  })
  project_name!: string;

  @Column({
    type: TINYINT,
    comment: '是否已停用',
    allowNull: true,
  })
  is_stop?: number;

  @Column({
    type: CHAR(255),
    comment: '页面路径',
    allowNull: false,
  })
  path!: string;

  @Column({
    type: TINYINT,
    comment: '是否需要登陆',
    allowNull: true,
  })
  need_login?: number;

  @Column({
    type: CHAR(255),
    comment: 'mtop api',
    allowNull: true,
  })
  mtop_api?: string;

  @Column({
    type: CHAR(20),
    comment: 'mtop version',
    allowNull: true,
  })
  mtop_version?: string;

  @Column({
    type: CHAR(255),
    comment: '强校验key',
    allowNull: true,
  })
  verify_keys?: string;

  @Column({
    type: CHAR(255),
    comment: '静态参数数据',
    allowNull: true,
  })
  static_params?: string;

  @Column({
    type: CHAR(255),
    comment: '动态参数集合',
    allowNull: true,
  })
  dynamic_params?: string;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: '过期时间',
    allowNull: true,
  })
  expiry_time_ms?: number;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: 'mtop超时时间',
    allowNull: true,
  })
  mtop_time_out_ms?: number;

  @Column({
    type: TINYINT,
    comment: '是否依赖定位',
    allowNull: true,
  })
  need_geolocation?: number;

  @Column({
    type: TINYINT,
    comment: '是否获取后删除',
    allowNull: true,
  })
  is_need_get_then_remove?: number;

  @Column({
    type: TINYINT,
    comment: '是否有冷启动场景',
    allowNull: true,
  })
  has_cold_start?: number;

  @Column({
    type: CHAR(255),
    comment: 'hsf id',
    allowNull: true,
  })
  hsf_id?: string;

  @Column({
    type: CHAR(255),
    comment: 'hsf method',
    allowNull: true,
  })
  hsf_method?: string;

  @Column({
    type: CHAR(255),
    comment: 'hsf parameter types',
    allowNull: true,
  })
  hsf_parameter_types?: string;

  @Column({
    type: CHAR(255),
    comment: 'hsf params obj',
    allowNull: true,
  })
  hsf_params_obj?: string;

  @Column({
    type: TINYINT,
    comment: '是否灰度',
    allowNull: true,
  })
  is_gray?: number;

  @Column({
    type: INTEGER,
    comment: '灰度进度',
    allowNull: true,
  })
  gray_number?: number;

  @Column({
    type: STRING(2000),
    comment: '入参处理函数',
    allowNull: true,
  })
  params_handle_func?: string;

  @Column({
    type: STRING(2000),
    comment: '取数据校验参数函数',
    allowNull: true,
  })
  check_params_func?: string;

  @Column({
    type: STRING(2000),
    comment: 'hsf 参数对象',
    allowNull: true,
  })
  hsf_params_mapping?: string;

  @Column({
    type: STRING(2000),
    comment: 'mtop 参数对象',
    allowNull: true,
  })
  mtop_params_mapping?: string;

  @Column({
    type: TINYINT,
    comment: '关闭冷启动预请求',
    allowNull: true,
  })
  not_cold_prefetch?: number;
}
