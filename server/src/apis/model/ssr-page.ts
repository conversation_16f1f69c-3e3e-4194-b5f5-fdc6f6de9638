import { providerWrapper } from '@midwayjs/core';
import {
  Column,
  DataType,
  Model,
  PrimaryKey,
  AutoIncrement,
  Table,
} from 'sequelize-typescript';

const { INTEGER, CHAR, TEXT, DATE, TINYINT } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => SsrPageModel;
providerWrapper([
  {
    id: 'SsrPageModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type ISsrPageModel = typeof SsrPageModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_ssr_page',
})
export class SsrPageModel extends Model<SsrPageModel> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: INTEGER.UNSIGNED,
    comment: '主键',
    allowNull: false,
  })
  id!: number;

  @Column({
    type: DATE,
    comment: '创建时间',
    allowNull: false,
  })
  gmt_create!: Date;

  @Column({
    type: DATE,
    comment: '修改时间',
    allowNull: true,
  })
  gmt_modified?: Date;

  @Column({
    type: INTEGER.UNSIGNED,
    comment: '项目id',
    allowNull: false,
  })
  project_id!: number;

  @Column({
    type: CHAR(255),
    comment: '项目分组',
    allowNull: true,
  })
  project_group?: string;

  @Column({
    type: CHAR(255),
    comment: '项目名称',
    allowNull: true,
  })
  project_name?: string;

  @Column({
    type: CHAR(255),
    comment: '页面名称',
    allowNull: false,
  })
  page_name!: string;
  
  @Column({
    type: CHAR(255),
    comment: '最近操作人',
    allowNull: true,
  })
  last_operator?: string;

  @Column({
    type: CHAR(255),
    comment: '预发版本号',
    allowNull: true,
  })
  pre_version?: string;

  @Column({
    type: CHAR(255),
    comment: '灰度版本号',
    allowNull: true,
  })
  gray_version?: string;

  @Column({
    type: CHAR(255),
    comment: '线上版本号',
    allowNull: true,
  })
  prod_version?: string;

  @Column({
    type: INTEGER.UNSIGNED,
    comment: '灰度比例',
    allowNull: true,
  })
  gray_ratio?: number;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '是否需要登录',
    allowNull: true,
  })
  need_login?: number;

  @Column({
    type: CHAR(255),
    comment: '路径项目名称',
    allowNull: true,
  })
  path_project_name?: string;

  @Column({
    type: CHAR(255),
    comment: '路径页面名称',
    allowNull: true,
  })
  path_page_name?: string;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '是否沉浸式',
    allowNull: true,
  })
  is_immersive?: number;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '预加载配置',
    allowNull: true,
  })
  ssr_preload?: number;

  @Column({
    type: DATE,
    comment: '预加载操作时间',
    allowNull: true,
  })
  gmt_preload?: Date;

  @Column({
    type: CHAR(255),
    comment: '预加载审批单实例ID',
    allowNull: true,
  })
  preload_apply_id?: string;

  @Column({
    type: CHAR(255),
    comment: '预加载任务触发页面SPMB',
    allowNull: true,
  })
  preload_spm?: string;

  @Column({
    type: TEXT,
    comment: '定向灰度用户ID',
    allowNull: true,
  })
  target_user_id?: string;

  @Column({
    type: CHAR(255),
    comment: '定向灰度诸葛ID',
    allowNull: true,
  })
  target_zg_id?: string;

  @Column({
    type: TEXT,
    comment: '流式ER配置，json格式对象',
    allowNull: true,
  })
  stream_config?: string;

  @Column({
    type: TEXT,
    comment: '预发流式ER配置，json对象',
    allowNull: true,
  })
  pre_stream_config?: string;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '是否强制降级',
    allowNull: true,
  })
  enforce_downgrade?: number;

  @Column({
    type: TEXT,
    comment: '报警配置',
    allowNull: true,
  })
  alarm_config?: string;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '是否同域名降级',
    allowNull: true,
  })
  csr_same_site?: number;

  @Column({
    type: TEXT,
    comment: 'cdn缓存配置',
    allowNull: true,
  })
  cdn_config?: string;
}