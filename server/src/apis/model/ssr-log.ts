import { providerWrapper } from '@midwayjs/core';
import {
  Column,
  DataType,
  Model,
  PrimaryKey,
  AutoIncrement,
  Table,
} from 'sequelize-typescript';

const { INTEGER, CHAR, DATE, TINYINT } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => SsrLogModel;
providerWrapper([
  {
    id: 'SsrLogModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type ISsrLogModel = typeof SsrLogModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_ssr_page_publish_log',
})
export class SsrLogModel extends Model<SsrLogModel> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: INTEGER.UNSIGNED,
    comment: '主键',
    allowNull: false,
  })
  id!: number;

  @Column({
    type: DATE,
    comment: '创建时间',
    allowNull: false,
  })
  gmt_create!: Date;

  @Column({
    type: INTEGER.UNSIGNED,
    comment: '页面id',
    allowNull: false,
  })
  page_id!: number;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '发布类型',
    allowNull: true,
  })
  publish_type!: number;

  @Column({
    type: CHAR(255),
    comment: '操作人',
    allowNull: true,
  })
  operator!: string;

  @Column({
    type: INTEGER.UNSIGNED,
    comment: '灰度比例',
    allowNull: true,
  })
  gray_ratio?: number;

  @Column({
    type: CHAR(64),
    comment: '版本号',
    allowNull: true,
  })
  version!: string;
}
