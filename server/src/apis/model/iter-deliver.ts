import { providerWrapper } from '@midwayjs/core';
import {
  AutoIncrement,
  Column,
  DataType,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';

const { BIGINT, CHAR, TEXT, DATE } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => IterDeliverTaskModel;
providerWrapper([
  {
    id: 'IterDeliverTaskModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IIterDeliverTaskModel = typeof IterDeliverTaskModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_iter_deliver_task',
})
export class IterDeliverTaskModel extends Model<IterDeliverTaskModel> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: BIGINT.UNSIGNED,
    comment: '主键,任务id',
    allowNull: false,
  })
  id!: number;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: '迭代分支id',
    allowNull: false,
  })
  iter_id!: number;

  @Column({
    type: CHAR(20),
    comment: '投放端，适用多端单独投放',
    allowNull: true,
  })
  client_name?: string;

  @Column({
    type: CHAR(20),
    comment: '小程序id',
    allowNull: false,
  })
  mini_app_id!: string;

  @Column({
    type: CHAR(20),
    comment: '小程序上传版本',
    allowNull: true,
  })
  mini_app_version?: string;

  @Column({
    type: CHAR(10),
    comment: '上传状态',
    allowNull: false,
  })
  upload_status!: string; // UPLOADING | FAILED | SUCCESS

  @Column({
    type: TEXT,
    comment: '上传日志',
    allowNull: true,
  })
  upload_log?: string;

  @Column({
    type: CHAR(10),
    comment: '创建人工号',
    allowNull: false,
  })
  creator_workid!: string;

  @Column({
    type: DATE,
    comment: '创建时间',
    allowNull: false,
  })
  gmt_create!: Date;

  @Column({
    type: CHAR(10),
    comment: '修改人工号',
    allowNull: true,
  })
  modifier_workid?: string;

  @Column({
    type: DATE,
    comment: '修改时间',
    allowNull: true,
  })
  gmt_modified?: Date;

  @Column({
    type: CHAR(100),
    comment: '投放端列表，适用多端统一投放，多个以逗号隔开',
    allowNull: true,
  })
  client_list?: string;
}
