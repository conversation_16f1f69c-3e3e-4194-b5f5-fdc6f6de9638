/**
 * 迭代-回归测试表
 * 用于迭代集成时发起的整体回归测试
 */
import { providerWrapper } from '@midwayjs/core';
import {
  AutoIncrement,
  Column,
  DataType,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';

const { BIGINT, CHAR, TEXT, DATE } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => IterQaReviewModel;
providerWrapper([
  {
    id: 'IterQaReviewModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IIterQaReviewModel = typeof IterQaReviewModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_iter_qa_review',
})
export class IterQaReviewModel extends Model<IterQaReviewModel> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: BIGINT.UNSIGNED,
    comment: '主键',
    allowNull: false,
  })
  id!: number;

  @Column({
    type: DATE,
    comment: '创建时间',
    allowNull: false,
  })
  gmt_create!: Date;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: '迭代分支id',
    allowNull: false,
  })
  iter_id!: number;

  @Column({
    type: CHAR(20),
    comment: '行业',
    allowNull: false,
  })
  biz_name!: string;

  @Column({
    type: TEXT,
    comment: '回归结论，json格式的数组',
    allowNull: false,
  })
  review_res_list!: string;

  @Column({
    type: TEXT,
    comment: '操作历史，json格式的数组',
    allowNull: true,
  })
  action_history?: string;
}
