import { providerWrapper } from '@midwayjs/core';
import {
  Column,
  DataType,
  Table,
  Model,
  PrimaryKey,
  AutoIncrement
} from 'sequelize-typescript';

const { BIGINT, TINYINT, CHAR, TEXT, DATE } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => ComponentDeliverDetailModel;
providerWrapper([
  {
    id: 'ComponentDeliverDetailModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IComponentDeliverDetailModel = typeof ComponentDeliverDetailModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_branch_deliver_detail',
})
export class ComponentDeliverDetailModel extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: BIGINT.UNSIGNED,
    comment: '主键，投放id',
    allowNull: false,
  })
  deliver_id!: number;

  @Column({
    type: TINYINT,
    comment: '投放类型（1：h5、2：weixin、3：alipay、4：bytedance）',
    allowNull: true,
  })
  deliver_type?: number;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: '迭代id（如果是开发分支类型，这里指被挂载到的迭代，如果是迭代类型，就是迭代本身）',
    allowNull: true,
  })
  iter_id?: number;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: '开发分支id（包含游离开发分支id、开发分支id）',
    allowNull: true,
  })
  dev_id?: number;

  @Column({
    type: TINYINT,
    comment: '分支类型（1表示游离开发分支，2表示开发分支，3表示迭代）',
    allowNull: true,
  })
  branch_type?: number;

  @Column({
    type: CHAR(50),
    comment: '投放应用名称',
    allowNull: true,
  })
  project_name?: string;

  @Column({
    type: CHAR(30),
    comment: '投放应用分支版本',
    allowNull: true,
  })
  project_version?: string;

  @Column({
    type: TEXT,
    comment: '需要更新的npm包，json格式的数组',
    allowNull: true,
  })
  npm_list?: string;

  @Column({
    type: CHAR(20),
    comment: '创建人',
    allowNull: false,
  })
  creator!: string;

  @Column({
    type: DATE,
    comment: '创建时间',
    allowNull: false,
  })
  gmt_create!: Date;

  @Column({
    type: CHAR(20),
    comment: '修改人',
    allowNull: true,
  })
  modifier?: string;

  @Column({
    type: DATE,
    comment: '修改时间',
    allowNull: true,
  })
  gmt_modified?: Date;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: 'def迭代id',
    allowNull: true,
  })
  def_iter_id?: number;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: 'def构建任务id',
    allowNull: true,
  })
  def_task_id?: number;

  @Column({
    type: TINYINT,
    comment: 'def构建状态（1：未构建、2：构建中、3：构建成功、4：构建失败）',
    allowNull: true,
  })
  def_build_status?: number;

  @Column({
    type: BIGINT.UNSIGNED,
    comment: 'def构建分支的branchId（用于绑定）',
    allowNull: true,
  })
  def_branch_id?: number;

  @Column({
    type: TINYINT,
    comment: 'def构建类型（1：预发、2：线上）',
    allowNull: true,
  })
  def_env_type?: number;

  @Column({
    type: TINYINT,
    comment: '投放的小程序信息（包含branchName、projectName、devId、status）',
    allowNull: true,
  })
  miniapp_info?: string;
}
