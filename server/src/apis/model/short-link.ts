import { providerWrapper } from '@midwayjs/core';
import {
  Column,
  DataType,
  Model,
  PrimaryKey,
  AutoIncrement,
  Table,
} from 'sequelize-typescript';

const { INTEGER, TEXT } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => ShortLinkModel;
providerWrapper([
  {
    id: 'ShortLinkModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IShortLinkModel = typeof ShortLinkModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'short_link',
})
export class ShortLinkModel extends Model<ShortLinkModel> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: INTEGER.UNSIGNED,
    comment: '主键',
    allowNull: false,
  })
  id!: number;

  @Column({
    type: TEXT,
    comment: '长链接',
    allowNull: false,
  })
  long_link!: string;

  @Column({
    type: TEXT,
    comment: '短链key',
    allowNull: false,
  })
  short_key!: string;
}
