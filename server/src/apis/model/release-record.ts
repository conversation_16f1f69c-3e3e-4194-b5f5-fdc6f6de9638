import { providerWrapper } from '@midwayjs/core';
import {
  Column,
  DataType,
  Model,
  PrimaryKey,
  AutoIncrement,
  Table,
} from 'sequelize-typescript';

const { INTEGER, TEXT, CHAR, DATE, TINYINT } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => ReleaseRecordModel;
providerWrapper([
  {
    id: 'ReleaseRecordModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IReleaseRecordModel = typeof ReleaseRecordModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'release-record',
})
export class ReleaseRecordModel extends Model<ReleaseRecordModel> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: INTEGER.UNSIGNED,
    comment: '主键',
    allowNull: false,
  })
  id!: number;

  @Column({
    type: DATE,
    comment: '创建时间',
    allowNull: false,
  })
  gmt_create!: Date;

  @Column({
    type: DATE,
    comment: '修改时间',
    allowNull: true,
  })
  gmt_modified?: Date;

  @Column({
    type: TINYINT.UNSIGNED,
    comment: '是否已同步过记录(0 - 未同步,1 - 已同步)',
    allowNull: false,
  })
  status!: number;

  @Column({
    type: CHAR(255),
    comment: 'diamond要更新的key值(只更新route-map/render-html)',
    allowNull: false,
  })
  diamond_key!: string;

  @Column({
    type: TEXT,
    comment: 'diamond要更新的value值(只更新route-map/render-html)',
    allowNull: false,
  })
  diamond_value!: string;

  @Column({
    type: CHAR(255),
    comment: 'diamond的更新环境',
    allowNull: false,
  })
  diamond_env!: string;

  @Column({
    type: TEXT,
    comment: '扩展字段',
  })
  ext!: string;

}
