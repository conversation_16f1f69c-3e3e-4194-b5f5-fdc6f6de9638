import { providerWrapper } from '@midwayjs/core';
import {
  Column,
  DataType,
  Model,
  PrimaryKey,
  AutoIncrement,
  Table,
} from 'sequelize-typescript';

const { INTEGER, CHAR, TEXT } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => UserModel;
providerWrapper([
  {
    id: 'UserModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IUserModel = typeof UserModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_user',
})
export class UserModel extends Model<UserModel> {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: INTEGER.UNSIGNED,
    comment: '主键',
    allowNull: false,
  })
  id!: number;

  @Column({
    type: CHAR(20),
    comment: '用户类型（admin、developer、qa）',
    allowNull: false,
  })
  user_type!: string;

  @Column({
    type: CHAR(20),
    comment: '用户id',
    allowNull: false,
  })
  userid!: string;

  @Column({
    type: CHAR(20),
    comment: '工号',
    allowNull: false,
  })
  workid!: string;

  @Column({
    type: CHAR(20),
    comment: '花名',
    allowNull: false,
  })
  name!: string;

  @Column({
    type: CHAR(20),
    comment: '真名',
    allowNull: false,
  })
  last_name!: string;

  @Column({
    type: TEXT,
    comment: '头像',
    allowNull: false,
  })
  avatar_url!: string;
}
