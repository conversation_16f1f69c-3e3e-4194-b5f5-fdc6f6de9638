import { providerWrapper } from '@midwayjs/core';
import {
  AutoIncrement,
  Column,
  DataType,
  Model,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { IWsyConfig, IClient, IDingtalkRobot } from '../interface/project';

const { INTEGER, CHAR, TEXT, STRING } = DataType;

// 提供model出去最好的办法是用factory方式，sequelize在model里面是static，如果需要使用 @provide，需要在类初始化的时候注入
export const factory = () => ProjectModel;
providerWrapper([
  {
    id: 'ProjectModel',
    provider: factory,
  },
]);

// 确保导出了model类，同时对外类型是安全的
export type IProjectModel = typeof ProjectModel;

@Table({
  freezeTableName: true,
  createdAt: false,
  updatedAt: false,
  tableName: 'mw_project',
})
export class ProjectModel extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: INTEGER.UNSIGNED,
    comment: '主键，项目id',
    allowNull: false,
  })
  id!: number;

  @Column({
    type: CHAR(30),
    comment: '项目名称',
    allowNull: false,
  })
  name!: string;

  @Column({
    type: CHAR(30),
    comment: '项目中文名',
    allowNull: false,
    field: 'cn_name',
  })
  cnName!: string;

  @Column({
    type: STRING(200),
    comment: '项目图标',
    allowNull: false,
  })
  icon!: string;

  @Column({
    type: TEXT,
    comment: '代码仓库',
    allowNull: false,
    field: 'git_repo',
  })
  gitRepo!: string;

  @Column({
    type: TEXT,
    comment: '投放端，json格式的数组', // [{ clientName, miniAppId }]
    allowNull: false,
    field: 'client_list',
    get() {
      const rawValue: string = this.getDataValue('clientList');
      return rawValue ? JSON.parse(rawValue) : [];
    },
    set(value: IClient[]) {
      if (value) {
        this.setDataValue('clientList', JSON.stringify(value));
      }
    }
  })
  clientList!: IClient[];

  @Column({
    type: TEXT,
    comment: '钉钉机器人配置，json格式的数组', // [{ signCode, robotHook }]
    allowNull: true,
    field: 'dingtalk_robots',
    get() {
      const rawValue: string = this.getDataValue('dingtalkRobots');
      return rawValue ? JSON.parse(rawValue) : [];
    },
    set(value: IDingtalkRobot[]) {
      if (value) {
        this.setDataValue('dingtalkRobots', JSON.stringify(value));
      }
    }
  })
  dingtalkRobots: IDingtalkRobot[];

  @Column({
    type: TEXT,
    comment: '发布规则，json格式的数组', // [{ type, value }]
    allowNull: true,
    field: 'publish_rules',
  })
  publishRules?: string;

  @Column({
    type: TEXT,
    comment: '投放H5/小程序/插件的配置信息', // { projectName, moduleName, gitRepo, gitProjectId, defProjectId, deliverList }
    allowNull: true,
    field: 'deliver_config',
    get() {
      const rawValue: string = this.getDataValue('deliverConfig');
      
      if (rawValue) {
        let res = JSON.parse(rawValue);
        if (typeof res === 'string') { // 兼容老数据
          res = JSON.parse(res);
        }
        return res;
      }
      
      return {};
    },
    set(value: string) {
      if (value) {
        let storedValue = value;
        if (typeof storedValue !== 'string') { // 兼容老数据
          storedValue = JSON.stringify(storedValue);
        }
        this.setDataValue('deliverConfig', storedValue);
      }
    }
  })
  deliverConfig: string;

  @Column({
    type: CHAR(50),
    comment: '项目类型',
    allowNull: false,
  })
  type: string;

  @Column({
    type: STRING(200),
    comment: '项目管理员，存的是工号，多个以逗号隔开',
    allowNull: true,
    field: 'admin_workids',
    get() {
      const rawValue: string = this.getDataValue('adminWorkidList');
      return rawValue?.split(',') || [];
    },
    set(value: string[]) {
      if (value) {
        this.setDataValue('adminWorkidList', value.join(','));
      }
    }
  })
  adminWorkidList: string[];

  @Column({
    type: TEXT,
    comment: '打码配置，json格式的对象', // { scene, accessKey, platform }
    allowNull: true,
    field: 'wsy_config',
    get() {
      const rawValue: string = this.getDataValue('wsyConfig');
      return rawValue ? JSON.parse(rawValue) : undefined;
    },
    set(value: IWsyConfig) {
      if (value) {
        this.setDataValue('wsyConfig', JSON.stringify(value));
      }
    }
  })
  wsyConfig?: IWsyConfig;
}