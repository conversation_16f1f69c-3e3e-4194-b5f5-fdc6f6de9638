import { Sequelize } from 'sequelize-typescript';
import { DevBranchDetailModel } from './dev-branch-detail';
import { FreeDevBranchDetailModel } from './free-dev-branch-detail';
import { IterBranchDetailModel } from './iter-branch-detail';
import { IterCalendarModel } from './iter-calendar';
import { IterDeliverTaskModel } from './iter-deliver';
import { ComponentDeliverDetailModel } from './component-deliver-detail';
import { MiniAppVersionInfoModel } from './miniapp-version-info';
import { ProjectModel } from './project';
import { UserModel } from './user';
import { ShortLinkModel } from './short-link';
import { PrefetchConfigModel } from './prefetch-config';
import { SsrProjectModel } from './ssr-project';
import { SsrPageModel } from './ssr-page';
import { SsrLogModel } from './ssr-log';
import { ReleaseRecordModel } from './release-record';
import { IterQaReviewModel } from '@/apis/model/iter-qa-review';
import { IterExperienceModel } from '@/apis/model/iter-experience';
import 'mysql2';

interface ISequelizeConfig {
  host?: string;
  port?: string;
  username?: string;
  password?: string;
  database?: string;
  dialect?: string;
}

export class DB {
  public static sequelize: Sequelize;
  public static async init(config: ISequelizeConfig) {
    // @ts-ignore
    DB.sequelize = new Sequelize({
      ...config,
      charset: 'utf8',
      logging: false,
      timezone: '+08:00',
    });
    // add models here before using them
    DB.sequelize.addModels([
      DevBranchDetailModel,
      IterBranchDetailModel,
      IterCalendarModel,
      IterDeliverTaskModel,
      MiniAppVersionInfoModel,
      ProjectModel,
      UserModel,
      PrefetchConfigModel,
      SsrProjectModel,
      SsrPageModel,
      SsrLogModel,
      ReleaseRecordModel,
      ShortLinkModel,
      FreeDevBranchDetailModel,
      ComponentDeliverDetailModel,
      IterQaReviewModel,
      IterExperienceModel,
    ]);
    try {
      await DB.sequelize.authenticate();
      // await DB.sequelize.sync({ alter: true });
      console.log('>>>>>>>数据库连接成功(无 sync)');
    } catch (error: any) {
      error.message = `DB connection error: ${error.message}`;
      throw error;
    }
  }
}
