
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import HSFService from './index';

@Provide('crmHSF')
export default class CrmHSF extends HSFService {
  @Inject()
  ctx!: Context;

  /**
   * 钉钉单聊发送通知
   * @param users 工号list，逗号分隔
   * @param title 标题
   * @param content 内容
   * @returns
   */
  async sendDingTalkMessage(users: string, title: string, content: string) {
    const res = await this.invoke({
      id: 'com.fliggy.crm.client.service.DingTalkService:1.0.0',
      method: 'sendMessage',
      parameterTypes: ['java.lang.String', 'java.lang.String', 'java.lang.String'],
      group: 'HSF',
      args: [users, title, content]
    });

    return res;
  }
}