import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';


@Provide('fLianHSF')
export default class fLianHSF {
  @Inject()
  ctx!: Context;

  /** 生成淘口令 */
  async generateTbSharePassword(data: any) {
    const { link = '', title = '', image = '' } = data;
    try {
      const result: any = await this.ctx.hsfClient.invoke({
        id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
        group: 'HSF',
        method: 'invoke',
        args: [
          "205569296758602178",
          "fl-sht-server",
          "materielHandler-FZGetTaoPassword",
          {
            link,
            title,
            image
          },
        ],
        parameterTypes: ['java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object'],
      });

      if (result) {
        return result;
      } else {
        return null;
      }
    } catch (err: any) {
      return null;
    }
  }
}
