
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import HSFService from './index';

@Provide('shortLinkHSF')
export default class ShortLinkHSF extends HSFService {
  @Inject()
  ctx!: Context;

  /**
   * 生成短链
   * @param longLink 长链
   * @param needForever 是否持久存储
   * @returns 
   */
  async createFurl(longLink: string, needForever?: boolean) {
    const res = await this.invoke({
      id: 'com.alibaba.fliggy.furl.service.FurlService:1.0.0',
      method: 'createFurl',
      parameterTypes: ['com.alibaba.fliggy.furl.model.FurlCreateModel'],
      args: [{ longLink, needForever }]
    });

    if (!res?.data?.shortKey) {
      throw Error('FurlService:createFurl failed');
    }

    return res.data as IShortLinkCreateRes;
  }

  /**
   * 查询短链信息
   * @param shortPath 短链path，类似1d3nL6
   * @returns 
   */
  async queryFurl(shortPath: string) {
    const res = await this.invoke({
      id: 'com.alibaba.fliggy.furl.service.FurlService:1.0.0',
      method: 'queryFurl',
      parameterTypes: ['com.alibaba.fliggy.furl.model.FurlVisitModel'],
      args: [{ shortPath }]
    });

    if (!res?.data?.longLink) {
      throw Error(`FurlService:queryFurl by ${shortPath} failed`);
    }

    return res.data as IShortLinkInfoQueryRes;
  }
}

export interface IShortLinkCreateRes {
  /** 短链，如https://a.feizhu.com/3aIwXF */
  shortKey: string;
  /** 短链path，如3aIwXF */
  shortPath: string;
}

export interface IShortLinkInfoQueryRes {
  /** 短链 */
  shortKey: string;
  /** 长链 */
  longLink: string;
}