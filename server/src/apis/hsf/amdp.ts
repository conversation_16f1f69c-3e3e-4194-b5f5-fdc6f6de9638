
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { APP_NAME } from '@/apis/const';
import HSFService from './index';

@Provide('amdpHSF')
export default class AmdpHSF extends HSFService {
  @Inject()
  ctx!: Context;

  /** amdp 信息聚合接口 */
  async amdpQuerySimpleData(athParam: string, DataQueryParam: string) {
    const res = await this.invoke({
      useCustomClient: true,
      requestProps: {
        'Consumer-AppName': APP_NAME,
      },
      dailySuffix: '.DAILY',
      id: 'com.alibaba.ihr.amdplatform.service.api.AmdpDataQueryService:1.0.0',
      method: 'querySimpleData',
      parameterTypes: ['java.lang.String', 'java.lang.String'],
      args: [athParam, DataQueryParam]
    });

    return res;
  }
}