
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { BIZ_LINE } from '@/apis/const';
import { IDevBranch } from '@/apis/interface/dev-branch';
import { IFreeDevBranch } from '@/apis/interface/free-dev-branch';
import { IIterBranch } from '@/apis/interface/iter-branch';
import * as chalk from 'chalk';
import HSFService from './index';

@Provide('towerHSF')
export default class TowerHSF extends HSFService {
  @Inject()
  ctx!: Context;

  /**
   * 查询自动化测试结果
   * @param taskId 任务id
   * @returns
   */
  async getTaskResult(taskId: number): Promise<any> {
    return this.invoke({
      id: 'com.taobao.triptower.service.miniApp.MiniAppTaskService:1.0.0',
      method: 'getTaskResult',
      parameterTypes: ['java.lang.Long'],
      group: 'HSF',
      args: [taskId]
    });
  }

  /**
   * 创建塔台任务
   * @param params 创建参数
   * @returns
   */
  async createTowerTask(params: ICreateTowerTaskParams): Promise<string> {
    this.ctx.logger.info(chalk.green('创建塔台任务 >>> '), params)

    const res = await this.invoke({
      id: 'com.taobao.triptower.service.miniApp.MiniAppTaskService:1.0.0',
      method: 'createMiniAppTask',
      parameterTypes: ['com.taobao.triptower.domain.miniApp.MiniAppTaskVO'],
      group: 'HSF',
      args: [params]
    }) as ICreateTowerTaskReturn;

    if (res.data) {
      return res.data;
    } else {
      this.ctx.logger.error(res.errorMsg || '创建塔台任务失败')
      return '';
    }
  }

  /**
   * 发送通知回归测试邮件
   * @param params
   * @returns
   */
  async sendMiniAppEmail(qrCodeLink: string, devBranchList: (IDevBranch | IFreeDevBranch)[], iterBranch: IIterBranch): Promise<any> {
    this.ctx.logger.info(chalk.green(`发送通知回归测试邮件 >>> iterId:${iterBranch.iterId}, qrCodeLink:${qrCodeLink}`));

    return this.invoke({
      id: 'com.taobao.triptower.service.miniApp.MiniAppTaskService:1.0.0',
      method: 'sendMiniAppEmail',
      parameterTypes: ['java.lang.String', 'java.util.List', 'com.taobao.triptower.domain.miniApp.IterBranch'],
      group: 'HSF',
      args: [qrCodeLink, devBranchList, iterBranch]
    });
  }

  /**
   * 查询回归报告数据统计
   * @returns
   */
  async getTotalTaskCount(startTime: string, endTime: string): Promise<any> {
    // return {
    //   "data": [
    //     {
    //       "taskType": 1,
    //       "taskTypeDesc": "自动化UI检查",
    //       "totalMainCount": null,
    //       "checkSuccCount": 0,
    //       "taskSuccCount": 12,
    //       "totalCount": 36,
    //       "class": "com.taobao.triptower.domain.miniApp.MiniAppCheckResultVO"
    //     },
    //     {
    //       "taskType": 4,
    //       "taskTypeDesc": "包大小检查",
    //       "totalMainCount": null,
    //       "checkSuccCount": 0,
    //       "taskSuccCount": 12,
    //       "totalCount": 12,
    //       "class": "com.taobao.triptower.domain.miniApp.MiniAppCheckResultVO"
    //     },
    //     {
    //       "taskType": 0,
    //       "taskTypeDesc": "总任务",
    //       "totalMainCount": 12,
    //       "checkSuccCount": 0,
    //       "taskSuccCount": 24,
    //       "totalCount": 48,
    //       "class": "com.taobao.triptower.domain.miniApp.MiniAppCheckResultVO"
    //     }
    //   ],
    //   "success": true,
    //   "errorCode": null,
    //   "class": "com.taobao.triptower.domain.common.CommonResult",
    //   "errorMsg": null
    // };

    return this.invoke({
      id: 'com.taobao.triptower.service.miniApp.MiniAppTaskService:1.0.0',
      method: 'getTotalTaskResult',
      parameterTypes: ['java.lang.String', 'java.lang.String'],
      group: 'HSF',
      args: [startTime, endTime]
    });
  }

  /**
   * 查询回归报告主任务list
   * @param pageSize 分页size，pageNum 分页
   * @returns
   */
  async getMainTaskDetail(startTime: string, endTime: string): Promise<any> {
    // return {
    //   "data": {
    //     "dataList": [
    //       {
    //         "checkType": null,
    //         "gmtModified": 1644301354000,
    //         "batchNo": "202202081422173",
    //         "mainTaskId": null,
    //         "ufoTaskId": null,
    //         "branchName": "rc/3.2.4",
    //         "gmtCreate": 1644301354000,
    //         "checkResult": 0,
    //         "resultInfo": null,
    //         "url": "https://img.alicdn.com/imgextra/i2/O1CN01cOVUPM1b2S8NPjrXW_!!6000000003407-0-tps-180-190.jpg",
    //         "appType": "WX",
    //         "id": 277,
    //         "alarmReceiver": "[{\"name\":\"兰湛\"}]",
    //         "businessType": "all",
    //         "class": "com.taobao.triptower.domain.miniApp.MiniAutotestCheckResultDO",
    //         "isMainTask": 1,
    //         "taskStatus": 3
    //       },
    //       {
    //         "checkType": null,
    //         "gmtModified": 1644301351000,
    //         "batchNo": "20220208142223",
    //         "mainTaskId": null,
    //         "ufoTaskId": null,
    //         "branchName": "rc/3.2.4",
    //         "gmtCreate": 1644301351000,
    //         "checkResult": 0,
    //         "resultInfo": null,
    //         "url": "https://img.alicdn.com/imgextra/i2/O1CN01cOVUPM1b2S8NPjrXW_!!6000000003407-0-tps-180-190.jpg",
    //         "appType": "WX",
    //         "id": 272,
    //         "alarmReceiver": "[{\"name\":\"兰湛\"}]",
    //         "businessType": "all",
    //         "class": "com.taobao.triptower.domain.miniApp.MiniAutotestCheckResultDO",
    //         "isMainTask": 1,
    //         "taskStatus": 3
    //       },
    //       {
    //         "checkType": null,
    //         "gmtModified": 1644292494000,
    //         "batchNo": "202202081154413",
    //         "mainTaskId": null,
    //         "ufoTaskId": null,
    //         "branchName": "rc/3.2.4",
    //         "gmtCreate": 1644292494000,
    //         "checkResult": 0,
    //         "resultInfo": null,
    //         "url": "https://img.alicdn.com/imgextra/i2/O1CN01cOVUPM1b2S8NPjrXW_!!6000000003407-0-tps-180-190.jpg",
    //         "appType": "WX",
    //         "id": 267,
    //         "alarmReceiver": "[{\"name\":\"兰湛\"}]",
    //         "businessType": "all",
    //         "class": "com.taobao.triptower.domain.miniApp.MiniAutotestCheckResultDO",
    //         "isMainTask": 1,
    //         "taskStatus": 3
    //       },
    //       {
    //         "checkType": null,
    //         "gmtModified": 1644292492000,
    //         "batchNo": "202202081154771",
    //         "mainTaskId": null,
    //         "ufoTaskId": null,
    //         "branchName": "rc/3.2.4",
    //         "gmtCreate": 1644292491000,
    //         "checkResult": 0,
    //         "resultInfo": null,
    //         "url": "https://img.alicdn.com/imgextra/i2/O1CN01cOVUPM1b2S8NPjrXW_!!6000000003407-0-tps-180-190.jpg",
    //         "appType": "WX",
    //         "id": 262,
    //         "alarmReceiver": "[{\"name\":\"兰湛\"}]",
    //         "businessType": "all",
    //         "class": "com.taobao.triptower.domain.miniApp.MiniAutotestCheckResultDO",
    //         "isMainTask": 1,
    //         "taskStatus": 3
    //       },
    //       {
    //         "checkType": null,
    //         "gmtModified": 1644289392000,
    //         "batchNo": "202202081103516",
    //         "mainTaskId": null,
    //         "ufoTaskId": null,
    //         "branchName": "rc/3.2.4",
    //         "gmtCreate": 1644289392000,
    //         "checkResult": 0,
    //         "resultInfo": null,
    //         "url": "https://img.alicdn.com/imgextra/i2/O1CN01cOVUPM1b2S8NPjrXW_!!6000000003407-0-tps-180-190.jpg",
    //         "appType": "WX",
    //         "id": 257,
    //         "alarmReceiver": "[{\"name\":\"兰湛\"}]",
    //         "businessType": "all",
    //         "class": "com.taobao.triptower.domain.miniApp.MiniAutotestCheckResultDO",
    //         "isMainTask": 1,
    //         "taskStatus": 3
    //       }
    //     ],
    //     "totalCount": 83
    //   },
    //   "success": true,
    //   "errorCode": null,
    //   "class": "com.taobao.triptower.domain.common.CommonResult",
    //   "errorMsg": null
    // };

    return this.invoke({
      id: 'com.taobao.triptower.service.miniApp.MiniAppTaskService:1.0.0',
      method: 'getMainTaskDetail',
      parameterTypes: ['java.lang.String', 'java.lang.String'],
      group: 'HSF',
      args: [startTime, endTime]
    });
  }
}

interface ICreateTowerTaskParams {
  /** 端类型：AP - 支付宝；WX - 微信 */
  appType: string;
  /** 创建人 */
  creator: string;
  /** 分支名 */
  branchName: string;
  /** 测试负责人 */
  testManager: string;
  /** 行业线 */
  businessLine: BIZ_LINE | 'all';
  /** 二维码 */
  url: string;
  /** 二维码内容 */
  miniAppPageUrl: string;
  /** 包大小分析结果 */
  packageResult: string;
}

interface ICreateTowerTaskReturn {
  /** 是否创建成功 */
  success: boolean;
  /** taskId */
  data: string;
  /** 错误码 */
  errorCode?: string;
  /** 错误信息 */
  errorMsg?: string;
}
