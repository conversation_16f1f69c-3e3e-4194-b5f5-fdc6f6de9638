
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import HSFService from './index';

const token = '420304ca9ef0d959';

@Provide('mtopHSF')
export default class MtopHSF extends HSFService {
  @Inject()
  ctx!: Context;

  /**
   * 获取mtop详细信息（max提供：https://yuque.antfin-inc.com/mtop/doc/interface-expose）
   * @param api mtop api
   * @param version mtop version
   * @returns
   */
  async getMtopApiData(api: string, version: string) {
    const res = await this.invoke({
      id: 'com.taobao.wireless.yogurt.mtop.service.inner.MtopApiQueryService:1.0',
      method: 'apiData',
      parameterTypes: ['com.taobao.wireless.yogurt.biz.mtop.api.domain.ApiName', 'com.taobao.wireless.yogurt.mtop.service.inner.Authorization'],
      group: 'HSF',
      args: [{
        v: version,
        api
      }, {
        id: token,
        schema: "TOKEN"
      }]
    });

    return res;
  }
}