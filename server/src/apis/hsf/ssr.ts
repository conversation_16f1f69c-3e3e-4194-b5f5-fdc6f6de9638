
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import HSFService from './index';

@Provide('ssrHSF')
export default class SsrHSF extends HSFService {
  @Inject()
  ctx!: Context;


  /** arms性能总览查询接口 */
  async armsPerfData(from, to, pid, projectGroup, projectName, pageName, projectBusiness) {
    // 下单页由于走的buyphoenix,入参的pagePath需要更改
    const pagePathMap = {
      "rx-vehicle-buy": {
        "confirm": "rx-vehicle-buy-ssr"
      }
    }
    const pagePath = pagePathMap[projectName]?.[pageName] || `${projectBusiness === 'btrip' ? 'er.m.alibtrip.com' : 'outfliggys.m.taobao.com'}/app/${projectGroup || 'trip'}/${projectName}/pages/${pageName}`;
    const page = encodeURIComponent(pagePath);
    const sqlStr = `* and  t : perf | select 
    detector_app, 
    round(sum(less_then_1000) * 1.00 / (sum(less_then_1000) + sum(greater_then_1000)) * 100, 2) as one_pass_rate, 
    round(avg(t2), 2) as avg_t2, 
    COUNT(*) as count,
    round(sum(pressr_less_1000) * 1.00 / (sum(is_pressr)) * 100, 2) as pressr_one_pass_rate,
    round(avg(if(is_pressr=1, t2, null)), 2) as pressr_avg_t2,
    sum(is_pressr) as pressr_count,
    round(sum(cache_less_1000) * 1.00 / (sum(is_cache)) * 100, 2) as cache_one_pass_rate,
    round(avg(if(is_cache=1, t2, null)), 2) as cache_avg_t2,
    sum(is_cache) as cache_count,
    round(sum(static_less_1000) * 1.00 / (sum(is_static)) * 100, 2) as static_one_pass_rate,
    round(avg(if(is_static=1, t2, null)), 2) as static_avg_t2,
    sum(is_static) as static_count 
    from (
      select t2, 
      c3, 
      detector_app, 
      case when t2 <= 1000 then 1 else 0 end less_then_1000, 
      case when t2 > 1000 then 1 else 0 end greater_then_1000,
      case when (c3 LIKE '%pressr%' and t2 <= 1000) then 1 else 0 end pressr_less_1000,
      case when (c3 LIKE '%pressr%') then 1 else 0 end is_pressr,
      case when (c3 LIKE '%cache%' and t2 <= 1000) then 1 else 0 end cache_less_1000,
      case when (c3 LIKE '%cache%') then 1 else 0 end is_cache,
      case when (c3 LIKE '%static%' and t2 <= 1000) then 1 else 0 end static_less_1000,
      case when (c3 LIKE '%static%') then 1 else 0 end is_static
      from (
        select t2, 
        c3, 
        page, 
        detector_app
        from log where page LIKE '%${page}%' and (detector_os = 'iOS' or detector_os = 'Android') and t2 < 20000
      )
    ) group by detector_app`;
    const res = await this.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      method: 'invoke',
      group: 'HSF',
      parameterTypes: ['java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String'],
      args: [
        '204323666841037463',
        'fl-page-back',
        'hsfHandler-getSls',
        {
          query: sqlStr,
          startTime: `${from}`,
          endTime: `${to}`,
          pid,
          page: pid ? '' : pagePath
        },
        'hsfHandler-getSls__stable'
      ]
    })
    return res;
  }

  /** arms性能详情查询接口 */
  async armsPerfDetailData(pid, projectGroup, projectName, pageName, projectBusiness, target) {
    const pagePath = `${projectBusiness === 'btrip' ? 'er.m.alibtrip.com' : 'outfliggys.m.taobao.com'}/app/${projectGroup || 'trip'}/${projectName}/pages/${pageName}`;
    const page = encodeURIComponent(pagePath);
    let appSql = '';
    if (target === 'other') {
      appSql = `and detector_app != 'lx' and detector_app != 'tb' and detector_app != 'ap'`
    } else if (target !== 'total') {
      appSql = `and detector_app = '${target}'`
    }
    const sqlStr = `* and  t : perf | select 
    date_trunc('day', __time__) as time, 
    round(sum(less_then_1000) * 1.00 / (sum(less_then_1000) + sum(greater_then_1000)) * 100, 2) as one_pass_rate, 
    round(avg(t2), 2) as avg_t2, 
    round(sum(pressr_less_1000) * 1.00 / (sum(is_pressr)) * 100, 2) as pressr_one_pass_rate,
    round(avg(if(is_pressr=1, t2, null)), 2) as pressr_avg_t2,
    round(sum(is_pressr) * 1.00 / (sum(less_then_1000) + sum(greater_then_1000)) * 100, 2) as pressr_rate,
    round(sum(cache_less_1000) * 1.00 / (sum(is_cache)) * 100, 2) as cache_one_pass_rate,
    round(avg(if(is_cache=1, t2, null)), 2) as cache_avg_t2,
    round(sum(is_cache) * 1.00 / (sum(less_then_1000) + sum(greater_then_1000)) * 100, 2) as cache_rate,
    round(sum(static_less_1000) * 1.00 / (sum(is_static)) * 100, 2) as static_one_pass_rate,
    round(avg(if(is_static=1, t2, null)), 2) as static_avg_t2,
    round(sum(is_static) * 1.00 / (sum(less_then_1000) + sum(greater_then_1000)) * 100, 2) as static_rate
    from (
      select t2, 
      c3, 
      __time__, 
      case when t2 <= 1000 then 1 else 0 end less_then_1000, 
      case when t2 > 1000 then 1 else 0 end greater_then_1000, 
      case when (c3 LIKE '%pressr%' and t2 <= 1000) then 1 else 0 end pressr_less_1000, 
      case when (c3 LIKE '%pressr%') then 1 else 0 end is_pressr,
      case when (c3 LIKE '%cache%' and t2 <= 1000) then 1 else 0 end cache_less_1000,
      case when (c3 LIKE '%cache%') then 1 else 0 end is_cache,
      case when (c3 LIKE '%static%' and t2 <= 1000) then 1 else 0 end static_less_1000,
      case when (c3 LIKE '%static%') then 1 else 0 end is_static
      from (
        select t2, 
        c3, 
        __time__, 
        page from log where page LIKE '%${page}%' and t2 < 20000 ${appSql}
      )
    ) group by time`
    const res = await this.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      method: 'invoke',
      group: 'HSF',
      responseTimeout: 1000000,
      parameterTypes: ['java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String'],
      args: [
        '204323666841037463',
        'fl-page-back',
        'hsfHandler-getSls',
        {
          query: sqlStr,
          startTime: `${(new Date()).valueOf() - 15 * 86400000}`,
          endTime: `${(new Date()).valueOf()}`,
          pid,
          page: pid ? '' : pagePath
        },
        'hsfHandler-getSls__stable'
      ]
    })
    return res;
  }

  /** 根据排期ID获取泰坦数据 */
  async getPlanDetailById(id): Promise<any> {
    return this.invoke({
      id: 'com.fliggy.fceadmin.pegasus.FcePlanService:1.0.0',
      method: 'getPlanDetailById',
      parameterTypes: ['java.lang.Long'],
      args: [id]
    });
  }

  /**
   * 发布资源位
   * @param sceneId
   * @param content
   */
  async savePlanBySceneId(sceneId: number, content: any): Promise<any> {
    return this.invoke({
      id: 'com.fliggy.fceadmin.pegasus.FcePlanService:1.0.0',
      method: 'saveAndPublishPlan',
      parameterTypes: ['java.lang.Long', 'com.fliggy.fceadmin.pegasus.domain.PlanParamDTO', 'java.lang.String'],
      args: [sceneId, content, '紫期']
    });
  }

  /**
   * 更新定投规则
   * @param ruleId
   * @param ruleName
   * @param content
   */
  async updatePlanRule(ruleId: number, ruleName: string, content: string): Promise<any> {
    return this.invoke({
      id: 'com.fliggy.fceadmin.pegasus.FcePlanRuleService:1.0.0',
      method: 'updatePlanRule',
      parameterTypes: ['java.lang.Long', 'java.lang.Long', 'java.lang.String', 'java.lang.String'],
      args: [205521, ruleId, ruleName, content]
    });
  }

  /**
   * 修改orange数据
   * @param resIdList
   * @param ownerMap
   */
  async orangeFunc(SinglePublishParam: any, OrangeClientInfo: any): Promise<any> {
    return this.invoke({
      id: 'com.taobao.wireless.orange.console.manager.api.service.PublishApiService:1.0.0',
      method: 'publishNamespace',
      parameterTypes: ['com.taobao.wireless.orange.console.manager.api.model.publish.SinglePublishParam', 'com.taobao.wireless.orange.console.manager.api.model.client.OrangeClientInfo'],
      args: [SinglePublishParam, OrangeClientInfo]
    });
  }

  // 获取orange资源
  async queryOrange(appId:any, nameSpace:any, OrangeClientInfo: any): Promise<any> {
    return this.invoke({
      id: 'com.taobao.wireless.orange.console.manager.api.service.NamespaceApiService:1.0.0',
      method: 'queryNamespace',
      parameterTypes: ['java.lang.String', 'java.lang.String', 'boolean', 'com.taobao.wireless.orange.console.manager.api.model.client.OrangeClientInfo'],
      args: [appId, nameSpace, true, OrangeClientInfo]
    });
  }

  // 查orange配置的信息
  async queryOrangeResource(resourceId:any, OrangeClientInfo: any): Promise<any> {
    return this.invoke({
      id: 'com.taobao.wireless.orange.console.manager.api.service.PublishApiService:1.0.0',
      method: 'queryResource',
      parameterTypes: ['java.lang.String', 'com.taobao.wireless.orange.console.manager.api.model.client.OrangeClientInfo'],
      args: [resourceId, OrangeClientInfo]
    });
  }
}