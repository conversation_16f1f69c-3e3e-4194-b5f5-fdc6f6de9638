import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import * as ConfigClient from '@ali/configclient';
import * as httpClient from 'urllib';
import { HsfClient } from 'hsf-client';
import { APP_NAME } from '@/apis/const';
import { isDaily } from '@/apis/utils';

export interface HSFParams {
  dailySuffix?: string,
  requestProps?: any,
  useCustomClient?: boolean,
  id: string,
  method: string,
  parameterTypes: string[],
  group?: string,
  args: any[],
  responseTimeout?: number
}

// 自定义的hsfClient
const customClient = new HsfClient({
  configclient: new ConfigClient({ httpclient: httpClient }),
  appName: APP_NAME,
  logger: console,
});

@Provide('hsfService')
export default class HSFService {
  @Inject()
  ctx!: Context;

  async invoke(params: HSFParams) {
    let { id, useCustomClient = false, dailySuffix = '.daily', ...others } = params;
    if (isDaily) {
      id += dailySuffix;
    }
    // 目前Faas内置的 this.ctx.hsfClient 传递 requestProps 时候有些问题导致校验失败。目前需要使用 hsf-client 这个npm包来调用绕过
    const client = useCustomClient ? customClient : this.ctx.hsfClient;
    return client.invoke({
      group: 'HSF',
      id,
      ...others
    });
  }
}