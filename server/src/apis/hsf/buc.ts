
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { isDaily } from '@/apis/utils';
import { IBucSimpleUser } from '@/apis/interface/buc';
import HSFService from './index';

@Provide('bucHSF')
export default class BucHSF extends HSFService {
  @Inject()
  ctx!: Context;

  /** 通过工号获取buc用户信息 */
  async getSimpleUserByEmpId(empId: string) {
    const res = await this.invoke({
      id: 'com.alibaba.buc.api.unit.HavanaQueryReadService:1.0.0',
      method: 'getSimpleUserByEmpId',
      parameterTypes: ['java.lang.String'],
      group: isDaily ? 'DAILYGROUP' : 'HSF',
      args: [empId]
    });

    return res.content as IBucSimpleUser;
  }

  /** 
   * 通过工号批量获取buc用户信息
   **/
  async getSimpleUserByEmpIdList(empIdList: string[]) {
    // 本地环境特殊处理
    if (this.ctx.env === 'local') {
      return [];
    }
    
    // 1. 出参顺序可能和入参顺序不一致
    // 2. 如果入参工号重复，则返回值会去重
    const res = await this.invoke({
      id: 'com.alibaba.buc.api.unit.HavanaQueryReadService:1.0.0',
      method: 'getSimpleUserByEmpIdList',
      parameterTypes: ['java.util.List'],
      group: isDaily ? 'DAILYGROUP' : 'HSF',
      args: [empIdList]
    });
    const userMap = (res.content as IBucSimpleUser[]).reduce((per, item) => {
      if (item.empId) {
        per[item.empId] = item;
      }
      return per;
    }, {} as { [empId: string]: IBucSimpleUser });

    return empIdList.map(empId => userMap[empId] || null)
  }

  /** 通过花名获取buc用户信息 */
  async getSimpleUserByNickNameCn(nickNameCn: string) {
    const res = await this.invoke({
      id: 'com.alibaba.buc.api.unit.HavanaQueryReadService:1.0.0',
      method: 'getSimpleUserByNickNameCn',
      parameterTypes: ['java.lang.String', 'boolean'],
      group: isDaily ? 'DAILYGROUP' : 'HSF',
      args: [nickNameCn, true]
    });

    return res.content as IBucSimpleUser;
  }
}