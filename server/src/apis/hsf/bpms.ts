
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { isDaily } from '@/apis/utils';
import HSFService from './index';

// 授权码：https://alibpmcp.alibaba-inc.com/processcenter/homepage/app_process?application=4062fdae-2b00-4237-889c-b97efc726759&an=miniwork
const AUTH_KEY = isDaily ? '1$miniwork$hello1234' : '1$miniwork$c80a2cff-ac04-40c3-bfe6-e90eba172b91';

@Provide('bpmsHSF')
export default class BpmsHSF extends HSFService {
  @Inject()
  ctx!: Context;

  /**
   * 获取指定条件的流程实例列表（https://yuque.antfin-inc.com/aliworkbpms/zg6dgr/meobuw#getprocessinstancesbyquery-）
   * @returns 
   */
  async getProcessInstancesByQuery(processCode: string, pageNum: number, pageSize: number) {
    const res = await this.invoke({
      id: 'com.alibaba.alipmc.api.ProcessInstanceService:1.1.5',
      method: 'getProcessInstancesByQuery',
      parameterTypes: ['com.alibaba.alipmc.api.param.ProcessInstanceQuery', 'java.lang.String'],
      group: 'HSF',
      args: [{
        processCodes: [processCode],
        pageSize,
        pageIndex: pageNum,
        createTimeAsc: false
      }, AUTH_KEY],
      dailySuffix: '.daily_aone_test'
    });

    const data = res as IGetProcessInstancesByQueryRes;
    const error = res as IErrorRes;

    if (error.message) {
      throw Error(error.message)
    } else if (!res.entities) {
      throw Error('获取指定条件的流程实例列表失败');
    }

    return {
      total: data.totalCount,
      list: data.entities
    }
  }

  /**
   * 启动流程实例（https://yuque.antfin-inc.com/aliworkbpms/zg6dgr/meobuw#startProcessInstance
   * @returns 
   */
  async startProcessInstance(processCode: string, title: string, initData: { [key: string]: any }) {
    const res = await this.invoke({
      id: 'com.alibaba.alipmc.api.ProcessInstanceService:1.1.5',
      method: 'startProcessInstance',
      parameterTypes: ['java.lang.String', 'java.lang.String', 'java.lang.String', 'java.util.Map', 'java.lang.String'],
      group: 'HSF',
      args: [processCode, title, this.ctx.user.workid, initData, AUTH_KEY],
      dailySuffix: '.daily_aone_test'
    });

    const data = res as IProcessInstance;
    const error = res as IErrorRes;

    if (error.message) {
      throw Error(error.message)
    } else if (!data.processInstanceId) {
      throw Error('启动流程实例失败');
    }

    return data;
  }
}

export enum EProcessInstanceStatus {
  /** 待激活 */
  NEW = 'NEW',
  /** 进行中 */
  RUNNING = 'RUNNING',
  /** 已完成 */
  COMPLETED = 'COMPLETED',
  /** 已终止 */
  TERMINATED = 'TERMINATED',
  /** 异常 */
  ERROR = 'ERROR',
}

interface IErrorRes {
  message: string;
}

interface IGetProcessInstancesByQueryRes {
  /** 实例总数 */
  totalCount: number;
  /**  */
  entities: IProcessInstance[];
}

/**
 * Demo:
 * {
 *    "isDeleted": null,
 *    "parentProcessInstanceId": "11e5ba9c-c213-495e-9806-cb7c00f66594",
 *    "finishTime": null,
 *    "dynamicProcess": false,
 *    "processVersion": "0",
 *    "rootParentProcessInstanceId": "11e5ba9c-c213-495e-9806-cb7c00f66594",
 *    "businessId": "",
 *    "title": "【飞猪微信小程序】非窗口期加版申请",
 *    "processCode": "test_extra_version",
 *    "processInstanceId": "11e5ba9c-c213-495e-9806-cb7c00f66594",
 *    "outResult": "",
 *    "superProcessInstanceId": "11e5ba9c-c213-495e-9806-cb7c00f66594",
 *    "processInstanceStatus": "RUNNING",
 *    "originatorId": "135378",
 *    "originatorJob": "",
 *    "appKey": "miniwork",
 *    "isUrlCard": null,
 *    "titleEn": "",
 *    "createTime": 1646988724121,
 *    "modifyTime": null,
 *    "id": ***********,
 *    "processId": ***********
 * }
 */
export interface IProcessInstance {
  /** 实例 id */
  processInstanceId: string;
  /** 标题 */
  title: string;
  /** 创建时间 */
  createTime: number;
  /** 流程状态 */
  processInstanceStatus: EProcessInstanceStatus;
  /** 流程完成结果 */
  outResult?: '同意' | '拒绝';
  /** 发起人工号 */
  originatorId: string;
}