import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import {  } from "@/apis/interface/prefetch-config";

@Provide('prefetchHSF')
export default class prefetchHSF {
  @Inject()
  ctx!: Context;

  /** prefetch数据测试 */
  async getPrefetchData(prefetchParams) {
    const { path, userId, query, code, end, longitude, latitude } = prefetchParams;
    const invokeData = await this.ctx.hsfClient.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      group: 'HSF',
      method: 'invoke',
      args: [
        '',
        "fl-wx-prefetch", 
        "hsfindex",
        {
          path,
          userId,
          query,
          code,
          end,
          longitude,
          latitude
        }, 
        ""
      ],
      parameterTypes: [ 'java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String' ],
    })
    return { invokeData };
  };

  async createPrefetchItemHandler(values) {
    const invokeData = await this.ctx.hsfClient.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      group: 'HSF',
      method: 'invoke',
      args: [
        '',
        "fl-wx-prefetch", 
        "createPrefetchItem",
        {
          values
        }, 
        ""
      ],
      parameterTypes: [ 'java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String' ],
    })
    return { ...(invokeData || {}) };
  }

  async updatePrefetchItem(values) {
    const invokeData = await this.ctx.hsfClient.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      group: 'HSF',
      method: 'invoke',
      args: [
        '',
        "fl-wx-prefetch", 
        "updatePrefetchItem",
        {
          values
        }, 
        ""
      ],
      parameterTypes: [ 'java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String' ],
    })
    return { ...(invokeData || {}) };
  }

  async getPrefetchByPathAndProj(path, projectName) {
    const invokeData = await this.ctx.hsfClient.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      group: 'HSF',
      method: 'invoke',
      args: [
        '',
        "fl-wx-prefetch", 
        "getPrefetchByPathAndProj",
        {
          path, projectName
        }, 
        ""
      ],
      parameterTypes: [ 'java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String' ],
    })
    return { ...(invokeData || {}) };
  }

  async getPrefetchById(id) {
    const invokeData = await this.ctx.hsfClient.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      group: 'HSF',
      method: 'invoke',
      args: [
        '',
        "fl-wx-prefetch", 
        "getPrefetchById",
        {
          id
        }, 
        ""
      ],
      parameterTypes: [ 'java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String' ],
    })
    return { ...(invokeData || {}) };
  }

  async deletePrefetchItem(id) {
    const invokeData = await this.ctx.hsfClient.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      group: 'HSF',
      method: 'invoke',
      args: [
        '',
        "fl-wx-prefetch", 
        "deletePrefetchItem",
        {
          id
        }, 
        ""
      ],
      parameterTypes: [ 'java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String' ],
    })
    return { ...(invokeData || {}) };
  }

  async getAllPrefetch() {
    const invokeData = await this.ctx.hsfClient.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      group: 'HSF',
      method: 'invoke',
      args: [
        '',
        "fl-wx-prefetch", 
        "getAllPrefetch",
        {}, 
        ""
      ],
      parameterTypes: [ 'java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String' ],
    })
    return { ...(invokeData || {}) };
  }

  async getPrefetchItemByKV(key, value) {
    const invokeData = await this.ctx.hsfClient.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      group: 'HSF',
      method: 'invoke',
      args: [
        '',
        "fl-wx-prefetch", 
        "getPrefetchItemByKV",
        {
          key, value
        }, 
        ""
      ],
      parameterTypes: [ 'java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String' ],
    })
    return { ...(invokeData || {}) };
  }
}
