import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import AoneHTTP from '@/apis/http/aone';
import BaseDevBranchHandler from '@/apis/functions/base-dev-branch';

@Provide()
export class AoneHandler extends BaseDevBranchHandler {
  @Inject()
  aoneHTTP!: AoneHTTP;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/aone/get',
    middleware: ['handlerMiddleware']
  })
  async get() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.query, ['id']);
    // 析取参数
    const { id } = this.ctx.query as { id: string };

    return await this.aoneHTTP.getById(id);
  }
}
