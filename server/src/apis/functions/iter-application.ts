import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import BpmsHSF, { EProcessInstanceStatus, IProcessInstance } from '@/apis/hsf/bpms';
import BucHSF from '@/apis/hsf/buc';
import { formatDate } from '@/apis/utils';
import { IIterApplication } from '@/apis/interface/iter-application';
import { EApplicationStatus } from '@/apis/const/iter-application';
import PROJECT_CONFIG from '@/apis/const/project';
import BaseHandler from '@/apis/functions/base';
import DingtalkService from '@/apis/service/dingtalk';
import IterBranchService from '@/apis/service/iter-branch';
import ProjectService from '@/apis/service/project';

// 线上正式流程编码: rollback(https://alibpmcp.alibaba-inc.com/processcenter/homepage/proc_version_manage?application=4062fdae-2b00-4237-889c-b97efc726759&an=%E5%B0%8F%E7%A8%8B%E5%BA%8F%E7%A0%94%E5%8F%91%E5%B9%B3%E5%8F%B0&code=rollback&name=%E5%B0%8F%E7%A8%8B%E5%BA%8F%E5%9B%9E%E6%BB%9A%E7%94%B3%E8%AF%B7&processType=0&appname=%E5%B0%8F%E7%A8%8B%E5%BA%8F%E7%A0%94%E5%8F%91%E5%B9%B3%E5%8F%B0&appKey=miniwork&status=PUBLISHED&version=1010d13f-dc1e-4f06-b959-fcb5da8db85d)
// 线上测试流程编码: test_rollback(https://alibpmcp.alibaba-inc.com/processcenter/homepage/proc_version_manage?application=4062fdae-2b00-4237-889c-b97efc726759&an=%E5%B0%8F%E7%A8%8B%E5%BA%8F%E7%A0%94%E5%8F%91%E5%B9%B3%E5%8F%B0&code=test_rollback&name=%E3%80%90%E6%B5%8B%E8%AF%95%E3%80%91%E5%B0%8F%E7%A8%8B%E5%BA%8F%E5%9B%9E%E6%BB%9A%E7%94%B3%E8%AF%B7&processType=0&appname=%E5%B0%8F%E7%A8%8B%E5%BA%8F%E7%A0%94%E5%8F%91%E5%B9%B3%E5%8F%B0&appKey=miniwork&status=PUBLISHED&version=dfc4fab6-37fe-4319-b8b4-04a3d806adb6)
const ROLLBACK_PROCESS_CODE = 'rollback';
// 线上正式流程编码: extra_version(https://alibpmcp.alibaba-inc.com/processcenter/homepage/proc_version_manage?application=4062fdae-2b00-4237-889c-b97efc726759&an=miniwork&code=extra_version&name=%E5%B0%8F%E7%A8%8B%E5%BA%8F%E9%9D%9E%E7%AA%97%E5%8F%A3%E6%9C%9F%E5%8A%A0%E7%89%88&processType=0&appname=%E5%B0%8F%E7%A8%8B%E5%BA%8F%E7%A0%94%E5%8F%91%E5%B9%B3%E5%8F%B0&appKey=miniwork&status=PUBLISHED&version=acf89fb0-c562-4099-966b-0279b577b500)
// 线上测试流程编码: test_extra_version(https://alibpmcp.alibaba-inc.com/processcenter/homepage/proc_version_manage?application=4062fdae-2b00-4237-889c-b97efc726759&an=miniwork&code=test_extra_version&name=%E3%80%90%E6%B5%8B%E8%AF%95%E3%80%91%E5%B0%8F%E7%A8%8B%E5%BA%8F%E9%9D%9E%E7%AA%97%E5%8F%A3%E6%9C%9F%E5%8A%A0%E7%89%88&processType=0&appname=%E5%B0%8F%E7%A8%8B%E5%BA%8F%E7%A0%94%E5%8F%91%E5%B9%B3%E5%8F%B0&appKey=miniwork&status=PUBLISHED&version=cfede9c8-fd97-4d2f-80c4-6381c93f8eb6)
const EXTRA_PROCESS_CODE = 'extra_version';

@Provide()
export class IterApplicationHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  bpmsHSF!: BpmsHSF;

  @Inject()
  bucHSF!: BucHSF;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  projectService!: ProjectService;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-application/list-extra-version',
    middleware: ['handlerMiddleware']
  })
  async listExtraVersion() {
    const { pageNum, pageSize } = this.ctx.query as { pageNum?: string; pageSize?: string; };
    const { list, total } = await this.bpmsHSF.getProcessInstancesByQuery(EXTRA_PROCESS_CODE, Number(pageNum) || 1, Number(pageSize) || 10)

    return {
      list: await this._handleReturnData(list),
      total
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-application/list-rollback',
    middleware: ['handlerMiddleware']
  })
  async listRollback() {
    const { pageNum, pageSize } = this.ctx.query as { pageNum?: string; pageSize?: string; };
    const { list, total } = await this.bpmsHSF.getProcessInstancesByQuery(ROLLBACK_PROCESS_CODE, Number(pageNum) || 1, Number(pageSize) || 10)

    return {
      list: await this._handleReturnData(list),
      total
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-application/apply-extra-version',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async applyExtraVersion() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['projectName', 'reason', 'effect', 'publishDay'])
    const { projectName, reason, effect, publishDay } = this.ctx.request.body as IIterApplicationCreateReq;

    // 获取项目详情
    const project = await this.projectService.get(projectName);
    if (!project) throw Error(`查询不到 projectName 为 ${projectName} 的项目`);

    // 创建并启动审批流
    const instance = await this.bpmsHSF.startProcessInstance(EXTRA_PROCESS_CODE, `${project.cnName} 申请 ${publishDay} 加版`, {
      projectName,
      projectCnName: project.cnName,
      reason,
      effect,
      // 需要转成时间戳格式
      publishDay: String(new Date(publishDay).getTime())
    });

    // 发送通知
    const title = `${project.cnName} 申请 ${publishDay} 加版`;
    const content = [
      `![bg](${PROJECT_CONFIG[project.type].dingtalkLogo})`,
      `#### **${title}**`,
      `- 申请人：${this.ctx.user.name}`,
      `- 加版原因：${reason}`,
      `- 影响：${effect}`,
      `\n👉[详见审批流](https://bpms.alibaba-inc.com/workdesk/instDetailLegao?__id=inist_detail&procInsId=${instance.processInstanceId})`
    ].join('\n');
    this.dingtalkService.notice(project.adminWorkidList, title, content);

    return this._handleReturnData(instance);
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-application/apply-rollback',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async applyRollback() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId', 'reason'])
    const { iterId, reason } = this.ctx.request.body as IApplyRollbackReq;

    // 获取迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw Error(`查找不到 iterId 为 ${iterId} 的迭代`);

    // 获取项目详情
    const project = await this.projectService.get(iterBranch.projectName);
    if (!project) throw Error(`查询不到 projectName 为 ${iterBranch.projectName} 的项目`);

    // 创建并启动审批流
    const instance = await this.bpmsHSF.startProcessInstance(ROLLBACK_PROCESS_CODE, `${project.cnName} v${iterBranch.version} 申请回滚`, {
      reason,
      version: iterBranch.version,
      projectCnName: project.cnName,
    });

    // 发送通知
    const title = `${project.cnName} [v${iterBranch.version}](https://fl-miniwork.fc.alibaba-inc.com/#/iter/detail?iterId=${iterId}) 申请回滚`;
    const content = [
      `![bg](${PROJECT_CONFIG[project.type].dingtalkLogo})`,
      `#### **${title}**`,
      `- 申请人：${this.ctx.user.name}`,
      `- 回滚原因：${reason}`,
      `\n👉[详见审批流](https://bpms.alibaba-inc.com/workdesk/instDetailLegao?__id=inist_detail&procInsId=${instance.processInstanceId})`
    ].join('\n');
    this.dingtalkService.notice(project.adminWorkidList, title, content);

    return this._handleReturnData(instance);
  }

  /** 处理返回的数据 */
  async _handleReturnData(rawData: IProcessInstance): Promise<IIterApplication>
  async _handleReturnData(rawData: IProcessInstance[]): Promise<IIterApplication[]>
  async _handleReturnData(rawData: IProcessInstance | IProcessInstance[]): Promise<IIterApplication | IIterApplication[]> {
    if (Array.isArray(rawData)) {
      const bucUserList = await this.bucHSF.getSimpleUserByEmpIdList(rawData.map(item => item.originatorId))

      return rawData.map((item, index) => {
        return {
          ...handle(item),
          bucUser: bucUserList[index]
        }
      })
    } else {
      const bucUser = await this.bucHSF.getSimpleUserByEmpId(rawData.originatorId);

      return {
        ...handle(rawData),
        bucUser
      }
    }

    function handle(processInstance: IProcessInstance) {
      let statusText = '';
      let status: EApplicationStatus;
      switch (processInstance.processInstanceStatus) {
        case EProcessInstanceStatus.NEW: status = EApplicationStatus.NEW; statusText = '待激活'; break;
        case EProcessInstanceStatus.RUNNING: status = EApplicationStatus.RUNNING; statusText = '进行中'; break;
        case EProcessInstanceStatus.COMPLETED: {
          if (processInstance.outResult === '同意') {
            status = EApplicationStatus.AGREE;
            statusText = '已同意';
          } else {
            status = EApplicationStatus.DISAGREE;
            statusText = '已拒绝';
          }
          break;
        }
        case EProcessInstanceStatus.TERMINATED: status = EApplicationStatus.TERMINATED; statusText = '已终止'; break;
        case EProcessInstanceStatus.ERROR: status = EApplicationStatus.ERROR; statusText = '异常'; break;
      }

      return {
        processInstanceId: processInstance.processInstanceId,
        status,
        statusText,
        title: processInstance.title,
        url: `https://bpms.alibaba-inc.com/workdesk/instDetailLegao?__id=inist_detail&procInsId=${processInstance.processInstanceId}`,
        gmtCreate: formatDate(new Date(processInstance.createTime))
      }
    }
  }
}

interface IIterApplicationCreateReq {
  /** 项目名称 */
  projectName: string;
  /** 加版原因 */
  reason: string;
  /** 不加版的影响 */
  effect: string;
  /** 期望发版日期，YYYY-MM-DD */
  publishDay: string;
}

interface IApplyRollbackReq {
  /** 回滚迭代id */
  iterId: number;
  /** 回滚原因 */
  reason: string;
}