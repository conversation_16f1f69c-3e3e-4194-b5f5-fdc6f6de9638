import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import PrefetchConfigService from '@/apis/service/prefetch-config';
import { IPrefetchConfig } from '@/apis/interface/prefetch-config';

@Provide()
export class PrefetchConfigHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  prefetchConfigService!: PrefetchConfigService;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/prefetch-config/list'
  })
  async list() {
    try {
      const prefetchConfigList = await this.prefetchConfigService.list();

      this.ctx.body = {
        success: true,
        data: prefetchConfigList
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/prefetch-config/find-by-key-value'
  })
  async findByKeyValue() {
    try {
      const prefetchConfig: any = this.ctx.query;
      const prefetchConfigList = await this.prefetchConfigService.findByKeyValue(prefetchConfig.key, prefetchConfig.value);

      this.ctx.body = {
        success: true,
        data: prefetchConfigList
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/prefetch-config/create',
    method: 'post',
  })
  async create() {
    try {
      const prefetchConfig: IPrefetchConfig = this.ctx.request.body;

      // 1. 判断项目是否已存在
      // const checkRes = await this.prefetchConfigService.get(prefetchConfig.path, prefetchConfig.projectName);
      // if (checkRes) throw Error(`该路径配置已存在`);

      // 3. 添加新项目
      await this.prefetchConfigService.create(prefetchConfig);

      this.ctx.body = {
        success: true,
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/prefetch-config/update',
    method: 'post',
  })
  async update() {
    try {
      const prefetchConfig: IPrefetchConfig = this.ctx.request.body;

      // 删除null
      Object.keys(prefetchConfig).forEach((key) => {
        if (prefetchConfig[key] === null) { delete prefetchConfig[key] }
      });

      // 1. 更新项目
      const updateRes = await this.prefetchConfigService.update(prefetchConfig);
      if (!updateRes) throw Error('更新失败');

      // 2. 获取最新项目信息
      const getRes = await this.prefetchConfigService.get(prefetchConfig.path, prefetchConfig.projectName);

      this.ctx.body = {
        success: true,
        data: getRes
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/prefetch-config/changeStopStatus',
  })
  async changeStopStatus() {
    try {
      // 1. 暂停配置
      const prefetchConfig = await this.prefetchConfigService.getById(this.ctx.query.id);
      if (prefetchConfig) {
        prefetchConfig.isStop = this.ctx.query.status;
        const updateRes = await this.prefetchConfigService.update(prefetchConfig);
        if (!updateRes) throw Error('更新失败');

        // 2. 获取最新项目信息
        const getRes = await this.prefetchConfigService.getById(this.ctx.query.id);

        this.ctx.body = {
          success: true,
          data: getRes
        };
      } else {
        this.ctx.body = {
          success: false,
          errorMsg: '未找到该配置，请刷新后重试'
        };
      }
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/prefetch-config/delete',
  })
  async delete() {
    try {
      // 1. 删除配置
      await this.prefetchConfigService.delete(this.ctx.query.id);

      this.ctx.body = {
        success: true
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/prefetch-config/getMtopData',
  })
  async getMtopData() {
    try {
      const mtopData = await this.prefetchConfigService.getMtopData(this.ctx.query.api, this.ctx.query.version);

      this.ctx.body = {
        success: true,
        mtopData
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/prefetch-config/getPrefetchData',
    method: 'post',
  })
  async getPrefetchData() {
    try {
      const prefetchParams: any = this.ctx.request.body;
      const invokeData = await this.prefetchConfigService.getPrefetchData(prefetchParams);

      this.ctx.body = {
        success: true,
        invokeData
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }
}
