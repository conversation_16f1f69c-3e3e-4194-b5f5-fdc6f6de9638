import { Config, Provide, ServerlessTrigger, ServerlessTriggerType } from '@midwayjs/core';
import BaseDevBranchHandler from '@/apis/functions/base-dev-branch';
import * as urllib from 'urllib';
import * as crypto from 'crypto';
import { isPre, isDaily } from '@/apis/utils';
import { PROD_URL, PRE_URL, LOCAL_URL } from '@/apis/const';

interface IChangefreeResponse {
  code: string;
  sub_code: string;
  body?: {
    apply_order_url?: string;
    check_status_enum?: string;
  }
}

const DEF_APP_ID_CONFIG = {
  'rx-fliggy-weixin': 311752, // 微信一体化
  'rx-fliggy-weixin-train': 448446, // 微信酒店火车票小程序
  'rx-fliggy-weixin-flight': 448305, // 微信机票小程序
  'rx-fliggy-weixin-traingrab': 506065, // 微信火车票抢票小程序
  'rx-weixin-test': 387126, // 微信测试应用，用于日常
  'rx-fliggy-allinone': 335253, // 支付宝一体化
};

// 变更信息
const CHANGE_INFO_COLUMN = [
  {
    prop: 'branchName',
    propCn: '分支名',
    propEn: 'branchName',
    type: 'STRING',
  },
  {
    prop: 'bizLine',
    propCn: '业务线',
    propEn: 'bizLine',
    type: 'STRING',
  },
  {
    prop: 'description',
    propCn: '描述',
    propEn: 'description',
    type: 'STRING',
  },
  {
    prop: 'creator',
    propCn: '创建人',
    propEn: 'creator',
    type: 'STRING',
  },
  {
    prop: 'qaInfo',
    propCn: '测试',
    propEn: 'qaInfo',
    type: 'STRING',
  },
  {
    prop: 'extra',
    propCn: '补充信息',
    propEn: 'extra',
    type: 'STRING',
  },
  {
    prop: 'aoneInfo',
    propCn: '关联Aone',
    propEn: 'aoneInfo',
    type: 'LINK',
  }
];

@Provide()
export class ChangefreeHandler extends BaseDevBranchHandler {
  @Config("changefree") changefreeConfig: any;

  /** 查询迭代是否触发了changefree */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/changefree/query',
    middleware: ['handlerMiddleware'],
    method: 'GET'
  })
  async queryIterChangefree() {
    const { iterId } = this.ctx.query;
    const queryBizContent = {
      source_order_id: iterId,
    };
    return this.handleRequest('changefree.change.query', queryBizContent);
  }

  /** 发布前检查 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/changefree/check',
    middleware: ['handlerMiddleware'],
    method: 'POST'
  })
  async publishCheck() {
    const { projectName, cnName = '', publishDay, group, version = '', iterId, branchList } = this.ctx.request.body;
    const queryBizContent = {
      source_order_id: iterId,
    };
    const queryRes = await this.handleRequest('changefree.change.query', queryBizContent);

    // 未检查过，直接去检查
    if ((Number(queryRes.code) === 500 && queryRes.sub_code === 'CHANGE_QUERY_PUBLISHINFO_NOTEXIST') || queryRes?.body?.check_status_enum === 'CHECK_CANCEL') {
      const detailUrl = isDaily ? LOCAL_URL : (isPre ? PRE_URL : PROD_URL);
      const defAppId = DEF_APP_ID_CONFIG[projectName];
      const defAppName = `${group}/${projectName}`;
      const now = Date.now();
      // 获取当天的结束时间 23:59:59
      const endOfDay = new Date(now);
      endOfDay.setHours(23, 59, 58);
      const changeEndTime = endOfDay.getTime();
      const extraInfo = {
          // 修改设备
        deviceChange: projectName,
        // 加签人
        addSigners: [],
        planChange: '发布',
        subChangeSystem: { name: defAppName },
        changeInfo: [{
          type: 'TABLE',
          nameEn: `branchList`,
          nameCn: `集成分支列表`,
          column: CHANGE_INFO_COLUMN,
          data: branchList,
        }],
      };
      const checkBizContent = {
        source_order_id: iterId,
        executor_emp_id: this.ctx.user?.workid || '',
        change_type_key: 'PUBLISH',
        change_title: `【${cnName}】申请${publishDay || ''}加版发布迭代V${version}`,
        change_start_time: now,
        change_end_time: changeEndTime,
        change_object_type: 'GENERAL',
        change_object: {
          def_app_id: defAppId,
          extra_info: {
            def_url: `https://space.o2.alibaba-inc.com/app/${defAppId}/iteration`,
            def_app_name: defAppName,
            def_iteration_version: version,
          }
        },
        change_object_level: 'GRADE1',
        env: 'PRODUCTION',
        detail_url: `${detailUrl}/#/iter/detail?iterId=${iterId}`,
        extra_info: JSON.stringify(extraInfo),
      };
      const checkRes = await this.handleRequest('changefree.change.check', checkBizContent);

      // 配置日常管控规则拦截后，CF会返回check_wait状态。此时，变更系统需要轮询调用query接口来获取检测单状态最终是check_hold还是check_pass
      if (checkRes?.body?.check_status_enum === 'CHECK_WAIT') {
        const watingResp = () => {
          return new Promise((resolve) => {
            const timer = setInterval(async () => {
            const pollQueryRes = await this.handleRequest('changefree.change.query', queryBizContent)
              if (pollQueryRes?.body?.check_status_enum !== 'CHECK_WAIT') {
                resolve(pollQueryRes);
                clearInterval(timer);
              }
            }, 1000);
          })
        };
        return watingResp();
      }
      return checkRes;
    }
    return queryRes;
  }

  /**
   * 接入文档：https://yuque.alibaba-inc.com/changefree/siuclg/rkog73
   * @param method 具体接口名
   * @param bizContent  业务信息
   * @returns 
   */
  async handleRequest(method: string, bizContent: Record<string, any>): Promise<IChangefreeResponse> {
    const { url, authKey } = this.changefreeConfig;
    const now = Date.now();
    const requestParams = {
      auth_key: authKey,
      version: '2.0',
      timestamp: now,
      method,
      biz_content: JSON.stringify(bizContent),
    };
    const authSign = this.getAuthSign(requestParams);
     const result = await urllib.request(url, {
      type: 'post',
      data: {
        ...requestParams,
        auth_sign: authSign
      },
    });

    return JSON.parse(result.data.toString());
  }

  /** 生成签名 */
  getAuthSign(params: Record<string, any>): string {
    const { authToken } = this.changefreeConfig;
    // 必须要按照字符升序排序
    const token = Object.keys(params).sort().map(key => `${key}=${params[key]}`).join('&') + authToken;
    console.log('token', token);
    return crypto.createHash('md5').update(token).digest('hex');
  }
}
