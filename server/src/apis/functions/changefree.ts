import { Config, Provide, ServerlessTrigger, ServerlessTriggerType } from '@midwayjs/core';
import BaseDevBranchHandler from '@/apis/functions/base-dev-branch';
import * as urllib from 'urllib';
import * as crypto from 'crypto';
import { isPre, isDaily } from '@/apis/utils';
import { PROD_URL, PRE_URL, LOCAL_URL } from '@/apis/const';

@Provide()
export class ChangefreeHandler extends BaseDevBranchHandler {
  @Config("changefree") changefreeConfig: any;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/changefree/check',
    middleware: ['handlerMiddleware'],
    method: 'POST'
  })
  async publishCheck() {
    // 析取参数
    const { project, cnName = '飞猪微信小程序', version = '1.0.0' } = this.ctx.request.body;
    const { url, authKey } = this.changefreeConfig;
    const detailUrl = isDaily ? LOCAL_URL : (isPre ? PRE_URL : PROD_URL);
    const defAppId = '311752';
    const now = Date.now();
    const requestParams = {
      auth_key: authKey,
      version: '2.0',
      timestamp: now,
      method: 'changefree.change.check',
      biz_content: JSON.stringify({
        source_order_id: '',
        executor_emp_id: this.ctx.user?.workid || '',
        change_type_key: 'PUBLISH',
        change_title: `${cnName}发布：${version}`,
        change_start_time: now,
        change_end_time: now + 24 * 60 * 60 * 1000, // 加一天
        change_object_type: 'GENERAL',
        change_object: {
          def_app_id: defAppId,
          extra_info: {
            def_url: 'https://space.o2.alibaba-inc.com/app/311752/basic',
            def_app_name: "trip/rx-fliggy-weixin",
            def_iteration_version: version
          }
        },
        change_object_level: 'GRADE1',
        env: 'PRODUCTION',
        detail_url: `${detailUrl}/#/iter/detail?iterId=958`,
        extra_info: {
          // 修改设备
          deviceChange: '',
          // 加签人
          addSigners: [],
          planChange: '发布',
          // changeInfo: {
          //   type: 'DIFF',
          //   nameEn: `${project} change`,
          //   nameCn: `${project}变更`,
          //   data: [
          //     {
          //       changeTarget: '{}',
          //       dataType: 'JSON',
          //       newValue: '{}',
          //       oldValue: '{}',
          //     }
          //   ]
          // },
        }
      })
    };

    const authSign = this.getAuthSign(requestParams);

    const result = await urllib.request(url, {
      type: 'post',
      data: {
        ...requestParams,
        auth_sign: authSign
      },
    });

    return {
      ...r
    };
  }

  /** 生成签名 */
  getAuthSign(params: Record<string, any>): string {
    const { authToken } = this.changefreeConfig;
    const token = Object.keys(params).map(key => `${key}=${params[key]}`).join('&') + authToken;
    return crypto.createHash('md5').update(token).digest('hex');
  }
}
