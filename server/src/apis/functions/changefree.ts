import { Config, Provide, ServerlessTrigger, ServerlessTriggerType } from '@midwayjs/core';
import BaseDevBranchHandler from '@/apis/functions/base-dev-branch';
import urllib from 'urllib';

@Provide()
export class ChangefreeHandler extends BaseDevBranchHandler {
  @Config("changefree") changefreeConfig: any;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/changefree/check',
    middleware: ['handlerMiddleware'],
    method: 'POST'
  })
  async check() {
    // 析取参数
    const { } = this.ctx.request.body;
    const { url, authKey, authToken } = this.changefreeConfig;
    const commonParams = {
      auth_key: authKey,
      version: '2.0',
      timestamp: Date.now(),
      method: 'changefree.change.check',
      biz_content: JSON.stringify({})
    };
    const result = await urllib.request(url, {
      type: 'post',
      data: {
        auth_key: authKey,
        auth_sign: ,
        version: '2.0',
        timestamp: Date.now(),
        method: 'changefree.change.check',
        biz_content: JSON.stringify({})
      },
    });
  }

  getAuthSign(params) {
    const { authToken } = this.changefreeConfig;
    const token = Object.keys(params).map(key => `${key}=${params[key]}`).join('&') + authToken;
    return crypto.createHash('md5').update(token).digest('hex');
  }
}
