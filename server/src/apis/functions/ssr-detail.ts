import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { HttpService } from '@midwayjs/axios';
import { AliFC } from '@ali/midway-faas-typings';

import { get, chunk } from 'lodash';

import <PERSON>Handler from '@/apis/functions/base';
import SsrService from '@/apis/service/ssr';
import PressrService from '@/apis/service/pre-ssr';
import BpmsHSF from '@/apis/hsf/bpms';
import DefService from '@/apis/service/def';
import ReleaseRecordService from '../service/release-record';
import HSFService from '@/apis/hsf';
import DingtalkService from '@/apis/service/dingtalk';
import { formatDate, isProd, sleepFunc, genQueryStr } from "@/apis/utils";
import { ALARM_CONFIG } from '@/apis/utils/alarm';
import { getHolderHtml } from '@/apis/utils/getHolderHtml';
import {
  AuthParam,
  DataQueryParam,
  FilterField,
  AmdpDataQueryServiceProxy,
  // HavanaQueryServiceProxy,
} from '@ali/midway-buc-client';

const cookie = require('cookie');
const md5 = require('md5');

const TAIR_USER = '6cf1ed1dbea14839';
const PROCESS_CODE = 'fliggy-ssr-preload-apply';
const env = process.env.MIDWAY_SERVER_ENV;
const isDaily = env === 'local' || env === 'daily';
const dto2UserField = {
  loginAccount: 'userid',
  nickName: 'nickName',
  name: 'name',
  workNo: 'workid',
  buMail: 'email',
  personalPhotoUrl: 'avatar_url',
  deptName: 'deptName'
};
const amdpAuthParams = {
  appKey: 'fn-fl-choreography',
  appSecret: isDaily ? 'test' : '90a23cb8-4427-449c-b07e-cdc36886b64f',
};
interface IKVParams {
  path: string | undefined;
  key?: string;
  value?: string;
};

@Provide()
export class SSRDetailHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  ssrService!: SsrService;

  @Inject('axios:httpService')
  httpService!: HttpService;

  @Inject()
  releaseRecordService!: ReleaseRecordService;

  @Inject()
  pressrService!: PressrService;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  bpmsHSF!: BpmsHSF;

  @Inject()
  defService!: DefService;

  @Inject()
  hsfService!: HSFService;

  @Inject('bucClient:amdpDataQueryServiceProxy')
  amdpDataQueryServiceProxy!: AmdpDataQueryServiceProxy;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/detail-data',
    middleware: ['handlerMiddleware']
  })
  async detailData() {
    const { sunfirePluginId, projectGroup, projectName, pageName, projectBusiness, from, to, pid, onlyArms, target,errorLogType } = this.ctx.query;
    if (onlyArms === 'true') {
      let armsRes: any;
      try {
        armsRes = await this.ssrService.queryPerfDetail(pid, projectGroup, projectName, pageName, projectBusiness, target);
      } catch (e) {}
      return {
        armsRes
      };
    } else {
      const alarmConf = ALARM_CONFIG[sunfirePluginId];
      const queryStr = alarmConf.getSunfireSql({ projectName, pageName, from, to });
      let result = await this.ssrService.querySunfireMonitor(queryStr);
      result = get(result, 'data.result', []);
      result = alarmConf.parseSunfireSqlResult(result, { projectName, pageName, from, to });

      let armsRes: any;
      let errorList: any;
      try {
        armsRes = await this.ssrService.queryPerfData(from, to, pid, projectGroup, projectName, pageName, projectBusiness);
        errorList = await this.ssrService.queryErrorList(from, to, projectName, pageName, errorLogType);
      } catch (e) {}

      return {
        sunfireRes: result,
        armsRes,
        errorList
      };
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/ssr-list',
    middleware: ['handlerMiddleware']
  })
  async ssrList() {
    const { current, pageSize, projectName, tabType, userWorkId, isSearch, project_business } = this.ctx.query;
    const workId = this.ctx.user && this.ctx.user.workid || userWorkId;
    const list = await this.ssrService.queryList({
      current,
      pageSize,
      projectName,
      isMy: tabType === 'my' ? true : false,
      workId,
      isSearch,
      project_business
    });


    return {
      total: list.total,
      data: list.res
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/batch-prepub',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async batchPrepub() {
    let reqBody = this.ctx.request.body;
    try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};

    const {
      projectGroup,
      projectName,
      pageList = [],
      userName,
      userWorkId,
    } = reqBody;
    const envParams = {
      unit: this.ctx.aliEnv === 'dev' ? 'daily' : 'pre'
    };
    const diamondRes = await this.ctx.diamond.getConfig('route-map', 'render-html', envParams);
    const username = this.ctx.user && this.ctx.user.name || userName;
    const workId = this.ctx.user && this.ctx.user.workid || userWorkId;
    // 获取页面配置
    const pageInfoList = await Promise.all(pageList.map((item: any) => this.ssrService.queryPageById({ id: item.id })));

    try {
      const diamondObj = JSON.parse(diamondRes);
      let newDiamond = {
        ...diamondObj
      };
      // 更新diamond配置
      for(let i = 0; i < pageList.length; i++) {
        const item = pageList[i];
        const pageInfo = pageInfoList.find((info: any) => info.id === item.id) || {};
        const pathProjectName = get(pageInfo, 'path_project_name', '') || projectName || '';
        const pathPageName = get(pageInfo, 'path_page_name', '') || item.pageName || '';
        const group = projectGroup || get(pageInfo, 'project_group', '') || 'trip';
        const pageKey = `/app/${group}/${pathProjectName}/pages/${pathPageName}`;
        newDiamond[pageKey] = {
          ...(newDiamond[pageKey] || {}),
          bundleUrl: `https://dev.g.alicdn.com/${group}/${projectName || ''}/${item.version || ''}/node/${(item.pageName || '').replace('/index.html', '')}.js`
        };
        if (pageInfo.csr_same_site === 2) {
          newDiamond[pageKey].csrSameSite = true;
        }
        // 追加diamond插入记录
        try{
          const recordRes = await this.releaseRecordService.create({status: 0, diamond_key: pageKey, diamond_value: JSON.stringify(newDiamond[pageKey]), gmt_create: formatDate(new Date())})
          if(!recordRes?.id){console.log("releaseRecordCreate failed", recordRes)}
        }catch(e){console.log("releaseRecordCreate failed catch", e)}
      }
      const updateMg = await this.ctx.diamond.publishSingle('route-map', 'render-html', JSON.stringify(newDiamond), envParams);
      // 更新db数据
      const updateDB = await Promise.all(
        pageList.map((item: any) => {
          return this.ssrService.updatePage({
            id: item.id,
            version: item.version,
            last_operator: `${username}-${workId}`,
            preStreamConfig: item.streamConfig ? JSON.stringify(item.streamConfig) : '',
          });
        })
      );
      // 更新db操作记录
      const updateRes = await Promise.all(
        pageList.map((item: any) => {
          return this.ssrService.createLog({
            gmt_create: formatDate(new Date()),
            page_id: item.id,
            publish_type: 1,
            operator: `${username}-${workId}`,
            version: item.version,
          })
        })
      );

      return {
        updateMg,
        updateRes,
        updateDB
      }
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/prepub',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async prepub(data) {
    let reqBody = data.hsfClient ?this.ctx.request.body : data;
    try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};

    const { projectGroup, projectName, pageName, version, id, userName, userWorkId } = reqBody;
    const envParams = {
      unit: this.ctx.aliEnv === 'dev' ? 'daily' : 'pre'
    };
    const diamondRes = await this.ctx.diamond.getConfig('route-map', 'render-html', envParams);
    const username = this.ctx.user && this.ctx.user.name || userName;
    const workId = this.ctx.user && this.ctx.user.workid || userWorkId;
    const pageInfo = await this.ssrService.queryPageById({id});
    const group = projectGroup || get(pageInfo, 'project_group', '') || 'trip';
    const pathProjectName = get(pageInfo, 'path_project_name', '') || projectName;
    const pathPageName = get(pageInfo, 'path_page_name', '') || pageName;
    // 流式配置
    let dbPreStreamConfig: any = null;
    try { dbPreStreamConfig = JSON.parse(get(pageInfo, 'pre_stream_config', '')) } catch { dbPreStreamConfig = null };
    const holdHtml = await getHolderHtml({
      group,
      version,
      projectName,
      pageName,
      env: 'pre',
    });
    const preStreamConfig = holdHtml ? {
      cacheKey: dbPreStreamConfig && dbPreStreamConfig.cacheKey || [],
      template: encodeURIComponent(holdHtml),
      version,
    } : null;

    try {
      const diamondObj = JSON.parse(diamondRes);
      let newDiamond = {
        ...diamondObj
      };
      const pageKey = `/app/${group}/${pathProjectName}/pages/${pathPageName}`
      newDiamond[pageKey] = {
        ...(newDiamond[pageKey] || {}),
        bundleUrl: `https://dev.g.alicdn.com/${group}/${projectName}/${version}/node/${(pageName || '').replace('/index.html', '')}.js`
      };
      if (pageInfo.csr_same_site === 2) {
        newDiamond[pageKey].csrSameSite = true;
      }
      try{
        const recordRes = await this.releaseRecordService.create({status: 0, diamond_key: pageKey, diamond_value: JSON.stringify(newDiamond[pageKey]), gmt_create: formatDate(new Date())})
        if(!recordRes?.id){console.log("releaseRecordCreate failed", recordRes)}
      }catch (e) {console.log("releaseRecordCreate failed catch", e)}
      // 追加diamond插入记录
      const updateRes = await this.ctx.diamond.publishSingle('route-map', 'render-html', JSON.stringify(newDiamond), envParams);
      const updateDB = await this.ssrService.updatePage({
        id,
        version,
        last_operator: `${username}-${workId}`,
        preStreamConfig: preStreamConfig ? JSON.stringify(preStreamConfig) : '',
      })
      await this.ssrService.createLog({
        gmt_create: formatDate(new Date()),
        page_id: id,
        publish_type: 1,
        operator: `${username}-${workId}`,
        version
      })
      return {
        updateRes,
        updateDB
      }
    } catch(err: any) {
      return {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/gray',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async gray(data) {
    let reqBody = data.hsfClient ?this.ctx.request.body : data;
    try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};

    const { version, id, ratio, projectGroup, projectName, pageName, userName, userWorkId, targetUser, targetZg, isTarget = false, enforcePub = false, streamConfig, isMetaQ = false } = reqBody;
    const username = userName || this.ctx.user && this.ctx.user.name;
    const workId = userWorkId || this.ctx.user && this.ctx.user.workid;
    const pageInfo = await this.ssrService.queryPageById({id});
    const group = projectGroup || get(pageInfo, 'project_group', '') || 'trip';
    const pathProjectName = get(pageInfo, 'path_project_name', '') || projectName;
    const pathPageName = get(pageInfo, 'path_page_name', '') || pageName;
    const grayVersion = version || get(pageInfo, 'gray_version', '');
    
    // 流式配置
    let streamParams: any = null;
    let dbStreamConfig: any = null;
    try { dbStreamConfig = JSON.parse(get(pageInfo, 'stream_config', '')) } catch { dbStreamConfig = null };
    // 当开发者从ssr发布后台直接修改版本号进行发布，则从html里获取流式配置
    const holdHtml = await getHolderHtml({
      group,
      version,
      projectName,
      pageName,
      env: 'prod',
    });
    // clam发布时，会增加流式配置streamConfig字段
    if (streamConfig) {
      streamParams = {
        ...(dbStreamConfig || {}),
        grayRatio: ratio,
        grayUserList: targetUser || '',
        grayCacheKey: streamConfig.cacheKey,
        grayTemplate: streamConfig.template,
        grayVersion: streamConfig.version,
      };
    } else if (isTarget && dbStreamConfig) {
      // 修改灰度人群
      streamParams = {
        ...dbStreamConfig,
        grayUserList: targetUser || '',
      }
    } else if (ratio === -1 && dbStreamConfig) {
      // 取消灰度，则清空灰度配置
      streamParams = {
        cacheKey: dbStreamConfig.cacheKey,
        template: dbStreamConfig.template,
        version: dbStreamConfig.version,
      }
    } else if (ratio === 0 && holdHtml) {
      // 灰度0%，且存在骨架图，则修改流式配置
      streamParams = {
        ...(dbStreamConfig || {}),
        grayRatio: ratio,
        grayUserList: targetUser || '',
        grayCacheKey: dbStreamConfig && dbStreamConfig.cacheKey || [],
        grayTemplate: encodeURIComponent(holdHtml),
        grayVersion: grayVersion,
      };
    } else if (ratio >= 0 && dbStreamConfig) {
      // 推进灰度，则增加灰度
      streamParams = {
        ...dbStreamConfig,
        grayRatio: ratio,
      }
    }
    
    const projectInfo = await this.ssrService.queryProjectByName({projectName: projectName});
    const businessPath = projectInfo.project_business;
    // 是否关联def发布
    const isAssociatedDef = get(projectInfo, 'is_associated_def') === 2;



    // 将发布人添加到成功率报警订阅配置中
    try{
      // 获取当前alarmConfig
      const currentAlarmStr = get(pageInfo, 'alarm_config', '');
      let newAlarmStr = ''
      const currentAlarm: any = JSON.parse(currentAlarmStr);
      const { list = [], rank = 0} = currentAlarm || {};
      if(!rank){
        newAlarmStr = JSON.stringify({list: [{value: workId, label: username}], rank: 99})
      }else{
        if(!list.find(r=> r.value === workId.toString())){
          newAlarmStr = JSON.stringify({list: [...list, {value: workId, label: username}], rank})
        }
      }
      newAlarmStr && await this.ssrService.updatePage({
        id: id,
        alarmConfig: newAlarmStr,
        last_operator: `${username}-${workId}`,
      })
    }catch(e: any){console.log("err", e.message)}

    // 更新def的灰度
    let updateDef = {};
    if (isAssociatedDef && !isMetaQ) {
      // 构造def后台appName
      const defAppIdorName = encodeURIComponent(encodeURIComponent(`${group}/${projectName}`));
      const { isInDefGrey } = await getDefPageInfo({
        group,
        projectName,
        pageName,
        version,
        defAppIdorName,
        defService: this.defService,
      });

      // 页面没有在灰度中
      if (!isInDefGrey) {
        updateDef = {
          data: {
            success: false,
            errorMsg: '没有灰度中的页面',
          }
        }
      } else if (ratio === -1) {
        // 取消def后台的灰度
        updateDef = await this.defService.cancelPageGrey({ appIdorName: defAppIdorName });
      } else if (ratio >= 0) {
        // 修改def灰度百分比
        updateDef = await this.defService.setPageGrey({
          appIdorName: defAppIdorName,
          rate: ratio,
        });
      }
    }

    // def灰度放量是否命中20分钟规则
    const defIsBlock = get(updateDef, 'data.data.is_block') || false;
    if (defIsBlock && !enforcePub) {
      return {
        updateDef,
      }
    }

    const updateDB = await this.ssrService.updatePage({
      id,
      grayVersion,
      last_operator: `${username}-${workId}`,
      ratio,
      targetUser,
      targetZg,
      isTarget,
      streamConfig: streamParams ? JSON.stringify(streamParams) : '',
    });

    // 连接tair
    const tairClient = await this.ctx.tairManager.getClient({
      username: TAIR_USER,
    });
    // 是否进行定向灰度
    if (isTarget) {
      // 先读tair的值，避免灰度覆盖
      const targetRes:any = await tairClient.get(`fliggy-ssr-${pathProjectName}-${pathPageName}`);
      let tairRatio = 0;
      let speConfig = '';
      if (targetRes && targetRes.data) {
        try {
          const tairConfig = JSON.parse(targetRes.data);
          tairRatio = tairConfig.ratio;
          speConfig = tairConfig.speConfig
        } catch(e) {}
      }
      await tairClient.put(`fliggy-ssr-${pathProjectName}-${pathPageName}`, JSON.stringify({
        version: grayVersion,
        targetUser,
        targetZg,
        speConfig,
        ratio: parseInt(`${tairRatio}`)
      }));
    } else {
      if (ratio === -1) {
        // 如果存在生产环境配置IP，不能完全注销
        const targetRes:any = await tairClient.get(`fliggy-ssr-${pathProjectName}-${pathPageName}`);
        let speConfig = '';
        if (targetRes && targetRes.data) {
          try {
            const tairConfig = JSON.parse(targetRes.data);
            speConfig = tairConfig.speConfig
          } catch(e) {}
        }
        if(speConfig){
          await tairClient.put(`fliggy-ssr-${pathProjectName}-${pathPageName}`, JSON.stringify({speConfig}));
        }else{
          await tairClient.invalid(`fliggy-ssr-${pathProjectName}-${pathPageName}`);
        }
        await this.ssrService.createLog({
          gmt_create: formatDate(new Date()),
          page_id: id,
          publish_type: 3,
          operator: `${username}-${workId}`,
          version: grayVersion,
          gray_ratio: ratio
        });
      } else if (ratio >= 0) {
        let tairTarget = '';
        let zgTarget = '';
        let speConfig = '';
        const targetRes: any = await tairClient.get(`fliggy-ssr-${pathProjectName}-${pathPageName}`);
        if (targetRes && targetRes.data) {
          try {
            const tairConfig = JSON.parse(targetRes.data);
            tairTarget = tairConfig.targetUser;
            zgTarget = tairConfig.targetZg;
            speConfig = tairConfig.speConfig;
          } catch (e) {}
        }
        await tairClient.put(`fliggy-ssr-${pathProjectName}-${pathPageName}`, JSON.stringify({
          version: grayVersion,
          targetUser: tairTarget,
          targetZg: zgTarget,
          speConfig,
          ratio: parseInt(ratio)
        }));
        await this.ssrService.createLog({
          gmt_create: formatDate(new Date()),
          page_id: id,
          publish_type: 2,
          operator: `${username}-${workId}`,
          version: grayVersion,
          gray_ratio: ratio
        });
      }
    }

    // 这里加个判断：因为商旅有自己的ER，所以不需要更新ER配置
    if (businessPath === 'btrip') {
      return {
        updateDef,
        updateDB,
      };
    }

    // 发布er配置
    if (streamParams) {
      const erDataValue = {
        ...streamParams,
        grayUpdateTime: Date.now(),
      };
      
      const erConfigKey = `${pageInfo.project_name}_${(pageInfo.page_name || '').replace('/index.html', '')}`;
      this.ssrService.setDcdnKV({
        nameSpace: 'fliggyrax_124215',
        key: erConfigKey,
        value: erDataValue,
      });
    }

    return {
      updateDef,
      updateDB,
    };
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/publish',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async publish(data) {
    let reqBody = data.hsfClient ?this.ctx.request.body : data;
    try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};

    const { projectGroup, projectName, pageName, version, id, streamConfig, isMetaQ = false, enforcePub = false, userName, userWorkId } = reqBody;
    const envParams = {
      unit: this.ctx.aliEnv === 'dev' ? 'daily' :
      this.ctx.aliEnv === 'pre' ? 'pre' : 'center'
    };
    const username = userName || this.ctx.user && this.ctx.user.name;
    const workId = userWorkId || this.ctx.user && this.ctx.user.workid;
    const diamondRes = await this.ctx.diamond.getConfig('route-map', 'render-html', envParams);
    const pageInfo = await this.ssrService.queryPageById({id})
    const group = projectGroup || get(pageInfo, 'project_group', '') || 'trip';
    const pathProjectName = get(pageInfo, 'path_project_name', '') || projectName;
    const pathPageName = get(pageInfo, 'path_page_name', '') || pageName;

    // 流式配置
    let streamParams: any = null;
    let dbStreamConfig: any = null;
    try { dbStreamConfig = JSON.parse(get(pageInfo, 'stream_config', '')) } catch { dbStreamConfig = null };
    // 当开发者从ssr发布后台直接修改版本号进行发布，则从html里获取流式配置
    const holdHtml = await getHolderHtml({
      group,
      version,
      projectName,
      pageName,
      env: 'prod',
    });
    // clam发布时，会增加流式配置streamConfig字段
    if (streamConfig) {
      streamParams = {
        cacheKey: streamConfig.cacheKey,
        template: streamConfig.template,
        version: streamConfig.version,
      };
    } else if (dbStreamConfig) {
      streamParams = {
        cacheKey: dbStreamConfig.grayCacheKey || dbStreamConfig.cacheKey,
        template: dbStreamConfig.grayTemplate || dbStreamConfig.template,
        version: dbStreamConfig.grayVersion || version,
      }
    } else if (holdHtml) {
      streamParams = {
        grayCacheKey: dbStreamConfig && (dbStreamConfig.grayCacheKey || dbStreamConfig.cacheKey) || [],
        template: encodeURIComponent(holdHtml),
        version,
      }
    }

    const projectInfo = await this.ssrService.queryProjectByName({projectName: projectName});
    const businessPath = projectInfo.project_business;

    // 是否关联def发布
    const isAssociatedDef = get(projectInfo, 'is_associated_def') === 2;

    // 完成def的灰度发布，正式发布上线
    let updateDef = {};
    if (isAssociatedDef && !isMetaQ) {
      // 构造def后台appName
      const defAppIdorName = encodeURIComponent(encodeURIComponent(`${group}/${projectName}`));
      const { isInDefGrey, canPublishDefPage } = await getDefPageInfo({
        group,
        projectName,
        pageName,
        version,
        defAppIdorName,
        defService: this.defService,
      });
      if (isInDefGrey && canPublishDefPage) {
        updateDef = await this.defService.finishPageGrey({ appIdorName: defAppIdorName });
      } else {
        updateDef = {
          data: {
            success: false,
            errorMsg: '没有可完成灰度的页面',
          }
        }
      }
    } else {
      updateDef = {
        data: {
          success: true,
        }
      }
    }

    // def灰度放量是否命中20分钟规则
    const defIsBlock = get(updateDef, 'data.data.is_block') || false;
    if (defIsBlock && !enforcePub) {
      return {
        updateDef
      };
    }

    try {
      const diamondObj = JSON.parse(diamondRes);
      let newDiamond = {
        ...diamondObj
      };
      const pageKey = `/app/${group}/${pathProjectName}/pages/${pathPageName}`;
      newDiamond[pageKey] = {
        ...(newDiamond[pageKey] || {}),
        bundleUrl: `https://g.alicdn.com/${group}/${projectName}/${version}/node/${(pageName || '').replace('/index.html', '')}.js`,
        business: businessPath || 'default'
      };
      if (pageInfo.csr_same_site === 2) {
        newDiamond[pageKey].csrSameSite = true;
      }
      if (pageInfo.cdn_config && pageInfo.cdn_config !== 'null') {
        try {
          const cdnKey = JSON.parse(pageInfo.cdn_config);
          newDiamond[pageKey].cdnKey = cdnKey;
        } catch (e) {}
      }
      try{
        const recordRes = await this.releaseRecordService.create({status: 0, diamond_key: pageKey, diamond_value: JSON.stringify(newDiamond[pageKey]), gmt_create: formatDate(new Date())})
        if(!recordRes?.id){console.log("releaseRecordCreate failed", recordRes, JSON.stringify(reqBody))}
      }catch (e: any) {console.log("releaseRecordCreate failed catch", e?.message, JSON.stringify(reqBody))}

      console.log("同步成功",JSON.stringify(reqBody), JSON.stringify(newDiamond[pageKey]))

      const updateRes = await this.ctx.diamond.publishSingle('route-map', 'render-html', JSON.stringify(newDiamond), envParams);

      // 更新线上版本
      const updateDB =  await this.ssrService.updatePage({
        id,
        prodVersion: version,
        last_operator: `${username}-${workId}`,
        ratio: -1,
        streamConfig: streamParams ? JSON.stringify(streamParams) : '',
      });
      await this.ssrService.createLog({
        gmt_create: formatDate(new Date()),
        page_id: id,
        publish_type: 4,
        operator: `${username}-${workId}`,
        version
      });
      // 删除灰度
      const tairClient = await this.ctx.tairManager.getClient({
        username: TAIR_USER,
      });

      // 如果存在生产环境配置IP，不能完全注销
      const targetRes:any = await tairClient.get(`fliggy-ssr-${pathProjectName}-${pathPageName}`);
      let speConfig = '';
      if (targetRes && targetRes.data) {
        try {
          const tairConfig = JSON.parse(targetRes.data);
          speConfig = tairConfig.speConfig
        } catch(e) {}
      }
      if(speConfig){
        await tairClient.put(`fliggy-ssr-${pathProjectName}-${pathPageName}`, JSON.stringify({speConfig}));
      }else{
        await tairClient.invalid(`fliggy-ssr-${pathProjectName}-${pathPageName}`);
      }

      // 这里加个判断：因为商旅有自己的ER，所以不需要更新ER配置
      if (businessPath === 'btrip') {
        return {
          updateRes,
          updateDB,
        };
      }

      if (isProd) {
        if (businessPath === 'hotel' || businessPath === 'traffic' || businessPath === 'vacation' || businessPath === 'member') {
          await this.ssrService.setDcdn({
            nameSpace: 'fliggyrax_124215',
            key: 'path-map',
            contentKey: `/app/${group}/${pathProjectName}/pages/${pathPageName}`,
            contentValue: `/${businessPath}/app/${group}/${pathProjectName}/pages/${pathPageName}`
          });
          await this.ssrService.setDcdn({
            nameSpace: 'fliggyrax_124215',
            key: 'path-map-new',
            contentKey: `/app/${group}/${pathProjectName}/pages/${pathPageName}`,
            contentValue: {
              path: `/${businessPath}/app/${group}/${pathProjectName}/pages/${pathPageName}`,
              csrSameSite: pageInfo.csr_same_site === 2 ? true : false
            }
          }, {syncOss: true});
        } else if (pageInfo.csr_same_site === 2) {
          await this.ssrService.setDcdn({
            nameSpace: 'fliggyrax_124215',
            key: 'path-map-new',
            contentKey: `/app/${group}/${pathProjectName}/pages/${pathPageName}`,
            contentValue: {
              csrSameSite: true
            }
          }, {syncOss: true});
        }
      }

      // 发布er配置
      if (streamParams) {
        const erDataValue = {
          ...streamParams,
          updateTime: Date.now(),
        };

        const erConfigKey = `${pageInfo.project_name}_${(pageInfo.page_name || '').replace('/index.html', '')}`;
        this.ssrService.setDcdnKV({
          nameSpace: 'fliggyrax_124215',
          key: erConfigKey,
          value: erDataValue,
        });
      }

      // 静态SSR配置更新
      const staticErKv = await this.ssrService.getDcdnKV({
        nameSpace: 'fliggyrax_124215' ,
        key: 'static-config'
      });
      let staticErData:any;
      try {
        staticErData = JSON.parse(staticErKv.data);
        if (staticErData[pageKey]) {
          const newErData = {
            ...staticErData,
            [pageKey]: {
              ...staticErData[pageKey],
              updateTime: (new Date()).getTime(),
              updateConfig: {}
            }
          }
          await this.ssrService.setDcdnKV({
            nameSpace: 'fliggyrax_124215',
            key: 'static-config',
            value: newErData
          })
        }
      } catch {}

      // 预加载配置更新
      if (isProd) {
        await this.ssrService.preUpdate(pageKey);
        if (pageInfo.cdn_config && pageInfo.cdn_config !== 'null') {
          // 1分钟延时
          setTimeout(() => {
            this.ssrService.updateCdn(projectName, pageName)
          }, 60000)
        }
      }

      return {
        updateDef,
        updateRes,
        updateDB,
      };
    } catch(err: any) {
      console.log("sync version error", JSON.stringify(reqBody), err?.message)
      return {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/add-project',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async addProject() {
    let reqBody = this.ctx.request.body;
    try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};

    const { projectGroup, projectName, projectBusiness, isAssociatedDef, userName, userWorkId } = reqBody;
    const username = this.ctx.user && this.ctx.user.name || userName;
    const workId = this.ctx.user && this.ctx.user.workid || userWorkId;

    const checkIsExist = await this.ssrService.checkProjectExist({
      projectName
    });
    if (checkIsExist) {
      return {
        success: false,
        errorMsg: '项目已存在'
      }
    }

    let businessPath = 'default';
    // 通过发布人来更改当前分组，只针对第一次发布迭代有效,不然会出现后面其他部门的同学发布，导致分组自动更改
    try{
      // let ssoTicket = 'cdc4a3c068d4454b9b100065b89826001ddcb700';
      // try{ssoTicket = await this.ctx.generateSsoTicket()}catch(e: any){console.log("ssr-detail-get-business-failed1", e.message, username)}
      const result = await this.queryUserByKeyword(workId.toString(), 1 ,10)
      const dep = get(result, 'data.0.deptName', '');
      const depName = dep.split("-").length > 3 ? dep.split("-")[3] : '';
      let depValue = 'default';
      [{ value: 'default', label: '默认' },
        { value: 'hotel', label: '酒店' },
        { value: 'traffic', label: '交通' },
        { value: 'vacation', label: '度假' },
        { value: 'btrip', label: '商旅' },
        { value: 'member', label: '用户' },
      ].forEach(res=>{
        if(depName.indexOf(res.label) !== -1){depValue = res.value}
      })
      businessPath = depValue;
    }catch(e: any){
      console.log("ssr-detail-get-business-failed2", e.message, username)
    }

    const res = await this.ssrService.createProject({
      gmt_create: formatDate(new Date()),
      gmt_modified: formatDate(new Date()),
      last_operator: `${username}-${workId}`,
      project_group: projectGroup || 'trip',
      project_name: projectName,
      project_business: (projectBusiness === 'default' || !projectBusiness) ? businessPath : projectBusiness || 'default',
      is_associated_def: isAssociatedDef || 2,
      admin_list: JSON.stringify([{
        value: workId,
        label: username,
      }])
    })

    return res
  }
  async queryUserByKeyword(keyword: string, page: number, pageSize: number) {
    // 鉴权参数
    const authParam = AuthParam.from({
      appKey: 'fn-fl-choreography',
      appSecret: isDaily ? 'test' : '90a23cb8-4427-449c-b07e-cdc36886b64f',
    });
    // 设置过滤字段名称和值
    const queryParam = DataQueryParam.from({
      filterFieldList: [
        FilterField.from({
          name: 'searchKey',
          value: keyword,
        }),
      ],
      page,
      pageSize,
      combineId: isDaily ? 1224 : 1369,
    });
    const amdpResult = await this.amdpDataQueryServiceProxy.queryDataSet(
      authParam,
      queryParam,
    );
    if (!amdpResult.success || !get(amdpResult, 'data.dataRows.length')) {
      return {
        data: [],
        page,
        pageSize,
        total: 0,
      };
    }
    const userList = amdpResult.data.dataRows.map((dataRow: any) => {
      const userInfo: any = {};
      dataRow.dataFields.forEach((dataField) => {
        if (dto2UserField[dataField.name]) {
          userInfo[dto2UserField[dataField.name]] = dataField.value;
        }else if(dataField.name === "empJobList"){
          userInfo['deptName'] = dataField.value[0].dataFields.find(r=> r.name === 'deptName')?.value
        }
      });
      userInfo.name = userInfo.nickName || userInfo.name;
      userInfo.workid = Number(userInfo.workid)
        ? Number(userInfo.workid).toString()
        : userInfo.workid;
      return userInfo;
    });
    return {
      data: userList,
      page,
      pageSize,
      total: amdpResult.data.totalCount || 0,
    };
  }

  async findUsersByIds(workids: string[]) {
    workids = workids.map((res: string, index: number) => {
      if (res.length <= 5) {
        res = (Math.pow(10,6 - res.length)).toString().substr(1) + res
      }
      return res;
    });
    const chunksWorkids = chunk(workids, 10);
    // 鉴权参数
    const authParam = AuthParam.from(amdpAuthParams);
    const chunksAmdpResult:any = await Promise.all(
      chunksWorkids.map((chunkWorkids) => {
        // 设置过滤字段名称和值
        const queryParam = DataQueryParam.from({
          filterFieldList: [
            FilterField.from({
              name: 'workNoList',
              value: chunkWorkids,
            }),
          ],
          page: 1,
          pageSize: 10,
          // combineId: isDaily ? 2168 : 2372,
          combineId: isDaily ? 1224 : 1369,
        });
        return this.amdpDataQueryServiceProxy.queryDataSet(
          authParam,
          queryParam,
        );
      }),
    );
    const amdpResult = chunksAmdpResult.reduce(
      (pre, chunkAmdpResult) => {
        return {
          success: chunkAmdpResult.success && pre.success,
          dataRows: pre.dataRows.concat(
            get(chunkAmdpResult, 'data.dataRows', []),
          ),
        };
      },
      {
        success: true,
        dataRows: [],
      },
    );
    if (!amdpResult.success || !amdpResult.dataRows.length) {
      return [];
    }
    const userList: any[] = [];
    amdpResult.dataRows.forEach((dataRow: any) => {
      const userInfo: any = {};
      dataRow.dataFields.forEach((dataField) => {
        if (dto2UserField[dataField.name]) {
          userInfo[dto2UserField[dataField.name]] = dataField.value;
        }
      });
      userList.push(userInfo);
    });
    return userList;
  }


  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/query-page',
    middleware: ['handlerMiddleware']
  })
  async queryPage() {
    const { id, isPreConfig, isSpeConfig } = this.ctx.query;
    const res = await this.ssrService.queryPageById({ id });
    if (isPreConfig && res) {
      const tairClient = await this.ctx.tairManager.getClient({
        username: TAIR_USER,
      });
      const tairRes: any = await tairClient.get(`${res.project_name}_${res.page_name}_pre_config`);
      const tairData = tairRes.data ? JSON.parse(tairRes.data) : [];
      return tairData;
    }else if(isSpeConfig && res){
      const tairClient = await this.ctx.tairManager.getClient({
        username: TAIR_USER,
      });
      const tairRes: any = await tairClient.get(`fliggy-ssr-${res.project_name}-${res.page_name}`);
      const tairData = tairRes.data ? JSON.parse(tairRes.data) : [];
      return tairData;
    }
    return res
    
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/add-page',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async addPage() {
    let reqBody = this.ctx.request.body;
    try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};

    const { projectGroup, projectId, pageName, projectName, isLogin, isImmersive, userName, userWorkId, pathProjectName, pathPageName } = reqBody;
    const username = this.ctx.user && this.ctx.user.name || userName;
    const workId = this.ctx.user && this.ctx.user.workid || userWorkId;

    const checkIsExist = await this.ssrService.checkPageExist({
      projectId,
      pageName
    });
    if (checkIsExist) {
      return {
        success: false,
        errorMsg: '页面已存在'
      }
    } else {
      const res = await this.ssrService.createPage({
        gmt_create: formatDate(new Date()),
        gmt_modified: formatDate(new Date()),
        last_operator: `${username}-${workId}`,
        project_id: projectId,
        project_group: projectGroup || 'trip',
        project_name: projectName,
        page_name: pageName,
        need_login: isLogin || 2,
        path_project_name: pathProjectName || '',
        path_page_name: pathPageName || '',
        is_immersive: isImmersive || 2,
      })
      return res;
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/delete',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async delete() {
    const { id, pageName } = this.ctx.request.body;
    const res = await this.ssrService.delete({
      id,
      pageName
    })
    return res;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/setDowngrade',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async setDowngrade() {
    const { id, enforceDowngrade } = this.ctx.request.body;

    const envParams = {
      unit: this.ctx.aliEnv === 'dev'
        ? 'daily'
        : this.ctx.aliEnv === 'pre'
          ? 'pre'
          : 'center'
    };
    const diamondRes = await this.ctx.diamond.getConfig('route-map', 'render-html', envParams);
    const username = this.ctx.user && this.ctx.user.name;
    const workId = this.ctx.user && this.ctx.user.workid;
    const pageInfo = await this.ssrService.queryPageById({id})
    const pathProjectName = get(pageInfo, 'path_project_name', '');
    const pathPageName = get(pageInfo, 'path_page_name', '');
    const project_group = get(pageInfo, 'project_group', '') || 'trip';
    const project_name = get(pageInfo, 'project_name', '');
    const page_name = get(pageInfo, 'page_name', '');

    const pre_version = get(pageInfo, 'pre_version', '');
    const prod_version = get(pageInfo, 'prod_version', '');
    const version = (this.ctx.aliEnv === 'dev' || this.ctx.aliEnv === 'pre') ? pre_version : prod_version;
    const publish_type = (this.ctx.aliEnv === 'dev' || this.ctx.aliEnv === 'pre') ? 1 : 4;

    try {
      const diamondObj = JSON.parse(diamondRes);
      let newDiamond = {
        ...diamondObj
      };
      const pageKey = `/app/${project_group}/${pathProjectName || project_name}/pages/${pathPageName || page_name}`;
      newDiamond[pageKey] = {
        ...newDiamond[pageKey],
        enforceDowngrade: enforceDowngrade === 2 ? true : false
      };

      try{
        const recordRes = await this.releaseRecordService.create({status: 0, diamond_key: pageKey, diamond_value: JSON.stringify(newDiamond[pageKey]), gmt_create: formatDate(new Date())})
        if(!recordRes?.id){console.log("releaseRecordCreate failed", recordRes)}
      }catch (e) {console.log("releaseRecordCreate failed catch", e)}

      const updateRes = await this.ctx.diamond.publishSingle('route-map', 'render-html', JSON.stringify(newDiamond), envParams);
      const updateDB = await this.ssrService.updatePage({
        id,
        enforceDowngrade,
        last_operator: `${username}-${workId}`
      });
      await this.ssrService.createLog({
        gmt_create: formatDate(new Date()),
        page_id: id,
        publish_type,
        version,
        operator: `${username}-${workId}`,
      })
      return {
        updateRes,
        updateDB
      }
    } catch(err: any) {
      return {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/preheat',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async setPreheatConfig() {
    const { id, type } = this.ctx.request.body;

    const envParams = {
      unit: this.ctx.aliEnv === 'dev'
        ? 'daily'
        : this.ctx.aliEnv === 'pre'
          ? 'pre'
          : 'center'
    };
    const diamondRes = await this.ctx.diamond.getConfig('route-map', 'render-html', envParams);

    const pageInfo = await this.ssrService.queryPageById({id})
    const pathProjectName = get(pageInfo, 'path_project_name', '');
    const pathPageName = get(pageInfo, 'path_page_name', '');
    const project_group = get(pageInfo, 'project_group', '') || 'trip';
    const project_name = get(pageInfo, 'project_name', '');
    const page_name = get(pageInfo, 'page_name', '');

    try {
      const diamondObj = JSON.parse(diamondRes);
      let newDiamond = {
        ...diamondObj
      };
      const pageKey = `/app/${project_group}/${pathProjectName || project_name}/pages/${pathPageName || page_name}`;
      newDiamond[pageKey] = type === 1 ? {
        ...newDiamond[pageKey],
        refreshTime: `${(new Date()).getTime()}`
      } : type === 2 ? {
        ...newDiamond[pageKey],
        refreshTime: ''
      } : {
        ...newDiamond[pageKey],
        refreshTime: '',
        disablePreCache: true
      };

      try{
        const recordRes = await this.releaseRecordService.create({status: 0, diamond_key: pageKey, diamond_value: JSON.stringify(newDiamond[pageKey]), gmt_create: formatDate(new Date())})
        if(!recordRes?.id){console.log("releaseRecordCreate failed", recordRes)}
      }catch (e) {console.log("releaseRecordCreate failed catch", e)}
      
      const updateRes = await this.ctx.diamond.publishSingle('route-map', 'render-html', JSON.stringify(newDiamond), envParams);

      return {
        updateRes
      }
    } catch(err: any) {
      return {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/update-project',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async updateProject() {
    const { id, adminList, projectGroup, projectBusiness, pid, isAssociatedDef } = this.ctx.request.body;
    const res = await this.ssrService.updateProject({
      id,
      adminList,
      projectGroup,
      projectBusiness,
      isAssociatedDef,
      last_operator: `${this.ctx.user.name}-${this.ctx.user.workid}`,
      pid
    })
    return res;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/update-page',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async updatePage() {
    const { id, isLogin, projectGroup, pathProjectName, pathPageName, isImmersive, csrSameSite, isAssociatedDef, preStreamConfig, streamConfig, alarmConfig, isPreConfig, preConfig, isSpeConfig, speConfig, cdnConfig } = this.ctx.request.body;
    if (isPreConfig) {
      const pageRes = await this.ssrService.queryPageById({ id });
      if (pageRes) {
        const tairClient = await this.ctx.tairManager.getClient({
          username: TAIR_USER,
        });
        const tairRes: any = await tairClient.put(`${pageRes.project_name}_${pageRes.page_name}_pre_config`, preConfig);
        return tairRes;
      }
    }else if(isSpeConfig){
      const pageRes = await this.ssrService.queryPageById({ id });
      if (pageRes) {
        const tairClient = await this.ctx.tairManager.getClient({
          username: TAIR_USER,
        });
          // 使用灰度的tair，先读取，然后写入，避免灰度覆盖
        const targetRes:any = await tairClient.get(`fliggy-ssr-${pageRes.project_name}-${pageRes.page_name}`);
        let targetNewRes = {}
        if (targetRes && targetRes.data) {
          targetNewRes = {...JSON.parse(targetRes.data), speConfig}
        }else{
          targetNewRes = {speConfig}
        }
        const tairRes:any = await tairClient.put(`fliggy-ssr-${pageRes.project_name}-${pageRes.page_name}`, JSON.stringify(targetNewRes));
        console.log("targetRes.data", `fliggy-ssr-${pageRes.project_name}-${pageRes.page_name}`, targetRes.data, speConfig, JSON.stringify(targetNewRes))
        return tairRes;
      }
    } else {
      const res = await this.ssrService.updatePage({
        id,
        isLogin,
        projectGroup,
        pathProjectName,
        pathPageName,
        isImmersive,
        csrSameSite,
        isAssociatedDef,
        preStreamConfig,
        streamConfig,
        alarmConfig,
        last_operator: `${this.ctx.user.name}-${this.ctx.user.workid}`,
        cdnConfig
      })
      return res;
    }
    
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/log-list',
    middleware: ['handlerMiddleware']
  })
  async logList() {
    const { current, pageSize, id, publish_type, version } = this.ctx.query;
    const list = await this.ssrService.logList({
      current,
      pageSize,
      id,
      publish_type,
      version,
    })
    return list;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/preload-apply',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async preloadApply() {
    const { id, project, page, login, path, spmb } = this.ctx.request.body;
    // 创建并启动审批流
    const instance = await this.bpmsHSF.startProcessInstance(PROCESS_CODE, `${this.ctx.user.name} 申请预加载配置`, {
      project,
      page,
      path,
      login: login === 1 ? "否" : "是",
      all: "否",
      spmb
      // all: all === 1 ? "否" : "是"
    });
    if (instance && instance.processInstanceId) {
      const username = this.ctx.user && this.ctx.user.name;
      const workId = this.ctx.user && this.ctx.user.workid;
      await this.ssrService.updatePage({
        id,
        last_operator: `${username}-${workId}`,
        processInstanceId: instance.processInstanceId,
        ssrPreload: 2,
        spmb
      })
      return {
        instance: instance.processInstanceId
      }
    }
    return {
      instance: null
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/preload-callback',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async preloadCallback() {
    this.ctx.set('Content-Type', 'application/x-www-form-urlencoded');
    console.log('callback')
    console.log(JSON.stringify(this.ctx.request.body))
    const preload_apply_id = this.ctx.request.body[`#formInstId`];
    const apply_res = this.ctx.request.body[`#res`];
    const pageInfo = await this.ssrService.queryPageById({ preload_apply_id });
    console.log('callback pageinfo')
    console.log(JSON.stringify(pageInfo))
    if (pageInfo && pageInfo.id && pageInfo.ssr_preload === 2) {
      await this.ssrService.updatePage({
        id: pageInfo.id,
        ssrPreload: apply_res == '1' ? 3 : 4
      })
    }
    return {}
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/preload-publish',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async preloadPublish() {
    const { id, type } = this.ctx.request.body;
    if (type === 'cancel') {
      await this.ssrService.updatePage({
        id,
        ssrPreload: 1
      })
      return {
        res: true
      }
    }
    const pageInfo = await this.ssrService.queryPageById({ id });
    if (pageInfo.forbiddenTag) {
      return {
        res: false,
        errorMsg: '灰度间隔需3小时以上，请稍后再试'
      }
    } else {
      await this.ssrService.updatePage({
        id,
        ssrPreload: pageInfo.ssr_preload === 3 ? 6 :
        pageInfo.ssr_preload === 8 ? 5 : pageInfo.ssr_preload + 1
      })
      return {
        res: true
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/log-query',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async logQuery() {
    let reqBody = this.ctx.request.body;
    try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};

    let {
      env,
      logType,
      userId,
      userNick,
      pagePath,
      traceId,
      sqlStr,
      startTime,
      endTime,
    } = reqBody;

    // ER的日志都没有userNick，所以调会员接口获取一下userId
    if (!userId && userNick) {
      const userInfo = await this.hsfService.invoke({
        id: 'com.fliggy.ffa.ffabrain.service.userinfo.UserInfoService:1.0.0',
        group: 'HSF',
        method: 'query',
        parameterTypes: ['com.fliggy.ffa.ffabrain.model.params.UserInfoRequest'],
        args: [{ userIdOrNick: userNick }],
      }).catch(err => {
        console.log('getUserInfoError>>>>>', err.message);
        return '';
      });

      userId = get(userInfo, 'data.userId') || '';
    }

    const from = Math.floor(startTime / 1000);
    const to = Math.floor(endTime / 1000);

    let ssrQuery = `* and logType: 'summary'`;
    let erEntryQuery = `* and timeStamp: 'er_entry'`;
    let erLogQuery = `* and not timeStamp: 'er_entry'`;
    let erStreamQuery = `*`;
    let preloadQuery = `*`;
    if (sqlStr) {
      ssrQuery = sqlStr;
      erEntryQuery = sqlStr;
      erLogQuery = sqlStr;
      erStreamQuery = sqlStr;
      preloadQuery = sqlStr;

    } else {
      if (logType === 'error') {
        ssrQuery += ' and success: 0';
        erLogQuery += ' and erErrorMessage';
        erStreamQuery += ' and errorMsg';
      }

      if (userId) {
        ssrQuery += ` and userId=${userId}`;
        erEntryQuery += ` and userId=${userId}`;
        erLogQuery += ` and userId=${userId}`;
        erStreamQuery += ` and userId=${userId}`;
        preloadQuery += ` and userId=${userId}`;
      }
      if (pagePath) {
        ssrQuery += ` and url:'${pagePath}'`;
        erEntryQuery += ` and timeStamp:'${pagePath}'`;
        erLogQuery += ` and url:'${pagePath}'`;
        erStreamQuery += ` and reqUrl:'${pagePath}'`;
        preloadQuery += ` and href:'${pagePath}'`;
      }
      if (traceId) {
        ssrQuery += ` and traceId='${traceId}'`;
        erEntryQuery += ` and traceId='${traceId}'`;
        erLogQuery += ` and traceId='${traceId}'`;
        erStreamQuery += ` and traceId='${traceId}'`;
        preloadQuery += ` and traceId='${traceId}'`;
      }
    }

    const ssrLogParams = {
      projectName: 'fliggy-rax-ssr',
      logStoreName: env === 'pre' ? 'ssr_pre_log' : 'ssr_log',
      query: ssrQuery,
      from,
      to,
    };
    const erEntryParams = {
      projectName: 'fliggy-rax-ssr',
      logStoreName: env === 'pre' ? 'ssr_pre_server_log' : 'ssr_server_log',
      query: erEntryQuery,
      from,
      to,
    };
    const erLogParams = {
      projectName: 'fliggy-rax-ssr',
      logStoreName:  env === 'pre' ? 'ssr_pre_server_log' : 'ssr_server_log',
      query: erLogQuery,
      from,
      to,
    };
    const erStreamParams = {
      projectName: 'fliggy-rax-ssr',
      logStoreName: 'er_stream_log',
      query: erStreamQuery,
      from,
      to,
    };
    const preloadLogParams = {
      projectName: 'fl-prefetch',
      logStoreName:  env === 'pre' ? 'preload_pre_log' : 'preload_log',
      query: preloadQuery,
      from,
      to,
    }

    // ssr函数日志
    const ssrLogData = this.ssrService.querySSRLogData(ssrLogParams);
    // er入口日志
    const erEntryData = this.ssrService.querySSRLogData(erEntryParams);
    // er回源日志
    const erLogData = this.ssrService.querySSRLogData(erLogParams);
    // er流式日志
    const erStreamData = this.ssrService.querySSRLogData(erStreamParams);
    // 源站tair预加载
    const preloadData = this.ssrService.querySSRLogData(preloadLogParams);

    const reqList = [ssrLogData, erEntryData, erLogData, erStreamData, preloadData];

    const res = await Promise.all(reqList).then(result => {
      const [ ssrLogRes, erEntryRes, erLogRes, erStreamRes, preloadRes ] = result;
      return {
        ssrLog: {
          req: ssrLogParams,
          res: ssrLogRes,
        },
        erEntryLog: {
          req: erEntryParams,
          res: erEntryRes,
        },
        erLog: {
          req: erLogParams,
          res: erLogRes,
        },
        erStreamLog: {
          req: erStreamParams,
          res: erStreamRes,
        },
        preloadLog: {
          req: preloadLogParams,
          res: preloadRes,
        }
      }
    }).catch(err => {
      return {
        success: false,
        error: err,
      }
    });

    return res;
  }

  

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/ip-query',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async ipQuery() {
    let reqBody = this.ctx.request.body;
    try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};

    const ipInfo = await this.hsfService.invoke({
      id: 'com.taobao.trip.wireless.rate.IpLocationService:1.0.0',
      method: 'searchRegionByIp',
      group: 'HSF',
      parameterTypes: ['java.lang.String'],
      args: [reqBody.ip || ''],
    }).catch(err => {
      console.log('getIpInfoError>>>>>', err.message);
      return {};
    });

    return ipInfo;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/set-dcdn',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async setDcdn() {
    if (isProd) {
      const cdnRes = await this.ssrService.setDcdn(this.ctx.request.body);
      return cdnRes;
    }
    return ;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/get-dcdnkv',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async getDcdnKV() {
    const cdnRes = await this.ssrService.getDcdnKV(this.ctx.request.body);
    return cdnRes;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/set-dcdnkv',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async setDcdnKV() {
    const cdnRes = await this.ssrService.setDcdnKV(this.ctx.request.body);
    return cdnRes;
  }

  @ServerlessTrigger(ServerlessTriggerType.HSF)
  async staticKV(event:IKVParams) {
    const { path, key, value } = event; 
    if (path) {
      try {
        const tairClient = await this.ctx.tairManager.getClient({
          username: TAIR_USER,
        });
        const tairRes: any = await tairClient.get(`fliggy-ssr-preload-static-config`);
        const erData = tairRes.data ? JSON.parse(tairRes.data) : {};
        let newData = {};
        if (erData[path]) {
          if (erData[path].key && key && erData[path].key === key && value) {
            newData = {
              ...erData,
              [path]: {
                ...erData[path],
                updateConfig: {
                  ...erData[path].updateConfig,
                  [value]: (new Date()).getTime()
                }
              }
            }
          } else {
            newData = {
              ...erData,
              [path]: {
                ...erData[path],
                updateTime: (new Date()).getTime()
              }
            }
          }

          await tairClient.put(`fliggy-ssr-preload-static-config`, JSON.stringify(newData));

          return {
            success: true
          }
        } else {
          return {
            success: false,
            errorMsg: '不存在path路径的配置'
          }
        }
      } catch(err: any) {
        return {
          success: false,
          errorMsg: `系统异常：${err?.message}`
        }
      }
    } else {
      return {
        success: false,
        errorMsg: '缺少path参数'
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/pre-update',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async preloadUpdate() {
    const res = await this.ssrService.changePreConfig(this.ctx.request.body);
    return res;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/pre-ssr',
    middleware: ['handlerMiddleware']
  })
  async queryPressr() {
    const res = await this.pressrService.queryMain();
    return res;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/pre-ssr-detail',
    middleware: ['handlerMiddleware']
  })
  async queryPressrDetail() {
    const res = await this.pressrService.queryDetail(this.ctx.query)
    return res;
  }

  // 获取迭代列表
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/getDefIteration',
    middleware: ['handlerMiddleware']
  })
  async getDefIteration() {
    const { appName = ''} = this.ctx.query;
    const defDetail = await this.defService.getAppDetails({appIdorName: appName})
    const defId = get(defDetail, 'data.data.app.id', 0)
    if(!defId){return {data: null,success: false, error: '查询def信息失败'}}
    const iterationDetails = await this.defService.getIterationDetails({ appId: defId, status: '3' });
    const detailList = (get(iterationDetails, 'data.data.iterations.iterations') || [])
    return {data: detailList, success: true};
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/alarm',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async alarm() {
    const reqBody = this.ctx.request.body;
    try {
      // 筛选出报警列表
      let message = reqBody.body.split('\n').filter(item => {
        return item.indexOf('*') >= 0
      });
      message = message.map(item => {
        return item.replace('*', '')
      });
      console.log('sunfire reqbody', JSON.stringify(message))
      const promiseList:any = [];
      message.forEach(item => {
        // 提取项目名称和页面名称
        const projectName = (item.match(/\[(\S*)\,/)) && (item.match(/\[(\S*)\,/))[1];
        const pageName = (item.match(/\,(\S*)\]/)) && (item.match(/\,(\S*)\]/))[1];
        if (projectName && pageName) {
          const handlePromise = new Promise(async(resolve, reject) => {
            // 请求页面信息
            try {
              const pageInfo = await this.ssrService.queryPageByParams({
                project_name: projectName,
                page_name: pageName
              });
              if (pageInfo && pageInfo.alarm_config) {
                const alarmConfig = JSON.parse(pageInfo.alarm_config);
                if (alarmConfig.list && alarmConfig.list.length > 0) {
                  // 告警用户列表
                  const alarmUser = alarmConfig.list.map(lItem => {
                    return lItem.value
                  });
                  const alarmNumList = item.match(/(\d+(\.\d+)?)/g);
                  if (alarmConfig.rank && parseFloat(alarmNumList[0]) < alarmConfig.rank) {
                    // 发送报警
                    const title = 'SSR成功率报警';
                    const content = [,
                      `#### **${title}**`,
                      `- 您负责的页面:`,
                      `- ${projectName}/pages/${pageName}`,
                      `- 成功率小于${alarmConfig.rank}%,当前成功率为${alarmNumList[0]}%`,
                      `\n👉[详见后台监控](https://fl-miniwork.fc.alibaba-inc.com/#/ssr/detail?id=${pageInfo.id})`
                    ].join('\n');
                    await this.dingtalkService.notice(alarmUser, title, content);
                    resolve(true);
                  } else {
                    resolve(true);
                  }
                } else {
                  resolve(true);
                }
              } else {
                resolve(true);
              }
            } catch (e) {
              resolve(true);
            }
          });
          promiseList.push(handlePromise);
        }
      });
      await Promise.all(promiseList);
      return true;
    } catch (e) {
      return false;
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.TIMER, {
    type: 'every',
    value: '10',
  })
  async preloadTimer() {
    if (isProd) {
      // 从tair获取缓存的预加载配置，写入KV
      try {
        const tairClient = await this.ctx.tairManager.getClient({
          username: TAIR_USER,
        });
        const tairRes: any = await tairClient.get(`fliggy-ssr-preload-static-config`);
        let tairData = tairRes.data ? JSON.parse(tairRes.data) :  {};

        // 获取ER的KV配置
        const erConfig = await this.ssrService.getDcdnKV({
          nameSpace: 'fliggyrax_124215',
          key: 'static-config'
        });
        const erData = erConfig.data ? JSON.parse(erConfig.data) : {};

        // 合并tair和er的数据
        let newData = {
          ...erData
        };
        Object.keys(tairData).forEach(path => {
          const erPaegConfig = newData[path] || {};
          const tairPageConfig = tairData[path] || {};
          const newPaegConfig = {
            ...erPaegConfig,
            ...tairPageConfig,
            updateConfig: {
              ...(erPaegConfig.updateConfig || {}),
              ...(tairPageConfig.updateConfig || {}),
            }
          };
          
          newData[path] = newPaegConfig;
          if (tairPageConfig.updateConfig) {
            tairData[path].updateConfig = {};
          }
        });

        // 存入KV配置
        await this.ssrService.setDcdnKV({
          nameSpace: 'fliggyrax_124215',
          key: 'static-config',
          value: newData,
        })

        // 清空tair updateConfig配置
        await tairClient.put(`fliggy-ssr-preload-static-config`, JSON.stringify(tairData));
      } catch {}

      await this.ssrService.preTask();
    } 
  }

  @ServerlessTrigger(ServerlessTriggerType.MQ, {
    topic: 'TP_DEF_WORK',
    tags: 'SetPageGrey||TaskPublish',
  })
  async handleDEFGray(event: AliFC.MQEvent) {
    try {
      console.log("handleDEFGray start enter")
      const body = Buffer.from(event.body, 'base64').toString('utf-8');
      console.log("handleDEFGray start", body)
      let dataObj: any = {};
      try {
        dataObj = JSON.parse(body);
      } catch (err: any) {
        return {
          success: false,
          message: err.message,
        };
      }
      
      // def的发布metaQ消息会返回50/’50‘,这里需要兼容下
      dataObj.rate = ['0','5', '25','50'].includes(dataObj.rate) ? Number(dataObj.rate) : dataObj.rate;

      // 是灰度发布的消息
      const isGray = [0, 5, 25, 50, 'finish', 'cancel'].includes(dataObj.rate);
      
      if (!isGray) {
        return {
          errMsg: '不是灰度放量消息'
        };
      }
      
      let defProjectName = get(dataObj, 'app.project') || '';

      let defPageList = (dataObj.realpaths || []).map((res: any)=>{
        // 去掉def部分场景，返回灰度标识的干扰
        return res.replace("\/fedflow", "")
      });
      let defVersion = get(dataObj, 'iteration.version') || [];
      let userName = 'def自动关联';
      let workId = dataObj.operator || '';
      
      // 1. 先校验本次发布是否属于飞猪的ssr项目
      const projectInfo = await this.ssrService.queryProjectByName({projectName: defProjectName});
      // 找不到项目 或者 项目不和def关联，则return
      if (!projectInfo || projectInfo.is_associated_def !== 2) {
        return {
          errMsg: '找不到项目或者项目不和def关联'
        }
      };

      console.log("handleDEFGray projectInfo success")
      
      // 2. 查询项目下的页面列表，校验ssr页面是否在def发布的页面列表中
      const pageList = await this.ssrService.queryList({
        defPublish: true,
        projectName: defProjectName,
      }).then(res => {
        return res && res.pRes || [];
      }).catch(err => {
        return [];
      });

      console.log("handleDEFGray pageList", JSON.stringify(pageList))
      
      // 3. 筛选符合发布条件的页面
      const publishList: any = [];
      const grayList: any = [];
      const prepubList: any = [];
      // 由于def与miniwork的预发信息存在不一致的情况，所以还存在一种状态，就是miniwork里面没有灰度信息
      // 具体：https://aliyuque.antfin.com/remtwr/sh17vq/cq4hsd4eu733prpn?singleDoc# 《多渠道发布同步问题》
      pageList.forEach(item => {
        const pagePath = `/app/${item.project_group}/${item.project_name}/pages/${item.page_name}`;
        const pubPage = defPageList.find((url: string) => {
          // 页面名字要完全匹配，不能使用indexOf，会出现home-demo和home匹配上的情况
          // 解决部分业务在ssr项目中存的页面路径是home/index.html而不是home的情况
          const parts = pagePath.split('/').filter(Boolean); // 去掉空字符串（如开头的 /）
          const pageName = pagePath.endsWith('.html') ? parts.slice(-2).join('/') : parts.slice(-1)[0] || ''
          return url.indexOf(pagePath) > -1 && (pageName === item.page_name)
        });
        // 灰度放量，当ssr页面灰度版本号和def一致，则同步更新灰度百分比
        if (pubPage && item.gray_version === defVersion) {
          // 全量发布
          if (dataObj.rate === 'finish') {
            publishList.push({
              version: defVersion, 
              id: item.id, 
              projectGroup: item.project_group, 
              projectName: item.project_name, 
              pageName: item.page_name, 
              isMetaQ: true,
              userName, 
              userWorkId: workId,
            });
          } else if (dataObj.rate === 'cancel') {
            // 取消灰度
            grayList.push({
              version: defVersion, 
              id: item.id, 
              ratio: -1, 
              projectGroup: item.project_group, 
              projectName: item.project_name, 
              pageName: item.page_name, 
              isMetaQ: true,
              userName, 
              userWorkId: workId,
            });
          } else if ([0, 5, 25, 50].includes(dataObj.rate) && item.gray_ratio !== dataObj.rate) {
            // 当ssr页面的灰度版本和def不一致，则同步修改
            grayList.push({
              version: defVersion, 
              id: item.id, 
              ratio: dataObj.rate, 
              projectGroup: item.project_group, 
              projectName: item.project_name, 
              pageName: item.page_name, 
              isMetaQ: true,
              userName, 
              userWorkId: workId,
            });
          }
        }else if(pubPage && !item.gray_version && [0, 5, 25, 50].includes(dataObj.rate)){
          // 必须灰度过程中，不能是finish和cancel
          console.log("handleDEFGray addGray", item, defVersion)

          // 如果灰度版本不一致，很有可能不是通过命令行进行发布的,是通过def发布的；并未同步到miniwork
          grayList.push({
            version: defVersion, 
            id: item.id, 
            ratio: dataObj.rate, 
            projectGroup: item.project_group, 
            projectName: item.project_name, 
            pageName: item.page_name, 
            isMetaQ: true,
            userName, 
            userWorkId: workId,
          });
          // 这里顺路检查一下def的预发版本号，很有可能也是为成功同步的
          if(defVersion !== item.pre_version){
            prepubList.push({
              version: defVersion, 
              id: item.id, 
              projectGroup: item.project_group, 
              projectName: item.project_name, 
              pageName: item.page_name, 
              isMetaQ: true,
              userName, 
              userWorkId: workId,
            })
          }
        }
      });

      console.log("handleDEFGray grayList",JSON.stringify(grayList))
      // 4. 灰度/线上发布ssr页面
      const grayRes = await Promise.all(grayList.map(item => {
        return this.gray(item);
      }));
      
      // 同步线上版本
      const self = this;
      const batchPublish = async () => {
        for (let i = 0; i < publishList.length; i++) {
          const item = publishList[i] || {};
          await self.publish(item);
          await sleepFunc();
        }
      }
      const publishRes = await batchPublish();

      // 同步预发版本
      const batchPrepub = async () => {
        for (let i = 0; i < prepubList.length; i++) {
          const item = prepubList[i] || {};
          await self.prepub(item);
          await sleepFunc();
        }
      }
      const prepubRes = await batchPrepub();

      console.log('handleDEFGray res>>>', JSON.stringify(dataObj));
  
      return {
        grayRes,
        publishRes,
        prepubRes
      };
    } catch (err: any) {
      console.log('handleDEFGray err>>>', (err && err.message) || JSON.stringify(err));
      return {};
    }
  }

   // 回滚事件触发（提供给蓑雨发布后台（通过def的事件中心被动通知，再调用miniwork的函数通知我们页面回滚））
   @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/rollback',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async rollbackIteration(data) {
    let reqBody = data.hsfClient ?this.ctx.request.body : data;

    try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};

    const { projectName, pageName, defVersionId, userName, userWorkId } = reqBody;

    const defDetail = await this.defService.getAppIterationByIdNew({defVersionId})
    const version = get(defDetail, 'data.data.iteration.version', 0)
    
    if(!version){return {data: null,success: false,defDetail, errMsg: '查询def版本信息失败'}}

    const projectInfo =  await this.ssrService.queryProjectByName({ projectName });

    if (!projectInfo || projectInfo.is_associated_def !== 2) {
      return {
        errMsg: '找不到SSR项目或者SSR项目不和def关联', success: false
      }
    };

    // 获取页面的id
    const pageList = await this.ssrService.queryList({
      current: 1,
      pageSize: 40,
      projectName,
      isMy: false,
      userWorkId: userWorkId || (this.ctx.user && this.ctx.user.workid),
      isSearch: true
    });

    const currentProject = get(pageList, 'res', []).find( r=>r.project_name === projectName)

    const currentPage = get(currentProject, 'children', []).find(r=>r.page_name === pageName)

    if (!currentProject || !currentPage) {
      return {
        errMsg: '找不到SSR页面相关信息', 
        success: false
      }
    };

    // 获取页面的迭代列表，验证版本号是否正确
    const iterationList = await this.ssrService.logList({
      current: 1,
      pageSize: 40,
      id: currentPage.id,
      publish_type: 4
    })

    const currentVersion = get(iterationList, 'data', []).find(r=>r.version === version)

    // 验证version是否正确合法
    if(!currentVersion){
      return {
        errMsg: '未匹配到SSR对应的版本，请检查回滚版本是否正确', 
        success: false
      }
    }

    try{
      const publishData = await this.publish({
        id: currentPage.id,
        projectName: projectName,
        pageName: pageName,
        version,
        userName,
        userWorkId: userWorkId || (this.ctx.user && this.ctx.user.workid)
      })
  
      if(publishData && !publishData.errorMsg){
        return {
          data: publishData, 
          success: true
        };
      }else{
        return {
          data: publishData, 
          success: false, 
          errMsg: get(publishData, 'errorMsg', 'SSR回滚失败，请去SSR管理平台手动回滚'),
          errData: {
            id: currentPage.id,
            projectName: projectName,
            pageName: pageName,
            version,
            userName,
            userWorkId: userWorkId || (this.ctx.user && this.ctx.user.workid)
          }
        }
      }

    }catch(e: any){
      return {success: false, errMsg: e.message}
    }

    
  }

     // 预发事件触发（提供给蓑雨发布后台（通过def的事件中心被动通知，再调用miniwork的函数通知我们页面预发））
     @ServerlessTrigger(ServerlessTriggerType.HTTP, {
      path: '/api/ssr-detail/prepub-def',
      method: 'post',
      middleware: ['handlerMiddleware']
    })
    async prepubDef(data) {
      let reqBody = data.hsfClient ?this.ctx.request.body : data;

      console.log("prepub-def", reqBody)
  
      try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};
  
      const { projectName, defVersionId, userName, userWorkId, extData } = reqBody;
  
      const defDetail = await this.defService.getAppIterationByIdNew({defVersionId})

      const version = get(defDetail, 'data.data.iteration.version', 0)
      
      if(!version){return {data: null,success: false,defDetail, errMsg: '查询def版本信息失败'}}
  
      const projectInfo =  await this.ssrService.queryProjectByName({ projectName });
  
      if (!projectInfo || projectInfo.is_associated_def !== 2) {
        return {
          errMsg: '找不到SSR项目或者SSR项目不和def关联', success: false
        }
      };
  
      // 获取页面的id
      const pageList = await this.ssrService.queryList({
        current: 1,
        pageSize: 40,
        projectName,
        isMy: false,
        userWorkId: userWorkId || (this.ctx.user && this.ctx.user.workid),
        isSearch: true
      });

      // 清洗cdn发布的数据，找到都有哪些页面在发布
      const cdnUrls = get(extData, 'deploy.cdnUrls', []).filter(r=> r && r.indexOf('/node/') !== -1).map(r=>r.slice(r.indexOf("/node/") + 6, r.lastIndexOf(".js")))

      // 处理函数预发
      const handlePrepubByPageName = async (pageName)=>{

        const currentProject = get(pageList, 'res', []).find( r=>r.project_name === projectName)
  
        const currentPage = get(currentProject, 'children', []).find(r=>r.page_name === pageName)
        
        if (!currentProject || !currentPage) {
          return {
            errMsg: '未发布此页面 || 找不到SSR页面相关信息', 
            success: false
          }
        };
    
        // 不用这部分操作了，因为如果能从cdn获取到node的编译结果，那一定是有这个迭代的；这个版本核对不一定有效，因为迭代如果也是def建立的，那么就无法送ssr的日志里面查到这个迭代了

        // 获取页面的迭代列表，验证版本号是否正确
        // const iterationList = await this.ssrService.logList({
        //   current: 1,
        //   pageSize: 1500,
        //   id: currentPage.id,
        //   publish_type: 4
        // })
    
        // const currentVersion = get(iterationList, 'data', []).find(r=>r.version === version)
    
        // // 验证version是否正确合法
        // if(!currentVersion){
        //   return {
        //     errMsg: '未匹配到SSR对应的版本，请检查回滚版本是否正确', 
        //     success: false
        //   }
        // }
    
        try{
          const publishData = await this.prepub({
            id: currentPage.id,
            projectName: projectName,
            pageName: pageName,
            version,
            userName,
            userWorkId: userWorkId || (this.ctx.user && this.ctx.user.workid)
          })
      
          if(publishData && !publishData.errorMsg){
            return {
              data: publishData, 
              success: true
            };
          }else{
            return {
              data: publishData, 
              success: false, 
              errMsg: get(publishData, 'errorMsg', 'SSR预发失败，请去SSR管理平台手动预发'),
              errData: {
                id: currentPage.id,
                projectName: projectName,
                pageName: pageName,
                version,
                userName,
                userWorkId: userWorkId || (this.ctx.user && this.ctx.user.workid)
              }
            }
          }
    
        }catch(e: any){
          return {success: false, errMsg: e.message}
        }
    
      }

      const result: any = [];

      for(let i=0; i < cdnUrls.length; i++) {
        const res = await handlePrepubByPageName(cdnUrls[i])
        result.push({pageName: cdnUrls[i], result: JSON.stringify(res)})
      }
      
      return result;
      
    }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/getDiamondWithDownGrade',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async getDiamondWithDownGrade(data) {

    let reqBody = data.hsfClient ?this.ctx.request.body : data;
    try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};
    const { env } = reqBody;
    
    let preloadConfig: any = await this.ctx.diamond.getConfig('preload-config', 'white-host-list', {unit: env});
    preloadConfig = JSON.parse(preloadConfig);

    return {data: preloadConfig,env, success: true};
    
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/setDiamondWithDownGrade',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async setDiamondWithDownGrade(data) {

    let reqBody = data.hsfClient ?this.ctx.request.body : data;
    try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};
    const { env, type, downgradeUidStr, downgradePageStr } = reqBody;
    
    // 获取该环境的diamond，重新设置

    let preloadConfig: any = await this.ctx.diamond.getConfig('preload-config', 'white-host-list', {unit: env});
    preloadConfig = JSON.parse(preloadConfig);

    if(type === 'uid'){
      preloadConfig.downgradeUid = downgradeUidStr.split(",")
    }else{
      preloadConfig.downgradePage = downgradePageStr.split(",")
    }

    const updateRes = await this.ctx.diamond.publishSingle('preload-config', 'white-host-list', JSON.stringify(preloadConfig), {unit: env});

    return {data: updateRes,env, success: true};
    
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/cdn-hot',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async cdnHot() {
    let reqBody = this.ctx.request.body;
    try { typeof reqBody === 'string' && (reqBody = JSON.parse(reqBody)) } catch {};
    if (reqBody && reqBody.length > 0) {
      const res = await this.ssrService.cdnHot(reqBody);
      return res;
    }
    return ;
  }

  // 提供给edith-dev-tool，查询已经接入ssr的列表
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/getSSRIteration',
    middleware: ['handlerMiddleware']
  })
  async getSSRIteration() {
    console.log("调用getSSRIteration日志")
    const { page, project } = this.ctx.query;
    console.log("getSSRIteration日志内容", page, project, JSON.stringify(this.ctx.request))
    // 先查询id
    const pageInfo = await this.ssrService.queryPageByParams({
      project_name: project,
      page_name: page
    })

    if(pageInfo && pageInfo.id){
      // // 再查发布日志
      const list = await this.ssrService.logList({
        current: 1,
        pageSize: 1000,
        id: pageInfo.id
      })
      return list;
    }
    return {}
  }

  // 获取服务端预加载的key
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/ssr-detail/get-preload-cache-key',
    middleware: ['handlerMiddleware']
  })
  async getPreloadCacheKey() {
    // env: pre|center
    const { query } = this.ctx;

    let preloadConfig: any = await this.ctx.diamond.getConfig('preload-config', 'white-host-list', {unit: query.env});
    preloadConfig = JSON.parse(preloadConfig);

    // 获取端名称
    const ua = decodeURIComponent(query.ua)
    const appInfo = ua.match(/AliApp\((.*)\)/) || {};
    let appName = appInfo[1] && appInfo[1].split('/')[0];
    if (
      appName === 'TB' ||
      appName === 'taobao' ||
      appName === 'com.taobao.taobao'
    ) {
      appName = 'TB';
    }
    // 增加对支付宝发起service worker请求的兼容
    if (!appName && ua.match(/\sMYWeb\//)) {
      appName = 'AP'
    }
    const newReqUrl = new URL(decodeURIComponent(query.tarUrl))
    const { pathname, searchParams } = newReqUrl;
    // 获取入参
    const searchStr = searchParams.toString().split('&');
    let searchObj: any = {};
    searchStr.forEach((item) => {
      const itemObj = item.split('=');
      searchObj[itemObj[0]] = itemObj[1];
    })

    const newCtx = {
      ...this.ctx, 
      request: {query: searchObj, path: pathname}, 
      headers: {cookie: `unb=${decodeURIComponent(query.unb)}`},
      env: this.ctx.env
    }

    const getPreloadCacheKey = (ctx: any, client: string, config: any) => {
      const preloadConfig = config || {};
      const { query = {}, path = '' } = ctx?.request || {};
      const pathKey = path.startsWith('/app/') ? path : path.replace(/\/([^/]+)/, '');
      const pathQueryWhite = preloadConfig?.pathQueryWhite || {};
      const queryWhiteList = query['_fz_cli_cache_key'] ? ['_fz_cli_cache_key'] : pathQueryWhite[pathKey] ? pathQueryWhite[pathKey] : [];
      const queryBlackList = preloadConfig?.queryBlackList || [];
      const headers = ctx.request.headers || {};
      const cookies = headers.cookie || headers['s-cookie'] ? cookie.parse(headers.cookie || headers['s-cookie']) : {};
      const munb = cookies.munb;
      const unb = cookies.unb;
      const queryStr = genQueryStr(query, queryWhiteList, queryBlackList);
      // const clientIP = headers["Ali-Cdn-Real-Ip"]; //为什么没登录态的走cdn ip，因为存在大量的可能命中一份无登录态缓存
      const preEnv = ctx.env === 'prod' ? '' : `&envTag=${ctx.env}`;

      return {
        ky: `PreloadK_${client}_${munb || unb || 'noUid'}_${pathKey}?${md5(queryStr)}${preEnv}`,
        kyStr: `PreloadK_${client}_${munb || unb || 'noUid'}_${pathKey}?${queryStr}${preEnv}`,
        kyQuery: queryStr,
        uid: munb || unb || 'noUid',
      }
    }
    return getPreloadCacheKey(newCtx as any, appName, preloadConfig)?.kyStr
  }

}

async function getDefPageInfo(opt) {
  const {
    group,
    projectName,
    pageName,
    version,
    defAppIdorName,
    defService,
  } = opt;
  // 构造def灰度页面路径
  const defPagePath = `/fedflow/app/${group}/${projectName}/pages/${pageName}`;
  // def后台灰度中的页面列表
  const getGreyPages = await defService.getGreyPages({ appIdorName: defAppIdorName });
  const defGreyPageList = (getGreyPages.data && getGreyPages.data.data || []).filter(item => item.realPath === defPagePath);
  // 1. 当前页面是否在灰度列表中
  const isInDefGreyPages = defGreyPageList.length > 0;

  if (!isInDefGreyPages) {
    return {
      isInDefGrey: false,
    }
  }

  // 获取灰度中的迭代
  const iterationDetails = await defService.getAppDetails({ appIdorName: defAppIdorName }).then(async res => {
    const appId = get(res, 'data.data.app.id', '');
    const detail = await defService.getIterationDetails({ appId });
    const detailList = (get(detail, 'data.data.iterations.iterations') || []).filter(item => {
      // 最后一次任务的发布环境：2为线上
      const lastTaskPubEnv = get(item, 'lastTask.pub_env')
      // 最后一次任务的发布状态：3为成功
      const lastTaskPubStatus = get(item, 'lastTask.pub_status')
      return lastTaskPubEnv === 2 && lastTaskPubStatus === 3;
    });

    return detailList[0];
  }).catch(err => {
    return null;
  });
  // 灰度迭代版本
  const defGreyVersion = iterationDetails && iterationDetails.version || '';
  // 2. 当前版本和灰度迭代版本是否一致
  const isGreyVersion = version === defGreyVersion;

  if (!isGreyVersion) {
    return {
      isInDefGrey: false,
    }
  }

  // 查询def后台灰度状态
  const defPageGreyTip = await defService.getPageGreyTip({ appIdorName: defAppIdorName });
  const defPageGreyStatus = get(defPageGreyTip, 'data.data.greytime.status');
  // 3. 是否正在灰度中
  const isInDefGrey = ['internal', '0', '5', '25', '50'].includes(defPageGreyStatus);
  // 是否可以完成灰度，进行线上发布
  const canPublishDefPage = defPageGreyStatus === '50';
  
  return {
    isInDefGrey,
    canPublishDefPage,
  }
}
