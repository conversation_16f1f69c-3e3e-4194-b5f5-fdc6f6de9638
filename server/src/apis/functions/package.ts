import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import PackageService from '@/apis/service/package';
import ProjectService from '@/apis/service/project';
import IterBranchService from '@/apis/service/iter-branch';
import FreeDevBranchService from '@/apis/service/free-dev-branch';
import DevBranchService from '@/apis/service/dev-branch';
import StabilityService from '@/apis/service/stability';
import { useTmpDir, useGit } from '@/apis/middleware/hooks';
import { IProject } from '@/apis/interface/project';
import BaseHandler from '@/apis/functions/base';
import { getAppStructure } from '@/apis/utils/package';
import { IPackageParams } from '../interface/package';

@Provide()
export class PackageHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  packageService!: PackageService;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  devBranchService!: DevBranchService;

  @Inject()
  freeDevBranchService!: FreeDevBranchService;

  @Inject()
  StabilityService!: StabilityService;

  /** 获取包结构 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/package/get-structure',
    middleware: ['handlerMiddleware']
  })
  async getStructure() {
    const reqParams = this.ctx.query as IGetStructure;
    const freeDevId = Number(reqParams.freeDevId);
    const devId = Number(reqParams.devId);
    const iterId = Number(reqParams.iterId);
    let projectName = reqParams.projectName;
    let branchName: string | undefined;
    let branchUrl: string | undefined;
    let dist: string | undefined;
    let reportAnalyzed: string | undefined;
    let project: IProject | null;
    let defUploadPkgRes: string | undefined;

    console.log('lingyue getStructure >>>>>>>>>>', JSON.stringify(reqParams))
    // todo lingyue
    // 调整下代码，需要拉一下最新分支的包信息，用户给指定迭代的对比，如果没有分支id，则默认是最新分支包信息

    if (iterId) {
      const iterBranch = await this.iterBranchService.get(iterId);
      if (!iterBranch) throw Error(`获取迭代分支 ${iterId} 失败`);

      branchName = iterBranch.rcGitBranch?.name;
      branchUrl = iterBranch.rcGitBranch?.url;
      dist = iterBranch.dist;
      reportAnalyzed = iterBranch.reportAnalyzed;
      projectName = iterBranch.projectName;
      defUploadPkgRes = iterBranch.defUploadPkgRes;
    } else if (freeDevId) {
      const freeDevBranch = await this.freeDevBranchService.get(freeDevId);
      if (!freeDevBranch) throw Error(`获取游离开发分支 ${freeDevId} 失败`);

      branchName = freeDevBranch.gitBranch.name;
      branchUrl = freeDevBranch.gitBranch.url;
      dist = freeDevBranch.dist;
      reportAnalyzed = freeDevBranch.reportAnalyzed;

      // @ts-ignore
      const iterBranch = await this.iterBranchService.get(freeDevBranch.iterId);
      if (!iterBranch) throw Error(`获取迭代分支 ${iterId} 失败`);
      projectName = iterBranch.projectName;
    } else if (devId) {
      const devBranch = await this.devBranchService.get(devId);
      if (!devBranch) throw Error(`获取开发分支 ${devId} 失败`);

      branchName = devBranch.gitBranch.name;
      branchUrl = devBranch.gitBranch.url;
      dist = devBranch.dist;
      reportAnalyzed = devBranch.reportAnalyzed;

      const iterBranch = await this.iterBranchService.get(devBranch.iterId);
      if (!iterBranch) throw Error(`获取迭代分支 ${iterId} 失败`);
      projectName = iterBranch.projectName;
    } else if (projectName) {
      project = await this.projectService.get(projectName);
      if (!project) throw Error(`未查询到项目 ${projectName}`);

      const latestPublishedIterBranch = await this.iterBranchService.getLatestPublishedByProject(projectName);
      if (latestPublishedIterBranch) {
        console.log('lingyue latestPublishedIterBranch >>>>>>>>>>>', JSON.stringify(latestPublishedIterBranch))
        branchName = latestPublishedIterBranch.rcGitBranch?.name;
        branchUrl = latestPublishedIterBranch.rcGitBranch?.url;
        dist = latestPublishedIterBranch.defDist || latestPublishedIterBranch.dist;
        reportAnalyzed = latestPublishedIterBranch.reportAnalyzed;
        defUploadPkgRes = latestPublishedIterBranch.defUploadPkgRes;
      } else {
        branchName = 'master';
        branchUrl = project.gitRepo;
      }
    } else {
      throw Error('projectName, iterId, devId 必传其一');
    }

    project ??= await this.projectService.get(projectName);

    if (!branchName || !branchUrl) throw Error('获取分支失败');

    // 2. 获取当前分支包结构
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const { projectTmpDirPath, gitRepoInfo } = await useGit(tmpDirPath, { branchUrl, branchName, cloneSingleBranch: true })

    const [appStructurePromiseSettledRes, packageSizePromiseSettledRes] = await Promise.allSettled([
      getAppStructure({
        projectTmpDirPath,
        gitRepoInfo,
        branchName,
      }),
      defUploadPkgRes ? this.packageService.getUploadPackageSize(defUploadPkgRes, branchName, dist, tmpDirPath) : (dist ? this.packageService.getPackageSize(dist, tmpDirPath) : Promise.resolve(null))
    ]);

    // 删除临时文件夹
    removeTmpDir();

    // 3. 获取最近一次线上发布版本的包结构（新建一个临时文件夹是因为拉之前的包的时候，会因为文件夹名称一致导致覆盖）
    const { tmpDirPath: newTmpDirPath, removeTmpDir: newRemoveTmpDir } = useTmpDir();
    const latestPublishedIterBranch = await this.iterBranchService.getLatestPublishedByProject(projectName);
    const latestPublishedBranchName = latestPublishedIterBranch?.rcGitBranch?.name || '';
    const latestPublishedBranchUrl = latestPublishedIterBranch?.rcGitBranch?.url || '';
    const {
      projectTmpDirPath: latestPublishedProjectTmpDirPath,
      gitRepoInfo: latestPublishedGitRepoInfo
    } = await useGit(newTmpDirPath, {
      branchUrl: latestPublishedBranchUrl,
      branchName: latestPublishedBranchName,
      cloneSingleBranch: true
    });
    let lastPublishedStructure = [];
    try {
      const lastPublishedStructureData = await getAppStructure({
        projectTmpDirPath: latestPublishedProjectTmpDirPath,
        gitRepoInfo: latestPublishedGitRepoInfo,
        branchName: latestPublishedBranchName,
      });
      // @ts-ignore
      lastPublishedStructure = lastPublishedStructureData?.packages;
    } catch {
      this.ctx.logger.info(`获取上一次线上发布版本的包结构失败`)
    }

    // 3. 获取已经发布的历史版本
    let publishedIterBranchList = await this.iterBranchService.getPublishedByProjectList(projectName);
    // 反转数组
    publishedIterBranchList = publishedIterBranchList.reverse();
    const publishedReportAnalyzedListRes = await Promise.all(publishedIterBranchList.map(item => {
      const rcBranchName = item?.rcGitBranch?.name || '';
      // 获取def构建产物体积（最终压缩后的）
      if (item.defUploadPkgRes) {
        return this.packageService.getUploadPackageSize(item.defUploadPkgRes, rcBranchName);
      }
      return item.reportAnalyzed ? this.packageService.getReportAnalyzedFile(item.reportAnalyzed, newTmpDirPath, rcBranchName) : Promise.resolve(null)
    }));
    publishedIterBranchList = publishedIterBranchList.map(item => {
      const rcBranchName = item.rcGitBranch?.name;
      const itemPackageSize = publishedReportAnalyzedListRes.find(p_item => p_item?.branchName === rcBranchName);
      return {
        ...item,
        packageSize: itemPackageSize,
      }
    }).sort((a, b) => {
      // 按发布时间排序
      return Number(a.publishDay.replace(/-/g, '')) - Number(b.publishDay.replace(/-/g, ''));
    });
    // 删除临时文件夹
    newRemoveTmpDir();


    return {
      appStructure: appStructurePromiseSettledRes.status === 'fulfilled' ? appStructurePromiseSettledRes.value : null,
      packageSize: packageSizePromiseSettledRes.status === 'fulfilled' ? packageSizePromiseSettledRes.value : null,
      lastPublishedStructure: lastPublishedStructure.length > 0 ? {
        branName: latestPublishedBranchName,
        structureList: lastPublishedStructure,
      } : {},
      publishedIterBranchList,
      dist,
      reportAnalyzed,
      project,
      branchName,
      branchUrl
    }
  }

  /** 指定分支获取包结构 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/package/get-structure-by-branch',
    middleware: ['handlerMiddleware']
  })
  async getStructureByBranch() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.query, ['branchName', 'branchUrl'])
    const { branchName, branchUrl } = this.ctx.query as { branchName: string, branchUrl: string };

    // git clone 分支代码
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const { projectTmpDirPath, gitRepoInfo } = await useGit(tmpDirPath, { branchUrl, branchName, cloneSingleBranch: true })

    // 获取小程序结构
    const appStructure = await getAppStructure({
      projectTmpDirPath,
      gitRepoInfo,
      branchName,
    });

    removeTmpDir();

    return appStructure
  }

    /** 指定分支获取包结构 */
    @ServerlessTrigger(ServerlessTriggerType.HSF)
    async getStructureByMainBranch(event: IPackageParams) {
      // 校验必传参数
      this.checkRequiredParams(event, ['branchName', 'branchUrl'])
      const { branchName = '', branchUrl = '' } = event;
  
      // git clone 分支代码
      const { tmpDirPath, removeTmpDir } = useTmpDir();
      const { projectTmpDirPath, gitRepoInfo } = await useGit(tmpDirPath, { branchUrl, branchName, cloneSingleBranch: true })
  
      // 获取小程序结构
      const appStructure = await getAppStructure({
        projectTmpDirPath,
        gitRepoInfo,
        branchName,
      });
  
      removeTmpDir();
  
      return appStructure
    }

  /** 指定项目获取包结构 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/package/get-structure-by-project',
    middleware: ['handlerMiddleware']
  })
  async getStructureByProject() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.query, ['projectName'])
    const { projectName } = this.ctx.query as { projectName: string };

    // 获取项目
    const project = await this.projectService.get(projectName);
    if (!project?.gitRepo) throw Error('项目不存在或缺少gitRepo')

    // git clone 分支代码
    const branchName = 'master';
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const { projectTmpDirPath, gitRepoInfo } = await useGit(tmpDirPath, { branchUrl: project.gitRepo, branchName, cloneSingleBranch: true })

    // 获取小程序结构
    const appStructure = await getAppStructure({
      projectTmpDirPath,
      gitRepoInfo,
      branchName,
    });

    removeTmpDir();

    return appStructure
  }

  /** 指定分支包体积分析 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/package/analyze-branch-size',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async analyzeBranch() {
    const { gmtModified, branchList, projectName } = this.ctx.request.body;
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    // 含当前迭代版本，需要查询上一个版本的包体积，游离分支查最新迭代版本
    const publishedIterBranch = await this.iterBranchService.getPrevPublishedIter(projectName, gmtModified);

    const publishedBranchName = publishedIterBranch?.rcGitBranch?.name || '';
    const publishPackageSize = publishedIterBranch ? await this.packageService.getReportAnalyzedFile(publishedIterBranch?.reportAnalyzed || '', tmpDirPath, publishedBranchName) : null;
    const branchListPackageSize = await Promise.all(branchList.map(item => this.packageService.getReportAnalyzedFile(item.reportAnalyzed, tmpDirPath, item.name)));

    const getSizeMap = (sizeInfo) => {
      if (!sizeInfo) return null;
      const res: Record<string, number> = {};
      res.total = sizeInfo.buildSize;
      return sizeInfo.subPackage.reduce((pre, item) => {
        pre[item.subPackageName] = item.buildSize;
        return pre;
      }, res);
    };

    const publishedSizeMap = getSizeMap(publishPackageSize);
    // 比较分支体积
    const compareBranchSize = (branchSize) => {
      const res = {};
      Object.keys(branchSize).forEach(packageName => {
        const diffVal = branchSize[packageName] - publishedSizeMap[packageName];
        if (diffVal !== 0) {
          res[packageName] = diffVal;
        }
      });
      return res;
    };
    const branchSizeMapList = branchListPackageSize.map(branch => {
      const branchSizeMap = getSizeMap(branch);
      const sizeDiffInfo = publishedSizeMap ? compareBranchSize(branchSizeMap) : null;
      return {
        branchName: branch.branchName,
        branchSizeMap,
        sizeDiffInfo,
      };
    });

    removeTmpDir();
    return {
      publishedBranchName,
      branchSizeMapList,
      publishedSizeMap,
    }
  }

  /** 指定分支包体积分析 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/package/analyze-iter-size',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async analyzeIterSize() {
    const { gmtModified, projectName, defUploadPkgRes } = this.ctx.request.body;
    return this.StabilityService.getIterSizeInfo({ gmtModified, projectName, defUploadPkgRes });
  }
}

interface IGetStructure {
  /** 项目名称 */
  projectName?: string;
  /** 迭代分支id */
  iterId?: string;
  /** 开发分支id */
  devId?: string;
  /** 游离开发分支 */
  freeDevId?: string;
}
