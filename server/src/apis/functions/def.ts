import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import DefService from '@/apis/service/def';
import ProjectService from '@/apis/service/project';
import DingtalkService from '@/apis/service/dingtalk';

@Provide()
export class DefHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  defService!: DefService;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  dingtalkService!: DingtalkService;

  // 创建迭代
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/create-iteration',
    method: 'post',
  })
  async createIteration() {
    const { repo, name, description, branch, version, trunk, aoneBinds } = this.ctx.request.body;
    this.ctx.body = await this.defService.createIteration({ repo, name, description, branch, version, trunk, aoneBinds })
  }

  // 根据 repo 和 branch 获取迭代基本信息(id 等)
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/get-iteration-id',
    method: 'get',
  })
  async getIterationId() {
    const { repo, branch } = this.ctx.query;
    this.ctx.body = await this.defService.getIterationId({ repo, branch })
  }

  // 根据迭代id获取迭代基本信息
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/get-iteration-info',
    method: 'get',
  })
  async getIterationInfoById() {
    const { iterationId } = this.ctx.query;
    this.ctx.body = await this.defService.getIterationInfoById({ iterationId })
  }

  // 废弃迭代
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/abandon-iteration',
    method: 'post',
  })
  async abandonIteration() {
    const { iterationId } = this.ctx.request.body;
    this.ctx.body = await this.defService.abandonIteration({ iterationId })
  }

  // 集成区绑定变更
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/bind-branch-to-iteration',
    method: 'post',
  })
  async bindBranchToIteration() {
    const { iterationId, branchIds } = this.ctx.request.body;
    this.ctx.body = await this.defService.bindBranchToIteration({ iterationId, branchIds })
  }

  // 集成区解绑变更
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/unbind-branch-to-iteration',
    method: 'post',
  })
  async unbindBranchToIteration() {
    const { iterationId, id } = this.ctx.request.body;
    this.ctx.body = await this.defService.unbindBranchToIteration({ iterationId, id })
  }

  // 创建变更
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/create-branch',
    method: 'post',
  })
  async createBranch() {
    const { appId, branch, branchType, description, aoneBinds } = this.ctx.request.body;
    this.ctx.body = await this.defService.createBranch({ appId, branch, branchType, description, aoneBinds })
  }

  // 删除变更
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/delete-branch',
    method: 'post',
  })
  async deleteBranch() {
    const { appId, branchId } = this.ctx.request.body;
    this.ctx.body = await this.defService.deleteBranch({ appId, branchId })
  }

  // 创建迭代发布任务
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/create-iter-publish-task',
    method: 'post',
  })
  async createIterPublishTask() {
    const { updateDB, taskType, devId, iterId, iterationId, pub_env } = this.ctx.request.body;
    this.ctx.body = await this.defService.createIterPublishTask({ updateDB, taskType, devId, iterId, iterationId, pub_env })
  }

  // 查询迭代发布任务结果
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/get-iter-publish-task-detail',
    method: 'get',
  })
  async getIterPublishTaskDetail() {
    const { updateDB, taskType, devId, iterId, defTaskId } = this.ctx.query;
    this.ctx.body = await this.defService.getIterPublishTaskDetail({ updateDB, taskType, devId, iterId, defTaskId })
  }

  // 查询迭代关联的门神任务
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/check-mensheng',
    method: 'get',
  })
  async checkMensheng() {
    // const { iterationId } = this.ctx.query;
    this.ctx.body = await this.defService.checkMensheng()
  }

  // 正式发布前的检查
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/publish-check',
    method: 'get',
  })
  async publishCheck() {
    const { defIterId } = this.ctx.query;
    this.ctx.body = await this.defService.publishCheck({ defIterId })
  }

  // 查询迭代全部发布任务
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/get-iter-publish-tasks',
    method: 'get',
  })
  async getIterPublishTasks() {
    const { appId } = this.ctx.query;
    this.ctx.body = await this.defService.getIterPublishTasks({ appId })
  }

  // 查询上一次提交以及主干同步状态
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/get-last-commit',
    method: 'get',
  })
  async getLastCommit() {
    const { iterationId } = this.ctx.query;
    this.ctx.body = await this.defService.getLastCommit({ iterationId })
  }

  // 查询CR
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/get-cr',
    method: 'post',
  })
  async getCR() {
    const { iterationId } = this.ctx.request.body;
    this.ctx.body = await this.defService.getCR({ iterationId })
  }

  // 创建CR
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/create-cr',
    method: 'post',
  })
  async createCR() {
    const { projectName, iterationId, devbranchId, title, description, ids } = this.ctx.request.body;
    const res = await this.defService.createCR({ iterationId, devbranchId, title, description, ids })

    if (res.success) {
      // 模块类型且是下单页冻结集成后，自动机器人发群消息通知CR
      if (['buy-phoenix', 'simu-test'].indexOf(projectName) !== -1) {
        // 获取项目详情
        const project = await this.projectService.get(projectName, { needDingtalkRobots: true });
        if (!project) throw Error(`查询不到 projectName 为 ${projectName} 的项目`)

        // 发送钉群消息
        this.dingtalkService.sendTextMsg({
          content: `${description}\n 请各行业负责人CR行业改动部分，没问题的记得评论一下噢~ \n ${res.data?.code_review_url}`,
          atUserIds: ids.split(','),
        }, project)
      }
    }

    this.ctx.body = res;
  }

  // 更新CR
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/update-cr',
    method: 'post',
  })
  async updateCR() {
    const { iterationId, title, description, ids } = this.ctx.request.body;
    this.ctx.body = await this.defService.updateCR({ iterationId, title, description, ids })
  }

   // 查询def基本信息
   @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/def/get-def-member',
    method: 'post',
  })
  async getInfo() {
    const { defAppIdorName } = this.ctx.request.body;
    const detail = await this.defService.getAppDetails({ appIdorName: defAppIdorName })
    const appId = detail?.data?.data?.app?.id;
    this.ctx.body = await this.defService.getMember({ appId });
  }
}
