import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import BaseHandler from './base';
import WechatService from '../service/wechat';

@Provide()
export class OpenLinkHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  wechatService: WechatService;


   /** 
    * 生成微信推广信息
    * 开放接口：https://aliyuque.antfin.com/remtwr/cvk271/gcpyp52pszkp8ae3#NShN5
    **/
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/open-link/wx-promotion-info',
    method: 'post',
    middleware: ['wrapResponseMiddleware']
  })
  async getWxPromotionInfo() {
    const params = this.ctx.request.body;
    const { url, shortLinkName, needShortLink, needQrcode, weappCodeWidth, isHyaline = false, qrcodePath } = params;

    const res = {
      errMsg: '',
      weappCode: null,
      weappShortLink: null,
    };
    // 小程序短链
    if (needShortLink) {
      res.weappShortLink = await this.wechatService.generateShortLink(url, shortLinkName);
    }

    if (needQrcode) {
      const qrcodeRes = await this.wechatService.generateQrcode({
        width: weappCodeWidth,
        pageUrl: url,
        isHyaline, 
        qrcodePath,
      }).catch(err => {
        return {
          weappCode: null,
          error: err,
        };
      });
      res.weappCode = qrcodeRes.weappCode;
      res.errMsg = qrcodeRes.error;
    }
    return res;
  }
}