import { Inject, Provide, ServerlessTrigger, ServerlessTriggerType } from '@midwayjs/core';
import BaseDevBranchHandler from '@/apis/functions/base-dev-branch';
import AmdpService from '@/apis/service/amdp';

@Provide()
export class AmdpHandler extends BaseDevBranchHandler {
  @Inject()
  amdpService!: AmdpService;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/amdp/search-by-keyword',
    middleware: ['handlerMiddleware']
  })
  async searchByKeyword() {
    // 析取参数
    const { keyword } = this.ctx.query as { keyword?: string; page?: string; pageSize?: string; };
    const page = Number(this.ctx.query.page ?? 1);
    const pageSize = Number(this.ctx.query.page ?? 10);

    if (!keyword) return { list: [], total: 0 };

    return await this.amdpService.searchByKeyword(keyword, page, pageSize);
  }
}
