/**
 * Antd Pro FaaS 一体化中的渲染 assert html 壳的函数，此文件一般不需要修改，特殊定制可见 apis/view 目录
 */
import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import * as ejs from 'ejs';
import * as fs from 'fs-extra';
import * as path from 'path';
import UserService from '@/apis/service/user';
import { convertsCollectionToCamel } from '@/apis/utils';
import indexEjs from '../view/index.ejs';

// 会通过 umi-plugin-faas 自动将`//${cndHost}/${group}/${name}/${version}/` 合成的 ASSET_PATH 放到构建目录去
const getAssetPath = (ctx: any) => {
  // 默认使用本地相对地址
  let assertPath = '/';
  const rootPath = path.join(__dirname, '../../');
  const configPath = path.join(rootPath, 'static', 'config.json');
  if (fs.pathExistsSync(configPath)) {
    const config = fs.readJSONSync(configPath);
    const { PROJECT_VERSION, PROJECT_GROUP, PROJECT_NAME } = config;
    const cndHost = ctx.env === 'prod' ? 'g.alicdn.com' : 'dev.g.alicdn.com';
    assertPath = `//${cndHost}/${PROJECT_GROUP}/${PROJECT_NAME}/${PROJECT_VERSION}/`;
  }
  return assertPath;
};

@Provide()
export class RenderHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  userService!: UserService;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/*'
  })
  async handler() {
    const workid = this.ctx.user.workid;
    const loginUser = await this.userService.get(workid);

    // 2. 返回当前登录用户信息
    const currentUser = {
      ...convertsCollectionToCamel(this.ctx.user),
      userType: loginUser?.userType ?? ''
    }

    this.ctx.set('Content-Type', 'text/html; charset=utf-8');
    this.ctx.body = ejs.render(
      indexEjs,
      {
        ASSET_PATH: getAssetPath(this.ctx),
        currentUser
      },
      { delimiter: '#' }
    );
  }
}
