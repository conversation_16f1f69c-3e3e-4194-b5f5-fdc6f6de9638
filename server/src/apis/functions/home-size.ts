import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import IterBranchService from '@/apis/service/iter-branch';
import ShrinkWrapService from '../service/shrinkwrap';
import BaseHandler from '@/apis/functions/base';
import { formatSubpackageSize } from '@/apis/utils/size';
import { analyzeDeps } from '../utils/dependencies';
import * as fse from 'fs-extra';
import * as path from 'path';

@Provide()
export class HomeSizeHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  iterBranchService: IterBranchService;

  @Inject()
  shrinkWrapService: ShrinkWrapService;

  /** 获取包结构 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/home-size/analyze',
    middleware: ['handlerMiddleware']
  })
  async analyzeWxHomeSize() {
    const { iterId, dist, projectName = 'fliggy-weixin' } = this.ctx.query;

    // 迭代体积分析
    if (!dist) {
      // 获取迭代id
      const iterBranch = iterId ? await this.iterBranchService.get(Number(iterId)) : await this.iterBranchService.getPrevPublishedIter(projectName);
      if (!iterBranch) throw Error(`获取迭代分支 ${iterId} 失败`);
      const { defDist, defUploadPkgRes } = iterBranch;
      const uploadRes = JSON.parse(defUploadPkgRes);
      const { unzipFilePath, removeTmpDir } = await this.shrinkWrapService.unzipFile(defDist);
      const dirPath = path.join(unzipFilePath, 'build_wechat');
      const uploadSize = formatSubpackageSize(uploadRes.uploadResult.subPackageInfo);
      const prevIter = await this.getPrevPubedIter(projectName, 'iter', iterBranch.gmtModified);
      const depRes = analyzeDeps(dirPath, uploadSize, Object.assign(prevIter, { version: iterBranch.version }));

      removeTmpDir();
      return depRes;
    }

    // 分支首页体积分析
    const { unzipFilePath, removeTmpDir } = await this.shrinkWrapService.unzipFile(dist);
    // 王守义打码生成的体积信息在根目录wxSizeInfo.json
    const sizeFilePath = path.join(unzipFilePath, 'wxSizeInfo.json');
    const analyzedJSON = await fse.readJSON(sizeFilePath);
    const uploadSize = formatSubpackageSize(analyzedJSON);
    const prevIter = await this.getPrevPubedIter(projectName, 'dist');
    const depRes = analyzeDeps(unzipFilePath, uploadSize, prevIter);

    removeTmpDir();

    return depRes;
  }

  /** 获取上个迭代的主包体积信息 */
  async getPrevPubedIter(projectName: string, type = 'iter', gmtModified?: string) {
    const prevIter = await this.iterBranchService.getPrevPublishedIter(projectName, gmtModified);
    if (!prevIter || (!prevIter.defDist && !prevIter.dist)) return {
      prevVersion: '',
      prevMainSize: {},
    };
    const useDist = type !== 'iter' && prevIter.dist;
    const dist = (useDist ? prevIter.dist : prevIter.defDist) as string;
    const { unzipFilePath, removeTmpDir } = await this.shrinkWrapService.unzipFile(dist);
    const dirPath = useDist ? unzipFilePath : path.join(unzipFilePath, 'build_wechat');
    const { pkgDepSummary, mainPkgSize } = analyzeDeps(dirPath);
    removeTmpDir();
    return {
      prevMainSize: mainPkgSize,
      prevVersion: prevIter.version,
      pkgDepSummary,
    };
  }
}