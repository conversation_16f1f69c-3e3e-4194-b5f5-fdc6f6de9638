import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import DevBranchService from '@/apis/service/dev-branch';
import BaseDevBranchHandler from '@/apis/functions/base-dev-branch';
import { IDevBranch } from '@/apis/interface/dev-branch';
import { IUser } from '@/apis/interface/user';
import { EProjectType } from '@/apis/const/project';
import { checkBizLinePackageSize } from '@/apis/utils/checkBizLinePackageSize';

@Provide()
export class DevBranchHandler extends BaseDevBranchHandler {
  @Inject()
  devBranchService!: DevBranchService;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/dev-branch/get',
    middleware: ['handlerMiddleware']
  })
  async get() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.query, ['devId'])
    const { devId } = this.ctx.query as { devId: string };

    return await this.devBranchService.get(Number(devId));
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/dev-branch/list',
    middleware: ['handlerMiddleware']
  })
  async list() {
    const { pageNum, pageSize, conditions, returnIterBranchDetail } = this.ctx.query as IDevBranchListReq;

    // 1. 筛选出符合条件的分支
    const { list, total } = await this.devBranchService.list(
      Number(pageSize), Number(pageNum),
      conditions ? JSON.parse(conditions) : {}
    )

    // 2. 返回扩展字段
    if (returnIterBranchDetail === 'true') {
      await Promise.allSettled(list.map(async item => {
        if (item.iterId) {
          const iterBranch = await this.iterBranchService.get(item.iterId);
          if (iterBranch) item.iterBranch = iterBranch;
        }
      }))
    }

    return { list, total }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/dev-branch/create',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async create() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId', 'branchName', 'description', 'mergeCode', 'bizLine', 'qaList'])
    const reqParams = this.ctx.request.body as IDevBranchCreateReq;
    if (reqParams.qaList.length === 0) throw Error('必选填写测试负责人');
    if (reqParams.aoneList.length === 0) throw Error('必选填写关联Aone');

    // 2. 创建开发分支
    const devBranch = await this.devBranchService.create(reqParams);

    // 3. 如果项目类型是组件，需要创建组件信息（创建def迭代）
    await this.createComponent(devBranch, reqParams);

    // 4. 钉钉群消息通知
    // this.sendMessage('create', '创建', devBranch);

    return devBranch
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/dev-branch/update',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async update() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const reqParams = this.ctx.request.body as IDevUpdateReq;

    // 2. 如果项目是组件类型，需要更新组件信息（def）
    await this.updateComponent(reqParams.devId, reqParams);

    // 3. 更新开发分支
    const devBranch = await this.devBranchService.update(reqParams.devId, reqParams);

    // 4. 钉钉群消息通知
    // this.sendMessage('update', '更新', devBranch);

    return devBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/dev-branch/delete',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async delete() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number; };

    // 2. 如果项目是组件类型，需要删除组件信息（废弃def迭代）
    await this.deleteComponent(devId);

    // 3. 删除
    const devBranch = await this.devBranchService.delete(devId);

    // 4. 钉钉群消息通知
    // this.sendMessage('delete', '删除', devBranch);

    return devBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/dev-branch/discard',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async discard() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number };

    // 2. 废弃
    const devBranch = await this.devBranchService.discard(devId);

    // 3. 钉钉群消息通知
    // this.sendMessage('discard', '废弃', devBranch);

    return devBranch
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/dev-branch/ready',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async ready() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number; };

    // 2. 准备就绪
    const devBranch = await this.devBranchService.ready(devId);

    // 3. 钉钉群消息通知
    this.sendMessage('ready', '准备就绪', devBranch);

    // 4. 触发构建及自动化测试
    if (devBranch.projectName && devBranch.bizLine) {
      this.branchPluginService.buildAndCreateAutomatedTestTask(devBranch).then(({ dist, towerId, packageResult }) => {
        // 绑定构建产物
        if (dist) this.devBranchService.addDist(devId, dist);

        // 绑定包大小文件(report_analyzed.json)
        if (packageResult && packageResult.reportAnalyzed) this.devBranchService.addReportAnalyzed(devId, packageResult.reportAnalyzed);

        // TODO: 暂时去除开发分支的自动化测试
        // 绑定塔台id
        // if (towerId) this.devBranchService.bindTowerId(devId, towerId);

        // 判断是否超出行业包大小限制，如果超过则告警
        const {
          isWarning = false,
          warningList = [],
        } = checkBizLinePackageSize(packageResult, devBranch.bizLine);
        if (isWarning) this.sendNotice(warningList, devBranch);
      }).catch(err => {
        this.ctx.logger.error(err)
      })
    }

    return devBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/dev-branch/cancel-ready',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async cancelReady() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number; };

    // 2. 取消就绪
    const devBranch = await this.devBranchService.cancelReady(devId);

    // 3. 钉钉群消息通知
    this.sendMessage('cancelReady', '取消就绪', devBranch);

    return devBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/dev-branch/check',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async check() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number };

    // 2. 确认已回归
    return await this.devBranchService._switchChecked(devId, true);
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/dev-branch/uncheck',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async uncheck() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number };

    // 2. 取消回归确认
    return await this.devBranchService._switchChecked(devId, false);
  }

  /** 批量生成体积分析文件 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/dev-branch/make-report-analyzed',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async addReportAnalyzed() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['branchList'])
    const { branchList } = this.ctx.request.body as { branchList: Array<{ devId: number; branchName: string; dist: string }>; };

    if (!branchList || !branchList.length) {
      return [];
    }
    const setReportAnalyzed = async (branch) => {
      const res = await this.branchPluginService.getReportAnalyze(branch.dist, branch.branchName);
      const { reportAnalyzed } = res || {};
      if (reportAnalyzed) {
        await this.devBranchService.addReportAnalyzed(branch.devId, reportAnalyzed);
      }
      return reportAnalyzed;
    };
    const result = await Promise.all(branchList.map(branch => setReportAnalyzed(branch)));
    return result;
  }
}

interface IDevBranchListReq {
  pageSize?: string;
  pageNum?: string;
  conditions?: string;
  /** 是否需要返回挂载的迭代分支详情 */
  returnIterBranchDetail?: string;
}

interface IDevBranchCreateReq {
  iterId: number;
  branchName: string;
  description: string;
  mergeCode: IDevBranch['mergeCode'];
  npmList?: IDevBranch['npmList'];
  npmResolutionList?: IDevBranch['npmResolutionList'];
  bizLine: IDevBranch['bizLine'];
  qaList: IUser[];
  aoneList: IDevBranch['aoneList'];
  projectType: EProjectType;
}

interface IDevUpdateReq {
  devId: number;
  description?: string;
  mergeCode?: IDevBranch['mergeCode'];
  npmList?: IDevBranch['npmList'];
  npmResolutionList?: IDevBranch['npmResolutionList'];
  bizLine?: IDevBranch['bizLine'];
  qaList?: IDevBranch['qaList'];
  aoneList?: IDevBranch['aoneList'];
}
