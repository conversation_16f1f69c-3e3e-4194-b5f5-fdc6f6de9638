import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import FreeDevBranchService from '@/apis/service/free-dev-branch';
import IterBranchService from '@/apis/service/iter-branch';
import BaseDevBranchHandler from '@/apis/functions/base-dev-branch';
import { IFreeDevBranch } from '@/apis/interface/free-dev-branch';
import { IUser } from '@/apis/interface/user';
import { EProjectType } from '@/apis/const/project';
import { checkBizLinePackageSize } from '@/apis/utils/checkBizLinePackageSize';

@Provide()
export class FreeDevBranchHandler extends BaseDevBranchHandler {
  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  freeDevBranchService!: FreeDevBranchService;

  isFreeDevBranch = true;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/get',
    middleware: ['handlerMiddleware']
  })
  async get() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.query, ['devId'])
    const { devId } = this.ctx.query;

    return await this.freeDevBranchService.get(Number(devId)) as IFreeDevBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/list',
    middleware: ['handlerMiddleware']
  })
  async list() {
    const { pageNum, pageSize, conditions, returnIterBranchDetail } = this.ctx.query as IFreeDevBranchListReq;

    // 1. 筛选出符合条件的分支
    const { list, total } = await this.freeDevBranchService.list(
      Number(pageSize), Number(pageNum),
      conditions ? JSON.parse(conditions) : {}
    )

    // 2. 返回扩展字段
    if (returnIterBranchDetail === 'true') {
      await Promise.allSettled(list.map(async item => {
        if (item.iterId) {
          const iterBranch = await this.iterBranchService.get(item.iterId);
          if (iterBranch) item.iterBranch = iterBranch;
        }
      }))
    }

    return { list, total }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/create',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async create() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['projectName', 'branchName', 'description', 'mergeCode', 'bizLine', 'qaList', 'aoneList'])
    const reqParams = this.ctx.request.body as IFreeDevBranchCreateReq;
    if (reqParams.qaList.length === 0) throw Error('必选填写测试负责人');
    if (reqParams.aoneList.length === 0) throw Error('必选填写关联Aone');

    // 创建游离开发分支
    const freeDevBranch = await this.freeDevBranchService.create(reqParams);

    // 同步创建其他项目的游离开发分支
    let otherFreeDevBranchMap = {} as { [key: string]: IFreeDevBranch | string };
    const syncProjects = reqParams.syncProjects;
    if (syncProjects && syncProjects.length > 0) {
      const promiseSettledResult = await Promise.allSettled(syncProjects.map((otherProjectName) => {
        return this.freeDevBranchService.create({
          ...reqParams,
          projectName: otherProjectName
        });
      }))
      promiseSettledResult.forEach((item, index) => {
        const projectName = syncProjects[index];
        if (item.status === 'fulfilled') {
          otherFreeDevBranchMap[projectName] = item.value;
        } else {
          let errMsg: string;
          if (item.reason instanceof Error) errMsg = item.reason.message
          else if (typeof item.reason === 'string') errMsg = item.reason
          else errMsg = '未知异常';

          otherFreeDevBranchMap[projectName] = errMsg;
        }
      })
    }

    // 如果项目类型是组件，需要创建组件信息（创建def迭代）
    await this.createComponent(freeDevBranch, reqParams);

    // 钉钉群消息通知
    // this.sendMessage('create', '创建', freeDevBranch);

    return {
      freeDevBranch,
      otherFreeDevBranchMap
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/update',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async update() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const reqParams = this.ctx.request.body as IFreeDevBranchUpdateReq;

    // 2. 如果项目是组件类型，需要更新组件信息（def）
    await this.updateComponent(reqParams.devId, reqParams);

    // 更新开发分支
    const freeDevBranch = await this.freeDevBranchService.update(reqParams.devId, reqParams);

    // 同步更新其他项目的游离开发分支
    let otherFreeDevBranchMap = {} as { [key: string]: IFreeDevBranch | string };
    const syncProjects = reqParams.syncProjects;
    if (syncProjects && syncProjects.length > 0) {
      const promiseSettledResult = await Promise.allSettled(syncProjects.map((otherProjectName) => {
        return this.freeDevBranchService.updateByBranchName(freeDevBranch.branchName, otherProjectName, reqParams);
      }))
      promiseSettledResult.forEach((item, index) => {
        const projectName = syncProjects[index];
        if (item.status === 'fulfilled') {
          otherFreeDevBranchMap[projectName] = item.value;
        } else {
          let errMsg: string;
          if (item.reason instanceof Error) errMsg = item.reason.message
          else if (typeof item.reason === 'string') errMsg = item.reason
          else errMsg = '未知异常';

          otherFreeDevBranchMap[projectName] = errMsg;
        }
      })
    }

    // 4. 钉钉群消息通知
    // this.sendMessage('update', '更新', freeDevBranch);

    return {
      freeDevBranch,
      otherFreeDevBranchMap
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/delete',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async delete() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number; };

    // 2. 如果项目是组件类型，需要删除组件信息（废弃def迭代）
    await this.deleteComponent(devId);

    // 3. 删除
    const freeDevBranch = await this.freeDevBranchService.delete(devId);

    // 4. 钉钉群消息通知
    // this.sendMessage('delete', '删除', freeDevBranch);

    return freeDevBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/discard',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async discard() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number };

    // 2. 废弃
    const freeDevBranch = await this.freeDevBranchService.discard(devId);

    // 3. 钉钉群消息通知
    // this.sendMessage('discard', '废弃', freeDevBranch);

    return freeDevBranch
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/ready',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async ready() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number; };

    // 2. 准备就绪
    const freeDevBranch = await this.freeDevBranchService.ready(devId);

    // 3. 钉钉群消息通知
    this.sendMessage('ready', '准备就绪', freeDevBranch);

    // 4. 触发构建及自动化测试
    if (freeDevBranch.projectName && freeDevBranch.bizLine) {
      this.branchPluginService.buildAndCreateAutomatedTestTask(freeDevBranch).then(({ dist, towerId, packageResult }) => {
        // 绑定构建产物
        if (dist) this.freeDevBranchService.addDist(devId, dist);

        // 绑定包大小文件(report_analyzed.json)
        if (packageResult && packageResult.reportAnalyzed) this.freeDevBranchService.addReportAnalyzed(devId, packageResult.reportAnalyzed);

        // TODO: 暂时去除开发分支的自动化测试
        // 绑定塔台id
        // if (towerId) this.freeDevBranchService.bindTowerId(devId, towerId);

        // 判断是否超出行业包大小限制，如果超过则告警
        const {
          isWarning = false,
          warningList = [],
        } = checkBizLinePackageSize(packageResult, freeDevBranch.bizLine);
        if (isWarning) this.sendNotice(warningList, freeDevBranch);
      }).catch(err => {
        this.ctx.logger.error(err)
      })
    }

    return freeDevBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/cancel-ready',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async cancelReady() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number };

    // 2. 取消就绪
    const freeDevBranch = await this.freeDevBranchService.cancelReady(devId);

    // 3. 钉钉群消息通知
    this.sendMessage('cancelReady', '取消就绪', freeDevBranch);

    return freeDevBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/check',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async check() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number };

    // 2. 确认已回归
    return await this.freeDevBranchService._switchChecked(devId, true);
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/uncheck',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async uncheck() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number };

    // 2. 取消回归确认
    return await this.freeDevBranchService._switchChecked(devId, false);
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/mount',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async mount() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId', 'iterId'])
    const { devId, iterId } = this.ctx.request.body as { devId: number; iterId: number; };

    // 2. 如果项目类型是模块，需要更新投放列表里当前开发分支所挂载的迭代id
    await this.freeDevBranchService.mountComponent(devId, iterId);

    // 3. 挂载
    const freeDevBranch = await this.freeDevBranchService.mount(devId, iterId);

    // 4. 获取扩展字段
    if (freeDevBranch.iterId) {
      const iterBranch = await this.iterBranchService.get(iterId);
      if (iterBranch) freeDevBranch.iterBranch = iterBranch;
    }

    // 4. 钉钉群消息通知
    // this.sendMessage('mount', '挂载', freeDevBranch);

    return freeDevBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/unmount',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async unmount() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['devId'])
    const { devId } = this.ctx.request.body as { devId: number; };

    // 2. 如果项目类型是模块，需要更新投放列表里当前开发分支所挂载的迭代id
    await this.freeDevBranchService.unmountComponent(devId);

    // 3. 取消挂载
    const freeDevBranch = await this.freeDevBranchService.unmount(devId);

    // 4. 钉钉群消息通知
    // this.sendMessage('unmount', '取消挂载', freeDevBranch);

    return freeDevBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/make-report-analyzed',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async addReportAnalyzed() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['branchList'])
    const { branchList } = this.ctx.request.body as { branchList: Array<{ devId: number; branchName: string; dist: string }>; };

    if (!branchList || !branchList.length) {
      return [];
    }
    const setReportAnalyzed = async (branch) => {
      const res = await this.branchPluginService.getReportAnalyze(branch.dist, branch.branchName);
      const { reportAnalyzed } = res || {};
      if (reportAnalyzed) {
        await this.freeDevBranchService.addReportAnalyzed(branch.devId, reportAnalyzed);
      }
      return reportAnalyzed;
    };
    const result = await Promise.all(branchList.map(branch => setReportAnalyzed(branch)));
    return result;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/free-dev-branch/analyze-size',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async analyzeSize() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['branchName', 'dist', 'projectName'])
    const { dist, branchName, projectName, gmtModified, noticeUsers } = this.ctx.request.body as { branchName: string; dist: string, projectName: string, gmtModified?: string, noticeUsers?: string };
    return this.branchPluginService.analyzeBranchSize(dist, projectName, branchName, gmtModified, noticeUsers);
  }
}

interface IFreeDevBranchListReq {
  pageSize?: string;
  pageNum?: string;
  conditions?: string;
  /** 是否需要返回挂载的迭代分支详情 */
  returnIterBranchDetail?: string;
}

interface IFreeDevBranchCreateReq {
  projectName: string;
  branchName: string;
  description: string;
  mergeCode: IFreeDevBranch['mergeCode'];
  npmList?: IFreeDevBranch['npmList'];
  npmResolutionList?: IFreeDevBranch['npmResolutionList'];
  bizLine: IFreeDevBranch['bizLine'];
  qaList: IUser[];
  aoneList: IFreeDevBranch['aoneList'];
  pkgVersion?: string;
  projectType: EProjectType;
  /** 同步项目，既在其他项目中同步创建对应游离分支 */
  syncProjects?: string[];
}

interface IFreeDevBranchUpdateReq {
  devId: number;
  description?: string;
  mergeCode?: IFreeDevBranch['mergeCode'];
  npmList?: IFreeDevBranch['npmList'];
  npmResolutionList?: IFreeDevBranch['npmResolutionList'];
  bizLine?: IFreeDevBranch['bizLine'];
  qaList?: IFreeDevBranch['qaList'];
  aoneList?: IFreeDevBranch['aoneList'];
  pkgVersion?: string;
  /** 同步项目，既在其他项目中同步创建对应游离分支 */
  syncProjects?: string[];
}
