import { Context } from '@midwayjs/faas';
import { Inject } from '@midwayjs/core';
import { formatDate } from '@/apis/utils';
import IterBranchService from '@/apis/service/iter-branch';
import BranchPluginService from '@/apis/service/branch-plugin';
import DingtalkService from '@/apis/service/dingtalk';
import ProjectService from '@/apis/service/project';
import DefService from '@/apis/service/def';
import { IBaseDevBranchUpdateParams, IDevBranchCreateParams, IFreeDevBranchCreateParams } from '@/apis/service/base-dev-branch';
import FreeDevBranchService from '@/apis/service/free-dev-branch';
import DevBranchService from '@/apis/service/dev-branch';
import BaseHandler from '@/apis/functions/base';
import { IDevBranch } from '@/apis/interface/dev-branch';
import { PRE_URL, PROD_URL } from '@/apis/const';
import { EBranchType, EDevStatus } from '@/apis/const/dev-branch';
import PROJECT_CONFIG, { EProjectType } from '@/apis/const/project';
import { EDefBuildStatus } from '@/apis/const/def';
import { isProd } from '@/apis/utils';
import { parseGitRepoInfo } from '@/apis/utils/git';
import { IFreeDevBranch } from '@/apis/interface/free-dev-branch';
import { EFreeDevMountStatus } from '@/apis/const/free-dev-branch';

export default class BaseDevBranchHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  defService!: DefService;

  @Inject()
  freeDevBranchService!: FreeDevBranchService;

  @Inject()
  devBranchService!: DevBranchService;

  @Inject()
  branchPluginService!: BranchPluginService;

  isFreeDevBranch = false;

  /**
   * 发送消息
   */
  async sendMessage(action: string, actionName: string, devBranch: IDevBranch | IFreeDevBranch) {
    let projectName: string | undefined;
    let branchTypeName: string;

    if (this.isFreeDevBranch) {
      projectName = (devBranch as IFreeDevBranch).projectName
      branchTypeName = '游离开发分支';
    } else {
      const iterBranch = await this.iterBranchService.get((devBranch as IDevBranch).iterId);
      projectName = iterBranch?.projectName;
      branchTypeName = '开发分支';
    }

    if (!projectName) return;

    const project = await this.projectService.get(projectName, { needDingtalkRobots: true });
    if (!project) return;

    let jumpUlr = `${isProd ? PROD_URL : PRE_URL}/#/iter/`;
    if (devBranch.iterId) {
      jumpUlr += `detail?iterId=${devBranch.iterId}`
    } else {
      jumpUlr += 'free-branch-list'
    }

    if (action === 'update') {
      this.dingtalkService.sendActionCardMessage({
        title: `${actionName} ${devBranch.gitBranch.name} ${branchTypeName}`,
        singleTitle: '查看详情',
        singleURL: jumpUlr,
        text: `### ${actionName} [${devBranch.gitBranch.name}](${devBranch.gitBranch.url}) ${branchTypeName}`
          + '\n'
          + `- 分支状态：**${devBranch.statusText}**`
          + (this.isFreeDevBranch ? `\n- 挂载状态：**${(devBranch as IFreeDevBranch).mountStatus === EFreeDevMountStatus.MOUNTED ? '已挂载' : '未挂载'}**` : '')
          + '\n'
          + `- 分支描述：${devBranch.description}`
          + '\n'
          + `- 所属项目：${project?.cnName ?? '未知'}`
          + '\n'
          + `- 执行人：${this.ctx.user.name}`
          + '\n'
          + `- 执行时间：${formatDate(new Date())}`
      }, project)
    } else {
      this.dingtalkService.sendActionCardMessage({
        title: `${branchTypeName} ${devBranch.gitBranch.name} 已${actionName}`,
        singleTitle: '查看详情',
        singleURL: jumpUlr,
        text: `![bg](${PROJECT_CONFIG[project.type].dingtalkLogo})`
          + '\n'
          + `**${branchTypeName} [${devBranch.gitBranch.name}](${devBranch.gitBranch.url}) 已${actionName}**`
          + '\n'
          + `- 分支状态：**${actionName === '删除' ? '已删除' : devBranch.statusText}**`
          + (this.isFreeDevBranch ? `\n- 挂载状态：**${(devBranch as IFreeDevBranch).mountStatus === EFreeDevMountStatus.MOUNTED ? '已挂载' : '未挂载'}**` : '')
          + '\n'
          + `- 分支描述：${devBranch.description}`
          + '\n'
          + `- 所属项目：${project?.cnName ?? '未知'}`
          + '\n'
          + `- 执行人：${this.ctx.user.name}`
          + '\n'
          + `- 执行时间：${formatDate(new Date())}`
      }, project)
    }
  }

  /**
   * 单聊发消息
   * */
  async sendNotice(warningList: any, devBranch: IDevBranch | IFreeDevBranch) {
    let projectName: string | undefined;
    let branchTypeName: string;

    if (this.isFreeDevBranch) {
      projectName = (devBranch as IFreeDevBranch).projectName
      branchTypeName = '游离开发分支';
    } else {
      const iterBranch = await this.iterBranchService.get((devBranch as IDevBranch).iterId);
      projectName = iterBranch?.projectName;
      branchTypeName = '开发分支';
    }

    if (!projectName) return;

    const project = await this.projectService.get(projectName, { needDingtalkRobots: true });
    if (!project) return;

    const title = `${branchTypeName} [${devBranch.gitBranch.name}](${devBranch.gitBranch.url}) 包体积超限告警`;
    const header = [
      `![bg](${PROJECT_CONFIG[project.type].dingtalkLogo})`,
      `**${branchTypeName} [${devBranch.gitBranch.name}](${devBranch.gitBranch.url}) 包体积超限告警**`,
      `- 分支状态：**${devBranch.statusText}**`,
      (this.isFreeDevBranch ? `- 挂载状态：**${(devBranch as IFreeDevBranch).mountStatus === EFreeDevMountStatus.MOUNTED ? '已挂载' : '未挂载'}**` : ''),
      `- 分支描述：${devBranch.description}`,
      `- 所属项目：${project?.cnName ?? '未知'}`,
      `- 执行人：${this.ctx.user.name}`,
      `- 执行时间：${formatDate(new Date())}`,
      `- 包体积详情如下：`,
    ];
    const bottom = [`- **[点击查看详情](${`${isProd ? PROD_URL : PRE_URL}/#/package/structure?${this.isFreeDevBranch ? 'freeDevId' : 'devId'}=${devBranch.devId}`})**`]
    let content = warningList.map(item => `- **${item.groupName}**：本次迭代体积[**${item.currentSize}**]，${item.isExceed ? '超出' : '剩余'}体积[**<font color='${item.isExceed ? 'red' : 'mediumseagreen'}'>${item.bufferSize}</font>**]`);
    content = header.concat(content).concat(bottom).filter(item => !!item).join('\n');

    const user = devBranch.creatorWorkid;
    this.dingtalkService.notice([user], title, content);
  }

  /**
   * 如果项目类型是组件，需要创建组件信息（创建def迭代）
   * @param devBranch 普通开发分支/游离开发分支数据
   * @param params 创建分支时的传参
   */
  async createComponent(devBranch: IDevBranch | IFreeDevBranch, params: IDevBranchCreateParams | IFreeDevBranchCreateParams) {
    const { branchName, description, aoneList, projectType, pkgVersion } = params;
    const isComponentProject = projectType === EProjectType.COMPONENT;
    const isComponentProjectWithPkgVer = isComponentProject && !!pkgVersion;
    let defIterId: number | undefined;
    let defBranchId: number | undefined;

    if (isComponentProjectWithPkgVer) {
      const { group, project } = parseGitRepoInfo(devBranch.gitBranch.url)!;
      const res = await this.defService.createIteration({
        repo: `${group}/${project}`,
        name: branchName,
        description,
        branch: branchName,
        version: pkgVersion!,
        trunk: 'master',
        aoneBinds: aoneList ? [aoneList[0]?.id] : []
      });

      const service = this.isFreeDevBranch ? this.freeDevBranchService : this.devBranchService;

      if (res?.success && res?.data) {
        defIterId = res.data.id;
      } else {
        service.delete(devBranch.devId);
        throw Error(`${devBranch.projectName} 项目创建def迭代失败（${res.errorMsg}）`);
      }

      // 查询迭代信息 (如branchId：后面绑定此分支到发布迭代要使用)
      const res2 = await this.defService.getIterationInfoById({
        iterationId: defIterId!
      })
      if (res2?.success && res2?.data) {
        defBranchId = res2.data.iteration.devbranches?.[0].branch_id;
      }

      service.update(devBranch.devId, Object.assign({
        defBuildStatus: EDefBuildStatus.NOT_START,
      },
        pkgVersion ? { pkgInitialVersion: pkgVersion } : {},
        defIterId ? { defIterId } : {},
        defBranchId ? { defBranchId } : {},
      ))
    }
  }

  /**
   * 如果项目类型是组件，需要删除组件信息（废弃def迭代）
   * @param devId 普通开发分支/游离开发分支id
   */
  async deleteComponent(devId: number) {
    // 1. 前置判断
    const service = this.isFreeDevBranch ? this.freeDevBranchService : this.devBranchService;
    const devBranch = await service.get(devId);

    if (!devBranch) throw Error(`查询不到 devId 为 ${devId} 的${this.isFreeDevBranch ? '游离' : ''}开发分支`)

    if (devBranch.status === EDevStatus.READY) throw Error('就绪状态不可删除')

    // 5. 如果项目类型是模块，需要废弃def迭代
    if (devBranch.branchType === EBranchType.COMPONENT) {
      const res = await this.defService.abandonIteration({
        iterationId: devBranch.defIterId!
      })
      if (!res?.success){
        throw Error(`项目废弃def迭代(${devBranch.defIterId})失败`);
      }
    }
  }

  /**
   * 如果项目是组件类型，需要更新组件信息（更新def）
   * @param devId 普通开发分支/游离开发分支id
   * @param params aoneList, description  aone列表、描述
   */
  async updateComponent(devId, { aoneList, description }: IBaseDevBranchUpdateParams) {
    // 1. 前置判断
    const service = this.isFreeDevBranch ? this.freeDevBranchService : this.devBranchService;
    const devBranch = await service.get(devId);

    if (!devBranch) throw Error(`查询不到 devId 为 ${devId} 的${this.isFreeDevBranch ? '游离' : ''}开发分支`)

    if (devBranch.status !== EDevStatus.DEVELOP) throw Error(`${devBranch.statusText} 状态不可更新`)

    if (devBranch.branchType === EBranchType.COMPONENT) {
      // 修改aone
      if (JSON.stringify(devBranch.aoneList) !== JSON.stringify(aoneList)) {
        // 获取项目详情
        const project = await this.projectService.get(devBranch.projectName!);
        if (!project) throw Error(`查询不到 projectName 为 ${devBranch.projectName} 的项目`)

        const res = await this.defService.editBranch({
          appId: project?.deliverConfig?.defProjectId!,
          branchId: devBranch.defBranchId!,
          aoneBinds: aoneList ? [aoneList[0]?.id] : []
        })
        if (!res?.success ) {
          throw Error(`def迭代的aone修改失败`);
        }
      }
      // 修改描述
      if (devBranch.description !== description) {
        const res = await this.defService.editIteration({
          iterationId: devBranch.defIterId!,
          name: devBranch.branchName,
          description: description || ''
        })
        if (!res?.success ) {
          throw Error(`def迭代的描述修改失败`);
        }
      }
    }
  }
}
