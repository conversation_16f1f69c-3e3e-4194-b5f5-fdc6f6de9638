import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import ProjectService from '@/apis/service/project';
import PackageService from '@/apis/service/package';
import DingtalkService from '@/apis/service/dingtalk';
import IterBranchService from '@/apis/service/iter-branch';
import BranchPluginService from '@/apis/service/branch-plugin';
import DevBranchService from '@/apis/service/dev-branch';
import FreeDevBranchService from '@/apis/service/free-dev-branch';
import DefService from '@/apis/service/def';
import ShrinkWrapService from '@/apis/service/shrinkwrap';
import StabilityService from '../service/stability';
import TowerHSF from '@/apis/hsf/tower';
import GitService from '@/apis/service/git';
import { useTmpDir, useGit } from '@/apis/middleware/hooks';
import { NOTICE_REGRESSION_TESTING, NOTICE_AT_MEMEBER_TEXT, PROJECT_TYPE_CN_NAME, NOTICE_AT_MEMEBER_TEXT_WITH_AP, EProjectType } from '@/apis/const/project';
import * as chalk from 'chalk';
import { IDevBranch } from '@/apis/interface/dev-branch';
import { IIterBranch } from '@/apis/interface/iter-branch';
import { IFreeDevBranch } from '@/apis/interface/free-dev-branch';
import { EDevStatus } from '@/apis/const/dev-branch';
import BaseHandler from '@/apis/functions/base';
import { getAppStructure } from '@/apis/utils/package';
import { updateBranchDeps } from '@/apis/utils/git';
import { getNoticeMemberIds } from '@/apis/utils/branch';
import { IIterBranchDetailModel } from '@/apis/model/iter-branch-detail';
import BucHSF from '@/apis/hsf/buc';

@Provide()
export class HelperHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  gitService!: GitService;

  @Inject()
  packageService!: PackageService;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  devBranchService!: DevBranchService;

  @Inject()
  freeDevBranchService!: FreeDevBranchService;

  @Inject()
  towerHSF!: TowerHSF;

  @Inject()
  branchPluginService!: BranchPluginService;

  @Inject()
  defService!: DefService;

  @Inject()
  shrinkWrapService!: ShrinkWrapService;

  @Inject()
  bucHSF!: BucHSF;

  @Inject()
  IterBranchDetailModel!: IIterBranchDetailModel;

  @Inject()
  stabilityService!: StabilityService;

  /**
   * 解析指定分支的依赖
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/analyze-dependencies',
    middleware: ['handlerMiddleware']
  })
  async analyzeDependencies() {
    const { projectName } = this.ctx.query as IAnalysisDependenciesReq;

    // 获取最近一次已发布完成的迭代
    const latestPublishedIterBranch = await this.iterBranchService.getLatestPublishedByProject(projectName);
    if (!latestPublishedIterBranch) {
      throw Error(`没有找到 ${projectName} 的最近一次已发布完成迭代`)
    } else if (!latestPublishedIterBranch.shrinkwrap) {
      throw Error(`${projectName} 的最近一次已发布完成迭代（${latestPublishedIterBranch.version}）没有 shrinkwrap.json`)
    }

    const dependencies = await this.packageService.analyzeDependencies(latestPublishedIterBranch.shrinkwrap, latestPublishedIterBranch.gitBranch.url, latestPublishedIterBranch.gitBranch.name);

    return {
      dependencies,
      iterBranch: latestPublishedIterBranch
    }
  }

  /**
   * diff指定分支的依赖，与 master diff
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/diff-dependencies',
    middleware: ['handlerMiddleware']
  })
  async diffDependencies() {
    const { iterId, fromIterId } = this.ctx.query as IDiffDependencyReq;

    // 获取迭代
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) {
      throw Error(`找不到 id 为 ${iterId} 的迭代`)
    } else if (!iterBranch.shrinkwrap) {
      // todo shrinkwrap.json查找
      throw Error(`没有找到迭代 ${iterBranch.version} 的 shrinkwrap.json`)
    }

    // TODO: 命名修改
    let prevPublishedIterBranch: IIterBranch | null;
    if (fromIterId) {
      prevPublishedIterBranch = await this.iterBranchService.get(fromIterId);
    } else {
      // 获取上一次已发布完成迭代
      prevPublishedIterBranch = await this.iterBranchService.getPrevPublishedIter(iterBranch.projectName, iterBranch.gmtModified);
    }
    if (!prevPublishedIterBranch) {
      // 获取上一次已发布完成迭代
      prevPublishedIterBranch = await this.iterBranchService.getPrevPublishedByProject(iterId);
    }
    let tips = '';
    let dependenciesDiff;
    if (!prevPublishedIterBranch) {
      tips = `迭代 v${iterBranch.version} 之前没有已发布完成的迭代`;
    } else if (!prevPublishedIterBranch.shrinkwrap) {
      tips = '获取比对迭代的依赖失败';
    } else {
      const { url: branchUrl, name: branchName } = iterBranch.rcGitBranch || iterBranch.gitBranch;
      dependenciesDiff = await this.packageService.diffDependencies(iterBranch.shrinkwrap, prevPublishedIterBranch.shrinkwrap, branchUrl, branchName);
      const noticeInfo = dependenciesDiff?.diffFlatInfo?.noticeInfo || {};
      const userIds = Object.values(noticeInfo).reduce((prev: string[], cur: any) => {
        return prev.concat(cur.noticeUser.split(','));
      }, []);

      // 获取通知人花名
      if (userIds.length) {
        const bucInfo = await this.bucHSF.getSimpleUserByEmpIdList(Array.from(new Set(userIds)));
        const noticeUserInfo =  bucInfo.reduce((prev, v) => {
          if (v && v.empId) {
            prev[v.empId] = v.nickNameCn;
          }
          return prev;
        }, {});
        Object.assign(dependenciesDiff, {  noticeUserInfo });
      }
    }

    // 无法diff的时候，降级成依赖分析
    const dependencies = tips ? await this.packageService.analyzeDependencies(iterBranch.shrinkwrap, iterBranch.gitBranch.url, iterBranch.gitBranch.name) : null;

    return {
      tips,
      iterBranch,
      prevPublishedIterBranch,
      dependenciesDiff,
      dependencies
    }
  }

  /**
   * 通知集成
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/notice-merge',
    middleware: ['handlerMiddleware']
  })
  async noticeMerge() {
    const { iterId } = this.ctx.query as INoticeMergeReq;
    if (!iterId) throw Error('缺少 iterId 参数')

    // 1. 查询迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw Error(`未查询到 id 为 ${iterId} 的迭代`)

    // 2. 查询归属项目信息
    const project = await this.projectService.get(iterBranch.projectName);
    if (!project) throw Error(`未查询到 id 为 ${iterId} 的迭代的归属项目`);

    // 3. 获取所有（游离）开发分支
    const [devBranchList, freeDevBranchList] = await Promise.all([
      Promise.all(iterBranch.devBranchList.map(devId => this.devBranchService.get(devId))),
      Promise.all(iterBranch.freeDevBranchList.map(devId => this.freeDevBranchService.get(devId)))
    ]);

    // 4. 通知所有未就绪的（游离）开发分支的创建者及最近修改者
    ([] as (IDevBranch | IFreeDevBranch | null)[]).concat(devBranchList, freeDevBranchList).forEach(devBranch => {
      if (!devBranch) return;

      if (devBranch.status !== EDevStatus.READY) {
        this.dingtalkService.notice([devBranch.creatorWorkid, devBranch.modifierWorkid], '催促集成', `【${project.cnName}】迭代 v${iterBranch.version} 即将冻结集成，您负责的开发分支 ${devBranch.branchName} 还未准备就绪，请前往研发平台点击就绪！`)
      }
    })

    return true;
  }

  /**
   * 通知回归
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/notice-regression-testing',
    middleware: ['handlerMiddleware']
  })
  async noticeRegressionTesting() {
    const query = this.ctx.query as INoticeRegressionTestingReq;
    const iterId = Number(query.iterId);
    let qrCodeLink = query.qrCodeLink;
    if (!iterId) throw Error('缺少 iterId 参数')

    // 1. 查询迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw Error(`未查询到 id 为 ${iterId} 的迭代`)

    // 2. 查询归属项目信息
    const project = await this.projectService.get(iterBranch.projectName);
    if (!project) throw Error(`未查询到 id 为 ${iterId} 的迭代的归属项目`);

    // 3. 发送通知
    const config = NOTICE_REGRESSION_TESTING[project.type];
    if (!config) throw Error(`${project.type} 类型项目不支持通知回归`);

    // 优先取传递的二维码，无则读取配置的
    qrCodeLink = qrCodeLink || config.qrCodeLink;
    if (!qrCodeLink) throw Error('二维码链接为空');

    this.ctx.logger.info(chalk.green(`通知回归测试 >>> iterId:${iterId}, qrCodeLink:${qrCodeLink}`));

    // 钉钉通知
    this.dingtalkService.sendMarkdownMessage({
      title: config.title.replace(/\$\{version\}/g, iterBranch.version),
      text: config.text.replace(/\$\{version\}/g, iterBranch.version).replace(/\$\{qrCodeLink\}/g, qrCodeLink),
      isAtAll: true
    }, project)

    // 邮件通知
    Promise.all([
      Promise.all(iterBranch.devBranchList.map(devId => this.devBranchService.get(devId))),
      Promise.all(iterBranch.freeDevBranchList.map(devId => this.freeDevBranchService.get(devId)))
    ]).then(([devBranchList, freeDevBranchList]) => {
      const list = ([] as (IDevBranch | IFreeDevBranch | null)[]).concat(devBranchList, freeDevBranchList).flatMap(res => res ? [res] : []);
      if (qrCodeLink) this.towerHSF.sendMiniAppEmail(qrCodeLink, list, iterBranch);
      // 钉钉通知到具体人
      const memberIds = getNoticeMemberIds(list, iterBranch.checked, iterBranch.qaList);
      if (memberIds.length) {
        const memberAtText = `@${memberIds.join('@')}`;
        this.dingtalkService.sendTextMsg({
          atUserIds: memberIds,
          content: memberAtText + (project.type === EProjectType.ALIPAY ? NOTICE_AT_MEMEBER_TEXT_WITH_AP : NOTICE_AT_MEMEBER_TEXT).replace(/\$\{iterId\}/g, `${iterId}`).replace(/\$\{appName\}/g, project.cnName || PROJECT_TYPE_CN_NAME[project.type]),
        }, project);
      }
    }).catch(err => {
      this.ctx.logger.error(err)
    })

    // 创建塔台任务
    if (qrCodeLink) {
      this.branchPluginService.createAutomatedTestTask(iterBranch, { qrCodeLink, onlyBuild: false }).then(({ towerId }) => {
        // 绑定塔台id
        if (towerId) this.iterBranchService.bindTowerId(iterId, towerId);
      })
    }

    return true;
  }

  /**
   * 触发自动化测试
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/trigger-automated-test',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async triggerAutomatedTest() {
    let { iterId, devId, isFreeDevBranch, qrCodeLink, dist, update } = this.ctx.request.body as ITriggerAutomatedTestReq;
    let branch: IIterBranch | IDevBranch;
    let towerId: string;

    // 触发迭代分支的自动化测试
    if (iterId) {
      const iterBranch = await this.iterBranchService.get(iterId);

      if (!iterBranch) throw Error(`未查询到 iterId 为 ${iterId} 的迭代`)

      branch = iterBranch;
    }
    // 触发开发分支的自动化测试
    else if (devId) {
      const devBranch = await this[isFreeDevBranch ? 'freeDevBranchService' : 'devBranchService'].get(devId);

      if (!devBranch) throw Error(`未查询到 devId 为 ${devId} 的${isFreeDevBranch ? '游离' : ''}开发分支`)

      branch = devBranch as IDevBranch;
    }
    else {
      throw Error('iterId、devId 必传其一')
    }

    // 如果传入了 qrCodeLink，则直接创建塔台任务
    if (qrCodeLink) {
      dist = dist || branch.dist; // 优先使用传入的 dist，否则取分支上保存的
      const res = await this.branchPluginService.createAutomatedTestTask(branch, { qrCodeLink, dist, onlyBuild: false })
      towerId = res.towerId
    }
    // 否则还要先构建
    else {
      const res = await this.branchPluginService.buildAndCreateAutomatedTestTask(branch, false);
      towerId = res.towerId;
      dist = res.dist;
    }
    if (!towerId) throw Error('创建自动化测试任务失败');

    // 更新入库
    if (update) {
      if (iterId) {
        await Promise.all([
          dist ? this.iterBranchService.addDist(iterId, dist) : Promise.resolve(null),
          this.iterBranchService.bindTowerId(iterId, towerId)
        ])
      } else if (devId) {
        await Promise.all([
          dist ? this[isFreeDevBranch ? 'freeDevBranchService' : 'devBranchService'].addDist(devId, dist) : Promise.resolve(null),
          this[isFreeDevBranch ? 'freeDevBranchService' : 'devBranchService'].bindTowerId(devId, towerId)
        ])
      }
    }

    return { towerId };
  }

  /**
   * 查询集成回归结果
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/get-task-result',
    middleware: ['handlerMiddleware']
  })
  async getTaskResult() {
    const { taskId } = this.ctx.query as IGetTaskResultReq;
    if (!taskId) throw Error('缺少 taskId 参数')

    // 1. 查询任务结果
    return this.towerHSF.getTaskResult(Number(taskId));
  }

  /**
   * 查询集成回归数据统计
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/get-total-task-count',
    middleware: ['handlerMiddleware']
  })
  async getTotalTaskCount() {
    const { startTime, endTime } = this.ctx.query;
    if (!startTime) throw Error('缺少 pageNum 参数')
    if (!endTime) throw Error('缺少 pageSize 参数')

    // 1. 查询数据统计值
    return this.towerHSF.getTotalTaskCount(startTime, endTime);
  }

  /**
   * 查询塔台主任务list
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/get-main-task-detail',
    middleware: ['handlerMiddleware']
  })
  async getMainTaskDetail() {
    const { startTime, endTime } = this.ctx.query;
    if (!startTime) throw Error('缺少 pageNum 参数')
    if (!endTime) throw Error('缺少 pageSize 参数')

    // 1. 查询主任务list
    return this.towerHSF.getMainTaskDetail(startTime, endTime);
  }

  /** 生成并保存shrinkWrap */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/make-and-save-shrinkwrap',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async makeAndSaveShrinkwrap() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId'])

    // 析取参数
    const { iterId } = this.ctx.request.body as { iterId: number; };

    // 查询迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw Error(`查询不到 id 为 ${iterId} 的迭代`)
    else if (!iterBranch.rcGitBranch) throw Error('该迭代暂无集成分支')
    
    // 鉴权
    await this.checkProjectPermissionByProjectName(iterBranch.projectName);

    // def生成
    if (iterBranch.defDist) {
      await this.shrinkWrapService.uploadShrinkWrap(iterId, iterBranch.defDist);
      return true;
    }

    // 拉取代码
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const { gitHandler, gitRepoInfo, projectTmpDirPath } = await useGit(tmpDirPath, {
      branchUrl: iterBranch.rcGitBranch.url,
      branchName: iterBranch.rcGitBranch.name,
    });

    // 【异步】生成 shrinkwrap.json
    this.branchPluginService.getShrinkwrap({
      projectTmpDirPath,
      gitHandler,
      gitRepoInfo,
      branchName: iterBranch.rcGitBranch.name
    }).then(async shrinkwrapUrl => {
      // 保存入库
      await this.iterBranchService.update({
        iterId,
        shrinkwrap: shrinkwrapUrl
      }, true)
    }).catch(err => {
      this.ctx.logger.error(err)
    }).finally(() => {
      // 释放临时目录
      removeTmpDir();
    })

    return true;
  }

  /** 生成并保存report_analyzed.json */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/make-and-save-reportAnalyzed',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async makeAndSaveReportAnalyzed() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId'])

    // 析取参数
    const { iterId } = this.ctx.request.body as { iterId: number; };

    // 查询迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw Error(`查询不到 id 为 ${iterId} 的迭代`)
    else if (!iterBranch.rcGitBranch) throw Error('该迭代暂无集成分支')

    // 鉴权
    await this.checkProjectPermissionByProjectName(iterBranch.projectName);

    new Promise(async () => {
      // 获取分支信息
      const branchName = iterBranch?.rcGitBranch?.name || '';
      const branchUrl = iterBranch?.rcGitBranch?.url || '';

      const { tmpDirPath, removeTmpDir } = useTmpDir();
      const { gitHandler, gitRepoInfo } = await useGit(tmpDirPath, {
        branchUrl,
        branchName,
        cloneSingleBranch: true
      });

      // 构建
      const { dist = '' } = await this.branchPluginService.build(iterBranch, {
        gitHandler,
        gitRepoInfo
      });
      // 分析上传report-analyed.json
      const { reportAnalyzed = '' } = await this.packageService.getPackageSize(dist, tmpDirPath, true, branchName);
      // 绑定包大小文件(report_analyzed.json)
      if (reportAnalyzed) this.iterBranchService.addReportAnalyzed(iterId, reportAnalyzed);

      removeTmpDir();
    });

    return true;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/get-latest-published-by-project',
    middleware: ['handlerMiddleware']
  })
  async getLatestPublishedByProject() {
    const { projectName, withAppStructure: withAppStructureTemp } = this.ctx.query as { projectName: string; withAppStructure?: string };
    const withAppStructure = withAppStructureTemp === 'true';

    // 获取最近一次已发布完成的迭代
    const latestPublishedIterBranch = await this.iterBranchService.getLatestPublishedByProject(projectName);
    if (!latestPublishedIterBranch) {
      throw Error(`没有找到 ${projectName} 的最近一次已发布完成迭代`)
    }

    // 如果携带withAppStructure参数，则额外返回小程序结构
    if (withAppStructure) {
      if (!latestPublishedIterBranch.rcGitBranch) throw Error(`${projectName} 的最近一次已发布完成迭代（${latestPublishedIterBranch.version}）没有 rc 分支`)
      const { url: branchUrl, name: branchName } = latestPublishedIterBranch.rcGitBranch;
      const { tmpDirPath, removeTmpDir } = useTmpDir();
      const { projectTmpDirPath, gitRepoInfo } = await useGit(tmpDirPath, { branchUrl, branchName, cloneSingleBranch: true })

      // 获取小程序结构
      const appStructure = await getAppStructure({
        projectTmpDirPath,
        gitRepoInfo,
        branchName,
      });

      removeTmpDir();

      return {
        iterBranch: latestPublishedIterBranch,
        appStructure
      }
    } else {
      return latestPublishedIterBranch;
    }
  }

  /** 触发组件的自动化测试 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/trigger-component-auto-test',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async triggerComponentAutoTest() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['pub_env', 'gitRepo', 'npmList', 'defIterId', 'branchName'])

    const { pub_env, gitRepo, npmList, defIterId, branchName } = this.ctx.request.body;

    // 2. 更新依赖包，需同步操作 git
    await updateBranchDeps(branchName, gitRepo, npmList, [], [], []);

    // 3. def部署发布
    return await this.defService.createIterPublishTask({
      updateDB: false,
      pub_env,
      iterationId: defIterId,
    })
  }

   /** 触发组件的自动化测试 */
   @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/helper/notice-publish-report',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async triggerDependenciesNotice() {
    this.ctx.logger.info(`sanglu triggerDependenciesNotice >>> begin`);
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, [
      'iterId',
      'projectName',
    ]);
    const { iterId, projectName } = this.ctx.request.body;
    // 查询迭代详情
    const [iterBranch, project] = await Promise.all([
      this.iterBranchService.get(iterId),
      this.projectService.get(projectName)
    ])
    // 发送报告
    this.stabilityService.generatePublishReport(iterBranch, project)
    return true;
  }
}

interface IDiffDependencyReq {
  /** 迭代 id */
  iterId: string;
  /** 对照的迭代分支id */
  fromIterId?: string;
}

interface IAnalysisDependenciesReq {
  /** 项目名称 */
  projectName: string;
}

interface INoticeMergeReq {
  /** 迭代分支id */
  iterId: string;
}

interface INoticeRegressionTestingReq {
  /** 迭代分支id */
  iterId: string;
  /** 小程序链接 */
  miniAppUrl?: string;
  /** 对应二维码链接 */
  qrCodeLink?: string;
  memberIds?: string;
  isAutoNoctice?: boolean
}

interface IGetTaskResultReq {
  /** 塔台任务id */
  taskId: string;
}

interface ITriggerAutomatedTestReq {
  /** 迭代分支id */
  iterId?: number;
  /** 开发分支id */
  devId?: number;
  /** 是否是游离开发分支，需与 devId 一同传递 */
  isFreeDevBranch?: boolean;
  /** 自定义的二维码链接，不传将自动构建一次 */
  qrCodeLink?: string;
  /** 自定义的构建产物地址 */
  dist?: string;
  /** 是否更新入库 */
  update?: boolean;
}
