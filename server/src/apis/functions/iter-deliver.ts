import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import IterBranchService from '@/apis/service/iter-branch';
import IterDeliverService from '@/apis/service/iter-deliver';
import DingtalkService from '@/apis/service/dingtalk';
import BaseHandler from '@/apis/functions/base';
import AlipayOpenApiHTTP from '@/apis/http/alipay-open-api';
import { EClient, EMiniAppUploadStatus, DELIVER_CLIENT_STATIC_CFG } from '@/apis/const/iter-deliver';
import PROJECT_CONFIG from '@/apis/const/project';
import MiniAppVersionInfoService from '@/apis/service/miniapp-version-info';
import ProjectService from '@/apis/service/project';
import { IIterDeliverTask } from '@/apis/interface/iter-deliver';
import { PRE_URL, PROD_URL } from '@/apis/const';
import { isProd, formatDate } from '@/apis/utils';
import FlBuyHTTP from '@/apis/http/fl-buy';
import { GetByBranchDTO } from '@/apis/dto/iter-deliver';
import IterExperienceService from '@/apis/service/iter-experience';

@Provide()
export class IterDeliverHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  iterDeliverService!: IterDeliverService;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  miniAppVersionInfoService!: MiniAppVersionInfoService;

  @Inject()
  alipayOpenApiHTTP!: AlipayOpenApiHTTP;

  @Inject()
  flBuyHTTP!: FlBuyHTTP;

  @Inject()
  iterExperienceService!: IterExperienceService;

  /** 获取投放任务列表 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/list',
    middleware: ['handlerMiddleware']
  })
  async list() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.query, ['iterId'])

    // 析取参数
    const { iterId, clientName } = this.ctx.query as {
      iterId: string;
      clientName?: EClient;
    };

    // 根据 iterId、clientName 获取投放任务列表
    return this.iterDeliverService.list({
      iterId: Number(iterId),
      clientName
    }, { needVersionInfo: true })
  }

  /** 构建&上传，会创建一条新的投放任务 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/upload',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async upload() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId', 'miniAppId'])

    // 析取参数
    const { iterId, clientName, miniAppId, miniAppVersion } = this.ctx.request.body as {
      iterId: number;
      // 多端统一投放的场景（如字节）不会带上 clientName
      clientName?: EClient;
      miniAppId: string;
      miniAppVersion?: string
    };

    // 鉴权
    await this.checkProjectPermissionByIterId(iterId);

    // 支持自动上传的场景
    
    if (clientName === EClient.ALIPAY) {
      // TODO: 暂时关闭支付宝的自动上传，使用手动上传
      if (!miniAppVersion) throw Error(`${clientName} 端不支持自动上传，请手动上传后填写版本号`)
      await this.iterDeliverService.findOrCreate({
        iterId: Number(iterId),
        clientName,
        miniAppId,
        miniAppVersion,
        uploadStatus: EMiniAppUploadStatus.SUCCESS
      })

      // 创建投放任务
      // const iterDeliverTask = await this.iterDeliverService.create({
      //   iterId: Number(iterId),
      //   clientName,
      //   miniAppId,
      //   uploadStatus: EMiniAppUploadStatus.UPLOADING
      // })

      // // 【异步】执行上传任务
      // this.iterDeliverService.upload(iterDeliverTask)
      //   .then(({ miniAppVersion, uploadSuccess, uploadLog, msg }) => {
      //     // 【异步】上传成功后更新投放状态
      //     this.iterDeliverService.update(iterDeliverTask.id, {
      //       miniAppVersion: uploadSuccess ? miniAppVersion : undefined,
      //       uploadLog,
      //       uploadStatus: uploadSuccess ? EMiniAppUploadStatus.SUCCESS : EMiniAppUploadStatus.FAILED
      //     })

      //     // 【异步】发送钉钉消息通知上传人
      //     this.dingtalkService.notice(this.ctx.user.workid, uploadSuccess ? '上传成功' : '上传失败', msg)
      //   })
      //   .catch(err => {
      //     // 【异步】上传失败后更新投放状态
      //     this.iterDeliverService.update(iterDeliverTask.id, {
      //       uploadStatus: EMiniAppUploadStatus.FAILED
      //     })

      //     // 【异步】发送钉钉消息通知上传人
      //     this.dingtalkService.notice(this.ctx.user.workid, '上传失败', err.message)
      //   })
    }
    // 需要手动上传的场景
    else {
      if (!miniAppVersion) throw Error(`${clientName} 端不支持自动上传，请手动上传后填写版本号`)

      // 创建小程序版本
      await this.miniAppVersionInfoService.create({
        clientName,
        miniAppId,
        miniAppVersion
      })

      // 创建投放任务
      await this.iterDeliverService.findOrCreate({
        iterId: Number(iterId),
        clientName,
        miniAppId,
        miniAppVersion,
        uploadStatus: EMiniAppUploadStatus.SUCCESS
      })
    }

    return true;
  }

  /** 刪除投放任务 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/delete',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async delete() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id'])

    // 析取参数
    const { id } = this.ctx.request.body as { id: number; };

    // 根据 id 获取投放任务，并做前置判断
    const deliverTask = await this.iterDeliverService.getById(id)
    if (!deliverTask) throw Error(`查询不到 id 为 ${id} 的投放任务`)

    // 鉴权
    await this.checkProjectPermissionByIterId(deliverTask.iterId);

    // 如有版本号，则还需要删除对应版本信息
    if (deliverTask.miniAppVersion) {
      // 分端处理
      if (deliverTask.clientName === EClient.ALIPAY) {
        await this.alipayOpenApiHTTP.infoDelete({
          clientName: deliverTask.clientName,
          miniAppId: deliverTask.miniAppId,
          appVersion: deliverTask.miniAppVersion
        });
      } else {
        await this.miniAppVersionInfoService.delete({
          clientName: deliverTask.clientName,
          miniAppId: deliverTask.miniAppId,
          miniAppVersion: deliverTask.miniAppVersion
        })
      }
    }

    // 删除投放任务
    await this.iterDeliverService.delete(id);

    // 如果此投放任务有绑定迭代，则解绑
    const matchedDeliverClientList = await this.iterBranchService.getDeliverClient(deliverTask.iterId, {
      clientName: deliverTask.clientName,
      clientList: deliverTask.clientList,
      miniAppId: deliverTask.miniAppId,
    });
    
    if (matchedDeliverClientList?.find(item => item.deliverTaskId === deliverTask.id)) {
      await this.iterBranchService.updateDeliverClient(deliverTask.iterId, {
        clientName: deliverTask.clientName,
        clientList: deliverTask.clientList,
        miniAppId: deliverTask.miniAppId,
      }, {
        deliverTaskId: null,
      })
    }

    return true
  }

  /** 提审 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/audit',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async audit() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id'])

    // 析取参数
    const { id, clientList } = this.ctx.request.body as { id: number; clientList?: EClient[] };

    // 根据 id 获取投放任务，并做前置判断
    const deliverTask = await this.iterDeliverService.getById(id)
    if (!deliverTask) throw Error(`查询不到 id 为 ${id} 的投放任务`)
    if (!deliverTask.miniAppVersion) throw Error('当前投放任务暂未生成小程序版本号，不能提审')
    if (!deliverTask.clientName && !clientList) throw Error('多端统一投放场景必须传 clientList')

    // 鉴权
    await this.checkProjectPermissionByIterId(deliverTask.iterId);

    // 如果参数里有clientList，则判定是多端统一投放场景，需要在投放任务中存储投放端列表
    if (clientList) {
      await this.iterDeliverService.update(id, { clientList });
    }

    // 分端处理
    if (deliverTask.clientName === EClient.ALIPAY) {
      // 支付宝端暂时不用接口来提审，因为提审的表单在平台上填写更加灵活方便
      const res = await this.iterExperienceService.submitMiniAppVersion({
        miniAppVersion: deliverTask.miniAppVersion, miniAppId: deliverTask.miniAppId, clientName: deliverTask.clientName
      }) || {};
      if(res?.code != '10000') {
        throw Error(res?.msg)
      }
    } else {
      await this.miniAppVersionInfoService.audit({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        miniAppVersion: deliverTask.miniAppVersion
      }, clientList);
    }

    // 绑定投放任务 id 到迭代分支上
    await this.iterBranchService.updateDeliverClient(deliverTask.iterId, {
      clientName: deliverTask.clientName,
      clientList,
      miniAppId: deliverTask.miniAppId,
    }, {
      deliverTaskId: deliverTask.id,
    })

    // 【异步】发送钉群消息
    this.sendMessage('audit', '提审', deliverTask);

    // 【异步】需要查询到迭代详情后再做的操作
    this.iterBranchService.get(deliverTask.iterId).then(async iterBranchDetail => {
      if (!iterBranchDetail) return;

      // 【异步】奥特发布后台钩子
      this.flBuyHTTP.pubHook({
        iterId: iterBranchDetail.iterId,
        operateName: '提审',
        appName: iterBranchDetail.projectName,
        version: iterBranchDetail.version,
        desc: iterBranchDetail.description,
      })
    });

    return true
  }

  /** 通过审核 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/pass-audit',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async passAudit() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id'])

    // 析取参数
    const { id } = this.ctx.request.body as { id: number; };

    // 根据 id 获取投放任务，并做前置判断
    const deliverTask = await this.iterDeliverService.getById(id)
    if (!deliverTask) throw Error(`查询不到 id 为 ${id} 的投放任务`)
    if (!deliverTask.miniAppVersion) throw Error('当前投放任务暂未生成小程序版本号，不能通过审核')

    // 鉴权
    await this.checkProjectPermissionByIterId(deliverTask.iterId);

    // 分端处理
    if (deliverTask.clientName === EClient.ALIPAY) {
      throw Error('研发平台已接入支付宝小程序的投放链路，审核状态会自动更新')
    } else {
      await this.miniAppVersionInfoService.passAudit({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        miniAppVersion: deliverTask.miniAppVersion
      })
    }

    // 【异步】需要查询到迭代详情后再做的操作
    this.iterBranchService.get(deliverTask.iterId).then(async iterBranchDetail => {
      if (!iterBranchDetail) return;

      // 【异步】奥特发布后台钩子
      this.flBuyHTTP.pubHook({
        iterId: iterBranchDetail.iterId,
        operateName: '通过审核',
        appName: iterBranchDetail.projectName,
        version: iterBranchDetail.version,
        desc: iterBranchDetail.description,
      })
    });

    return true
  }

  /** 撤销审核 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/cancel-audit',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async cancelAudit() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id'])

    // 析取参数
    const { id } = this.ctx.request.body as { id: number; };

    // 根据 id 获取投放任务，并做前置判断
    const deliverTask = await this.iterDeliverService.getById(id)
    if (!deliverTask) throw Error(`查询不到 id 为 ${id} 的投放任务`)
    if (!deliverTask.miniAppVersion) throw Error('当前投放任务暂未生成小程序版本号，不能撤销审核')

    // 鉴权
    await this.checkProjectPermissionByIterId(deliverTask.iterId);

    // 分端处理
    if (deliverTask.clientName === EClient.ALIPAY) {
      await this.alipayOpenApiHTTP.auditCancel({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        appVersion: deliverTask.miniAppVersion
      });
    } else {
      await this.miniAppVersionInfoService.cancelAudit({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        miniAppVersion: deliverTask.miniAppVersion
      })
    }

    // 解绑投放任务id
    await this.iterBranchService.updateDeliverClient(deliverTask.iterId, {
      miniAppId: deliverTask.miniAppId,
      clientName: deliverTask.clientName,
    }, {
      deliverTaskId: null,
    })

    // 【异步】发送钉群消息
    this.sendMessage('cancelAudit', '撤销审核', deliverTask);

    // 【异步】需要查询到迭代详情后再做的操作
    this.iterBranchService.get(deliverTask.iterId).then(async iterBranchDetail => {
      if (!iterBranchDetail) return;

      // 【异步】奥特发布后台钩子
      this.flBuyHTTP.pubHook({
        iterId: iterBranchDetail.iterId,
        operateName: '撤销审核',
        appName: iterBranchDetail.projectName,
        version: iterBranchDetail.version,
        desc: iterBranchDetail.description,
      })
    });

    return true
  }

  /** 退回开发 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/back-dev',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async backDev() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id'])

    // 析取参数
    const { id } = this.ctx.request.body as { id: number; };

    // 根据 id 获取投放任务，并做前置判断
    const deliverTask = await this.iterDeliverService.getById(id)
    if (!deliverTask) throw Error(`查询不到 id 为 ${id} 的投放任务`)
    if (!deliverTask.miniAppVersion) throw Error('当前投放任务暂未生成小程序版本号，不能退回开发')

    // 鉴权
    await this.checkProjectPermissionByIterId(deliverTask.iterId);

    // 分端处理
    if (deliverTask.clientName === EClient.ALIPAY) {
      await this.alipayOpenApiHTTP.backdevPublish({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        appVersion: deliverTask.miniAppVersion
      });
    } else {
      await this.miniAppVersionInfoService.backDev({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        miniAppVersion: deliverTask.miniAppVersion
      })
    }

    // 解绑投放任务id
    await this.iterBranchService.updateDeliverClient(deliverTask.iterId, {
      clientName: deliverTask.clientName,
      miniAppId: deliverTask.miniAppId,
    }, {
      deliverTaskId: null,
    })

    // 【异步】发送钉群消息
    this.sendMessage('backDev', '退回开发', deliverTask);

    // 【异步】需要查询到迭代详情后再做的操作
    this.iterBranchService.get(deliverTask.iterId).then(async iterBranchDetail => {
      if (!iterBranchDetail) return;

      // 【异步】奥特发布后台钩子
      this.flBuyHTTP.pubHook({
        iterId: iterBranchDetail.iterId,
        operateName: '退回开发',
        appName: iterBranchDetail.projectName,
        version: iterBranchDetail.version,
        desc: iterBranchDetail.description,
      })
    });

    return true;
  }

  /** 灰度 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/gray',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async gray() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id', 'grayStrategy'])

    // 析取参数
    const { id, grayStrategy } = this.ctx.request.body as { id: number; grayStrategy: string; };

    // 根据 id 获取投放任务，并做前置判断
    const deliverTask = await this.iterDeliverService.getById(id, { needVersionInfo: true })
    if (!deliverTask) throw Error(`查询不到 id 为 ${id} 的投放任务`)
    if (!deliverTask.miniAppVersion || !deliverTask.miniAppVersionInfo) throw Error('当前投放任务暂未生成小程序版本号，不能灰度')

    // 鉴权
    await this.checkProjectPermissionByIterId(deliverTask.iterId);

    /**
     * 灰度规则
     * 范围只能增加，不能缩小，灰度值以英文逗号分割
     * 例如：集团灰度完，不能再蚂蚁灰度, gray_alibaba_rule后再外灰，需要传已经灰度的值+新灰度的值
     * 第一次灰度值：gray_alibaba_rule
     * 第二次灰度值：p1,gray_alibaba_rule
     **/
    const clientName = deliverTask.clientName || (deliverTask.clientList && deliverTask.clientList[0]) as EClient; // 适配多端统一、单独投放
    const deliverClientCfg = DELIVER_CLIENT_STATIC_CFG[clientName];
    const graySteps = deliverClientCfg.graySteps;
    if (!graySteps) throw Error(`投放${deliverClientCfg.cnName}端无需灰度`);

    // 获取进行到的灰度步骤
    const prevStrategyList = (deliverTask.miniAppVersionInfo.grayStrategy?.split(',') || [])
      .filter(strategy => {
        // 支付宝需要过滤内置灰度策略：gray_whitelist_developer:2018081461095002,gray_whitelist_experience:2018081461095002,inner_dev_exp_whitelist
        if (deliverTask.clientName === EClient.ALIPAY) {
          return graySteps.find(step => strategy === step.strategy)
        }
        return true;
      });
    const prevStepIndex = graySteps.findIndex(step => step.strategy === prevStrategyList[0]);
    // 获取目标灰度值在灰度步骤中的位置
    const newStepIndex = graySteps.findIndex(step => step.strategy === grayStrategy);

    // 判断目标灰度策略是否合法
    if (newStepIndex === -1) throw Error(`不支持灰度策略 ${grayStrategy}`);
    if (prevStepIndex >= newStepIndex) throw Error('灰度范围只能增加');

    const newStrategy = graySteps[newStepIndex];

    // 判断目标灰度的类型是否新的
    const prevStrategyTypeList = prevStrategyList.map(prevStrategy => graySteps.find(step => step.strategy === prevStrategy)?.type)

    // clone 一份新的灰度策略列表
    const newStrategyList = prevStrategyList.slice();
    // 如果是新类型，则直接拼接
    if (prevStrategyTypeList.indexOf(newStrategy.type) === -1) {
      newStrategyList.unshift(grayStrategy)
    } else { // 否则替换第一位的（理论上来说只会和第一位的类型相匹配）
      newStrategyList.splice(0, 1, grayStrategy)
    }

    // 生成新的灰度策略
    const newStrategyStr = newStrategyList.join(',')

    // 分端处理
    if (deliverTask.clientName === EClient.ALIPAY) {
      await this.alipayOpenApiHTTP.grayPublish({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        appVersion: deliverTask.miniAppVersion,
        grayStrategy: newStrategyStr
      });
    } else {
      await this.miniAppVersionInfoService.gray({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        miniAppVersion: deliverTask.miniAppVersion
      }, newStrategyStr)
    }

    // 灰度的时候也进行一次绑定，防止支付宝场景下没有前置调用 audit 接口
    await this.iterBranchService.updateDeliverClient(deliverTask.iterId, {
      miniAppId: deliverTask.miniAppId,
      clientName: deliverTask.clientName,
      clientList: deliverTask.clientList,
    }, {
      deliverTaskId: deliverTask.id,
    })

    // 【异步】发送钉群消息
    this.sendMessage('gray', newStrategy.title, deliverTask);

    // 【异步】需要查询到迭代详情后再做的操作
    this.iterBranchService.get(deliverTask.iterId).then(async iterBranchDetail => {
      if (!iterBranchDetail) return;

      // 【异步】奥特发布后台钩子
      this.flBuyHTTP.pubHook({
        iterId: iterBranchDetail.iterId,
        operateName: '灰度',
        rate: grayStrategy,
        appName: iterBranchDetail.projectName,
        version: iterBranchDetail.version,
        desc: iterBranchDetail.description,
      })
    });

    return true;
  }

  /** 结束灰度 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/finish-gray',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async finishGray() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id'])

    // 析取参数
    const { id } = this.ctx.request.body as { id: number; };

    // 根据 id 获取投放任务，并做前置判断
    const deliverTask = await this.iterDeliverService.getById(id)
    if (!deliverTask) throw Error(`查询不到 id 为 ${id} 的投放任务`)
    if (!deliverTask.miniAppVersion) throw Error('当前投放任务暂未生成小程序版本号，不能结束灰度');

    // 鉴权
    await this.checkProjectPermissionByIterId(deliverTask.iterId);

    const clientName = deliverTask.clientName || (deliverTask.clientList && deliverTask.clientList[0]) as EClient; // 适配多端统一、单独投放
    const deliverClientCfg = DELIVER_CLIENT_STATIC_CFG[clientName];
    const graySteps = deliverClientCfg.graySteps;
    if (!graySteps) throw Error(`投放${deliverClientCfg.cnName}端无需灰度`);

    // 分端处理
    if (deliverTask.clientName === EClient.ALIPAY) {
      await this.alipayOpenApiHTTP.grayFinish({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        appVersion: deliverTask.miniAppVersion
      });
    } else {
      await this.miniAppVersionInfoService.finishGray({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        miniAppVersion: deliverTask.miniAppVersion
      })
    }

    // 【异步】发送钉群消息
    this.sendMessage('finishGray', '结束灰度', deliverTask);

    // 【异步】需要查询到迭代详情后再做的操作
    this.iterBranchService.get(deliverTask.iterId).then(async iterBranchDetail => {
      if (!iterBranchDetail) return;

      // 【异步】奥特发布后台钩子
      this.flBuyHTTP.pubHook({
        iterId: iterBranchDetail.iterId,
        operateName: '结束灰度',
        appName: iterBranchDetail.projectName,
        version: iterBranchDetail.version,
        desc: iterBranchDetail.description,
      })
    });

    return true;
  }

  /** 上架 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/online',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async online() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id'])

    // 析取参数
    const { id } = this.ctx.request.body as { id: number; };

    // 根据 id 获取投放任务，并做前置判断
    const deliverTask = await this.iterDeliverService.getById(id)
    if (!deliverTask) throw Error(`查询不到 id 为 ${id} 的投放任务`)
    if (!deliverTask.miniAppVersion) throw Error('当前投放任务暂未生成小程序版本号，不能上架');

    // 鉴权
    await this.checkProjectPermissionByIterId(deliverTask.iterId);

    // 分端处理
    if (deliverTask.clientName === EClient.ALIPAY) {
      await this.alipayOpenApiHTTP.onlinePublish({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        appVersion: deliverTask.miniAppVersion
      });
    } else {
      const clientName = deliverTask.clientName || (deliverTask.clientList && deliverTask.clientList[0]) as EClient; // 适配多端统一、单独投放
      const deliverClientCfg = DELIVER_CLIENT_STATIC_CFG[clientName];
      const graySteps = deliverClientCfg.graySteps;

      await this.miniAppVersionInfoService.online({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        miniAppVersion: deliverTask.miniAppVersion
      }, { ignoreGray: !graySteps })
    }

    // 【异步】发送钉群消息
    this.sendMessage('online', '上架', deliverTask);

    // 【异步】需要查询到迭代详情后再做的操作
    this.iterBranchService.get(deliverTask.iterId).then(async iterBranchDetail => {
      if (!iterBranchDetail) return;

      // 【异步】奥特发布后台钩子
      this.flBuyHTTP.pubHook({
        iterId: iterBranchDetail.iterId,
        operateName: '上架',
        appName: iterBranchDetail.projectName,
        version: iterBranchDetail.version,
        desc: iterBranchDetail.description,
      })
    });

    return true;
  }

  /** 下架 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/offline',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async offline() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id'])

    // 析取参数
    const { id } = this.ctx.request.body as { id: number; };

    // 根据 id 获取投放任务，并做前置判断
    const deliverTask = await this.iterDeliverService.getById(id)
    if (!deliverTask) throw Error(`查询不到 id 为 ${id} 的投放任务`)
    if (!deliverTask.miniAppVersion) throw Error('当前投放任务暂未生成小程序版本号，不能下架')

    // 鉴权
    await this.checkProjectPermissionByIterId(deliverTask.iterId);

    // 分端处理
    if (deliverTask.clientName === EClient.ALIPAY) {
      await this.alipayOpenApiHTTP.offlinePublish({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        appVersion: deliverTask.miniAppVersion
      });
    } else {
      await this.miniAppVersionInfoService.offline({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        miniAppVersion: deliverTask.miniAppVersion
      })
    }

    // 【异步】发送钉群消息
    this.sendMessage('offline', '下架', deliverTask);

    // 【异步】需要查询到迭代详情后再做的操作
    this.iterBranchService.get(deliverTask.iterId).then(async iterBranchDetail => {
      if (!iterBranchDetail) return;

      // 【异步】奥特发布后台钩子
      this.flBuyHTTP.pubHook({
        iterId: iterBranchDetail.iterId,
        operateName: '下架',
        appName: iterBranchDetail.projectName,
        version: iterBranchDetail.version,
        desc: iterBranchDetail.description,
      })
    });

    return true;
  }

  /** 回滚 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/rollback',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async rollback() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id'])

    // 析取参数
    const { id, miniAppVersion } = this.ctx.request.body as { id: number; miniAppVersion?: string; };

    // 根据 id 获取投放任务，并做前置判断
    const deliverTask = await this.iterDeliverService.getById(id);
    if (!deliverTask) throw Error(`查询不到 id 为 ${id} 的投放任务`)
    if (!deliverTask.miniAppVersion) throw Error('当前投放任务暂未生成小程序版本号，不能回滚')

    // 鉴权
    await this.checkProjectPermissionByIterId(deliverTask.iterId);
    
    // 投放端
    const clientName = deliverTask.clientName || (deliverTask.clientList && deliverTask.clientList[0]);
    if (!clientName) throw Error(`id 为 ${id} 的投放任务没有指定投放端`);

    // 分端处理
    if (clientName === EClient.ALIPAY) {
      await this.alipayOpenApiHTTP.contentRollback({
        clientName,
        miniAppId: deliverTask.miniAppId,
        appVersion: deliverTask.miniAppVersion
      });
    } else {
      if (!miniAppVersion) throw Error('需指定回滚到的版本');

      await this.miniAppVersionInfoService.rollback({
        miniAppId: deliverTask.miniAppId,
        miniAppVersion: deliverTask.miniAppVersion,
        clientName,
      }, miniAppVersion)
    }

    // 【异步】发送钉群消息
    this.sendMessage('rollback', '回滚', deliverTask);

    // 【异步】需要查询到迭代详情后再做的操作
    this.iterBranchService.get(deliverTask.iterId).then(async iterBranchDetail => {
      if (!iterBranchDetail) return;

      // 【异步】奥特发布后台钩子
      this.flBuyHTTP.pubHook({
        iterId: iterBranchDetail.iterId,
        operateName: '回滚',
        appName: iterBranchDetail.projectName,
        version: iterBranchDetail.version,
        desc: iterBranchDetail.description,
      })
    });

    return true;
  }

  /** 通过迭代版本获取投放信息 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-deliver/get-by-iter-version',
    middleware: ['handlerMiddleware', 'corsMiddleware']
  })
  async getByIterVersion() {
    const { projectName, version, clientName } = this.ctx.request.query as GetByBranchDTO;
    const { list: iterBranchList } = await this.iterBranchService.list(1, 1, {
      projectName,
      version
    });
    const matchedIterBranch = iterBranchList[0];

    if (!matchedIterBranch) throw Error('未找到符合条件的迭代');
    if (!matchedIterBranch.deliverClientList || matchedIterBranch.deliverClientList.length === 0) throw Error(`匹配到的迭代${matchedIterBranch.iterId}没有投放信息`);

    return this.iterDeliverService.list({
      iterId: matchedIterBranch.iterId,
      clientName
    }, { needVersionInfo: true });
  }

  /**
   * 发送消息
   */
  async sendMessage(_action: string, actionName: string, deliverTask: IIterDeliverTask) {
    // 获取迭代详情
    const iterBranch = await this.iterBranchService.get(deliverTask.iterId);
    if (!iterBranch) throw Error(`查询不到 iterId 为 ${deliverTask.iterId} 的迭代`)

    // 获取项目详情
    const project = await this.projectService.get(iterBranch.projectName, { needDingtalkRobots: true });
    if (!project) throw Error(`查询不到 projectName 为 ${iterBranch.projectName} 的项目`)

    let clientCnName = '';
    if (deliverTask.clientName) {
      clientCnName = DELIVER_CLIENT_STATIC_CFG[deliverTask.clientName].cnName;
    } else if (deliverTask.clientList) {
      clientCnName = deliverTask.clientList.map(item => DELIVER_CLIENT_STATIC_CFG[item].cnName).join('、');
    }

    // 只有投放超过1个端才展示端信息
    let showClientNameInTitle = false;
    if (clientCnName && project.clientList.length > 1) {
      showClientNameInTitle = true
    }

    // 发送钉群消息
    this.dingtalkService.sendActionCardMessage({
      title: `[${project.cnName}]迭代 v${iterBranch.version} ${showClientNameInTitle ? clientCnName + '端' : ''}已${actionName}`,
      singleTitle: '查看详情',
      singleURL: `${isProd ? PROD_URL : PRE_URL}/#/iter/detail?iterId=${iterBranch.iterId}`,
      text: [
        `![bg](${PROJECT_CONFIG[project.type].dingtalkBigLogo})`,
        `**迭代 v${iterBranch.version} ${showClientNameInTitle ? clientCnName + '端' : ''}已${actionName}**`,
        `- 迭代描述：${iterBranch.description}`,
        `- 所属项目：${project.cnName || '未知'}`,
        `- 基线分支：[${iterBranch.gitBranch.name}](${iterBranch.gitBranch.url})`,
        `- 集成分支：[${iterBranch.rcGitBranch?.name}](${iterBranch.rcGitBranch?.url})`,
        `- 投放端：${clientCnName || '未知'}`,
        `- 小程序版本：${deliverTask.miniAppVersion || '未知'}`,
        `- 执行人：${this.ctx.user.name}`,
        `- 执行时间：${formatDate(new Date())}`
      ].join('\n')
    }, project)
  }
}
