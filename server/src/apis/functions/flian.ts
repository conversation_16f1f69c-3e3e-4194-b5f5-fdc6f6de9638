import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import FLianService from '@/apis/service/flian';
import { Context } from '@midwayjs/faas';

@Provide()
export class FLianHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  fLianService!: FLianService;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/flian/generateTbSharePassword'
  })
  async generateTbSharePassword() {
    try {
      const { link, title, image } = this.ctx.query;
      const data = await this.fLianService.generateTbSharePassword({ link, title, image });

      this.ctx.body = {
        success: true,
        data
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }
}
