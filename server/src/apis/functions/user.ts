import { Inject, Provide, ServerlessTrigger, ServerlessTriggerType } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import UserService from '@/apis/service/user';
import { convertsCollectionToCamel } from '@/apis/utils';
import { UserType } from '@/apis/interface/user';
import { IUser } from '@/apis/interface/user';

interface IUpdateReq {
  userList: { value: string }[];
  userType: UserType;
}

@Provide()
export class UserHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  userService!: UserService;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/user/current'
  })
  async current() {
    // 1. 获取当前登录用户的类型
    const workid = this.ctx.user.workid;
    const loginUser = await this.userService.get(workid);

    // 2. 返回当前登录用户信息
    this.ctx.body = {
      success: true,
      data: {
        ...convertsCollectionToCamel(this.ctx.user),
        userType: loginUser?.userType ?? ''
      }
    };
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/user/list'
  })
  async list() {
    try {
      const userList = await this.userService.list();

      this.ctx.body = {
        success: true,
        data: userList
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/user/create-multi',
    method: 'post'
  })
  async createMulti() {
    try {
      const { userList, userType }: IUpdateReq = this.ctx.request.body;

      // 1. 鉴权
      const isAdmin = await this.userService.isAdmin();
      if (!isAdmin) throw Error('无权限');

      // 2. 判断用户是否已存在
      const userListArr = userList.map(item => JSON.parse(item.value)); // 上传的是序列化后的字符串，需要 parse 一下
      const checkRes = await Promise.all(userListArr.map(item => this.userService.get(item.workid)));
      const exitUser = checkRes.filter((item): item is IUser => !!item);
      if (exitUser.length > 0) throw Error(`用户${exitUser.map(item => item.name).join('、')}已存在`);

      // 3. 添加新用户
      await Promise.all(userListArr.map(item => {
        return this.userService.create({
          ...item,
          userType
        })
      }));

      this.ctx.body = {
        success: true,
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/user/update-multi',
    method: 'post'
  })
  async updateMulti() {
    try {
      const { userList, userType }: IUpdateReq = this.ctx.request.body;

      // 1. 鉴权
      const isAdmin = await this.userService.isAdmin();
      if (!isAdmin) throw Error('无权限');

      // 2. 更新用户信息
      const userListArr = userList.map(item => JSON.parse(item.value)); // 上传的是序列化后的字符串，需要 parse 一下
      const updateRes = await Promise.all(userListArr.map(item => {
        return this.userService.update({
          ...item,
          userType
        })
      }));
      if (updateRes.some(item => !item)) throw Error('删除失败');

      // 3. 获取最新用户信息
      const getRes = await Promise.all(userListArr.map(item => {
        return this.userService.get(item.workid)
      }));

      this.ctx.body = {
        success: true,
        data: getRes
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/user/delete'
  })
  async delete() {
    try {
      // 1. 鉴权
      const isAdmin = await this.userService.isAdmin();
      if (!isAdmin) throw Error('无权限');

      // 2. 删除用户
      await this.userService.delete(this.ctx.query.workid);

      this.ctx.body = {
        success: true
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }
}
