import {
  ServerlessTrigger,
  ServerlessTriggerType,
  Inject,
  Provide,
} from "@midwayjs/core";
import UFOHTTP from "@/apis/http/ufo";
import BaseDevBranchHandler from "@/apis/functions/base-dev-branch";

@Provide()
export class UFOHandler extends BaseDevBranchHandler {
  @Inject()
  ufoHTTP!: UFOHTTP;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: "/api/ufo/getListByPage",
    method: "get",
  })
  async getListByPage() {
    // 析取参数
    const { mirrorAlgorithmId, pageNum, pageSize } = this.ctx.query;

    this.ctx.body = await this.ufoHTTP.getListByPage({
      mirrorAlgorithmId,
      pageNum,
      pageSize,
    });
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: "/api/ufo/getTaskInfo",
    method: "get",
  })
  async getTaskInfo() {
    // 析取参数
    const { taskId } = this.ctx.query;

    this.ctx.body = await this.ufoHTTP.getTaskInfo(taskId);
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: "/api/ufo/getAllListByPage",
    method: "get",
  })
  async getAllListByPage() {
    // 析取参数
    const { taskIds, pageNum, pageSize } = this.ctx.query;
    const ids = JSON.parse(taskIds);
    try {
      const result = await this.ufoHTTP.getAllListByPage(
        ids,
        pageNum,
        pageSize
      );
      this.ctx.body = { success: true, data: result };
    } catch (err) {
      this.ctx.body = { success: false, message: err };
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: "/api/ufo/getBranchList",
    method: "get",
  })
  async getBranchList() {
    // 析取参数
    const { projectName } = this.ctx.query;
    const publishedIterBranchList = await this.iterBranchService.getPublishedByProjectList(projectName);
    console.log("🚀 ~ UFOHandler ~ getBranchList ~ publishedIterBranchList:", publishedIterBranchList)
    publishedIterBranchList.reverse().sort((a, b) => {
      // 按发布时间排序
      return Number(a.publishDay.replace(/-/g, '')) - Number(b.publishDay.replace(/-/g, ''));
    });;

    this.ctx.body = {
      success: true,
      data: publishedIterBranchList,
    };
  }
}
