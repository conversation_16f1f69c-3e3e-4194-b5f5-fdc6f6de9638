import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import GitService from '@/apis/service/git';

@Provide()
export class GitHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  gitService!: GitService;

  /** 构建打码 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/git/sync-branch',
    middleware: ['handlerMiddleware']
  })
  async syncBranch() {
    const { branchName, branchUrl, targetBranchName } = this.ctx.query as ISyncBranchReq;

    await this.gitService.syncBranch(branchName, branchUrl, targetBranchName);

    return true;
  }

  /** 查询gitlab现有分支(被删除的查不到) */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/git/get-branches',
    method: 'get',
    middleware: ['handlerMiddleware']
  })
  async getBranches() {
    const { projectId, page, per_page } = this.ctx.query;
    this.ctx.body = await this.gitService.getBranches({ projectId, page, per_page } );
  }

  /** 查询git branch -a 分支 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/git/get-remote-branches',
    method: 'get',
    middleware: ['handlerMiddleware']
  })
  async getRemoteBranches() {
    const { url } = this.ctx.query;
    this.ctx.body = await this.gitService.getRemoteBranches({ url } );
  }

}

interface ISyncBranchReq {
  /** 操作的分支名 */
  branchName: string;
  /** 操作的分支url */
  branchUrl: string;
  /** 目标分支名 */
  targetBranchName: string;
}
