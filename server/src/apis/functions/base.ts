import { Inject } from '@midwayjs/core';
import UserService from '@/apis/service/user';
import { IProject } from '@/apis/interface/project';
import ProjectService from '@/apis/service/project';
import IterBranchService from '@/apis/service/iter-branch';

export default class BaseHandler {
  @Inject()
  userService!: UserService;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  projectService!: ProjectService;

  checkRequiredParams(params: { [key: string]: any }, requiredParams: string[]) {
    requiredParams.forEach(key => {
      if (!params.hasOwnProperty(key)) throw Error(`缺少必传参数: ${key}`)
    })
    return true;
  }

  // 检测是否有超管权限
  async checkSuperPermission() {
    const isAdmin = await this.userService.isAdmin();
    if (!isAdmin) throw Error('无权限')
    return true;
  }

  /**
   * 通过项目名称查询是否有项目权限
   */
  async checkProjectPermission(project: IProject) {
    // 是否是项目管理员
    if (project.isAdmin) return true;

    // 是否是超管
    const isAdmin = await this.userService.isAdmin();
    if (!isAdmin) throw Error('没有操作权限');

    return true;
  }

  /**
   * 通过项目名称查询是否有项目权限
   */
  async checkProjectPermissionByProjectName(projectName: string) {
    // 查询归属项目信息
    const project = await this.projectService.get(projectName);
    if (!project) throw Error(`查询不到 projectName 为 ${projectName} 的项目`);

    return this.checkProjectPermission(project);
  }

  /**
   * 通过迭代id查询是否有项目权限
   */
  async checkProjectPermissionByIterId(iterId: number) {
    // 查询迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw Error(`查询不到 id 为 ${iterId} 的迭代`)

    return this.checkProjectPermissionByProjectName(iterBranch.projectName)
  }
}
