import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide, ServerlessFunction } from '@midwayjs/core';
import { Validate } from '@midwayjs/validate';
import { Context } from '@midwayjs/faas';
import BaseHandler from './base';
import { CreateForWebviewDTO } from '../dto/one-link';
import OneLinkService from '../service/one-link';
import { WrapResponse } from '../middleware/handler';

@Provide()
export class OneLinkHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  oneLinkService: OneLinkService;

  /** 
   * 生成套壳页one-link链接
   * 开放接口：https://aliyuque.antfin.com/remtwr/cvk271/gcpyp52pszkp8ae3#NShN5
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/one-link/create-for-webview',
    method: 'post',
    middleware: ['wrapResponseMiddleware']
  })
  async createForWebview() {
    const createParams = this.ctx.request.body as CreateForWebviewDTO;
    return this.oneLinkService.createForWebview(createParams)
  }

  /** 
   * 【hsf接口】生成套壳页one-link链接
   * 开放接口：https://aliyuque.antfin.com/remtwr/cvk271/gcpyp52pszkp8ae3#aZaE4
   */
  @ServerlessFunction({ functionName: 'createForWebview' })
  @ServerlessTrigger(ServerlessTriggerType.HSF)
  @Validate()
  @WrapResponse
  async createForWebviewByHSF(createParams: CreateForWebviewDTO) {
    return this.oneLinkService.createForWebview(createParams)
  }
}