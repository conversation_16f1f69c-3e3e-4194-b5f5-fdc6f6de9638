import { Inject, Provide, ServerlessTrigger, ServerlessTriggerType } from '@midwayjs/core';

import { Context } from '@midwayjs/faas';
import { isProd } from "@/apis/utils";
import ReleaseRecordService from '@/apis/service/release-record';

@Provide()
export class ReleaseRecordHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  releaseRecordService!: ReleaseRecordService;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/release-record/getAndUpdateRecord'
  })
  async getAndUpdateRecord() {
      this.ctx.body = await this.releaseRecordService.getAndUpdateRecord()
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/release-record/add-record',
    method: 'post'
  })
  async addRecord() {
    try{
      const { data } = this.ctx.request.body;
      const newData = JSON.parse(data);
      if(newData && newData.diamond_key){
        // 1. 获取当前登录用户的类型
        const res = await this.releaseRecordService.create(newData);
        if(res.id){
          // 2. 返回当前登录用户信息
          this.ctx.body = {
            success: true,
            data: res
          };
        }else{
          this.ctx.body = {
            success: false,
            data: res,
            errorMsg: '数据存储失败'
          };
        }

      }else{
        this.ctx.body = {
          success: false,
          errorMsg: '输入有误，检查输入条件'
        }
      }
    }catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.TIMER, {
    type: 'every',
    value: '10',
  })
  async releaseDiamondTimer() {
    console.log("releaseDiamondTimer start")
    // TODO:临时开放全部
    if (isProd || true) {
      try{
        const result = await this.releaseRecordService.getAndUpdateRecord()
        if(result.success) {
          console.log("releaseDiamondTimer success")
        }else{
          console.log("releaseDiamondTimer error", result)
        }
      }catch(e){
        console.log("releaseDiamondTimer error catch", e)
      }
    } 
  }

}
