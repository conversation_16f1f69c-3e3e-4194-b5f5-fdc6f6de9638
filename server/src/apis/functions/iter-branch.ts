import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import DevBranchService from '@/apis/service/dev-branch';
import FreeDevBranchService from '@/apis/service/free-dev-branch';
import IterBranchService from '@/apis/service/iter-branch';
import BranchPluginService from '@/apis/service/branch-plugin';
import IterCalendarService from '@/apis/service/iter-calendar';
import IterDeliverService from '@/apis/service/iter-deliver';
import StabilityService from '@/apis/service/stability';
import ProjectService from '@/apis/service/project';
import GitService from '@/apis/service/git';
import DingtalkService from '@/apis/service/dingtalk';
import PackageService from '@/apis/service/package';
import DefService from '@/apis/service/def';
import ComponentDeliverService from '@/apis/service/component-deliver';
import ShrinkWrapService from '@/apis/service/shrinkwrap';
import FlBuyHTTP from '@/apis/http/fl-buy';
import { IIterBranch, IDeliverClient } from '@/apis/interface/iter-branch';
import { IGitOptResult } from '@/apis/interface/git-opts';
import { PRE_URL, PROD_URL } from '@/apis/const';
import { IterStatus, EBranchType } from '@/apis/const/iter-branch';
import { isProd, formatDate } from '@/apis/utils';
import PROJECT_CONFIG, { NOTICE_REGRESSION_TESTING, NOTICE_AT_MEMEBER_TEXT, PROJECT_TYPE_CN_NAME, NOTICE_AT_MEMEBER_TEXT_WITH_AP, EProjectType } from '@/apis/const/project';
import { useTmpDir, useGit } from '@/apis/middleware/hooks';
import { EDevStatus } from '@/apis/const/dev-branch';
import { IDevBranch } from '@/apis/interface/dev-branch';
import { IFreeDevBranch } from '@/apis/interface/free-dev-branch';
import { EFreeDevMountStatus } from '@/apis/const/free-dev-branch';
import HSFService from '@/apis/hsf';
import BaseHandler from '@/apis/functions/base';
import * as chalk from 'chalk';
import TowerHSF from '@/apis/hsf/tower';

import { IIterDeliverTask } from '@/apis/interface/iter-deliver';
import { IIterBranchDetailModel } from '@/apis/model/iter-branch-detail';

import { EClient, EMiniAppVersionStatus, DELIVER_CLIENT_STATIC_CFG } from '@/apis/const/iter-deliver';
import { getOriginalPlatform } from '@/apis/utils/deliver';
import { checkBizLinePackageSize } from '@/apis/utils/checkBizLinePackageSize';
import { getNoticeMemberIds } from '@/apis/utils/branch';

@Provide()
export class IterHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  devBranchService!: DevBranchService;

  @Inject()
  freeDevBranchService!: FreeDevBranchService;

  @Inject()
  iterCalendarService!: IterCalendarService;

  @Inject()
  iterDeliverService!: IterDeliverService;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  gitService!: GitService;

  @Inject()
  branchPluginService!: BranchPluginService;

  @Inject()
  packageService!: PackageService;

  @Inject()
  flBuyHTTP!: FlBuyHTTP;

  @Inject()
  hsfService!: HSFService;

  @Inject()
  defService!: DefService;

  @Inject()
  componentDeliverService!: ComponentDeliverService;

  @Inject()
  shrinkWrapService!: ShrinkWrapService;

  @Inject()
  IterBranchDetailModel!: IIterBranchDetailModel;

  @Inject()
  stabilityService!: StabilityService;

  @Inject()
  towerHSF!: TowerHSF;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/list',
    middleware: ['handlerMiddleware']
  })
  async list() {
    const { pageSize = 10, pageNum = 1, conditions } = this.ctx.query as IIterListReq;

    return await this.iterBranchService.list(Number(pageSize), Number(pageNum), conditions ? JSON.parse(conditions) : {});
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/get',
    middleware: ['handlerMiddleware']
  })
  async get() {
    const { iterId } = this.ctx.query as IIterGetReq;

    // 查询迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw new Error(`查询不到 id 为 ${iterId} 的迭代`)

    // 查询扩展信息
    const [project, devBranchList, freeDevBranchList] = await Promise.all([
      // 查询该迭代归属的项目信息
      this.projectService.get(iterBranch.projectName),
      Promise.all(iterBranch.devBranchList.map(devId => this.devBranchService.get(devId))),
      Promise.all(iterBranch.freeDevBranchList.map(devId => this.freeDevBranchService.get(devId)))
    ]);

    return {
      detail: iterBranch,
      project,
      devBranchList,
      freeDevBranchList
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/create',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async create() {
    const reqParams = this.ctx.request.body as IIterCreateReq;

    // 查询归属项目信息
    const project = await this.projectService.get(reqParams.projectName);
    if (!project) throw Error(`未查项目${reqParams.projectName}的信息`);

    // 鉴权
    await this.checkProjectPermission(project);

    // 创建迭代分支
    const iterBranch = await this.iterBranchService.create({
      creator: this.ctx.user.name,
      gmtCreate: formatDate(new Date()),
      project,
      ...reqParams
    });

    // 向迭代日历中增加迭代分支
    const [year, month, day] = reqParams.publishDay.split('-');
    await this.iterCalendarService.create({
      iterId: iterBranch.iterId,
      year: +year,
      month: +month,
      day: +day,
      projectName: reqParams.projectName
    });

    // 钉钉群消息通知
    this.sendMessage('create', '创建', iterBranch);

    return iterBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/delete',
    middleware: ['handlerMiddleware']
  })
  async delete() {
    const { iterId } = this.ctx.query;

    // 查询迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw new Error(`查询不到 id 为 ${iterId} 的迭代`)

    // 鉴权
    await this.checkProjectPermissionByProjectName(iterBranch.projectName);

    // 如果项目类型是组件，需要废弃def迭代
    this.iterBranchService.deleteComponent(iterId);

    // 删除迭代分支
    await this.iterBranchService.delete(iterId);

    // 钉钉群消息通知
    this.sendMessage('delete', '删除', iterBranch);

    return true;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/discard',
    middleware: ['handlerMiddleware']
  })
  async discard() {
    const { iterId } = this.ctx.query;

    // 查询迭代详情
    let iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw new Error(`查询不到 id 为 ${iterId} 的迭代`)

    // 鉴权
    await this.checkProjectPermissionByProjectName(iterBranch.projectName);

    // 废弃迭代分支
    iterBranch = await this.iterBranchService.discard(iterId)

    // 钉钉群消息通知
    if (iterBranch) this.sendMessage('discard', '废弃', iterBranch);

    return true;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/update',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async update() {
    const reqParams = this.ctx.request.body as IIterUpdateReq;

    // 查询迭代详情
    const oldIterBranch = await this.iterBranchService.get(reqParams.iterId);
    if (!oldIterBranch) throw Error(`查询不到 id 为 ${reqParams.iterId} 的迭代`)

    // 鉴权
    await this.checkProjectPermissionByProjectName(oldIterBranch.projectName);

    // 更新迭代分支
    const iterBranch = await this.iterBranchService.update({
      modifier: this.ctx.user.name,
      gmtModified: formatDate(new Date()),
      ...reqParams
    });

    // 3. 如果项目是组件类型，需要更新def
    if (iterBranch.branchType === EBranchType.COMPONENT && reqParams.description !== oldIterBranch.description) {
      // 修改描述
      const res = await this.defService.editIteration({
        iterationId: iterBranch.defIterId!,
        name: `${iterBranch.publishDay}发布迭代的冻结版本（分支号 rc/${iterBranch.version}）`,
        description: reqParams.description || ''
      })
      if (!res?.success ) {
        throw Error(`def迭代的描述修改失败`);
      }
    }

    // 钉钉群消息通知
    if (iterBranch) this.sendMessage('update', '更新', iterBranch);

    return iterBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/merge',
    middleware: ['handlerMiddleware']
  })
  async merge() {
    const { iterId, ignoreEmpty } = this.ctx.query;
    let defBranchId: number | undefined;

    // 查询迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw new Error(`查询不到 id 为 ${iterId} 的迭代`)
    else if (iterBranch.status !== IterStatus.PLAN) throw Error('只有“计划发布”状态才能集成')

    // 查询归属项目信息
    const project = await this.projectService.get(iterBranch.projectName);
    if (!project) throw Error(`查询不到 projectName 为 ${iterBranch.projectName} 的项目`);

    // 鉴权
    await this.checkProjectPermission(project);

    // 项目类型是模块类型
    const isComponentProject = iterBranch.branchType === EBranchType.COMPONENT;

    // 查询需要集成的（游离）开发分支
    const [devBranchList, freeDevBranchList] = await Promise.all([
      Promise.all(iterBranch.devBranchList.map(devId => this.devBranchService.get(devId))),
      Promise.all(iterBranch.freeDevBranchList.map(devId => this.freeDevBranchService.get(devId)))
    ]);
    const availableDevBranchList = devBranchList
    .flatMap(devBranch => devBranch?.status === EDevStatus.READY ? [devBranch] : [])
    const availableFreeDevBranchList = freeDevBranchList
    .flatMap(freeDevBranch => freeDevBranch?.status === EDevStatus.READY && freeDevBranch.mountStatus === EFreeDevMountStatus.MOUNTED ? [freeDevBranch] : [])

    let toBeMergedDevBranchList = [] as (IFreeDevBranch | IDevBranch)[];
    toBeMergedDevBranchList = toBeMergedDevBranchList.concat(availableDevBranchList).concat(availableFreeDevBranchList);

    // 若待集成开发分支为空，且没有设置 ignoreEmpty，则直接返回空数组，让端侧做警告
    if (!ignoreEmpty && toBeMergedDevBranchList.length === 0) {
      return [];
    };

    // git 操作
    const rcBranchName = `rc/${iterBranch.version}`;
    const { tmpDirPath } = useTmpDir();
    const { gitHandler, gitRepoInfo, projectTmpDirPath } = await useGit(tmpDirPath, {
      branchUrl: iterBranch.gitBranch.url,
      branchName: iterBranch.gitBranch.name,
    });
    const gitRes = await this.gitService.mergeIterAlters(gitHandler, {
      gitRepoInfo,
      projectTmpDirPath,
      iterBranchName: iterBranch.gitBranch.name,
      alterBranchList: toBeMergedDevBranchList.map(item => ({
        branch: item.gitBranch.name,
        mergeCode: item.mergeCode,
        npmList: item.npmList,
        npmResolutionList: item.npmResolutionList,
      })),
      rcBranchName,
      isComponentProject,
      version: iterBranch.version,
    });


    const mergeResults = gitRes.singleResList?.reduce((per, cur) => {
      if (cur.branch) per[cur.branch] = cur;
      return per;
    }, {} as { [key: string]: IGitOptResult }) || {};
    const toBeMergedDevBranchListWithMergeRes = toBeMergedDevBranchList.map(item => ({
      ...item,
      mergeRes: mergeResults[item.branchName]
    }))

    if (gitRes.errorMsg) {
      return {
        errorMsg: gitRes.errorMsg,
        data: toBeMergedDevBranchListWithMergeRes
      }
    }

    // 如果是组件形式，需要为组件创建def变更，并且绑定到def迭代上
    if (isComponentProject) {
      const aoneIdList = toBeMergedDevBranchList.map(item => item.aoneList?.[0]?.id);
      // 创建def变更
      const res = await this.defService.createBranch({
        appId: project?.deliverConfig?.defProjectId!,
        branch: rcBranchName,
        branchType: 'exist',
        description: `${iterBranch.publishDay}发布迭代的冻结版本（分支号 ${rcBranchName}）`,
        aoneBinds: aoneIdList
      });
      if (res?.success && res?.data) {
        defBranchId = res.data.id;
      } else {
        throw Error(`${iterBranch.projectName} 项目def冻结发布 创建变更失败`);
      }

      // def绑定变更到发布迭代
      const res2 = await this.defService.bindBranchToIteration({
        iterationId: iterBranch.defIterId!,
        branchIds: [defBranchId!],
      })
      if (!res2?.success) {
        throw Error(`${iterBranch.projectName} 项目def冻结发布 绑定当前变更失败`);
      }
    }

    // 将迭代状态置为“集成回归”
    const newIterBranch = await this.iterBranchService.update({
      modifier: this.ctx.user.name,
      gmtModified: formatDate(new Date()),
      iterId,
      status: IterStatus.MERGE,
      defBranchId,
    });

    // 钉钉群消息通知
    this.sendMessage('merge', '集成', newIterBranch);

    // 生成并上传 shrinkwrap.json
    this.branchPluginService.getShrinkwrap({
      projectTmpDirPath, gitHandler, gitRepoInfo, branchName: rcBranchName
    }).then(shrinkwrapUrl => {
      this.iterBranchService.update({ // 将生成的 shrinkwrap.json 文件地址写入表中
        iterId,
        shrinkwrap: shrinkwrapUrl
      });

      // 获取重复依赖，如果有，则通知
      this.notifyMultiVersion(shrinkwrapUrl, newIterBranch.gitBranch.url, rcBranchName, iterId, newIterBranch.projectName);
    }).catch(err => {
      this.ctx.logger.error(err)
    })

    // 项目类型是小程序时，触发构建
    let defBuildRes
    if (!isComponentProject) {
      // 如果是一体化微信小程序，需要创建def迭代，并绑定rc分支
      const miniType = project.type;
      if (miniType === 'weixin') {
        const aoneIdList = toBeMergedDevBranchList.map(item => item.aoneList?.[0]?.id);
        defBuildRes = await this.branchPluginService.defBuildAndCreateAutomatedTestTask(rcBranchName, aoneIdList, iterBranch, project)
      } 

      // 这里面是创建王守义打码 & 自动测试，王守义目前打码是开发码，主要是用于做结构分析，先保留，后续看下是不是能替换去掉
      this.branchPluginService.buildAndCreateAutomatedTestTask(newIterBranch).then(({ dist, towerId, packageResult }) => {
        // 绑定构建产物
        if (dist) this.iterBranchService.addDist(iterId, dist);

        // 绑定包大小文件(report_analyzed.json)
        if (packageResult && packageResult.reportAnalyzed) this.iterBranchService.addReportAnalyzed(iterId, packageResult.reportAnalyzed);

        // TODO: 暂时去除开发分支的自动化测试
        // 绑定塔台id
        // if (towerId) this.iterBranchService.bindTowerId(iterId, towerId);

        // 判断是否超出行业包大小限制，如果超过则告警
        const {
          isWarning = false,
          warningList = [],
        } = checkBizLinePackageSize(packageResult, 'all');
        if (isWarning) this.sendNotice(warningList, newIterBranch);
      }).catch(err => {
        this.ctx.logger.error(`${newIterBranch.projectName} ${newIterBranch.version} 王守义打码失败`,  err)
      })
    }
    // 奥特发布后台钩子
    this.flBuyHTTP.pubHook({
      iterId,
      operateName: '冻结集成',
      appName: newIterBranch.projectName,
      version: newIterBranch.version,
      desc: newIterBranch.description,
    })

    // return toBeMergedDevBranchListWithMergeRes
    return {
      defBuildRes,
      data: toBeMergedDevBranchListWithMergeRes
    }
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/cancel-merge',
    middleware: ['handlerMiddleware']
  })
  async cancelMerge() {
    const { iterId } = this.ctx.query;

    // 查询迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw new Error(`查询不到 id 为 ${iterId} 的迭代`)
    else if (iterBranch.status !== IterStatus.MERGE) throw Error('只有“集成回归”状态才能集成')

    // 查询归属项目信息
    const project = await this.projectService.get(iterBranch.projectName);
    if (!project) throw Error(`查询不到 projectName 为 ${iterBranch.projectName} 的项目`);

    // 鉴权
    await this.checkProjectPermission(project);

    // 项目类型是模块类型
    const isComponentProject = iterBranch.branchType === EBranchType.COMPONENT;

    // git 操作
    // TODO: 删除 rc 分支

    // 如果是组件形式，需要取消def迭代里的变更并删除
    if (isComponentProject) {
      // 先查询发布迭代全部分支再找到当前分支取消绑定
      const detailRes = await this.defService.getIterationInfoById({
        iterationId: iterBranch.defIterId!
      })
      if (detailRes?.success) {
        const id = detailRes.data.iteration.devbranches.filter(item => item.branch_id === iterBranch.defBranchId)?.[0]?.id
        const unbindRes = await this.defService.unbindBranchToIteration({
          iterationId: iterBranch.defIterId!,
          id,
        })
        if (unbindRes?.success) {
          const deleteRes = await this.defService.deleteBranch({
            appId: project?.deliverConfig?.defProjectId!,
            branchId: iterBranch.defBranchId!
          });
          if (!deleteRes?.success) {
            throw Error(`${iterBranch.projectName} 项目def取消冻结 删除变更失败`);
          }
        } else {
          throw Error(`${iterBranch.projectName} 项目def取消冻结 解绑变更失败`);
        }
      } else {
        throw Error(`${iterBranch.projectName} 项目def取消冻结 查询变更失败`);
      }
    }

    // 将迭代状态置为“计划发布”
    const newIterBranch = await this.iterBranchService.update({
      modifier: this.ctx.user.name,
      gmtModified: formatDate(new Date()),
      iterId,
      status: IterStatus.PLAN,
      towerId: null,
      dist: null,
      shrinkwrap: null,
      autoNoticeStatus: 0,
      reportAnalyzed: null,
      defBranchId: null,
    });

    // 钉钉群消息通知
    if (newIterBranch) this.sendMessage('cancelMerge', '取消集成', newIterBranch);

    // 奥特发布后台钩子
    this.flBuyHTTP.pubHook({
      iterId,
      operateName: '取消冻结',
      appName: newIterBranch.projectName,
      version: newIterBranch.version,
      desc: newIterBranch.description,
    })

    return true;
  }

  /**
   * @deprecated
   * 新投放链路下将不再使用该接口
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/review',
    middleware: ['handlerMiddleware']
  })
  async review() {
    const { iterId } = this.ctx.query as IIterReviewReq;

    // 查询迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw new Error(`查询不到 id 为 ${iterId} 的迭代`)
    else if (iterBranch.status !== IterStatus.MERGE) throw Error('只有“集成回归”状态，才能提审')

    // 鉴权
    await this.checkProjectPermissionByProjectName(iterBranch.projectName);

    // 更新迭代状态
    const newIterBranch = await this.iterBranchService.update({
      iterId,
      status: IterStatus.AUDITING,
      modifier: this.ctx.user.name,
      gmtModified: formatDate(new Date()),
    });

    // 【异步】钉钉群消息通知
    this.sendMessage('review', '提审', newIterBranch);

    // 【异步】奥特发布后台钩子
    this.flBuyHTTP.pubHook({
      iterId,
      operateName: '提审',
      appName: newIterBranch.projectName,
      version: newIterBranch.version,
      desc: newIterBranch.description,
    })

    return newIterBranch;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/publish',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async publish() {
    const { iterId, isGray, grayStatus } = this.ctx.request.body as IIterPublishReq;

    // 查询迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw new Error(`查询不到 id 为 ${iterId} 的迭代`)
    const useNewDeliver = !!iterBranch.deliverClientList; // 是否使用新投放链路

    // 鉴权
    await this.checkProjectPermissionByProjectName(iterBranch.projectName);

    // 上架操作的前置检测
    if (!isGray) {
      if (iterBranch.status === IterStatus.PUBLISHED) { // 已发布的过不能再发布
        throw Error(`迭代版本 ${iterBranch.version} 已发布`);
      }

      // 新链路，前置状态必须所有投放端都上架或跳过投放
      if (useNewDeliver) {
        const failedDeliverClientList = await Promise.all((iterBranch.deliverClientList as IDeliverClient[]).map(async deliverClient => {
          // 若是跳过投放，通过
          if (deliverClient.skip) {
            return false
          } else {
            // 没有绑定投放任务id，不通过
            if (!deliverClient.deliverTaskId) return true

            // 获取绑定的投放任务
            const deliverTask = await this.iterDeliverService.getById(deliverClient.deliverTaskId, { needVersionInfo: true });
            if (!deliverTask) throw Error(`查询不到 id 为 ${deliverClient.deliverTaskId} 的投放任务`)

            // 还未生成小程序版本信息，不通过
            if (!deliverTask.miniAppVersionInfo) return true

            // 状态不是已上架，不通过
            return deliverTask.miniAppVersionInfo.status !== EMiniAppVersionStatus.RELEASE;
          }
        }))

        if (failedDeliverClientList.some(item => item)) throw Error('投放端必须上架或跳过投放，才能发布代码')
      } else {
        // 老链路，前置状态必须是灰度中
        if (iterBranch.status !== IterStatus.GRAY) {
          throw Error('必须先进行灰度，才能发布代码');
        }
      }

      // 合并 rc 分支到主干
      if (!iterBranch.rcGitBranch) throw Error('集成分支为空');
      await this.gitService.publishIter(iterBranch.rcGitBranch.name, iterBranch.rcGitBranch.url);
    }

    // 更新迭代分支
    const newIterBranch = await this.iterBranchService.update({
      iterId,
      status: isGray ? IterStatus.GRAY : IterStatus.PUBLISHED,
      grayStatus,
      modifier: this.ctx.user.name,
      gmtModified: formatDate(new Date()),
    });

    // 钉钉群消息通知，新链路不再发消息
    if (!useNewDeliver) this.sendMessage('publish', '发布', newIterBranch);

    // 奥特发布后台钩子
    if (useNewDeliver) {
      this.flBuyHTTP.pubHook({
        iterId,
        operateName: '发布(合并master)',
        appName: newIterBranch.projectName,
        version: newIterBranch.version,
        desc: newIterBranch.description,
      })
    } else {
      this.flBuyHTTP.pubHook({
        iterId,
        operateName: isGray ? '灰度' : '发布',
        rate: grayStatus,
        appName: newIterBranch.projectName,
        version: newIterBranch.version,
        desc: newIterBranch.description,
      })
    }

    return newIterBranch;
  }

  /**
   * @deprecated
   * 新投放链路下将不再使用该接口
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/rollback',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async rollback() {
    const { iterId } = this.ctx.request.body as IIterRollbackReq;

    // 查询迭代详情
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw Error(`查询不到 id 为 ${iterId} 的迭代`)
    else if (iterBranch.status !== IterStatus.GRAY && iterBranch.status !== IterStatus.AUDITING) throw Error('只有“审核中”或“灰度中”状态才能回滚')

    // 鉴权
    await this.checkProjectPermissionByProjectName(iterBranch.projectName);

    // 将迭代状态回退为“计划发布”
    const newIterBranch = await this.iterBranchService.update({
      modifier: this.ctx.user.name,
      gmtModified: formatDate(new Date()),
      iterId,
      status: IterStatus.PLAN,
      grayStatus: null,
      towerId: null,
      dist: null,
      shrinkwrap: null,
    });

    // 钉钉群消息通知
    if (newIterBranch) this.sendMessage('rollback', '回滚', newIterBranch);

    // 奥特发布后台钩子
    this.flBuyHTTP.pubHook({
      iterId,
      operateName: '回滚',
      appName: newIterBranch.projectName,
      version: newIterBranch.version,
      desc: newIterBranch.description,
    })

    return newIterBranch;
  }

  /** 获取投放端列表（包含投放任务） */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/list-deliver-client',
    middleware: ['handlerMiddleware']
  })
  async listDeliverClient() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.query, ['iterId'])

    // 析取参数
    const { iterId } = this.ctx.query as { iterId: string; };

    // 前置判断
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw Error(`查询不到 iterId 为 ${iterId} 的迭代`)

    // 为空直接返回
    if (!iterBranch.deliverClientList) return [];

    // 根据投放端查询投放状态
    const deliverClientWithTaskList = await Promise.all(iterBranch.deliverClientList.map(async item => {
      // 如果绑定了投放任务id，则顺带返回投放任务详情
      let deliverTask: IIterDeliverTask | null = null;
      if (item.deliverTaskId) {
        deliverTask = await this.iterDeliverService.getById(item.deliverTaskId, { needVersionInfo: true })
        if (!deliverTask) throw Error(`获取投放任务失败(id:${item.deliverTaskId})`)
      }

      // 投放端扩展信息
      const clientExtInfo = DELIVER_CLIENT_STATIC_CFG[item.clientName];

      return {
        ...item,
        clientExtInfo: clientExtInfo ? {
          cnName: clientExtInfo.cnName,
          icon: clientExtInfo.icon,
        } : null,
        deliverTask,
        originalPlatform: getOriginalPlatform(item.miniAppId, item.clientName)
      }
    }))

    return deliverClientWithTaskList
  }

  /** 获取投放端（包含投放任务） */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/get-deliver-client',
    middleware: ['handlerMiddleware']
  })
  async getDeliverClient() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.query, ['iterId', 'miniAppId'])

    // 析取参数
    const { iterId, clientName, miniAppId } = this.ctx.query as { iterId: string; clientName?: EClient; miniAppId: string; };

    // 前置判断
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw Error(`查询不到 iterId 为 ${iterId} 的迭代`)

    // 为空直接返回
    if (!iterBranch.deliverClientList) return null;

    // 匹配指定投放端
    const matchRes = iterBranch.deliverClientList.filter(item => {
      if (item.miniAppId !== miniAppId) return false;

      return clientName ? item.clientName === clientName : true
    });
    if (matchRes.length > 1) throw Error('超过一条数据被匹配到')
    const deliverClient = matchRes[0]
    if (!deliverClient) return null;

    // 如果绑定了投放任务id，则顺带返回投放任务详情
    let deliverTask: IIterDeliverTask | null = null;
    if (deliverClient.deliverTaskId) {
      deliverTask = await this.iterDeliverService.getById(deliverClient.deliverTaskId, { needVersionInfo: true })
    }

    return {
      ...deliverClient,
      deliverTask
    }
  }

  /** 跳过投放 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/skip-deliver',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async skipDeliver() {
    return await this._switchSkip(true);
  }

  /** 取消跳过投放 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/cancel-skip-deliver',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async cancelSkipDeliver() {
    return await this._switchSkip(false);
  }

  async _switchSkip(skip: boolean) {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId', 'clientName', 'miniAppId'])

    // 析取参数
    const { iterId, clientName, miniAppId } = this.ctx.request.body as { iterId: number; clientName: EClient; miniAppId: string; };

    // 鉴权
    await this.checkProjectPermissionByIterId(iterId);

    // 更新对应投放端的“跳过”状态
    await this.iterBranchService.updateDeliverClient(iterId, {
      clientName,
      miniAppId
    }, {
      skip
    })

    return true;
  }

  /** 解绑投放任务 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/unbind-deliver-task',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async unbindDeliverTask() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId'])

    // 析取参数
    const { iterId, clientName, miniAppId } = this.ctx.request.body as { iterId: number; clientName?: EClient; miniAppId?: string; };

    // 鉴权
    await this.checkProjectPermissionByIterId(iterId);

    // 置空对应投放端绑定的投放任务id
    await this.iterBranchService.updateDeliverClient(iterId, {
      miniAppId,
      clientName,
    }, {
      deliverTaskId: null
    })

    return true;
  }

  /** 绑定投放任务 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/bind-deliver-task',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async bindDeliverTask() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId', 'deliverTaskId'])

    // 析取参数
    const { iterId, deliverTaskId, clientName, clientList, miniAppId } = this.ctx.request.body as { iterId: number; deliverTaskId: number; clientName?: EClient; clientList?: EClient[]; miniAppId?: string; };

    // 鉴权
    await this.checkProjectPermissionByIterId(iterId);

    // 置空对应投放端绑定的投放任务id
    await this.iterBranchService.updateDeliverClient(iterId, {
      miniAppId,
      clientName,
      clientList
    }, {
      deliverTaskId
    })

    return true;
  }

  /** 组件类型的迭代状态更新（组件已经发布完成，迭代状态从发布状态改为投放中，删除所有无用的def迭代和变更） */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/start-component-deliver',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async startComponentDeliver() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId', 'status']);

    // 析取参数
    const { iterId, status } = this.ctx.request.body as { iterId: number; status: IterStatus; };

    // 鉴权
    await this.checkProjectPermissionByIterId(iterId);

    // 更新迭代分支
    const iterBranch = await this.iterBranchService.update({
      modifier: this.ctx.user.name,
      gmtModified: formatDate(new Date()),
      iterId,
      status,
    });

    // 查询已集成的（游离）开发分支
    const [devBranchList, freeDevBranchList] = await Promise.all([
      Promise.all(iterBranch.devBranchList.map(devId => this.devBranchService.get(devId))),
      Promise.all(iterBranch.freeDevBranchList.map(devId => this.freeDevBranchService.get(devId)))
    ]);
    const availableDevBranchList = devBranchList
    .flatMap(devBranch => devBranch?.status === EDevStatus.READY ? [devBranch] : []);
    const availableFreeDevBranchList = freeDevBranchList
    .flatMap(freeDevBranch => freeDevBranch?.status === EDevStatus.READY && freeDevBranch.mountStatus === EFreeDevMountStatus.MOUNTED ? [freeDevBranch] : []);

    let mergedDevBranchList = [] as (IFreeDevBranch | IDevBranch)[];
    mergedDevBranchList = mergedDevBranchList.concat(availableDevBranchList).concat(availableFreeDevBranchList);

    // 发布文本通知并@相关人员
    const userIds = mergedDevBranchList.map(branch => branch.creatorWorkid).filter(i => !!i) as string[];
    if (iterBranch) this.sendMessage('startComponentDeliver', '发布模块至线上', iterBranch, { userIds, msg: `组件 v${iterBranch.version} 可以投放线上页面/小程序啦~` });

    // 查询归属项目信息
    const project = await this.projectService.get(iterBranch.projectName);
    if (!project) throw Error(`查询不到 projectName 为 ${iterBranch.projectName} 的项目`);

    // 删除已预发投放的页面对应的def迭代，以及迭代下的def变更
    mergedDevBranchList.forEach(async (devBranch) => {
      const deliverBranchRes = await this.componentDeliverService.list({ pageSize: 20, pageNum: 1, conditions: { devId: devBranch.devId } });
      if (deliverBranchRes?.total) {
        await this.deleteDefIterationBranches(project, deliverBranchRes.list.map(devBranch => devBranch.defIterId!));

        const abandonIters = deliverBranchRes.list.map((devBranch) => this.defService.abandonIteration({
          iterationId: devBranch.defIterId!
        }))
        const res = await Promise.all(abandonIters);
        if (res.some(item => !item?.success)) throw Error(`项目废弃${iterId}迭代已投放的页面def迭代失败`);
      }
    })

    // 删除当前迭代下的全部开发分支对应的def迭代，以及迭代下的def变更
    await this.deleteDefIterationBranches(project, mergedDevBranchList.map(devBranch => devBranch.defIterId!));

    const abandonIters = mergedDevBranchList.map((devBranch) => this.defService.abandonIteration({
      iterationId: devBranch.defIterId!
    }))
    const res = await Promise.all(abandonIters);
    if (res.some(item => !item?.success)) throw Error(`项目废弃${iterId}迭代已集成分支的def迭代失败`);

    return iterBranch;
  }


  // 用于def hook触发，目前仅微信小程序def配置
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/def-hook-trigger',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async defHookTrigger() {
    // const a = {"is_successful":true,"build":{},"deploy":{"pages":[],"assets":[],"page_multiend":[],"tnpm_package":{},"tnpm_multi_packages":[],"tnpm_assets":{},"tnpm_multi_assets":[],"assets_plus":[],"web_assets":[],"web_pages":[]},"pub_env":"prepub","task_id":136221188,"app":{"type":"tbapp_v2","id":311752,"description":"飞猪一体化微信小程序"},"appconf":{"faas_id":null,"level":0},"user":{"emp_id":"414026","nick":"桑陆","name":"杨金凤","dept_name":"飞猪-飞猪-飞猪技术部-用户技术-用户营销前端-用户产品前端和多端技术","dept_path":"00001/K6033/K6034/72609/48805/88961/L9833","dept_no":"L9833"},"repo":{"project":"rx-fliggy-weixin","group":"trip","branch":"def_releases_2024032120553838_trip_rx-fliggy-weixin/4.3.15","commit_id":"319c975807daa464d9063d6065c004e49460cff8"},"version":"4.3.15","iteration":{"id":15193902,"name":"4.3.15","description":"周四正常迭代","version":"4.3.15"},"iterationconf":{"trunk":"master","changefree_id":"15193902-319c975807daa464d9063d6065c004e49460cff8-1711027157110","changefree_executor":"414026"},"branches":{"releasebranch":{"branch":"def_releases_2024032120553838_trip_rx-fliggy-weixin/4.3.15","commit_id":"319c975807daa464d9063d6065c004e49460cff8"},"devbranches":[{"id":21143139,"branch":"rc/4.3.15","description":"","emp_id":"414026","aonecr_id":null,"aone_binds":"req|54426192,req|54732448,req|54388216,req|55637034,req|48735782,req|55524272,req|55175947","cr_status":null,"commit_id":"319c975807daa464d9063d6065c004e49460cff8"}]},"hl_faas":[]}
    const b = {"is_successful":true,"build":{},"deploy":{"pages":[],"assets":[],"page_multiend":[],"tnpm_package":{},"tnpm_multi_packages":[],"tnpm_assets":{},"tnpm_multi_assets":[],"assets_plus":[],"web_assets":[],"web_pages":[]},"pub_env":"prepub","task_id":144073823,"app":{"type":"tbapp_v2","id":311752,"description":"飞猪一体化微信小程序"},"appconf":{"faas_id":null,"level":0},"user":{"emp_id":"414026","nick":"桑陆","name":"杨金凤","dept_name":"飞猪-飞猪-飞猪技术部-用户技术-用户营销前端-用户产品前端和多端技术","dept_path":"00001/K6033/K6034/72609/48805/88961/L9833","dept_no":"L9833"},"repo":{"project":"rx-fliggy-weixin","group":"trip","branch":"def_releases_20250724160814140003_trip_rx-fliggy-weixin/4.5.5","commit_id":"c1805210c3145322c0edec2704c767f96a357062"},"version":"4.5.5","iteration":{"id":142,"name":"4.5.5","description":"周四正常迭代","version":"4.5.5"},"iterationconf":{"trunk":"master","changefree_id":"16165921-c1805210c3145322c0edec2704c767f96a357062-1753361943007","changefree_executor":"414026","is_pause_gitclean":null},"branches":{"releasebranch":{"branch":"def_releases_20250724160814140003_trip_rx-fliggy-weixin/4.5.5","commit_id":"c1805210c3145322c0edec2704c767f96a357062"},"devbranches":[{"id":22194595,"branch":"rc/4.5.5","description":"","emp_id":"414026","aonecr_id":null,"aone_binds":"req|67490485,req|63782591","cr_status":null,"commit_id":"c1805210c3145322c0edec2704c767f96a357062"}]},"hl_faas":[]};
    this.ctx.logger.info(`lingyue def >>> ${JSON.stringify(this.ctx.request.body)}`);
    const { is_successful, version, repo, task_id, iteration } = b;

    if (!is_successful) return false;

    // def 版本号查询对应 一体化平台的迭代ID
    // 这个写法只兼容了一体化小程序仓库
    const projectName = repo.project?.replace('rx-', '') || '';
    
    const iterId = await this.iterBranchService.findIterIdByVersion(version, projectName);
    let qrCodeLink = '';
    if (!iterId) {
      return {
        errorMsg: '缺少 iterId 参数'
      };
    }

    // 查询def的构建信息
    const defResult = await this.defService.getIterPublishTaskDetail({
      updateDB: false,
      // taskType: EDefBuildTaskType.ITERATION,
      iterId: iteration.id,
      defTaskId: task_id
    })

    if (!defResult?.success) {
      return {
        errorMsg: `查询def 迭代id ${iteration.id} 任务id ${task_id} 的构建信息失败`
      };
    }

    const { flownodes } = defResult.data;
    const prepubResult = flownodes.find(cur => cur.name === 'miniapp-prepub');
    const { output = '' } = prepubResult || {};
    let uploadResult = '';
    let buildUrl = ''
    try {
      const { preBuildResultTarGzPackageOSSURL, releaseResult } = JSON.parse(output)?.result[0];

      uploadResult = JSON.stringify(releaseResult);
      buildUrl = preBuildResultTarGzPackageOSSURL
    } catch(e) {
      return {
        errorMsg: 'def构建结果parse失败'
      };
    }

    console.log(`lingyue 数据库更新def构建和包大小 >>>`);
    // lingyue 更新自动更新的状态
    // await this.IterBranchDetailModel.update({
    //   auto_notice_status: 1,
    //   def_dist: buildUrl,
    //   def_upload_pkg_res: uploadResult
    // }, {
    //   where: { iter_id: iterId },
    //   fields: ['auto_notice_status', 'def_dist', 'def_upload_pkg_res']
    // });

    // 以下是通知需要的调用
    // 1. 查询迭代详情
    const iterBranch = await this.iterBranchService.get(Number(iterId));
    if (!iterBranch) {
      return {
        errorMsg: `未查询到 id 为 ${iterId} 的迭代`
      };
    }

    // 2. 查询归属项目信息
    const project = await this.projectService.get(iterBranch.projectName, {
      needDingtalkRobots: true
    });
    if (!project) {
      return {
        errorMsg: `未查询到 id 为 ${iterId} 的迭代的归属项目`
      };
    }
   
     // const type = iterBranch?.deliverClientList?.[0]?.clientName || ''
    // 3. 发送通知
    const config = NOTICE_REGRESSION_TESTING[project.type];
    if (!config) {
      return {
        errorMsg: `${project.type} 类型项目不支持通知回归`
      };
    }

    // 优先取传递的二维码，无则读取配置的
    qrCodeLink = qrCodeLink || config.qrCodeLink;
    if (!qrCodeLink) {
      return {
        errorMsg: '二维码链接为空'
      };
    }

    this.ctx.logger.info(chalk.green(`通知回归测试 >>> iterId:${iterId}, qrCodeLink:${qrCodeLink}`));

    // 钉钉通知
    // this.dingtalkService.sendMarkdownMessage({
    //   title: config.title.replace(/\$\{version\}/g, iterBranch.version),
    //   text: config.text.replace(/\$\{version\}/g, iterBranch.version).replace(/\$\{qrCodeLink\}/g, qrCodeLink),
    //   isAtAll: true
    // }, project)
    // 邮件通知
    // Promise.all([
    //   Promise.all(iterBranch.devBranchList.map(devId => this.devBranchService.get(devId))),
    //   Promise.all(iterBranch.freeDevBranchList.map(devId => this.freeDevBranchService.get(devId)))
    // ]).then(([devBranchList, freeDevBranchList]) => {
    //   const list = ([] as (IDevBranch | IFreeDevBranch | null)[]).concat(devBranchList, freeDevBranchList).flatMap(res => res ? [res] : []);
    //   if (qrCodeLink) this.towerHSF.sendMiniAppEmail(qrCodeLink, list, iterBranch);
    //   // 钉钉通知到具体人
    //   const memberIds = getNoticeMemberIds(list, iterBranch.checked, iterBranch.qaList);
    //   if (memberIds.length) {
    //     const memberAtText = `@${memberIds.join('@')}`;
    //     this.dingtalkService.sendTextMsg({
    //       atUserIds: memberIds,
    //       content: memberAtText + (project.type === EProjectType.ALIPAY ? NOTICE_AT_MEMEBER_TEXT_WITH_AP : NOTICE_AT_MEMEBER_TEXT).replace(/\$\{iterId\}/g, `${iterId}`).replace(/\$\{appName\}/g, project.cnName || PROJECT_TYPE_CN_NAME[project.type]),
    //     }, project);
    //   }
    // }).catch(err => {
    //   this.ctx.logger.error(err)
    // })

    // 创建塔台任务
    // if (qrCodeLink) {
    //   this.branchPluginService.createAutomatedTestTask(iterBranch, { qrCodeLink, onlyBuild: false }).then(({ towerId }) => {
    //     // 绑定塔台id
    //     if (towerId) this.iterBranchService.bindTowerId(iterId, towerId);
    //   })
    // }

    try {
      // 上传shrinkwrap.json
      // this.uploadShrinkwrap(iterId, buildUrl);
      // 生成发版报告
      this.stabilityService.generatePublishReport( buildUrl, iterBranch, project);
    } catch (err) {
      this.ctx.logger.error(`生成 shrinkwrap.json 失败：${err}`);
    }
    return true;
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-branch/switch-checked',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async switchCheck() {
    // 1. 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId']);
    const { iterId, checked } = this.ctx.request.body;

    // 2. 确认已回归
    return this.iterBranchService.switchChecked(iterId, checked);
  }


  /** 上传shrinkwrap 并发送依赖变更通知 */
  async uploadShrinkwrap(iterId, buildUrl) {
    const { unzipFilePath, removeTmpDir } = await this.shrinkWrapService.unzipFile(buildUrl);
    await this.shrinkWrapService.uploadShrinkWrap(iterId, buildUrl, unzipFilePath)
    removeTmpDir();
  }

  /**
   * 删除N个迭代下的第1个def变更
   * @param project 项目配置
   * @param defIterIds 迭代id列表
   */
  async deleteDefIterationBranches(project, defIterIds: number[] = []) {
    try {
      // 1. 查询发布迭代全部变更分支
      const getDefBranchDetails = defIterIds.map((defIterId) => this.defService.getIterationInfoById({
        iterationId: defIterId
      }))
      const detailResList = await Promise.all(getDefBranchDetails);

      // 2. 从信息中获取def迭代id和变更id
      let defBranchDetails =  detailResList.map(detailRes => {
        if (detailRes?.success) {
          // 页面一般来说就一个变更，所以取第一个
          return {
            iterationId: detailRes.data.iteration.id,
            id: detailRes.data.iteration.devbranches?.[0]?.id,
            branchId: detailRes.data.iteration.devbranches?.[0]?.branch_id
          }
        } else {
          return null;
        }
      })
      defBranchDetails = defBranchDetails.filter(defBranch => defBranch && defBranch.iterationId && defBranch.branchId && defBranch.id);

      // 3. 把变更从迭代中解绑
      const unbindDefBranch = defBranchDetails.map(defBranch => this.defService.unbindBranchToIteration({
        iterationId: defBranch!.iterationId,
        id: defBranch?.id,
      }))
      await Promise.all(unbindDefBranch);

      // 4. 删除变更
      const deleteDefBranch = defBranchDetails.map(defBranch => this.defService.deleteBranch({
        appId: project?.deliverConfig?.defProjectId!,
        branchId: defBranch!.branchId
      }))
      await Promise.all(deleteDefBranch);
    } catch (error) {
      console.log('error', error);
    }
  }

  /**
   * 发送消息
   */
  async sendMessage(action: string, actionName: string, iterBranch: IIterBranch, textMsgParams?: { userIds?: string[]; msg: string; }) {
    // 获取项目详情
    const project = await this.projectService.get(iterBranch.projectName, { needDingtalkRobots: true });
    if (!project) throw Error(`查询不到 projectName 为 ${iterBranch.projectName} 的项目`)

    let gitBranch = iterBranch.rcGitBranch || iterBranch.gitBranch;
    let title = '';
    if (action === 'publish') {
      title = iterBranch.grayStatus || actionName;
    } else {
      title = actionName
    }

    // 发送钉群消息
    this.dingtalkService.sendActionCardMessage({
      title: `[${project.cnName}]迭代 v${iterBranch.version} 已${title}`,
      singleTitle: '查看详情',
      singleURL: `${isProd ? PROD_URL : PRE_URL}/#/iter/detail?iterId=${iterBranch.iterId}`,
      text: [
        `![bg](${PROJECT_CONFIG[project.type].dingtalkBigLogo})`,
        `**迭代 v${iterBranch.version} 已${title}**`,
        `- 迭代描述：${iterBranch.description}`,
        `- 所属项目：${project.cnName || '未知'}`,
        `- 基线分支：[${iterBranch.gitBranch.name}](${iterBranch.gitBranch.url})`,
        iterBranch.rcGitBranch && `- 集成分支：[${gitBranch.name}](${gitBranch.url})`,
        iterBranch.pkgPublishVersion ? `- 模块版本：${iterBranch.pkgPublishVersion}` : '',
        `- 执行人：${this.ctx.user.name}`,
        `- 执行时间：${formatDate(new Date())}`
      ].filter(item => !!item).join('\n')
    }, project)

    if (textMsgParams) {
      // 延时发送 自定义文本消息@相关人员
      setTimeout(() => {
        const { userIds, msg } = textMsgParams;
        this.dingtalkService.sendTextMsg({ content: msg, atUserIds: userIds?.length ? userIds : [] }, project)
      }, 1000)
    }
  }

  /**
   * 通知多版本依赖
   */
  async notifyMultiVersion(shrinkwrapUrl: string, branchUrl: string, branchName: string, iterId: number, projectCName: string) {
    const { multiVersionCount, realMultiVersionCount, multiVersionPackageCount, realMultiVersionPackageCount, disabledNpmList } = await this.packageService.analyzeDependencies(shrinkwrapUrl, branchUrl, branchName);
    const project = await this.projectService.get(projectCName, {});
    if(!project) return;
    const title = `${project?.cnName} · 迭代${branchName}依赖检查`;
    const header = [
      `![bg](${PROJECT_CONFIG[project.type].dingtalkLogo})`,
      `**${project?.cnName}·迭代${branchName}依赖检查**`,
      `- 不建议使用依赖：${disabledNpmList.slice(0, 3).join('、')}等${disabledNpmList.length || 0}个`,
      `- 重复依赖包个数：**${realMultiVersionCount}个**（另有${multiVersionCount - realMultiVersionCount}个不纳入统计）`,
      `- 重复依赖包版本数：**${realMultiVersionPackageCount}个**（另有${multiVersionPackageCount - realMultiVersionPackageCount}个不纳入统计）`,
    ]
    const bottom = [
      `\n**➡️[点击查看详情](${isProd ? PROD_URL : PRE_URL}/#/package/dependencies?iterId=${iterId})⬅️**`
    ]
    const content = header.concat(bottom).join('\n');
    this.dingtalkService.notice(project.adminWorkidList, title, content);
  }

  /**
   * 单聊发消息
   * */
  async sendNotice(warningList: any, iterBranch: IIterBranch) {
    // 获取项目详情
    const project = await this.projectService.get(iterBranch.projectName, { needDingtalkRobots: true });
    if (!project) throw Error(`查询不到 projectName 为 ${iterBranch.projectName} 的项目`)

    let gitBranch = iterBranch.rcGitBranch || iterBranch.gitBranch;
    const header = [
      `![bg](${PROJECT_CONFIG[project.type].dingtalkBigLogo})`,
      `**迭代 v${iterBranch.version} 包体积超限告警**`,
      `- 迭代描述：${iterBranch.description}`,
      `- 所属项目：${project.cnName || '未知'}`,
      `- 基线分支：[${iterBranch.gitBranch.name}](${iterBranch.gitBranch.url})`,
      iterBranch.rcGitBranch && `- 集成分支：[${gitBranch.name}](${gitBranch.url})`,
      `- 执行人：${this.ctx.user.name}`,
      `- 执行时间：${formatDate(new Date())}`,
      `- 包体积详情如下：`,
    ];
    const content = warningList.map(item => `- **${item.groupName}**：本次迭代体积[${item.currentSize}]，体积${item.isExceed ? '超出' : '仅剩'}[**<font color='${item.isExceed ? 'red' : 'orange'}'>${item.bufferSize}</font>**]`);

    // 发送钉群消息
    this.dingtalkService.sendActionCardMessage({
      title: `微信小程序[${project.cnName}]迭代 v${iterBranch.version} 包体积超限告警`,
      singleTitle: '查看详情',
      singleURL: `${isProd ? PROD_URL : PRE_URL}/#/package/structure?iterId=${iterBranch.iterId}`,
      text: header.concat(content).filter(item => !!item).join('\n'),
    }, project);
  }
}

interface IIterGetReq {
  iterId: IIterBranch['iterId'];
}

interface IIterListReq {
  pageSize?: string;
  pageNum?: string;
  conditions?: string;
}

interface IIterCreateReq {
  version: IIterBranch['version'];
  description: IIterBranch['description'];
  publishDay: IIterBranch['publishDay'];
  projectName: IIterBranch['projectName'];
}

interface IIterUpdateReq {
  iterId: IIterBranch['iterId'];
  description: IIterBranch['description'];
  status: IIterBranch['status'];
  grayStatus: IIterBranch['grayStatus'];
}

interface IIterPublishReq {
  iterId: IIterBranch['iterId'];
  // 是否灰度
  isGray: boolean;
  grayStatus?: IIterBranch['grayStatus'];
}

interface IIterReviewReq {
  iterId: IIterBranch['iterId'];
}

interface IIterRollbackReq {
  iterId: number;
}
