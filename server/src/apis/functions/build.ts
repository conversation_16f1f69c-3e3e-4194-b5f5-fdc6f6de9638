import { Context } from '@midwayjs/faas';
import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import DingtalkService from '@/apis/service/dingtalk';
import EStoreService from '@/apis/service/estore';
import ProjectService from '@/apis/service/project';
import BaseHandler from '@/apis/functions/base';
import { useTmpDir, useGit } from '@/apis/middleware/hooks';
import { getAppStructure } from '@/apis/utils/package';
import { EBuildPlatform } from '../const/build';
import { IProject } from '../interface/project';

@Provide()
export class BuildHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  eStoreService!: EStoreService;

  @Inject()
  projectService!: ProjectService;

  /** 构建打码 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/build/create',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async create() {
    const { branchName, projectName, pagePath, pageQuery, env, platform, customArgv } = this.ctx.request.body as ICreateReq;

    // 获取项目信息
    const project = await this.projectService.get(projectName);
    if (!project) throw Error(`查询不到名称为 ${projectName} 的项目`);

    // 创建打码任务
    if (isSupportBuild(project)) {
      return await this.eStoreService.build(projectName, {
        branch: branchName,
        pagePath,
        pageQuery,
        env,
        platform,
        // 自定义向云端构建传入的参数。可以通过process.en.customArgv获取
        customArgv,
        userInfo: JSON.stringify({
          name: this.ctx.user.name,
          empId: this.ctx.user.workid
        }),
      });
    } else {
      throw Error(`暂不支持 ${project.type} 类型项目的构建打码`);
    }
  }

  /** 查询打码任务 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/build/list',
    middleware: ['handlerMiddleware']
  })
  async list() {
    const { branchName, projectName, filterMine, page = 1, pageSize = 5 } = this.ctx.query as IListReq;

    // 获取项目信息
    const project = await this.projectService.get(projectName);
    if (!project) throw Error(`查询不到名称为 ${projectName} 的项目`);

    // 批量查询打码任务
    if (isSupportBuild(project)) {
      return await this.eStoreService.getTasks(projectName, {
        branch: branchName,
        creatorWorkid: filterMine ? this.ctx.user?.workid : '',
        page,
        perPage: pageSize,
      });
    } else {
      throw Error(`暂不支持 ${project.type} 类型项目的构建打码`);
    }
  }

  /** 查询构建中的任务 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/build/get',
    middleware: ['handlerMiddleware']
  })
  async get() {
    const { projectName, taskId, filterMine } = this.ctx.query as IGetReq;

    if (!taskId) throw Error('缺少 taskId 参数');

    // 获取项目信息
    const project = await this.projectService.get(projectName);
    if (!project) throw Error(`查询不到名称为 ${projectName} 的项目`);
    if (!isSupportBuild(project)) throw Error(`暂不支持 ${project.type} 类型项目的构建打码`);

    // 查询打码任务
    return await this.eStoreService.getTask(projectName, {
      taskId,
      creatorWorkid: filterMine ? this.ctx.user.workid : '',
    });
  }

  /** 查询构建中的任务,携带app结构 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/build/get-with-app-structure',
    middleware: ['handlerMiddleware']
  })
  async getWithAppStructure() {
    const { projectName, taskId, filterMine } = this.ctx.query as IGetReq;

    if (!taskId) throw Error('缺少 taskId 参数');

    // 获取项目信息
    const project = await this.projectService.get(projectName);
    if (!project) throw Error(`查询不到名称为 ${projectName} 的项目`);
    if (!isSupportBuild(project)) throw Error(`暂不支持 ${project.type} 类型项目的构建打码`);

    // 查询打码任务
    const buildTask = await this.eStoreService.getTask(projectName, {
      taskId,
      creatorWorkid: filterMine ? this.ctx.user.workid : '',
    });
    if (!buildTask) throw Error(`查询不到 taskId 为 ${taskId} 的打码任务`);

    // 进一步获取小程序结构
    const branchUrl = project.gitRepo;
    const branchName = buildTask.branchName;
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const { projectTmpDirPath, gitRepoInfo } = await useGit(tmpDirPath, { branchUrl, branchName, cloneSingleBranch: true });

    const appStructure = await getAppStructure({
      projectTmpDirPath,
      gitRepoInfo,
      branchName,
    });

    removeTmpDir();

    return {
      buildTask,
      appStructure,
    }
  }
}

// 是否支持打码
export function isSupportBuild(project: IProject) {
  return !!project.wsyConfig;
}

interface ICreateReq {
  /** 代码分支 */
  branchName: string;
  /** 项目名称 */
  projectName: string;
  /** 页面路径 */
  pagePath?: string;
  /** 页面参数 */
  pageQuery?: string;
  /** 构建环境，只对微信小程序有效 */
  env?: string;
  /** 构建平台 */
  platform?: EBuildPlatform;
  /** 自定义参数 */
  customArgv?: {
    /** 启用按需构建 */
    minifyBuild?: boolean;
    /** 参与按需构建的包 */
    minifyBuildPackages?: string[];
    /** 启用快速构建，只对微信小程序有效。与按需构建冲突，按需构建优先 */
    fastbuild?: 1 | 0;
    /** 备注 */
    remark?: string;
  };
}

interface IListReq {
  /** 项目名称 */
  projectName: string;
  /** 代码分支 */
  branchName: string;
  /** 是否只展示自己的 */
  filterMine?: boolean;
  /** 请求页数 */
  page?: number;
  /** 每页条数 */
  pageSize?: number;
}

interface IGetReq {
  /** 项目名称 */
  projectName: string;
  /** 云构建任务id */
  taskId: string;
  /** 是否只展示自己的 */
  filterMine?: boolean;
}
