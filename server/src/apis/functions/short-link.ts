import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';

@Provide()
export class ShortLinkHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  serviceManager;

  /** 生成短链接 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/shortlink/create',
  })
  async create() {
    try {
      const { longLink } = this.ctx.query as ICreateReq;
      const result = await this.serviceManager.fuFurlServiceCreateFurlService.invoke({ longLink, requestAppName: 'miniwork' });

      if (result && result.success) {
        this.ctx.body = result;
      }

      
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }
}

interface ICreateReq {
  /** 项目名称 */
  longLink: string;
}
