import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import IterBranchService from '@/apis/service/iter-branch';
import IterQaReviewService from '@/apis/service/iter-qa-review';
import BaseHandler from '@/apis/functions/base';
import { BIZ_LINE } from '@/apis/const';
import { IterStatus } from '@/apis/const/iter-branch';
import ProjectService from '@/apis/service/project';

@Provide()
export class IterQaReviewHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  iterQaReviewService!: IterQaReviewService;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  projectService!: ProjectService;

  /** 创建全量回归 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-qa-review/create',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async create() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId', 'bizNames'])

    // 析取参数
    const { iterId, bizNames } = this.ctx.request.body as { iterId: number; bizNames: BIZ_LINE[] };

    // 获取指定迭代下所有回归记录
    const [iterBranch, { list: iterQaReview }] = await Promise.all([
      this.iterBranchService.get(iterId),
      this.iterQaReviewService.list({ iterId })
    ]);

    if (!iterBranch) throw Error(`查询不到 id 为 ${iterId} 的迭代`)
    if ([IterStatus.ABANDON, IterStatus.PLAN].includes(iterBranch.status)) throw Error(`迭代处于${iterBranch.statusText}状态，不能触发全量回归`)
    if (iterQaReview.length > 0) throw Error('已触发过全量回归')

    // 鉴权
    await this.checkProjectPermissionByProjectName(iterBranch.projectName);

    // 创建全量回归记录
    const list = await Promise.all(bizNames.map(async bizName => {
      return this.iterQaReviewService.create({ iterId, bizName })
    }));

    return {
      list,
      total: list.length
    }
  }

  /** 取消全量回归 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-qa-review/cancel',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async cancel() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId'])

    // 析取参数
    const { iterId } = this.ctx.request.body as { iterId: number; };

    // 鉴权
    await this.checkProjectPermissionByIterId(iterId);

    // 删除所有回归记录
    await this.iterQaReviewService.deleteAllByIterId(iterId);

    return true;
  }

  /** 删除回归记录 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-qa-review/delete',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async delete() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id'])

    // 析取参数
    const { id } = this.ctx.request.body as { id: number; };

    // 查询回归记录
    let iterQaReview = await this.iterQaReviewService.get(id);
    if (!iterQaReview) throw new Error(`查询不到 id 为 ${id} 的回归记录`)

    // 鉴权
    await this.checkProjectPermissionByIterId(iterQaReview.iterId);

    // 删除回归记录
    await this.iterQaReviewService.delete(id);

    return true;
  }

  /** 获取指定迭代的所有回归记录 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-qa-review/list',
    middleware: ['handlerMiddleware']
  })
  async list() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.query, ['iterId'])

    // 析取参数
    const { iterId } = this.ctx.query as { iterId: number; };

    // 获取指定迭代下所有回归记录
    return await this.iterQaReviewService.list({ iterId });
  }

  /** 新增回归结论 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-qa-review/add-review-res',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async addReviewRes() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id', 'pass', 'title', 'comment'])

    // 析取参数
    const { id, pass, title, comment } = this.ctx.request.body as { id: number; pass: boolean; title: string; comment: string; };

    // 新增回归结论
    const iterQaReview = await this.iterQaReviewService.addReviewRes(id, { pass, title, comment });

    return iterQaReview;
  }

  /** 修改回归结论 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-qa-review/modify-review-res',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async modifyReviewRes() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id', 'resId', 'pass', 'title', 'comment'])

    // 析取参数
    const { id, resId, pass, title, comment } = this.ctx.request.body as { id: number; resId: number; pass: boolean; title: string; comment: string; };

    // 修改回归结论
    const iterQaReview = await this.iterQaReviewService.modifyReviewRes(id, resId, { pass, title, comment });

    return iterQaReview;
  }

  /** 删除回归结论 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-qa-review/delete-review-res',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async deleteReviewRes() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id', 'resId'])

    // 析取参数
    const { id, resId } = this.ctx.request.body as { id: number; resId: number; };

    // 删除回归结论
    const iterQaReview = await this.iterQaReviewService.deleteReviewRes(id, resId);

    return iterQaReview;
  }
}
