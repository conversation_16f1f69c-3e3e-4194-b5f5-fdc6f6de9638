
import { Context } from '@midwayjs/faas';
import { Provide, ServerlessTrigger, ServerlessTriggerType, Inject } from '@midwayjs/core';
import axios, { Method } from 'axios';
import BaseHandler from '@/apis/functions/base';

@Provide()
export class CaptureHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  /** 构建打码 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/capture/proxy',
    method: 'post',
  })
  async proxy() {
    this.ctx.response.headers['Access-Control-Allow-Origin'] = '*';

    if (!this.ctx.request.body) return false;
    
    const { data, url, method = 'GET' } = this.ctx.request.body as { data: string, url: string, method?: Method };

    const res = await axios({
      url,
      method,
      data: JSON.parse(data)
    })

    return res.data;
  }
}
