import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { IIterExperienceGetReq, IIterExperienceCreateReq } from '@/apis/interface/iter-experience';
import IterExperienceService from '@/apis/service/iter-experience';
import { EMiniAppUploadStatus } from '@/apis/const/iter-deliver';
import { useTmpDir, useGit } from '@/apis/middleware/hooks';
import IterDeliverService from '@/apis/service/iter-deliver';
import ProjectService from '@/apis/service/project';
import { EClient } from '@/apis/const/iter-deliver';
import BaseHandler from '@/apis/functions/base';
import { Context } from '@midwayjs/faas';

const TAIR_USERNAME = '1b624e98ce3e4273';

@Provide()
export class IterExperienceHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  iterExperienceService!: IterExperienceService;

  @Inject()
  iterDeliverService!: IterDeliverService;

  /** 支端提审 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-experience/submit',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async submit() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['id'])

    // 析取参数
    const { id, clientList } = this.ctx.request.body as { id: number; clientList?: EClient[] };

    // 根据 id 获取投放任务，并做前置判断
    const deliverTask = await this.iterDeliverService.getById(id)
    if (!deliverTask) throw Error(`查询不到 id 为 ${id} 的投放任务`)
    if (!deliverTask.miniAppVersion) throw Error('当前投放任务暂未生成小程序版本号，不能提审')
    if (!deliverTask.clientName && !clientList) throw Error('多端统一投放场景必须传 clientList')
    if (deliverTask.clientName === EClient.ALIPAY) {
      // 支付宝端暂时不用接口来提审，因为提审的表单在平台上填写更加灵活方便
      const req = {
        miniAppVersion: deliverTask.miniAppVersion, miniAppId: deliverTask.miniAppId, clientName: deliverTask.clientName
      };
      const { code } = await this.iterExperienceService.submitMiniAppVersion(req) || {};
      return code == "10000"
    }
    return false
  }

  /** 获取投放任务列表(新)，附加支端通知回归体验码写入数据 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-experience/getDeliverList',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async getDeliverList() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['iterId'])

    // 析取参数
    const { iterId, clientName, miniAppId } = this.ctx.request.body as {
      iterId: string;
      clientName?: any;
      miniAppId?: string;
    };

    if(clientName === EClient.ALIPAY && miniAppId && clientName) {
      const [r1, r2] = await Promise.all([
        this.iterExperienceService.get({
          iter_id: Number(iterId),
          mini_app_id: miniAppId,
          client_name: clientName,
        }),
        this.iterDeliverService.list({
          iterId: Number(iterId),
          clientName,
          miniAppId
        }, { needVersionInfo: true })
      ]);
      if(!r1?.miniAppVersion) {
        this.ctx.logger.info(`支系小程序投放 >>> 当前投放端(${clientName}-${miniAppId}-${iterId})，无体验码上传任务`);
        return r2;
      } else {
        const { list=[] } = r2 || {};
        if(list.some(it =>  it.miniAppVersion === r1.miniAppVersion)) {
          this.ctx.logger.info(`支系小程序投放 >>> 投放任务(${clientName}-${miniAppId}-${iterId})中已覆盖当前体验码上传任务`);
          return r2;
        } else {
          this.ctx.logger.info(`支系小程序投放 >>> 当前体验码上传任务写入至投放任务(${clientName}-${miniAppId}-${iterId})`);
          await this.iterDeliverService.findOrCreate({
            iterId: Number(iterId),
            clientName,
            miniAppId,
            miniAppVersion: r1.miniAppVersion,
            uploadStatus: EMiniAppUploadStatus.SUCCESS
          })
        }
      }
    }
    // 根据 iterId、clientName、miniAppId 获取投放任务列表
    return await this.iterDeliverService.list({
      iterId: Number(iterId),
      clientName,
      miniAppId
    }, { needVersionInfo: true });
  }

  /**
   * 获取当前迭代最新commitId的体验版信息
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-experience/get',
    method: 'post',
    middleware: ['wrapResponseMiddleware']
  })
  async get() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['miniAppId', 'clientName', 'iterId']);
    let { miniAppId, clientName, iterId, rcGitName, rcGitUrl, commitId } = this.ctx.request.body as IIterExperienceGetReq;
    if(!commitId) {
      if (!rcGitName || !rcGitUrl) {
        const iterBranch = await this.iterBranchService.get(iterId);
        if (!iterBranch) throw Error(`查询不到 iterId 为 ${iterId} 的迭代`);
        else if (!iterBranch.rcGitBranch) throw Error(`${iterBranch.projectName} 的迭代 ${iterBranch.version} 缺少 rc 分支，无法生成体验码`);
        rcGitName = iterBranch.rcGitBranch?.name || '';
        rcGitUrl = iterBranch.rcGitBranch?.url || '';
      };
      // rcGitName = "rc/1.17.21"; // test
      // rcGitUrl = "http://gitlab.alibaba-inc.com/trip/rx-fliggy-allinone/tree/rc/1.17.21";// test
      if (!rcGitName || !rcGitUrl) throw Error(`获取迭代分支 ${iterId} gitRepo获取失败`);
      // 拉取代码
      const { tmpDirPath, removeTmpDir } = useTmpDir();
      let { gitHandler, gitRepoInfo } = await useGit(tmpDirPath, {
        branchUrl: rcGitUrl,
        branchName: rcGitName,
        cloneSingleBranch: true
      });
      // 获取commitId
      const remote = await gitHandler.listRemote(['--heads', gitRepoInfo.sshUrl, rcGitName]);
      commitId = remote && remote.replace(/\W/, ' ').split(' ')[0];
      setTimeout(() => {
        removeTmpDir();
      }, 1000 * 20);
    }

    const res = await this.iterExperienceService.getExperienceInfo({ miniAppId, clientName, commitId, iterId });
    this.ctx.logger.info(`支系小程序体验码回归 >>> 查询体验版信息-${commitId}-${iterId}-${clientName}-${miniAppId}`, res);
    if(res) {
      res.commitId = commitId; // 确保commitId无异常
    }
    // 上传任务已完成
    if(res && res.status == 2) {
      const miniappVer = res.miniAppVersion;
      const originK = `${iterId}_${clientName}_${miniAppId}_${commitId}_${miniappVer}`
      const hashK = this.stringHash(originK);
      res.hashK = hashK;
      console.error("hashK", hashK, originK);
      // 异步
      new Promise(async () => {
        const curData: any = await this.getTair(hashK);
        console.error("tair中有数据", curData);
        this.ctx.logger.info(`支系小程序体验码回归 >>> 查询体验码QRCode-${hashK}-#$#${originK}#$#-${curData ? 'setTair&getExperienceQRCode' : '跳过'}`, curData);
        if(!curData) {
          await this.setTair(hashK, {
            iterId,
            miniAppId,
            clientName,
            commitId,
            status: 1,
          });
          const result = await this.iterExperienceService.getExperienceQRCode({ 
            miniAppId, clientName, miniAppVersion: miniappVer, hashK, commitId, iterId  
          } as any);
          await this.setTair(hashK, result);
        }
      });
    }
    return res
  }

  /** 获取当前迭代、投放端、appid下所有的体验码 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-experience/getQRCode',
    method: 'post',
    middleware: ['wrapResponseMiddleware']
  })
  async getQRCode() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['hashK']);
    const { hashK } = this.ctx.request.body as {
      hashK: any;
    };
    if(!hashK) throw Error('hashK is required');
    const resData = await this.getTair(hashK);
    return resData
  }

  /**
   * 构建上传小程序体验版
   * miniAppId: string,
   * clientName: EClient,
   * iterId: string
   * */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-experience/create',
    method: 'post',
    middleware: ['wrapResponseMiddleware']
  })
  async create() {
    // 校验必传参数
    this.checkRequiredParams(this.ctx.request.body, ['miniAppId', 'clientName', 'iterId']);
    const { miniAppId, clientName, iterId, reBuild } = this.ctx.request.body as IIterExperienceCreateReq;
    // 获取迭代信息
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) throw Error(`查询不到 iterId 为 ${iterId} 的迭代`);
    else if (!iterBranch.rcGitBranch) throw Error(`${iterBranch.projectName} 的迭代 ${iterBranch.version} 缺少 rc 分支，无法生成体验码`);

    // 查询归属项目信息
    const project = await this.projectService.get(iterBranch.projectName);
    if (!project) throw Error(`查询不到 projectName 为 ${iterBranch.projectName} 的项目`);
    // 鉴权  
    await this.checkProjectPermission(project);
    
    let branchName = iterBranch.rcGitBranch?.name || '';
    let branchUrl = iterBranch.rcGitBranch?.url || '';
    // branchName = "rc/1.17.21"; // test
    // branchUrl = "http://gitlab.alibaba-inc.com/trip/rx-fliggy-allinone/tree/rc/1.17.21";// test
    if (!branchName || !branchUrl) throw Error(`获取迭代分支 ${iterId} 失败`);
    console.error("branchName", branchName);

    const data = await this.iterExperienceService.createExperienceTask({
      branchName, branchUrl, clientName, miniAppId, iterId, reBuild
    });
    console.error('responseData', data)
    return data
  }

  /**
   * 取消当前体验码
   */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-experience/cancel',
    middleware: ['wrapResponseMiddleware']
  })
  async cancel() {
    // 校验必传参数
    // this.checkRequiredParams(this.ctx.query, ['iterId'])

    // // 析取参数
    // const { iterId, clientName } = this.ctx.query as {
    //   iterId: string;
    //   clientName?: EClient;
    // };

    // // 根据 iterId、clientName 获取投放任务列表
    // return this.iterDeliverService.list({
    //   iterId: Number(iterId),
    //   clientName
    // }, { needVersionInfo: true })
  }
  stringHash(str) {
    let hash = 0;
    if (str.length === 0) return hash;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char; // hash * 31 + char
      hash |= 0; // Convert to 32bit integer
    }
    return hash;
  }
  async getTair(k) {
    const tair = await this.ctx.tairManager.getClient({
      username: TAIR_USERNAME
    });
    const tairRes = await tair.get(String(k));
    let resData = null;
    if(tairRes?.data) {
      try {
        resData = JSON.parse(tairRes.data);
      } catch(e) {
        console.error("解析tair数据异常", TAIR_USERNAME)
       }
    }
    return resData;
  }
  async setTair(k, v) {
    const tair = await this.ctx.tairManager.getClient({
      username: TAIR_USERNAME
    });    
    await v && tair.put(String(k), JSON.stringify(v), {
      expired: 24 * 60 * 60 * 7
    });
    return 
  }
}
