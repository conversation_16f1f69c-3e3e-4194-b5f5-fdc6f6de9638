import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import ComponentDeliverService from '@/apis/service/component-deliver';

@Provide()
export class ComponentDeliverHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  componentDeliverService!: ComponentDeliverService;

  /** 查询投放列表信息 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/component-deliver/list',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async list() {
    const { pageSize, pageNum, conditions } = this.ctx.request.body;
    this.ctx.body = await this.componentDeliverService.list({ pageSize, pageNum, conditions })
  }

  /** 查询单条投放信息 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/component-deliver/get',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async get() {
    const { deliverId, assertExist } = this.ctx.request.body;
    this.ctx.body = await this.componentDeliverService.get(deliverId, assertExist)
  }

  /** 初始化H5投放 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/component-deliver/init',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async init() {
    const { deliverId, devId, iterId, deliverType, branchType, pub_env, projectName, gitRepo, gitProjectId, npmList, aoneBinds, description } = this.ctx.request.body;
    this.ctx.body = await this.componentDeliverService.init({ deliverId, devId, iterId, deliverType, branchType, pub_env, projectName, gitRepo, gitProjectId, npmList, aoneBinds, description })
  }

  /** 更新依赖并开始H5投放 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/component-deliver/create-publish',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async createPublish() {
    const { deliverId, pub_env, gitRepo, npmList, defIterId } = this.ctx.request.body;
    this.ctx.body = await this.componentDeliverService.createPublish({ deliverId, pub_env, gitRepo, npmList, defIterId })
  }

  /** 查询H5投放结果并更新到DB */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/component-deliver/get-publish-detail',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async getPublishDetail() {
    const { project, deliverId, defTaskId, npmList } = this.ctx.request.body;
    this.ctx.body = await this.componentDeliverService.getPublishDetail({ project, deliverId, defTaskId, npmList })
  }

  /** 开始小程序投放 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/component-deliver/init-miniapp',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async initMiniapp() {
    const {
      deliverId,
      devId,
      iterId,
      deliverType,
      branchType,
      pub_env,
      branchName,
      description,
      mergeCode,
      npmList,
      npmResolutionList,
      bizLine,
      qaList,
      aoneList,
      projectType,
      projectName,
    } = this.ctx.request.body;
    this.ctx.body = await this.componentDeliverService.initMiniapp({
      deliverId,
      devId,
      iterId,
      deliverType,
      branchType,
      pub_env,
      branchName,
      description,
      mergeCode,
      npmList,
      npmResolutionList,
      bizLine,
      qaList,
      aoneList,
      projectType,
      projectName,
    });
  }

  /** 预发环境更新小程序投放 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/component-deliver/update-beta-miniapp',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async updateBetaMiniapp() {
    const {
      deliverId,
      npmList,
    } = this.ctx.request.body;
    this.ctx.body = await this.componentDeliverService.updateBetaMiniapp({
      deliverId,
      npmList,
    });
  }

  /** 线上环境更新小程序投放 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/component-deliver/update-prod-miniapp',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async updateProdMiniapp() {
    const {
      deliverId,
      iterId,
      deliverType,
      npmList,
      projectName,
    } = this.ctx.request.body;
    this.ctx.body = await this.componentDeliverService.updateProdMiniapp({
      deliverId,
      iterId,
      deliverType,
      npmList,
      projectName,
    });
  }

  /** 删除投放 */
  // async delete() {
  //   const { iterationId, branchIds } = this.ctx.request.body;
  //   this.ctx.body = await this.componentDeliverService.delete({ iterationId, branchIds })
  // }

  // /** 废弃投放 */
  // async discard() {
  //   const { iterationId, id } = this.ctx.request.body;
  //   this.ctx.body = await this.componentDeliverService.discard({ iterationId, id })
  // }
}
