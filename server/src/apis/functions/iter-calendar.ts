import { ServerlessTrigger, ServerlessTriggerType, ServerlessFunction, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import IterBranchService from '@/apis/service/iter-branch';
import IterCalendarService from '@/apis/service/iter-calendar';
import ProjectService from '@/apis/service/project';
import { IIterBranch } from '@/apis/interface/iter-branch';

@Provide()
export class IterCalendarHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  iterCalendarService!: IterCalendarService;

  @Inject()
  projectService!: ProjectService;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/iter-calendar/list',
    middleware: ['handlerMiddleware']
  })
  async list() {
    let { year, month, projectName } = this.ctx.query as IListReq;
    projectName ||= 'fliggy-allinone'
    year &&= Number(year);
    month &&= Number(month);

    // 1. 先筛选出符合条件的迭代分支
    let filteredIter = await this.iterCalendarService.filter({ year, month, projectName });
    // 如果有年、月，则会实际筛选出当前月加减1的月份数据
    if (year && month) {
      // 前一个月
      filteredIter = filteredIter.concat(await this.iterCalendarService.filter({
        year: month === 1 ? (year - 1) : year,
        month: month === 1 ? 12 : (month - 1),
        projectName
      }))

      // 后一个月
      filteredIter = filteredIter.concat(await this.iterCalendarService.filter({
        year: month === 12 ? (year + 1) : year,
        month: month === 12 ? 1 : (month + 1),
        projectName
      }))
    }

    // 2. 再查询每个迭代分支的详情
    const iterList = await Promise.all(filteredIter.map(item => this.iterBranchService.get(item.iterId)));

    // 3. 查询项目列表
    const project = await this.projectService.get(projectName, { needPkgJSON: true });

    return {
      condition: { year, month, projectName },
      iterList: iterList.filter((item): item is IIterBranch => !!item),
      project
    }
  }

  /** 获取时间区间内的迭代列表 */
  @ServerlessFunction({ functionName: 'iter-list' })
  @ServerlessTrigger(ServerlessTriggerType.HSF)
  async getIterListByMonth(event: { dateRange: string[], projectName }) {
    const { dateRange, projectName } = event;
    const list = await this.iterBranchService.getPrevPublishedIterByDateRange(projectName, dateRange);
    return { list };
  }
}

interface IListReq {
  year?: number;
  month?: number;
  projectName?: string;
}
