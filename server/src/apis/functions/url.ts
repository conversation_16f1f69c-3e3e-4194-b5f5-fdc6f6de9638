import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import UrlService from '@/apis/service/url';

@Provide()
export class UrlHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  urlService!: UrlService;

  /** 获取 url 关系表 */
  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/url/list',
  })
  async list() {
    try {
      const { projectName } = this.ctx.query as IListReq;

      // 1. 获取 url 关系表（数组格式）
      const urlList = await this.urlService.getList(projectName);

      this.ctx.body = {
        success: true,
        data: urlList
      };
    } catch (err: any) {
      this.ctx.body = {
        success: false,
        errorMsg: err?.message
      }
    }
  }
}

interface IListReq {
  /** 项目名称 */
  projectName: string;
}
