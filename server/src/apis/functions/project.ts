import { ServerlessTrigger, ServerlessTriggerType, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import ProjectService from '@/apis/service/project';
import UserService from '@/apis/service/user';
import GitService from '@/apis/service/git';
import BaseHandler from '@/apis/functions/base';
import { CreateDTO, UpdateDTO, DeleteDTO } from '@/apis/dto/project';

@Provide()
export class ProjectHandler extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  userService!: UserService;

  @Inject()
  gitService!: GitService;

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/project/list',
    middleware: ['handlerMiddleware']
  })
  async list() {
    return await this.projectService.list();
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/project/create',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async create() {
    const createParams = this.ctx.request.body as CreateDTO;

    // 鉴权
    await this.checkSuperPermission();

    // 判断项目是否已存在
    const checkRes = await this.projectService.get(createParams.name);
    if (checkRes) throw Error(`项目${createParams.name}已存在`);

    // 添加新项目
    return await this.projectService.create(createParams);
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/project/update',
    method: 'post',
    middleware: ['handlerMiddleware']
  })
  async update() {
    const updateParams = this.ctx.request.body as UpdateDTO;

    // 鉴权
    await this.checkProjectPermissionByProjectName(updateParams.name);

    // 更新项目
    await this.projectService.update(updateParams);

    // 获取最新项目信息
    return await this.projectService.getById(updateParams.id);
  }

  @ServerlessTrigger(ServerlessTriggerType.HTTP, {
    path: '/api/project/delete',
    middleware: ['handlerMiddleware']
  })
  async delete() {
    const deleteParams = this.ctx.request.query as DeleteDTO;
    
    // 获取项目信息
    const project = await this.projectService.getById(deleteParams.id);
    if (!project) throw Error(`项目${deleteParams.id}不存在`);

    // 鉴权
    await this.checkProjectPermission(project);

    // 删除项目
    await this.projectService.delete(deleteParams.id);

    return true;
  }
}