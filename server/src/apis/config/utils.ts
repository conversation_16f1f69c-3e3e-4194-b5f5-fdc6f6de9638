import { Context } from "@midwayjs/faas"

export function ignoreBucLogin(pathName = '', ctx: Context) {
  if (ctx.user) return false;

  const rules = [
    /^\/api\/capture\//,
    /^\/api\/helper\/get-latest-published-by-project/,
    /^\/api\/build\/(list|get)/,
    /^\/api\/ssr-detail\/(ssr-list|add-project|add-page|batch-prepub|prepub|gray|publish|preload-callback|getSSRIteration|alarm|rollback|prepub-def|log-query)/,
    /^\/api\/iter-deliver\/get-by-iter-version/,
    /^\/api\/one-link\//,
    /^\/api\/def\/get-def-member/,
    /\/api\/iter-branch\/def-hook-trigger/,
    /\/api\/release-record\/(put-record|get-record|update-record)/
  ]
  return rules.some(rule => rule.test(pathName))
}