import { ignoreBucLogin } from "./utils";
const path = require("path");

export default {
  keys: ["fn-fl-miniwork"],
  eggPaths: [path.resolve("../server")],

  sequelize: {
    host: "rm-bp15929g4158t1ux988270.mysql.rds.aliyuncs.com",
    port: "3306",
    dialect: "mysql",
    database: "trip_division",
    username: "fzdivision",
    password: "fz_division$1",
    charset: "utf8"
  },
  bucLogin: {
    appname: "fn-fl-miniwork",
    appcode: "fn-fl-miniwork-PzHA92u95Z77Bvu",
    ssoURL: "https://login.alibaba-inc.com",
    host: "fl-miniwork.fc.alibaba-inc.com",
    protocol: "https",
    loginPath: "/login",
    refreshToken: true,
    ignore: ignoreBucLogin
  },
  bucClient: {
    appname: "fn-fl-miniwork",
    apiKey: "fn-fl-miniwork-PzHA92u95Z77Bvu",
    access_key: "fn-fl-miniwork-PzHA92u95Z77Bvu",
    secretKey: "3G30V3X7P8uwJz5D79QABC1b8361f8s8Wj06n9IS",
    timeout: "30s"
  },
  wsy: {
    name: "fliggymw",
    token: "99b3556b1fac802fa556e74f99ee3724"
  },
  validate: {
    validationOptions: {
      allowUnknown: true // 全局生效,https://midway.alibaba-inc.com/docs/extensions/validate#1%E5%85%81%E8%AE%B8%E6%9C%AA%E5%AE%9A%E4%B9%89%E7%9A%84%E5%AD%97%E6%AE%B5
    }
  },
  miniworkService: {
    host: "https://miniwork-service.alibaba-inc.com"
  },
  changefree: {
    url: "http://alichange-api.alibaba-inc.com/openApi/v2/gateway",
    authKey: "da320f0d-9944-44ec-bb44-abab9d337b3b",
    authToken: "04a3d2e3-1a2a-4fef-8fef-0f0482f11db9"
  }
};
