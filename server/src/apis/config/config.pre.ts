import { ignoreBucLogin } from './utils';

export default {
  bucLogin: {
    appname: 'fn-fl-miniwork',
    appcode: 'fn-fl-miniwork-PzHA92u95Z77Bvu',
    ssoURL: 'https://login.alibaba-inc.com',
    host: 'fl-miniwork.pre-fc.alibaba-inc.com',
    protocol: 'https',
    loginPath: '/login',
    refreshToken: true,
    ignore: ignoreBucLogin
  },
  bucClient: {
    appname: 'fn-fl-miniwork',
    apiKey: 'fn-fl-miniwork-PzHA92u95Z77Bvu',
    access_key: 'fn-fl-miniwork-PzHA92u95Z77Bvu',
    secretKey: '3G30V3X7P8uwJz5D79QABC1b8361f8s8Wj06n9IS',
    timeout: '30s',
  },
};
