import { ignoreBucLogin } from './utils';

export default {
  sequelize: {
    host: 'rm-8vb2k918p45nx75ry.mysql.zhangbei.rds.aliyuncs.com',
    port: '3306',
    dialect: 'mysql',
    database: 'trip_division',
    username: 'fzdivision',
    password: 'fz_division$1',
    charset: 'utf8'
  },
  bucLogin: {
    appname: 'fn-fl-miniwork',
    appcode: '3bf027c35e0c444596f950f4e6748293',
    ssoURL: 'https://login.alibaba-inc.com',
    host: 'localhost:8000',
    protocol: 'http',
    loginPath: '/login',
    refreshToken: true,
    useProdURL: true,
    ignore: ignoreBucLogin,
    loginCallback: (_ctx, user) => {
      return [user, 'http://localhost:8000'];
    },
  },
  bucClient: {
    appname: 'fn-fl-miniwork',
    apiKey: 'fn-fl-miniwork-PzHA92u95Z77Bvu',
    access_key: 'fn-fl-miniwork-PzHA92u95Z77Bvu',
    secretKey: '3G30V3X7P8uwJz5D79QABC1b8361f8s8Wj06n9IS',
    timeout: '30s',
  },
};
