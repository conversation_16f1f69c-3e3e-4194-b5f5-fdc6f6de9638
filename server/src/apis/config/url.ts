export default {
  'fliggy-weixin': {
    gitUrl: '**************************:rxpi/wechat-url-map.git',
    filePath: 'src/url.js',
    parseFunc: (text: string) => {
      const regRes = text.match(/const URL_MAP = (.+?;)/s);
      if (!regRes) throw Error('解析 urlMap 失败')

      return new Function('return ' + regRes[1])();
    }
  },
  'fliggy-allinone': {
    gitUrl: '**************************:rxpi/alipay-url-map.git',
    filePath: 'src/url.js',
    parseFunc: (text: string) => {
      const regRes = text.match(/const URL_MAP = (.+?;)/s);
      if (!regRes) throw Error('解析 urlMap 失败')

      return new Function('return ' + regRes[1])();
    },
    titanRid: '554992'
  }
}