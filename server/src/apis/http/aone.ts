
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { IAone } from '@/apis/interface/aone';
import { AONE_CONFIG, APP_NAME } from '@/apis/const';
import axios from 'axios';

const crypto = require('crypto');

@Provide('aoneHTTP')
export default class AoneHTTP {
  @Inject()
  ctx!: Context;

  async getById(id: string) {
    return this._fetchAone('getById', { id });
  }

  async _fetchAone(apiName: string, params: any) {
    const appSecret = AONE_CONFIG.appSecret;
    const timestamp = Date.now();
    // content to be encrypted
    const content = `appName=${APP_NAME};timestamp=${timestamp}`;
    // decode secret
    const secret = new Buffer(appSecret, 'base64');
    // initialization vector
    const iv = new Buffer('');
    const cipher = crypto.createCipheriv('aes-128-ecb', secret, iv);

    cipher.setAutoPadding(true);

    // do encrypt
    const chunks = [];
    // @ts-ignore
    chunks.push(cipher.update(content, 'utf8', 'base64'));
    // @ts-ignore
    chunks.push(cipher.final('base64'));

    // get signature
    let signature = chunks.join('');
    // to be url safe
    signature = signature.replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');

    const searchParams = new URLSearchParams(params);
    const res = await axios({
      // OpenAPI 接口：https://yuque.antfin-inc.com/aone/platform/issue-api-facade
      url: `http://aone-api.alibaba-inc.com/issue/openapi/IssueTopService/${apiName}?${searchParams.toString()}`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        clientKey: APP_NAME,
        timestamp: timestamp + '',
        signature
      }
    });

    return res.data.result as IAone;
  }
}