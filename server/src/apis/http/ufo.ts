import { Inject, Provide } from "@midwayjs/core";
import { Context } from '@midwayjs/faas';
import axios from "axios";

interface UFOParams {
  mirrorAlgorithmId: string;
  pageNum: number;
  pageSize: number;
}

@Provide("UFOHTTP")
export default class UFOHTTP {
  @Inject()
  ctx!: Context;
  /**
   * 获取页面数据
   * @param params
   * @returns
   */
  async getListByPage(params: UFOParams) {
    const { mirrorAlgorithmId, pageNum, pageSize } = params;
    try {
      const result = await axios.post(
        "https://ufo2.alitrip.com/api/auto/UfoNativePagePerfomance/listByPage.json",
        {
          mirrorAlgorithmId,
          pageNum,
          pageSize,
        }
      );

      // 取最近的20次迭代数据
      let list = result?.data.resultObj.jobList;
      // list = list.slice(list.length - 20);

      return {
        success: true,
        data: list,
      };
    } catch (err) {
      return {
        success: false,
        message: err,
      };
    }
  }

  async getTaskInfo(query: { taskId: string }) {
    const { taskId } = query;
    try {
      const result = await axios.get(
        `https://ufo2.alitrip.com/api/auto/UfoNativePagePerfomance/getTaskIds.json?${taskId}`
      );
      return {
        success: true,
        data: result?.data,
      };
    } catch (e) {
      return {
        success: false,
        message: e,
      };
    }
  }

  async getAllListByPage(taskIds: string[], pageNum: number, pageSize: number) {
    return Promise.all(
      taskIds.map((item: string) =>
        this.getListByPage({ mirrorAlgorithmId: item, pageNum, pageSize })
      )
    );
  }
}
