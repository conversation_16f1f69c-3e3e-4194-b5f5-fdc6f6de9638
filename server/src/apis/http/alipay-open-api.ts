
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { IAlipayVersionInfo } from '@/apis/interface/iter-deliver';
import { catchError } from '@/apis/middleware';
import { EClient, MINIAPP_CONFIG, BUNDLE_ID_CONFIG, EMiniAppVersionStatus } from '@/apis/const/iter-deliver';
import AlipaySdkManager from '@/apis/utils/alipaysdk-manager';
import * as chalk from 'chalk';

const alipaySdkManager = new AlipaySdkManager();

@Provide('alipayOpenApiHTTP')
export default class AlipayOpenApiHTTP {
  @Inject()
  ctx!: Context;

  /**
   * 小程序版本信息查询: https://opendocs.alipay.com/pre-apis/api_pre/alipay.open.mini.innerversion.info.query
   * @param params 
   * @returns 
   */
  @catchError
  async infoQuery(params: IInfoQueryReq) {
    return this._request<IInfoQueryRes>('alipay.open.mini.innerversion.info.query', params)
  }

  /**
   * 删除版本: https://opendocs.alipay.com/pre-apis/api_pre/alipay.open.mini.innerversion.condition.batchquery
   * @param params 
   * @returns 
   */
  @catchError
  async infoDelete(params: IInfoQueryReq) {
    return this._request<ICommonRes>('alipay.open.mini.innerversion.info.delete', params)
  }

  /**
   * 小程序版本信息查询: https://opendocs.alipay.com/pre-apis/api_pre/alipay.open.mini.innerversion.last.query
   * @param params 
   * @returns 
   */
  @catchError
  async lastQuery(params: ICommonReq) {
    return this._request<ILastQueryRes>('alipay.open.mini.innerversion.last.query', params)
  }

  /**
   * 根据状态查询版本列表: https://opendocs.alipay.com/pre-apis/api_pre/alipay.open.mini.innerversion.condition.batchquery
   * @param params 
   * @returns 
   */
  @catchError
  async batchQuery(params: IBatchQueryReq) {
    return this._request<IBatchQueryRes>('alipay.open.mini.innerversion.condition.batchquery', params)
  }

  /**
   * 退回开发: https://opendocs.alipay.com/pre-apis/api_pre/alipay.open.mini.innerversion.backdev.publish
   * @param params 
   * @returns 
   */
  @catchError
  async backdevPublish(params: IBackdevPublishReq) {
    return this._request<ICommonRes>('alipay.open.mini.innerversion.backdev.publish', params)
  }

  /**
   * 撤销审核: https://opendocs.alipay.com/pre-apis/api_pre/alipay.open.mini.innerversion.audit.cancel
   * @param params 
   * @returns 
   */
  @catchError
  async auditCancel(params: IAuditCancelReq) {
    return this._request<ICommonRes>('alipay.open.mini.innerversion.audit.cancel', params)
  }

  /**
   * 灰度: https://opendocs.alipay.com/pre-apis/api_pre/alipay.open.mini.innerversion.gray.publish
   * @param params 
   * @returns 
   */
  @catchError
  async grayPublish(params: IGrayPublishReq) {
    return this._request<ICommonRes>('alipay.open.mini.innerversion.gray.publish', params)
  }

  /**
   * 结束灰度: https://opendocs.alipay.com/pre-apis/api_pre/alipay.open.mini.innerversion.gray.finish
   * @param params 
   * @returns 
   */
  @catchError
  async grayFinish(params: IGrayFinishReq) {
    return this._request<ICommonRes>('alipay.open.mini.innerversion.gray.finish', params)
  }

  /**
   * 上架: https://opendocs.alipay.com/pre-apis/api_pre/alipay.open.mini.innerversion.online.publish
   * @param params 
   * @returns 
   */
  @catchError
  async onlinePublish(params: IOnlinePublishReq) {
    return this._request<ICommonRes>('alipay.open.mini.innerversion.online.publish', params)
  }

  /**
   * 下架: https://opendocs.alipay.com/pre-apis/api_pre/alipay.open.mini.innerversion.offline.publish
   * @param params 
   * @returns 
   */
  @catchError
  async offlinePublish(params: IOfflinePublishReq) {
    return this._request<ICommonRes>('alipay.open.mini.innerversion.offline.publish', params)
  }

  /**
   * 回滚: https://opendocs.alipay.com/pre-apis/api_pre/alipay.open.mini.innerversion.content.rollback
   * @param params 
   * @returns 
   */
  @catchError
  async contentRollback(params: IContentRollbackReq) {
    return this._request<ICommonRes>('alipay.open.mini.innerversion.content.rollback', params)
  }

  async _request<T>(method: string, params: ICommonReq) {
    const { miniAppId, clientName } = params;
    const alipaySdk = await alipaySdkManager.getAlipaySdk(miniAppId)
    const startTime = Date.now();
    this.ctx.logger.info(chalk.cyan(`调用 Alipay OpenAPI >>> ${method} 开始`), params)
    const res = await alipaySdk.exec<T>(method, {
      bizContent: {
        bundleId: BUNDLE_ID_CONFIG[clientName], // 端信息
        appOrigin: 'fliggy', // 业务来源，找支付宝官方申请的
        pid: MINIAPP_CONFIG[miniAppId].pid, // pid
        ...params
      }
    });
    this.ctx.logger.info(chalk.cyan(`调用 Alipay OpenAPI >>> ${method} 结束(耗时 ${Date.now() - startTime}ms)`), res)

    return res
  }
}

interface ICommonReq {
  /** 小程序id */
  miniAppId: string;
  /** 投放端 */
  clientName: EClient;
}

interface IInfoQueryReq extends ICommonReq {
  /** 小程序版本 */
  appVersion: string;
}

type IGrayFinishReq = IInfoQueryReq;
type IOfflinePublishReq = IInfoQueryReq;
type IBackdevPublishReq = IInfoQueryReq;
type IAuditCancelReq = IInfoQueryReq;
type IContentRollbackReq = IInfoQueryReq;

interface IBatchQueryReq extends ICommonReq {
  /** 版本状态, INIT:开发中，RELEASE:上架，OFFLINE:下架，AUDITING:审核中，AUDIT_REJECT:审核驳回，WAIT_RELEASE:待上架，GRAY:灰度中, 以,号隔开 */
  versionStatus: string;
  /** 页码，从1开始，默认值为1 */
  pageNum?: number;
  /** 页数，默认值为10 */
  pageSize?: number;
}

interface IGrayPublishReq extends ICommonReq {
  /** 小程序版本 */
  appVersion: string;
  /** 灰度值 */
  grayStrategy: string;
}

interface IOnlinePublishReq extends ICommonReq {
  /** 小程序版本 */
  appVersion: string;
  /** 应用发布类型, normal - 默认普通发布、high - 高保发布 */
  releaseType?: 'normal' | 'high';
  /** 是否服务降级，如果降级，拉包策略如果端上有本地包，则使用本地包打开，同时异步下载新包，待下次打开则使用新包 */
  downGrade?: boolean;
}

interface ICommonRes {
  /** 网关返回码:https://opendocs.alipay.com/common/02km9f */
  code: string;
  /** 网关返回码描述:https://opendocs.alipay.com/common/02km9f */
  msg: string;
}

interface IInfoQueryRes extends ICommonRes, IAlipayVersionInfo { }

interface ILastQueryRes extends ICommonRes {
  /** 小程序的名称 */
  appName: string;
  /** 端信息 */
  bundleId: string;
  /** 小程序id */
  miniAppId: string;
  /** 小程序版本 */
  appVersion: string;
  /** 小程序版本状态 **/
  status: EMiniAppVersionStatus;
  /** 小程序英文名称 */
  englishName: string;
  /** 小程序应用简介，一句话描述小程序功能 */
  slogan: string;
  /** 小程序应用logo图标 */
  logoUrl: string;
  /** 小程序应用描述 */
  appDesc: string;
  /** 小程序客服电话 */
  servicePhone: string;
  /** 小程序所属类目 */
  categoryIds: string;
}

interface IBatchQueryRes extends ICommonRes {
  miniVersionBaseInfoList?: {
    /** 端信息 */
    bundleId: string;
    /** 小程序id */
    miniAppId: string;
    /** 小程序版本 */
    appVersion: string;
    /** 小程序版本状态 **/
    status: EMiniAppVersionStatus;
  }[]
}
