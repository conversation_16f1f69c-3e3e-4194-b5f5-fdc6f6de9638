/**
 * 研发平台（node服务） - HTTP接口
 */
import { Inject, Provide, Config } from '@midwayjs/core';
import { catchError } from '@/apis/middleware';
import { Context } from '@midwayjs/faas';
import axios from 'axios';
import * as https from 'https';
import { isDaily } from '../utils';

@Provide('miniworkServiceHTTP')
export default class MiniworkServiceHTTP {
  @Inject() ctx!: Context;
  @Config("miniworkService") miniworkServiceConfig: any;

  @catchError
  async uploadAlipay(params: any): Promise<any> {
    const res = await axios({
      url: `${this.miniworkServiceConfig.host}/api/upload_alipay_server`,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      params: params || {},
      ...(isDaily ? {
        httpsAgent: new https.Agent({
          rejectUnauthorized: false
        })
      } : {})
    })
    return res.data;
  }
}