/**
 * 奥特后台 - HTTP接口
 */
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { formatDate, isDaily } from '@/apis/utils';
import axios from 'axios';
import { catchError } from '@/apis/middleware';

@Provide('flBuyHTTP')
export default class FlBuyHTTP {
  @Inject()
  ctx!: Context;

  @catchError
  async pubHook(params: IPubHookParams): Promise<any> {
    if (isDaily) return;

    const res = await axios({
      url: isDaily ? 'http://fl-buy.fc.alibaba.net/api/pub/hooks' : 'https://fl-buy.fc.alibaba-inc.com/api/pub/hooks',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      data: {
        ...params,
        // 应用ID，研发平台指定值 6666
        appId: 66666,
        // 线上|预发
        env: '线上',
        // 平台标识 
        platform: 'fl-miniwork',
        // 开发者昵称
        updatorNick: this.ctx.user.name,
        // 开发者工号
        updatorEmpId: this.ctx.user.workid,
        // 修改时间
        gmt_modified: formatDate(new Date()),
        // 此次发布详情地址
        detailUrl: `https://fl-miniwork.fc.alibaba-inc.com/#/iter/detail?iterId=${params.iterId}`
      }
    })

    return res.data;
  }
}

interface IPubHookParams {
  // 版本id
  iterId: number;
  // 操作
  operateName: '冻结集成' | '取消冻结' | '提审' | '通过审核' | '撤销审核' | '退回开发' | '灰度' | '结束灰度' | '上架' | '下架' | '发布' | '发布(合并master)' | '回滚';
  // 应用名
  appName: string;
  // 百分比
  rate?: string;
  // 版本号
  version: string;
  // 发布描述
  desc: string;
}