import { Rule, RuleType } from '@midwayjs/validate';
import { EPage } from '@/apis/const/one-link';

export class CreateDTO {
  /** 页面 */
  @Rule(RuleType.string().required().valid(EPage))
  page: EPage;

  /** 页面参数 */
  @Rule(RuleType.object().pattern(RuleType.string(), RuleType.string()))
  pageParams?: { [key: string]: string };

  /** 是否需要短链 */
  @Rule(RuleType.boolean())
  needShortLink?: Boolean;
}


export class CreateForWebviewDTO {
  /** h5链接 */
  @Rule(RuleType.string().required())
  h5Url: string;

  @Rule(RuleType.object().pattern(RuleType.string(), RuleType.string()))
  /** 页面参数，带给webview的 */
  pageParams?: { [key: string]: string };

  @Rule(RuleType.boolean())
  /** 是否需要短链 */
  needShortLink?: <PERSON>olean;
}
