import { Rule, RuleType } from '@midwayjs/validate';
import { EClient } from '@/apis/const/iter-deliver';
import { EProjectType } from '@/apis/const/project';
import { EBuildPlatform } from '../const/build';

const requiredString = RuleType.string().required();

class ClientDTO {
  /** 小程序id */
  @Rule(requiredString)
  miniAppId: string;

  /** 端名称 */
  @Rule(requiredString)
  clientName: EClient;
}

class DingtalkRobotDTO {
  /** 钉钉webhook地址 */
  @Rule(requiredString)
  robotHook: string;

  /** 秘钥 */
  @Rule(requiredString)
  signCode: string;
}

class WsyConfigDTO {
  /** 场景值 */
  @Rule(requiredString)
  scene: string;

  /** 秘钥 */
  @Rule(requiredString)
  accessKey: string;

  /** 支持平台 */
  @Rule(RuleType.array().items(RuleType.string()).min(1).required())
  platformList: EBuildPlatform[];
}

export class CreateDTO {
  /** 项目名称 */
  @Rule(requiredString)
  name: string;

  /** 项目中文名 */
  @Rule(requiredString)
  cnName: string;

  /** 项目类型 */
  @Rule(requiredString)
  type: EProjectType;

  /** 项目图标 */
  @Rule(requiredString)
  icon: string;

  /** 代码仓库地址 */
  @Rule(requiredString)
  gitRepo: string;

  /** 管理员 */
  @Rule(RuleType.array().items(RuleType.string()))
  adminWorkidList?: string[];

  /** 投放端 */
  @Rule(ClientDTO, { required: false })
  clientList?: ClientDTO[];

  /** 钉钉机器人 */
  @Rule(DingtalkRobotDTO, { required: false })
  dingtalkRobots?: DingtalkRobotDTO[];

  /** 项目是组件类型时，投放的配置信息 */
  @Rule(RuleType.string())
  deliverConfig?: string;

  /** 打码配置 */
  @Rule(WsyConfigDTO, { required: false })
  wsyConfig?: WsyConfigDTO;
}

export class UpdateDTO extends CreateDTO {
  /** 项目id */
  @Rule(RuleType.number().required())
  id: number;
}

export class DeleteDTO {
  /** 项目id */
  @Rule(RuleType.string().required())
  id: string;
}
