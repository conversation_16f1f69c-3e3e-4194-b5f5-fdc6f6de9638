import { Provide, ScopeEnum, Scope } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';

@Provide('handlerMiddleware')
@Scope(ScopeEnum.Singleton)
export class HandlerMiddleware {
  resolve() {
    return async (ctx: Context, next) => {
      try {
        await next();
        if (ctx.body.errorMsg) {
          ctx.body = {
            success: false,
            data: ctx.body.data,
            errorMsg: ctx.body.errorMsg
          };
        } else {
          ctx.body = {
            success: true,
            data: ctx.body
          };
        }
      } catch (err: any) {
        ctx.body = {
          success: false,
          errorMsg: err?.message
        }
        ctx.logger.error(err);
      }
    }
  }
}


@Provide('wrapResponseMiddleware')
@Scope(ScopeEnum.Singleton)
export class WrapResponseMiddleware {
  resolve() {
    return async (ctx: Context, next) => {
      try {
        await next();

        ctx.body = {
          success: true,
          message: '成功',
          data: ctx.body
        };
      } catch (err: any) {
        ctx.body = {
          success: false,
          message: err?.message,
          data: null
        }
        ctx.logger.error(err);
      }
    }
  }
}

export function WrapResponse(_target: any, _key: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value;

  descriptor.value = async function (...args: any[]) {
    try {
      const data = await originalMethod.apply(this, args);

      return {
        success: true,
        message: '成功',
        data
      };
    } catch (err: any) {
      // @ts-ignore
      this.ctx.logger.error(err);

      return {
        success: false,
        message: err?.message,
        data: null
      }
    }
  };

  return descriptor;
}