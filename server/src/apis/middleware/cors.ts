import { Provide, ScopeEnum, Scope } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';

const WHITE_HOST = [
  '.taobao.com'
]
const ACCESS_NAME_PREFIX = 'Access-Control-Allow';

@Provide('corsMiddleware')
@Scope(ScopeEnum.Singleton)
export class CorsMiddleware {
  resolve() {
    return async (ctx: Context, next) => {
      await next();

      // 如果匹配到白名单域名，则允许跨域
      if (WHITE_HOST.some(host => {
        // 如果是.开头则模糊匹配，否则强匹配
        if (host.substring(0, 1) === '.') {
          return ctx.host.endsWith(host)
        } else {
          return ctx.host === host;
        }
      })) {        
        ctx.set(`${ACCESS_NAME_PREFIX}-Origin`, '*');
        ctx.set(`${ACCESS_NAME_PREFIX}-Credentials`, 'true');
      }
    }
  }
}
