import * as path from 'path';
import * as fse from 'fs-extra';
import { exec } from 'child_process';
import simpleGit, { SimpleGit, TaskOptions } from 'simple-git';
import { platform, tmpdir } from 'os';
import { isServer } from '@/apis/utils';
import { parseGitRepoInfo } from '@/apis/utils/git';
import { IGitRepoInfo } from '@/apis/interface/git-opts';
import { USER_CONFIG } from '@/apis/config/git';

/**
 * 生成临时目录，以供文件操作场景
 * @param opt.dirName 目录名
 * @returns 
 */
export function useTmpDir(opt?: { dirName?: string }) {
  let { dirName = String(Date.now()) } = opt || {};
  // 获取系统临时目录
  let tmpDirPath = tmpdir();

  // 创建当前操作专属临时目录
  tmpDirPath = path.join(tmpDirPath, dirName)
  fse.mkdirSync(tmpDirPath)

  return {
    tmpDirPath: tmpDirPath,
    removeTmpDir() {
      // 删除临时目录
      exec(`rm -rf ${tmpDirPath}`);
    }
  }
}

/**
 * 创建 git 句柄
 * @param baseDir 文件操作根目录
 * @returns 
 */
export function useGit(baseDir: string): Promise<{ gitHandler: SimpleGit }>

/**
 * 创建 git 句柄
 * @param baseDir 文件操作根目录
 * @param opts 配置项
 */
export async function useGit(baseDir: string, opts: IUseGitOpts): Promise<{
  gitHandler: SimpleGit;
  projectTmpDirPath: string;
  projectTmpDirName: string;
  gitRepoInfo: IGitRepoInfo;
}>

export async function useGit(baseDir: string, opts?: IUseGitOpts) {
  const { branchUrl, branchName, cloneTaskOptions, cloneSingleBranch } = opts || {};
  const binDir = path.join(process.cwd(), isServer ? 'bin' : '../server/bin');
  const privateKeyFile = path.join(binDir, './ssh/private');
  const GIT_SSH_COMMAND = `ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no -i ${privateKeyFile}`;
  // 创建 git 句柄
  const gitHandler = simpleGit({
    baseDir,
    binary: platform() === 'darwin' ? 'git' : path.join(binDir, './git'),
    maxConcurrentProcesses: 6,
  }).env({ GIT_SSH_COMMAND });
  // 如果没有传入具体分支地址，则直接返回，否则进一步初始化
  if (!branchUrl) return Promise.resolve({ gitHandler })

  // 根据 git 仓库地址，解析出 git 仓库信息
  const gitRepoInfo = parseGitRepoInfo(branchUrl);
  if (!gitRepoInfo?.group || !gitRepoInfo.project) throw new Error('parse git repo info fail');

  // 如果传入了操作分支名，则提前检查分支是否存在
  if (branchName) {
    const branchIsExist = await gitHandler.listRemote(['--heads', gitRepoInfo.sshUrl, branchName]);
    if (!branchIsExist) throw Error(`BRANCH_DOES_NOT_EXIT::代码分支 ${branchName} 不存在`)
  }

  // 代码本地操作目录
  const projectTmpDirName = `${gitRepoInfo.group}-${gitRepoInfo.project}`;
  const projectTmpDirPath = path.join(baseDir, projectTmpDirName);

  // clone 仓库并配置用户信息
  const projectIsExist = await fse.pathExists(projectTmpDirPath);
  if (projectIsExist) {
    const statusRes = await gitHandler.cwd(projectTmpDirPath).status(['-s']);
    if (!statusRes.isClean()) {
      await gitHandler.reset(['--hard']);
    }
  } else {
    await gitHandler.clone(gitRepoInfo.sshUrl, projectTmpDirName,
      cloneTaskOptions ||
      (cloneSingleBranch && branchName ? {
        '--branch': branchName,
        '--depth': '1',
        '--single-branch': null,
      } : {
        '--no-single-branch': null,
        '--shallow-since': '3.month.ago'
      }))
  }
  await gitHandler.cwd(projectTmpDirPath)
    .addConfig('user.name', USER_CONFIG.NAME)
    .addConfig('user.email', USER_CONFIG.EMAIL)
    .addConfig('merge.ours.driver', "true")

  // 如果没有传入具体分支名，则直接返回，否则进一步初始化
  if (!branchName) return Promise.resolve({ gitHandler, projectTmpDirName, projectTmpDirPath, gitRepoInfo })

  // 检出分支
  await gitHandler.checkout([branchName])

  return {
    gitHandler,
    projectTmpDirPath,
    projectTmpDirName,
    gitRepoInfo
  }
}

interface IUseGitOpts {
  branchUrl: string;
  branchName?: string;
  /** 只克隆指定分支，且克隆深度为 1，前提条件是传递 branchName */
  cloneSingleBranch?: boolean;
  /** 优先级高于 cloneSingleBranch */
  cloneTaskOptions?: TaskOptions;
}