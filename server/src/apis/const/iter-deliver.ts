import { isDaily } from '@/apis/utils';

/** 投放端 */
export enum EClient {
  /** 支付宝 */
  ALIPAY = 'alipay',
  /** 淘宝 */
  TAOBAO = 'taobao',
  /** 微信 */
  WEIXIN = 'weixin',
  /** 抖音 */
  DOUYIN = 'douyin',
  /** 今日头条 */
  TOUTIAO = 'toutiao',
  /** 抖音lite */
  DOUYIN_LITE = 'douyin_lite',
  /** 今日头条lite */
  TT_LITE = 'tt_lite',
}

/** 投放端中文名称 */
export const CLIENT_CN_NAME = {
  [EClient.ALIPAY]: '支付宝',
  [EClient.TAOBAO]: '淘宝',
  [EClient.WEIXIN]: '微信',
  [EClient.DOUYIN]: '抖音',
  [EClient.TOUTIAO]: '今日头条',
  [EClient.DOUYIN_LITE]: '抖音lite',
  [EClient.TT_LITE]: '今日头条lite',
}

/** 小程序投放静态配置 */
export const DELIVER_CLIENT_STATIC_CFG = {
  [EClient.WEIXIN]: {
    cnName: '微信',
    icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01gCVbk41OeNuXphUlf_!!6000000001730-55-tps-200-200.svg',
    graySteps: [
      { type: 'external', title: '外灰10%', strategy: 'p10' },
      { type: 'external', title: '外灰30%', strategy: 'p30' },
      { type: 'external', title: '外灰50%', strategy: 'p50' }
    ]
  },
  [EClient.ALIPAY]: {
    cnName: '支付宝',
    icon: 'https://gw.alicdn.com/imgextra/i1/O1CN013iDn3m1ryeIHebpZd_!!6000000005700-55-tps-200-200.svg',
    graySteps: [
      { type: 'internal', title: '蚂蚁内灰', strategy: 'gray_mayi_rule' },
      { type: 'internal', title: '集团内灰', strategy: 'gray_alibaba_rule' },
      { type: 'external', title: '外灰1%', strategy: 'p1' },
      { type: 'external', title: '外灰5%', strategy: 'p5' },
      { type: 'external', title: '外灰10%', strategy: 'p10' },
      { type: 'external', title: '外灰30%', strategy: 'p30' },
      { type: 'external', title: '外灰50%', strategy: 'p50' },
    ]
  },
  [EClient.DOUYIN]: {
    cnName: '抖音',
    icon: 'https://gw.alicdn.com/imgextra/i1/O1CN01okMEEr20gAul3MxOP_!!6000000006878-55-tps-200-200.svg',
    graySteps: null // 字节小程序没有灰度节点
  },
  [EClient.DOUYIN_LITE]: {
    cnName: '抖音lite',
    icon: 'https://gw.alicdn.com/imgextra/i1/O1CN01IzSbza1hwgUvpWbHU_!!6000000004342-55-tps-200-200.svg',
    graySteps: null // 字节小程序没有灰度节点
  },
  [EClient.TOUTIAO]: {
    cnName: '今日头条',
    icon: 'https://gw.alicdn.com/imgextra/i4/O1CN01PRQzfV1zXdvYa6gqu_!!6000000006724-55-tps-200-200.svg',
    graySteps: null // 字节小程序没有灰度节点
  },
  [EClient.TT_LITE]: {
    cnName: '今日头条lite',
    icon: 'https://gw.alicdn.com/imgextra/i1/O1CN01MmPfyc1rhhhTXwx3I_!!6000000005663-55-tps-200-200.svg',
    graySteps: null // 字节小程序没有灰度节点
  }
}

/** 小程序上传状态 */
export enum EMiniAppUploadStatus {
  /** 上传中 */
  UPLOADING = 'UPLOADING',
  /** 上传成功 */
  SUCCESS = 'SUCCESS',
  /** 上传失败 */
  FAILED = 'FAILED',
}

/** 小程序上传状态文案 */
export const MINI_APP_UPLOAD_STATUS_TEXT = {
  [EMiniAppUploadStatus.UPLOADING]: '上传中',
  [EMiniAppUploadStatus.SUCCESS]: '上传成功',
  [EMiniAppUploadStatus.FAILED]: '上传失败',
}

/** 小程序版本状态 */
export enum EMiniAppVersionStatus {
  /** 开发中 */
  INIT = 'INIT',
  /** 审核中 */
  AUDITING = 'AUDITING',
  /** 审核通过 */
  WAIT_RELEASE = 'WAIT_RELEASE',
  /** 审核驳回 */
  AUDIT_REJECT = 'AUDIT_REJECT',
  /** 灰度中 */
  GRAY = 'GRAY',
  /** 已上架 */
  RELEASE = 'RELEASE',
  /** 下架 */
  OFFLINE = 'OFFLINE'
}

/** 小程序版本状态文案 */
export const MINI_APP_VERSION_STATUS_TEXT = {
  [EMiniAppVersionStatus.INIT]: '开发中',
  [EMiniAppVersionStatus.AUDITING]: '审核中',
  [EMiniAppVersionStatus.WAIT_RELEASE]: '审核通过',
  [EMiniAppVersionStatus.AUDIT_REJECT]: '审核驳回',
  [EMiniAppVersionStatus.GRAY]: '灰度中',
  [EMiniAppVersionStatus.RELEASE]: '已上架',
  [EMiniAppVersionStatus.OFFLINE]: '已下架'
}

/** 小程序投放信息 */
export const MINIAPP_CONFIG = {
  // 支付宝一体化小程序
  '2018081461095002': {
    pid: '2088401403430061',
    privateKeyName: isDaily ? 'fliggy_pid_prikey_2088401403430061_2018081461095002_rsa2' : 'pid_prikey_2088401403430061_2018081461095002_rsa2',
    alipayPublicKeyName: 'fliggy_alipay_pubkey_2088401403430061_2018081461095002_rsa2'
  },
  // 微信小程序
  'wx6a96c49f29850eb5': {
    astoreId: '500051'
  }
}

/** bundle id 对应表 */
export const BUNDLE_ID_CONFIG = {
  alipay: 'com.alipay.alipaywallet'
}