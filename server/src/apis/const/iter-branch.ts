export enum EBranchType {
  /** 迭代类型：项目类型 */
  APP = 'app',
  /** 迭代类型：组件类型 */
  COMPONENT = 'component',
}

export enum IterStatus {
  /** 废弃 */
  ABANDON = -1,
  /** 计划发布 */
  PLAN = 0,
  /** 集成回归 */
  MERGE = 1,
  /**
   * @deprecated 新链路中，该状态废弃
   * 审核中
   **/
  AUDITING = 4,
  /**
   * @deprecated 新链路中，该状态废弃
   * 灰度中
   **/
  GRAY = 2,
  /** 投放中 */
  DELIVERING = 5,
  /** 已发布 */
  PUBLISHED = 3
}

export const ITER_STATUS_TEXT = {
  [IterStatus.ABANDON]: '废弃',
  [IterStatus.PLAN]: '计划发布',
  [IterStatus.MERGE]: '集成回归',
  [IterStatus.AUDITING]: '审核中',
  [IterStatus.GRAY]: '灰度中',
  [IterStatus.DELIVERING]: '投放中',
  [IterStatus.PUBLISHED]: '已发布',
}
