export enum EProjectType {
  /** 支系小程序 */
  ALIPAY = 'alipay',
  /** 淘系轻应用 */
  TAOBAO = 'taobao',
  /** 字节小程序 */
  BYTEDANCE = 'bytedance',
  /** 微信小程序 */
  WEIXIN = 'weixin',
  /** 组件 */
  COMPONENT = 'component',
  /** 其他类型 */
  OTHER = 'other'
}

export const PROJECT_TYPE_CN_NAME = {
  [EProjectType.ALIPAY]: '支系小程序',
  [EProjectType.TAOBAO]: '淘系轻应用',
  [EProjectType.BYTEDANCE]: '字节小程序',
  [EProjectType.WEIXIN]: '微信小程序',
  [EProjectType.COMPONENT]: '组件',
  [EProjectType.OTHER]: '其他'
}

export default {
  [EProjectType.WEIXIN]: {
    steps: [
      { title: '外灰10%' },
      { title: '外灰30%' },
      { title: '外灰50%' },
      { title: '上架' }
    ],
    platform: 'https://mp.weixin.qq.com',
    dingtalkBigLogo: 'https://gw.alicdn.com/imgextra/i2/O1CN01ZfF0bt1TNVOeUe0Zs_!!6000000002370-2-tps-1000-150.png',
    dingtalkLogo: 'https://gw.alicdn.com/imgextra/i2/O1CN01ZE4xW81TbFHVFHbBD_!!6000000002400-2-tps-1000-100.png'
  },
  [EProjectType.ALIPAY]: {
    steps: [
      { title: '蚂蚁内灰' },
      { title: '集团内灰' },
      { title: '外灰1%' },
      { title: '外灰5%' },
      { title: '外灰10%' },
      { title: '外灰30%' },
      { title: '外灰50%' },
      { title: '上架' }
    ],
    platform: 'https://openhome.alipay.com/mini/dev/sub/dev-manage?appId=2018081461095002&bundleId=com.alipay.alipaywallet',
    dingtalkBigLogo: 'https://gw.alicdn.com/imgextra/i3/O1CN01vK9sGg1TSXkgo4CUa_!!6000000002381-2-tps-1000-150.png',
    dingtalkLogo: 'https://gw.alicdn.com/imgextra/i3/O1CN019mzviU1zv04HHIdBq_!!6000000006775-2-tps-1000-100.png'
  },
  [EProjectType.BYTEDANCE]: {
    steps: [
      { title: '上架' }
    ],
    platform: 'https://microapp.bytedance.com/app/tt38454ef8a76f8b46/indexpage',
    dingtalkBigLogo: 'https://gw.alicdn.com/imgextra/i3/O1CN01lERF5i1UnRV0i8t42_!!6000000002562-0-tps-1000-150.jpg',
    dingtalkLogo: 'https://gw.alicdn.com/imgextra/i4/O1CN010DD9Yq1f1m32oO7B4_!!6000000003947-0-tps-1000-100.jpg'
  },
  [EProjectType.COMPONENT]: {
    steps: [],
    platform: '',
    dingtalkBigLogo: 'https://gw.alicdn.com/imgextra/i2/O1CN01bnfhVx1yGrq75ME2f_!!6000000006552-2-tps-1420-196.png',
    dingtalkLogo: 'https://gw.alicdn.com/imgextra/i3/O1CN01bMon4G23EBbAicE7Z_!!6000000007223-2-tps-1202-188.png'
  }
}

// 通知回归测试
export const NOTICE_REGRESSION_TESTING = {
  [EProjectType.WEIXIN]: {
    qrCodeLink: 'https://img.alicdn.com/imgextra/i1/O1CN0137BPQc1e3JidE68MJ_!!6000000003815-2-tps-500-440.png',
    title: '迭代 v${version} 已生成体验版，请大家回归测试',
    text: '### 飞猪微信小程序 v${version} 已生成体验版，请大家回归测试'
      + '\n'
      + '![bg](${qrCodeLink})'
      + '\n'
      + '大家可以扫码体验，也可以通过研发平台的[体验码生成工具](https://fl-miniwork.fc.alibaba-inc.com/#/helper/link)自助扫码，自助扫码可以指定启动的页面和参数哦~'
  },
  [EProjectType.ALIPAY]: {
    title: '迭代 v${version} 已生成体验版，请大家回归测试',
    text: '### 支付宝一体化小程序 v${version} 已生成体验版，请大家回归测试'
      + '\n'
      + '![bg](${qrCodeLink})'
  },
  [EProjectType.BYTEDANCE]: {
    title: '迭代 v${version} 已生成体验版，请大家回归测试',
    text: '### 飞猪字节小程序 v${version} 已生成体验版，请大家回归测试'
      + '\n'
      + '![bg](${qrCodeLink})'
  },
}

// at迭代上的开发、测试回归
export const NOTICE_AT_MEMEBER_TEXT = '辛苦回归下${appName}本次迭代，回归完成后，麻烦回研发平台点下「确认已回归」[送花花][送花花][送花花]本次迭代地址：https://fl-miniwork.fc.alibaba-inc.com/#/iter/detail?iterId=${iterId}';
export const NOTICE_AT_MEMEBER_TEXT_WITH_AP = '辛苦回归下${appName}本次迭代（外网环境，覆盖安卓、iOS、鸿蒙），回归完成后，麻烦回研发平台点下「确认已回归」[送花花][送花花][送花花]本次迭代地址：https://fl-miniwork.fc.alibaba-inc.com/#/iter/detail?iterId=${iterId}';
