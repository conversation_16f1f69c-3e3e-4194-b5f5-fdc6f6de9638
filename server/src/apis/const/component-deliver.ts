/** 投放类型 */
export enum EDeliverType {
  H5 = 1,
  WEIXIN = 2,
  ALIPAY = 3,
  BYTEDANCE = 4,
}

/** 投放分支类型 */
export enum EDeliverBranchType {
  FREE_DEV_BRANCH = 1,
  DEV_BRANCH = 2,
  ITERATION = 3,
}

/** 小程序投放状态 */
export enum EMinaStatus {
  INIT = 1, // 初始化
  BETA = 2, // 预发投放
  PROD = 3, // 线上投放
}

export enum EDeliverStatus {
  /** 废弃 */
  ABANDON = -1,
  /** 待初始化 */
  NOT_INITIAL = 0,
  /** 未投放 */
  INITIAL = 1,
  /** 预发投放中 */
  PREPUBING = 2,
  /** 预发投放成功 */
  PREPUB_SUCCESS = 3,
  /** 预发投放失败 */
  PREPUB_FAIL = 4,
  /** 线上投放中 */
  PUBLISHING = 5,
  /** 线上投放成功 */
  PUBLISH_SUCCESS = 6,
  /** 线上投放失败 */
  PUBLISH_FAIL = 7,
}

export const DELIVER_STATUS_WORDING = {
  [EDeliverStatus.ABANDON]: '废弃',
  [EDeliverStatus.NOT_INITIAL]: '待初始化',
  [EDeliverStatus.INITIAL]: '未投放',
  [EDeliverStatus.PREPUBING]: '预发投放中',
  [EDeliverStatus.PREPUB_SUCCESS]: '预发投放成功',
  [EDeliverStatus.PREPUB_FAIL]: '预发投放失败',
  [EDeliverStatus.PUBLISHING]: '线上投放中',
  [EDeliverStatus.PUBLISH_SUCCESS]: '线上投放成功',
  [EDeliverStatus.PUBLISH_FAIL]: '线上投放失败',
}
