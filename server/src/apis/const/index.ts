import { isDaily } from '@/apis/utils';

export const APP_NAME = 'fn-fl-miniwork';

// 本地地址
export const LOCAL_URL = 'http://localhost:8000';

// 预发地址
export const PRE_URL = 'https://fl-miniwork.pre-fc.alibaba-inc.com';

// 线上地址
export const PROD_URL = 'https://fl-miniwork.fc.alibaba-inc.com';

// Aone 配置
export const AONE_CONFIG = {
  appSecret: '6MleF9GES46+kQ9tbNY49A=='
};

// amdp 配置
export const AMDP_CONFIG = {
  appKey: APP_NAME,
  appSecret: isDaily ? 'test' : 'b7664bfb-bde9-4a4a-acdd-bcf2e3475f66'
}

export enum BIZ_LINE {
  Common = 'common',
  Hotel = 'hotel',
  Vacation = 'vacation',
  Bus = 'bus',
  Ship = 'ship',
  Flight = 'flight',
  Train = 'train',
  Vehicle = 'vehicle',
  User = 'user',
  Growth = 'growth',
  Member = 'member',
  Shop = 'shop',
}

// 业务名称
export const BIZ_TITLE = {
  [BIZ_LINE.Common]: '公共',
  [BIZ_LINE.Hotel]: '酒店',
  [BIZ_LINE.Vacation]: '度假',
  [BIZ_LINE.Bus]: '汽车票',
  [BIZ_LINE.Ship]: '船票',
  [BIZ_LINE.Flight]: '机票',
  [BIZ_LINE.Train]: '火车票',
  [BIZ_LINE.Vehicle]: '用车',
  [BIZ_LINE.User]: '用户',
  [BIZ_LINE.Growth]: '用增',
  [BIZ_LINE.Member]: '会员',
  [BIZ_LINE.Shop]: '店铺',
}

// 团队维度包大小限制
export const GROUP_SIZE = [
  {
    group: 'platform',
    name: '平台',
    size: 2 * 1024 * 1024,
    bizLines: [BIZ_LINE.Member, BIZ_LINE.Growth, BIZ_LINE.User,],
  },
  {
    group: 'common',
    name: '公共',
    size: 4 * 1024 * 1024,
    bizLines: [BIZ_LINE.Common,],
  },
  {
    group: 'hotel',
    name: '酒店',
    size: 4 * 1024 * 1024,
    bizLines: [BIZ_LINE.Hotel, BIZ_LINE.Shop,],
  },
  {
    group: 'traffic',
    name: '交通',
    size: 6 * 1024 * 1024,
    bizLines: [BIZ_LINE.Flight, BIZ_LINE.Train, BIZ_LINE.Bus, BIZ_LINE.Ship, BIZ_LINE.Vehicle,],
  },
  {
    group: 'vacation',
    name: '度假',
    size: 4 * 1024 * 1024,
    bizLines: [BIZ_LINE.Vacation,],
  },
];