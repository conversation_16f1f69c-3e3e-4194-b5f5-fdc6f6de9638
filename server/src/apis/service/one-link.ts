import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import ShortLinkHSF from '../hsf/short-link';
import { CreateForWebviewDTO } from '../dto/one-link';
import { IOneLinkInfo } from '../interface/one-link';
import { WEBVIEW_ONE_LINK, WEBVIEW_SHORT_ONE_LINK, WEBVIEW_TO_LINK_QUERIES } from '../const/one-link';

@Provide()
export default class OneLinkService {
  @Inject()
  ctx!:Context;

  @Inject()
  shortLinkHSF: ShortLinkHSF;

  async createForWebview(createParams: CreateForWebviewDTO) {
    const res = {} as IOneLinkInfo;
    const h5Url = createParams.h5Url;
    const webviewPageParams = createParams.pageParams || {}; // 小程序webview页消费的参数

    // h5链接上的部分参数，需要提到套壳页链接上，比如titlebar参数，否则可能会不生效
    const h5UrlInstance = new URL(h5Url);
    Object.entries(h5UrlInstance.searchParams).forEach(([key, value]) => {
      if (WEBVIEW_TO_LINK_QUERIES.includes(key)) webviewPageParams[key] = value;
    });

    // 生成套壳页的链接，如https://router.feizhu.com/multi/webview?url=https%3A%2F%2Fmarket.m.taobao.com%2Fapp%2Ftrip%2Frx-trip-ticket%2Fpages%2Fhome%3FtitleBarHidden%3D2%26disableNav%3DYES&titleBarHidden=2&disableNav=YES
    const link = generateWebviewLink(h5Url, webviewPageParams);
    res.link = link;

    // 如果需要生成短链，再额外处理下，这里短链结构比较奇怪的原因是需要兼容微信扫码
    if (createParams.needShortLink) {
      // 将套壳页链接转成短链
      const shortLinkInfo = await this.shortLinkHSF.createFurl(link, true);
      // 替换短链域名，收口到fl-router处理
      const shortLink = `${WEBVIEW_SHORT_ONE_LINK}${shortLinkInfo.shortPath}`;
      // 生成套壳页的短链，如https://router.feizhu.com/multi/webview?url=https%3A%2F%2Frouter.feizhu.com%2Fws%2Fabcd&titleBarHidden=2&disableNav=YES
      res.shortLink = generateWebviewLink(shortLink, webviewPageParams);
    }

    return res;
  }
}

function generateWebviewLink(h5Url: string, appendParams?: Record<string, string>) {
  const urlInstance = new URL(WEBVIEW_ONE_LINK);
  urlInstance.searchParams.append('url', h5Url);

  if (appendParams) {
    Object.entries(appendParams).forEach(([key, value]) => {
      urlInstance.searchParams.append(key, value);
    });
  }

  return urlInstance.href;
}
