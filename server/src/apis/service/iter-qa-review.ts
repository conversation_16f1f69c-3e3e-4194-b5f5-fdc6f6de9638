import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { convertsCollectionToCamel, convertsCollectionToSnake, formatDate, filterOutNull } from '@/apis/utils';
import { IIterQaReview, IIterQaReviewRes } from '@/apis/interface/iter-qa-review';
import { IIterQaReviewModel } from '@/apis/model/iter-qa-review';
import UserService from '@/apis/service/user';
import IterBranchService from '@/apis/service/iter-branch';
import DingtalkService from '@/apis/service/dingtalk';
import { BIZ_LINE, BIZ_TITLE } from '@/apis/const';
import ActionHistoryService from '@/apis/service/action-history';
import { EIterQaReviewAction } from '@/apis/const/iter-qa-review';
import { IActionRecord } from '@/apis/interface/action-history';

@Provide()
export default class IterQaReviewService {
  @Inject()
  ctx!: Context

  @Inject()
  IterQaReviewModel!: IIterQaReviewModel;

  @Inject()
  userService!: UserService;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  actionHistoryService!: ActionHistoryService;

  /** 创建回归记录 */
  async create({ iterId, bizName }: { iterId: number; bizName: BIZ_LINE; }) {
    const dbValues = this._covertToDBData({
      iterId,
      bizName,
      reviewResList: [],
      gmtCreate: formatDate(new Date()),
      actionHistory: this.actionHistoryService.init('create', '触发全量回归')
    });
    const iterQaReviewModel = await this.IterQaReviewModel.create(dbValues);

    return this._formatDBData(iterQaReviewModel.toJSON());
  }

  /** 更新回归记录 */
  async update(id: number, { reviewResList, actionHistory }: { reviewResList?: IIterQaReviewRes[]; actionHistory?: IActionRecord<EIterQaReviewAction>[] }) {
    const dbValues = this._covertToDBData({
      reviewResList,
      actionHistory
    });

    const updateResult = await this.IterQaReviewModel.update(dbValues, {
      where: { id },
    });

    if (updateResult[0] === 0) throw Error(`更新迭代回归记录(id: ${id})失败`);

    return await this.get(id) as IIterQaReview;
  }

  /** 删除回归记录 */
  async delete(id: number) {
    const destroyResult = await this.IterQaReviewModel.destroy({ where: { id } });
    return destroyResult > 0;
  }

  /** 获取回归记录 */
  async get(id: number) {
    const res = await this.IterQaReviewModel.findOne({
      where: { id },
      raw: true
    });

    if (res) {
      return this._formatDBData(res);
    } else {
      return null;
    }
  }

  /** 删除指定迭代的所有回归记录 */
  async deleteAllByIterId(iterId: number) {
    const destroyResult = await this.IterQaReviewModel.destroy({ where: { iter_id: iterId } });
    return destroyResult > 0;
  }

  /** 获取符合条件的所有回归记录 */
  async list(conditions: { iterId?: number; }) {
    const whereOptions: any = {};

    if (conditions.iterId) whereOptions.iter_id = conditions.iterId;

    const { rows, count } = await this.IterQaReviewModel.findAndCountAll({
      where: whereOptions,
      order: [['gmt_create', 'DESC']],
      raw: true
    });

    const list = rows.map(item => this._formatDBData(item))

    return {
      list,
      total: count
    }
  }

  /** 新增回归结论 */
  async addReviewRes(id: number, { pass, title, comment }: { pass: boolean; title: string; comment: string; }) {
    const iterQaReview = await this.get(id);
    if (!iterQaReview) throw Error(`查询不到id为${id}的迭代回归记录`);

    // 构造回归结论
    const reviewRes = {
      resId: Date.now(),
      reviewer: {
        name: this.ctx.user.name,
        workid: this.ctx.user.workid,
      },
      pass,
      title,
      comment
    }
    const reviewResList = iterQaReview.reviewResList.concat(reviewRes);

    // 记录行为
    let action: EIterQaReviewAction;
    let actionTitle: string;
    if (pass) {
      action = EIterQaReviewAction.ReviewPass;
      actionTitle = '新增回归结论：通过';
    } else {
      action = EIterQaReviewAction.ReviewNoPass;
      actionTitle = '新增回归结论：不通过';
    }
    const actionHistory = this.actionHistoryService.add<EIterQaReviewAction>(action, actionTitle, iterQaReview.actionHistory);

    // 更新回归记录
    return await this.update(id, { reviewResList, actionHistory })
  }

  /** 删除回归结论 */
  async deleteReviewRes(id: number, resId: number) {
    const iterQaReview = await this.get(id);
    if (!iterQaReview) throw Error(`查询不到id为${id}的迭代回归记录`);
    const isAdmin = await this.userService.isAdmin();

    // 查询并删除回归结论
    const reviewResList = iterQaReview.reviewResList.filter(reviewRes => {
      if (reviewRes.resId === resId) {
        // 仅管理员或回归者本人有权限删除回归结论
        if (!isAdmin && reviewRes.reviewer.workid !== this.ctx.user.workid) throw Error('仅管理员或回归者本人有权限删除回归结论')

        return false;
      }
      return true
    });
    if (reviewResList.length === iterQaReview.reviewResList.length) throw Error(`查询不到resId为${resId}的迭代回归结论`)

    // 记录行为
    const actionHistory = this.actionHistoryService.add<EIterQaReviewAction>(EIterQaReviewAction.DeleteReviewRes, '删除回归结论', iterQaReview.actionHistory);

    // 更新回归记录
    return await this.update(id, { reviewResList, actionHistory })
  }

  /** 修改回归结论 */
  async modifyReviewRes(id: number, resId: number, { pass, title, comment }: { pass: boolean; title: string; comment: string; }) {
    const iterQaReview = await this.get(id);
    if (!iterQaReview) throw Error(`查询不到id为${id}的迭代回归记录`);
    const isAdmin = await this.userService.isAdmin();

    // 查询并修改回归结论
    let actionHistory = iterQaReview.actionHistory;
    let whetherFind = false;
    const reviewResList = iterQaReview.reviewResList.map(reviewRes => {
      if (reviewRes.resId !== resId) return reviewRes;

      // 仅管理员或回归者本人有权限修改回归结论
      if (!isAdmin && reviewRes.reviewer.workid !== this.ctx.user.workid) throw Error('仅管理员或回归者本人有权限修改回归结论')

      whetherFind = true;

      // 如果前后结论不一致，则记录行为
      if (pass !== reviewRes.pass) {
        let action: EIterQaReviewAction;
        let actionTitle: string;
        if (pass) {
          action = EIterQaReviewAction.ReviewPass;
          actionTitle = '修改回归结论：通过';
        } else {
          action = EIterQaReviewAction.ReviewNoPass;
          actionTitle = '修改回归结论：不通过';
        }
        actionHistory = this.actionHistoryService.add<EIterQaReviewAction>(action, actionTitle, actionHistory);
      }

      return {
        ...reviewRes,
        pass, title, comment
      } as IIterQaReviewRes
    });
    if (!whetherFind) throw Error(`查询不到resId为${resId}的迭代回归结论`)

    // 更新回归记录
    return await this.update(id, { reviewResList, actionHistory })
  }

  /** 转换成 db 数据 */
  _covertToDBData(data: any) {
    return convertsCollectionToSnake({
      ...data,
      reviewResList: data.reviewResList && JSON.stringify(data.reviewResList),
      actionHistory: data.actionHistory && JSON.stringify(data.actionHistory)
    }, 1)
  }

  /** 格式化 db 数据 */
  _formatDBData(dbData: any): IIterQaReview {
    // 过滤掉 null 值，并转驼峰
    const res = convertsCollectionToCamel(filterOutNull(dbData), 1);

    return {
      ...res,
      bizTitle: BIZ_TITLE[res.bizName],
      gmtCreate: formatDate(res.gmtCreate),
      reviewResList: res.reviewResList ? JSON.parse(res.reviewResList) : [],
      actionHistory: res.actionHistory ? JSON.parse(res.actionHistory) : []
    }
  }
}