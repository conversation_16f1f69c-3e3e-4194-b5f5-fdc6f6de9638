import { Context } from '@midwayjs/faas';
import { Provide, Inject } from '@midwayjs/core';
import AmdpHSF from '@/apis/hsf/amdp';
import { AMDP_CONFIG } from '@/apis/const';
import { isDaily } from '@/apis/utils';
import { IUser } from '@/apis/interface/user';

@Provide()
export default class AmdpService {
  @Inject()
  ctx!: Context;

  @Inject()
  amdpHSF!: AmdpHSF;

  async searchByKeyword(keyword: string, pageNo: number, pageSize: number) {
    const amdpResult = await this.amdpHSF.amdpQuerySimpleData(
      JSON.stringify(AMDP_CONFIG),
      JSON.stringify({
        filterFieldList: [{
          name: 'searchKey',
          value: keyword
        }],
        pageNo,
        pageSize,
        combineId: isDaily ? 1224 : 1369
      })
    );

    if (!amdpResult?.success) {
      return {
        list: [],
        total: 0
      };
    }

    const list = amdpResult.content.dataRows.map((dataRow: any) => {
      const { workNo, loginAccount, nickName, name, personalPhotoUrl } = dataRow.EMP_EMPLOYEE || dataRow;

      return {
        userid: loginAccount,
        name: nickName || name,
        lastName: name,
        workid: workNo,
        avatarUrl: personalPhotoUrl, 
      };
    }) as IUser[];
    const total: number = amdpResult.content.totalCount || 0;

    return {
      list,
      total
    };
  }
}