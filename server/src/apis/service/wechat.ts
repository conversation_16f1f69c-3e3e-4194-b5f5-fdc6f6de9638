import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';

@Provide()
export default class WechatService {
  @Inject()
  ctx!: Context;

  /** 微信小程序短链 */
  async generateShortLink(url: string, pageTitle: string) {
    const pageUrl = url.replace(/^\//, '');
    try {
      const result: any = await this.ctx.hsfClient.invoke({
        id: 'com.alibaba.tripzoo.proxy.api.service.WxMiniProgramService:1.0.0',
        group: 'HSF',
        method: 'generateShortLink',
        args: [
          {
            pageTitle,
            pageUrl
          }
        ],
        parameterTypes: ['com.alibaba.tripzoo.proxy.request.GenerateWechatMiniProgramShortLinkRequest'],
      });
      if (result && result.success) {
        return result?.model;
      }
    } catch (err: any) {
      return null;
    }
  }

  /** 生成微信小程序码 scene */
  async generateScene(targetUrl: string) {
    try {
      const result: any = await this.ctx.hsfClient.invoke({
        id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
        group: 'HSF',
        method: 'invoke',
        args: [
          "201703019888404473", // accountID : String
          "fl-wx-middle", // 服务名称 : String
          "qrcode-scene-create",  // 函数名称 : String
          {
            targetUrl
          }
        ],
        parameterTypes: ['java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object'],
        responseTimeout: 1000,
      });
      if (result && result.success) {
        return {
          scene: result?.data?.scene,
        };
      }
      return Promise.reject(result);
    } catch (error: any) {
      return {
        error,
      };
    }
  }

  async generateQrcode(params) {
    const { width, pageUrl, isHyaline, qrcodePath } = params;
    const _url = pageUrl.replace(/^\//, '');
    const [path, query] = _url.split('?');
    // 不能携带参数
    let page = path;
    let scene = 'miniwork';

    // 携带参数
    if (query) {
      const sceneRes = await this.generateScene(_url);
    
      if (!sceneRes.scene) {
        return {
          weappCode: null,
          error: sceneRes.error
        };
      }
      page = qrcodePath;
      scene = sceneRes.scene;
    }

    try {
      const result: any = await this.ctx.hsfClient.invoke({
        id: 'com.alibaba.tripzoo.proxy.api.service.WxMiniProgramService:1.0.0',
        group: 'HSF',
        method: 'getUnlimitedQrCode',
        args: [
          {
            width: width || 430,
            page,
            scene,
            isHyaline,
          }
        ],
        parameterTypes: ['com.alibaba.tripzoo.proxy.request.GetWechatMiniProgramUnlimitedQrCodeRequest'],
        responseTimeout: 3000,
      });
      if (result?.success) {
        return {
          weappCode: result?.model
        };
      }
      return Promise.reject(result);
    } catch (error: any) {
      return {
        weappCode: null,
        error
      };
    }
  }
}