import { Op } from 'sequelize';
import { Model<PERSON><PERSON> } from 'sequelize-typescript';
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { convertsCollectionToCamel, convertsCollectionToSnake, formatDate } from '@/apis/utils';
import { IDevBranch } from '@/apis/interface/dev-branch';
import { IFreeDevBranch } from '@/apis/interface/free-dev-branch';
import { IBaseDevBranch } from '@/apis/interface/base-dev-branch';
import IterBranchService from '@/apis/service/iter-branch';
import ProjectService from '@/apis/service/project';
import UserService from '@/apis/service/user';
import { getBranchUrl, parseGitRepoInfo, deleteBranch, updateBranchDeps, createDevBranch } from '@/apis/utils/git';
import { IterStatus } from '@/apis/const/iter-branch';
import { EFreeDevMountStatus } from '@/apis/const/free-dev-branch';
import { EBranchType, EChecked, EDevStatus, EMergeCode } from '@/apis/const/dev-branch';
import { EProjectType } from '@/apis/const/project';
import { EDefBuildStatus } from '@/apis/const/def';
import { IAone } from '@/apis/interface/aone';
import { IProject } from '@/apis/interface/project';

@Provide()
export default abstract class BaseDevBranchService {
  @Inject()
  ctx!: Context;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  userService!: UserService;

  isFreeDevBranch = false;

  abstract getBranchModel(): ModelCtor;

  /**
   * 获取单条数据
   * @param devId 分支id
   * @returns
   */
  async _get(devId: number): Promise<IDevBranch | IFreeDevBranch | null>
  async _get(devId: number, assertExist: true): Promise<IDevBranch | IFreeDevBranch>
  async _get(devId: number, assertExist?: boolean): Promise<IDevBranch | IFreeDevBranch | null> {
    const res = await this.getBranchModel().findOne({
      where: { dev_id: devId },
      raw: true,
    });

    if (res) {
      return this._covertToClientData(res);
    } else if (assertExist) {
      throw Error(`查询不到 devId 为 ${devId} 的${this.isFreeDevBranch ? '游离' : ''}开发分支`)
    } else {
      return null;
    }
  }

  /**
   * 批量获取
   * @param pageSize 每页条数
   * @param pageNum 页码
   * @param conditions 查询条件
   * @returns
   */
  async _list(pageSize: number, pageNum: number, conditions: {
    projectName?: string;
    iterId?: number;
    filterMine?: boolean;
    searchBranchName?: string;
  }) {
    const whereOptions: any = {};
    pageSize = pageSize || 10;
    pageNum = pageNum || 1;

    if (conditions.projectName) whereOptions.project_name = conditions.projectName;
    if (conditions.iterId) whereOptions.iter_id = conditions.iterId;
    if (conditions.filterMine) whereOptions.creator_workid = this.ctx.user.workid;
    if (conditions.searchBranchName) whereOptions.branch_name = { [Op.like]: `%${conditions.searchBranchName}%` };

    const { rows, count } = await this.getBranchModel().findAndCountAll({
      where: whereOptions,
      order: [['dev_id', 'DESC']],
      limit: pageSize,
      offset: pageSize * (pageNum - 1),
      raw: true
    });

    return {
      list: rows.map(item => this._covertToClientData(item)),
      total: count
    }
  }

  /**
   * 创建分支
   * @param params
   * @returns
   */
  async _create(params: IDevBranchCreateParams | IFreeDevBranchCreateParams): Promise<IDevBranch | IFreeDevBranch> {
    const {
      branchName, description, mergeCode,
      npmList, npmResolutionList, bizLine, qaList, aoneList,
      projectType, pkgVersion,
    } = params;
    const isComponentProject = projectType === EProjectType.COMPONENT;
    const isComponentProjectWithPkgVer = isComponentProject && !!pkgVersion;
    let iterId: number | undefined;
    let projectName: string | undefined;
    let project: IProject | null = null;
    let baseBranchUrl: string;
    let baseBranchName: string;

    // 1. 前置判断
    if ('iterId' in params) { // 开发分支逻辑
      iterId = params.iterId;

      // 校验迭代是否存在
      const iterBranch = await this.iterBranchService.get(iterId);
      if (!iterBranch) throw new Error(`查询不到 iterId 为 ${iterId} 的迭代分支`);

      // 校验分支名是否已存在
      const exitDevBranch = await this.getBranchModel().findOne({
        where: {
          branch_name: branchName,
          [Op.and]: [{ iter_id: iterId }]
        },
        raw: true,
      });

      if (exitDevBranch) throw new Error(`迭代 ${iterBranch.version} 下已存在 ${branchName} 同名开发分支`);

      baseBranchUrl = iterBranch.gitBranch.url;
      baseBranchName = iterBranch.gitBranch.name;
      projectName = iterBranch.projectName;
    } else { // 游离开发分支逻辑
      projectName = params.projectName;

      // 校验项目是否存在
      project = await this.projectService.get(projectName);
      if (!project) throw Error(`查询不到 projectName 为 ${projectName} 的项目`);

      // 校验分支名是否已存在
      const existingFreeDevBranch = await this.getBranchModel().findOne({
        where: {
          branch_name: branchName,
          [Op.and]: [{ project_name: projectName }]
        },
        raw: true,
      });

      if (existingFreeDevBranch) throw new Error(`${projectName} 下已存在 ${branchName} 同名游离开发分支`);

      baseBranchUrl = project.gitRepo;
      baseBranchName = 'master';
    }

    // 2. git 操作
    if (!project) {
      project = await this.projectService.get(projectName);
      if (!project) throw Error(`查询不到 projectName 为 ${projectName} 的项目`);
    }
    await createDevBranch(branchName, baseBranchUrl, {
      npmList: mergeCode === EMergeCode.NO ? npmList : [],
      npmResolutionList: mergeCode === EMergeCode.NO ? npmResolutionList : [],
      baseBranchName,
      bizLine: bizLine,
      project,
      isComponentProjectWithPkgVer,
      pkgVersion,
    });

    // 3. 写库
    const dbValues = this._covertToDBData(Object.assign({
      branchType: isComponentProject ? EBranchType.COMPONENT : EBranchType.APP,
      branchName, description, mergeCode,
      npmList, npmResolutionList, bizLine, qaList, aoneList,
      projectName,
      status: EDevStatus.DEVELOP,
      checked: EChecked.NO,
      gitBranch: {
        name: branchName,
        url: getBranchUrl(baseBranchUrl, branchName),
      },
      creator: this.ctx.user.name,
      gmtCreate: formatDate(new Date()),
      creatorWorkid: this.ctx.user.workid
    }, iterId ? {
      iterId,
    } : {
      mountStatus: EFreeDevMountStatus.UNMOUNT,
    }
    ));

    const devBranchDetailModel = await this.getBranchModel().create(dbValues);
    const devBranch = this._covertToClientData(devBranchDetailModel.toJSON());

    // 4. 如果是创建开发分支，则还需要绑定和迭代的关联关系
    if (iterId) {
      await this.iterBranchService.insertDevBranch(iterId, devBranch.devId);
    }

    return devBranch;
  }

  /**
   * 更新
   * @param devId 分支id
   * @param params 入参
   * @param recordOperator 是否记录操作者
   * @returns
   */
  async _update(devId: number, params: IBaseDevBranchUpdateParams, recordOperator = true) {
    const {
      branchType, description, mergeCode,
      npmList, npmResolutionList, bizLine, qaList, aoneList,
      pkgInitialVersion, pkgPublishVersion, defIterId, defTaskId, defBuildStatus, defBranchId
    } = params;

    // 1. 前置判断
    const devBranch = await this._get(devId, true);

    if (devBranch.status !== EDevStatus.DEVELOP) throw Error(`${devBranch.statusText} 状态不可更新`)

    // 2. 如果集成模式是更新依赖包，则需同步操作 git
    if (mergeCode === EMergeCode.NO) {
      await updateBranchDeps(devBranch.branchName, devBranch.gitBranch.url, npmList, devBranch.npmList, npmResolutionList, devBranch.npmResolutionList)
    }

    // 3. 写库
    const dbValues = this._covertToDBData(Object.assign(
      {
        branchType, description, mergeCode,
        bizLine, qaList, aoneList,
        npmList: mergeCode === EMergeCode.YES ? null : npmList,
        npmResolutionList: mergeCode === EMergeCode.YES ? null : npmResolutionList,
        pkgInitialVersion, pkgPublishVersion, defIterId, defTaskId, defBuildStatus, defBranchId
      },
      recordOperator ? {
        modifier: this.ctx.user.name,
        gmtModified: formatDate(new Date()),
        modifierWorkid: this.ctx.user.workid
      } : undefined));
    const updateResult = await this.getBranchModel().update(dbValues, {
      where: { dev_id: devId }
    });

    if (updateResult[0] === 0) throw Error(`SQL: 更新失败`);

    return this._get(devId, true);
  }

  /**
   * 删除
   * @param devId 分支id
   * @returns
   */
  async _delete(devId: number) {
    // 1. 前置判断
    const devBranch = await this._get(devId, true);

    if (devBranch.status === EDevStatus.READY) throw Error('就绪状态不可删除')

    // 2. git 操作
    await deleteBranch(devBranch.branchName, devBranch.gitBranch.url)

    // 3. 删除开发分支
    const destroyCount = await this.getBranchModel().destroy({ where: { dev_id: devId } });

    if (destroyCount === 0) throw Error('SQL: 删除失败');

    // 4. 如果关联了迭代，则还需要移除关联关系
    if (devBranch.iterId) {
      const iterBranch = await this.iterBranchService.get(devBranch.iterId);
      const devBranchListField = this.isFreeDevBranch ? 'freeDevBranchList' : 'devBranchList';
      const devBranchList = iterBranch ? iterBranch[devBranchListField] : [];
      const index = devBranchList.indexOf(devId);

      if (index >= 0) {
        devBranchList.splice(index, 1);
        await this.iterBranchService.update({
          iterId: devBranch.iterId,
          [devBranchListField]: devBranchList
        });
      }
    }

    return devBranch;
  }

  /**
   * 废弃
   * @param devId 分支id
   * @returns
   */
  async _discard(devId: number) {
    // 1. 前置判断
    const devBranch = await this._get(devId, true);

    if (devBranch.status === EDevStatus.READY) throw new Error('就绪状态不可废弃')
    else if (devBranch.status === EDevStatus.ABANDON) throw new Error('已废弃，不可重复废弃')

    // 2. 写库
    const dbValues = this._covertToDBData({
      status: EDevStatus.ABANDON,
      modifier: this.ctx.user.name,
      gmtModified: formatDate(new Date()),
      modifierWorkid: this.ctx.user.workid
    });

    const updateResult = await this.getBranchModel().update(dbValues, {
      where: { dev_id: devId }
    });

    if (updateResult[0] === 0) throw Error(`SQL: 废弃失败`);

    return this._get(devId, true);
  }

  /**
   * 切换就绪状态
   * @param devId 分支id
   * @param ready 是否就绪
   * @returns
   */
   async _switchReady(devId: number, ready: boolean) {
    const actionName = ready ? '准备就绪' : '取消就绪';

    // 1. 前置判断
    const devBranch = await this._get(devId, true);

    // 只有开发状态能准备就绪，只有就绪状态能取消就绪
    if (devBranch.status !== (ready ? EDevStatus.DEVELOP : EDevStatus.READY)) throw new Error(`${devBranch.statusText} 状态不可${actionName}`)
    // 游离开发分支需要挂载迭代才能（取消）就绪
    else if (!devBranch.iterId || (devBranch as IFreeDevBranch).mountStatus === EFreeDevMountStatus.UNMOUNT) throw new Error(`游离开发分支尚未挂载，不可${actionName}`)

    // 如果关联了迭代，则还需要判断迭代状态
    const iterBranch = await this.iterBranchService.get(devBranch.iterId);
    if (!iterBranch) throw new Error(`devId 为 ${devId} 的${this.isFreeDevBranch ? '游离' : ''}开发分支没有找到所属迭代`)
    else if (iterBranch.status !== IterStatus.PLAN) throw Error(`只有当迭代是 计划发布 状态时才能${actionName}`)

    // 2. 写库
    const dbValues = this._covertToDBData(Object.assign(
      {
        status: ready ? EDevStatus.READY : EDevStatus.DEVELOP,
        modifier: this.ctx.user.name,
        gmtModified: formatDate(new Date()),
        modifierWorkid: this.ctx.user.workid
      },
      !ready && { // 取消就绪时，重置一些状态
        checked: EChecked.NO,
        dist: null,
        towerId: null,
        report_analyzed: null,
      }
    ));

    const updateResult = await this.getBranchModel().update(dbValues, {
      where: { dev_id: devId }
    });

    if (updateResult[0] === 0) throw Error(`SQL: ${actionName}失败`);

    return this._get(devId, true);
  }

  /**
   * 切换测试回归状态
   * @param devId 分支id
   * @param checked 是否已测试回归
   * @returns
   */
  async _switchChecked(devId: number, checked: boolean) {
    const actionName = checked ? '确认已回归' : '取消回归确认';

    // 1. 前置判断
    const devBranch = await this._get(devId, true);
    const isInQaList = !!devBranch.qaList?.find(item => item.workid === this.ctx.user.workid);

    if (!isInQaList) {
      const isAdmin = await this.userService.isAdmin();
      if (!isAdmin) throw Error('仅测试成员或管理员有权限操作')
    }

    // 只有就绪的分支能切换测试回归状态
    if (devBranch.status !== EDevStatus.READY) throw new Error(`${devBranch.statusText} 状态不可${actionName}`)
    // 游离开发分支需要挂载迭代才能（取消）回归确认
    else if (!devBranch.iterId || (devBranch as IFreeDevBranch).mountStatus === EFreeDevMountStatus.UNMOUNT) throw new Error(`游离开发分支尚未挂载，不可${actionName}`)

    // 如果关联了迭代，则还需要判断迭代状态
    const iterBranch = await this.iterBranchService.get(devBranch.iterId);
    if (!iterBranch) throw new Error(`devId 为 ${devId} 的${this.isFreeDevBranch ? '游离' : ''}开发分支没有找到所属迭代`)
    else if (iterBranch.status !== IterStatus.MERGE) throw Error(`只有当迭代是 集成回归 状态时才能${actionName}`)

    // 2. 写库
    const dbValues = this._covertToDBData({
      checked: checked ? EChecked.YES : EChecked.NO,
      modifier: this.ctx.user.name,
      gmtModified: formatDate(new Date()),
      modifierWorkid: this.ctx.user.workid
    });

    const updateResult = await this.getBranchModel().update(dbValues, {
      where: { dev_id: devId }
    });

    if (updateResult[0] === 0) throw Error(`SQL: ${actionName}失败`);

    return this._get(devId, true);
  }

  /**
   * 添加构建产物
   * @param devId 开发分支 id
   * @param dist 构建产物地址
   */
  async addDist(devId: number, dist: string) {
    // 1. 前置判断
    const devBranch = await this._get(devId, true);

    if (devBranch.status !== EDevStatus.READY) throw Error('只有就绪状态才能添加构建产物');

    // 写库
    await this.getBranchModel().update({
      dist
    }, {
      where: { dev_id: devId },
      fields: ['dist']
    });
  }

  /**
   * 添加包大小文件
   * @param devId 开发分支 id
   * @param reportAnalyzed 构建产物地址
   */
  async addReportAnalyzed(devId: number, reportAnalyzed: string) {
    // 1. 前置判断
    const devBranch = await this._get(devId, true);

    if (devBranch.status !== EDevStatus.READY) throw Error('只有就绪状态才能添加report_analyzed.json');

    // 写库
    await this.getBranchModel().update({
      report_analyzed: reportAnalyzed
    }, {
      where: { dev_id: devId },
      fields: ['report_analyzed']
    });
  }

  /**
   * 绑定自动化测试任务
   * @param devId 开发分支 id
   * @param towerId 自动化测试任务 id
   */
  async bindTowerId(devId: number, towerId: string) {
    // 1. 前置判断
    const devBranch = await this._get(devId, true);

    if (devBranch.status !== EDevStatus.READY) throw Error('只有就绪状态才能绑定自动化测试任务');

    // 写库
    await this.getBranchModel().update({
      tower_id: towerId
    }, {
      where: { dev_id: devId },
      fields: ['tower_id']
    });
  }

  _covertToDBData(clientData: any) {
    const dbValues = convertsCollectionToSnake(clientData, 1);
    // 数组或对象类型的需处理下再写库
    dbValues.aone_list &&= (dbValues.aone_list.length > 0 ? JSON.stringify(dbValues.aone_list) : null);
    dbValues.npm_list &&= (dbValues.npm_list.length > 0 ? JSON.stringify(dbValues.npm_list) : null);
    dbValues.npm_resolution_list &&= (dbValues.npm_resolution_list.length > 0 ? JSON.stringify(dbValues.npm_resolution_list) : null);
    dbValues.qa_list &&= (dbValues.qa_list.length > 0 ? JSON.stringify(dbValues.qa_list) : null);
    dbValues.git_branch &&= JSON.stringify(dbValues.git_branch);

    return dbValues;
  }

  _covertToClientData(dbData): IDevBranch | IFreeDevBranch {
    const aoneList: IAone[] = dbData.aone_list?.substring(0, 1) === '['
      ? JSON.parse(dbData.aone_list) // 新版采用 json 序列化存储
      : (dbData.aone_list?.split(',') || []).map((item: string) => {  // 历史数据采用 : 分隔存储
        const [stamp, id] = item.split(':');
        return { id, stamp };
      });
    const gitBranch = dbData.git_branch ? JSON.parse(dbData.git_branch) : {};

    let statusText = '';
    // 分支状态文案
    switch (dbData.status) {
      case EDevStatus.ABANDON: statusText = '已废弃'; break;
      case EDevStatus.DEVELOP: statusText = '开发中'; break;
      case EDevStatus.READY: statusText = '准备就绪'; break;
    }

    return {
      ...convertsCollectionToCamel(dbData, 1),
      branchName: dbData.branch_name || dbData.version, // 兼容存量数据，只有 version，没有 branchName
      version: undefined, // 不返回 version 字段
      gmtCreate: dbData.gmt_create && formatDate(dbData.gmt_create),
      gmtModified: dbData.gmt_modified && formatDate(dbData.gmt_modified),
      gitBranch,
      aoneList,
      npmList: dbData.npm_list ? JSON.parse(dbData.npm_list) : [],
      npmResolutionList: dbData.npm_resolution_list ? JSON.parse(dbData.npm_resolution_list) : [],
      qaList: dbData.qa_list && JSON.parse(dbData.qa_list),

      statusText,
      gitRepo: parseGitRepoInfo(gitBranch.url),
    }
  }

  canDiscard(devBranch: IDevBranch): boolean {
    // 就绪状态不能废弃
    return devBranch.status !== 1;
  }

  canDelete(devBranch: IDevBranch): boolean {
    // 就绪状态不能删除
    return devBranch.status !== 1;
  }
}

/** 开发分支创建入参 */
interface IBaseDevBranchCreateParams {
  branchType?: IBaseDevBranch['branchType'];
  branchName: string;
  description: string;
  mergeCode: IBaseDevBranch['mergeCode'];
  npmList?: IBaseDevBranch['npmList'];
  npmResolutionList?: IBaseDevBranch['npmResolutionList'];
  bizLine: IBaseDevBranch['bizLine'];
  qaList: IBaseDevBranch['qaList'];
  aoneList?: IBaseDevBranch['aoneList'];
  pkgVersion?: string;
  projectType: EProjectType;
  pkgInitialVersion?: string;
  pkgPublishVersion?: string;
  defIterId?: number;
  defTaskId?: number;
  defBuildStatus?: EDefBuildStatus;
  defBranchId?: number;
}

export interface IDevBranchCreateParams extends IBaseDevBranchCreateParams {
  iterId: number;
}

export interface IFreeDevBranchCreateParams extends IBaseDevBranchCreateParams {
  projectName: string;
}

// 开发分支更新入参
export interface IBaseDevBranchUpdateParams {
  branchType?: IBaseDevBranch['branchType'];
  description?: string;
  mergeCode?: IBaseDevBranch['mergeCode'];
  npmList?: IBaseDevBranch['npmList'];
  npmResolutionList?: IBaseDevBranch['npmResolutionList'];
  bizLine?: IBaseDevBranch['bizLine'];
  qaList?: IBaseDevBranch['qaList'];
  aoneList?: IBaseDevBranch['aoneList'];
  pkgVersion?: string;
  pkgInitialVersion?: string;
  pkgPublishVersion?: string;
  defIterId?: number;
  defTaskId?: number;
  defBuildStatus?: EDefBuildStatus;
  defBranchId?: number;
}
