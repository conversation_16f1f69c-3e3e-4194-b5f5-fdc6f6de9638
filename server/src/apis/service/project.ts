import * as fse from 'fs-extra';
import * as path from 'path';
import * as chalk from 'chalk';
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { IProject } from '@/apis/interface/project';
import { CreateDTO, UpdateDTO } from '@/apis/dto/project';
import { IProjectModel, ProjectModel } from '@/apis/model/project';
import GitService from '@/apis/service/git';
import UserService from '@/apis/service/user';
import PROJECT_CONFIG from '@/apis/const/project';
import { useGit, useTmpDir } from '@/apis/middleware/hooks';
import { parseGitRepoInfo } from '@/apis/utils/git';
import BucHSF from '@/apis/hsf/buc';
import { maskString } from '@/apis/utils';

@Provide()
export default class ProjectService {
  @Inject()
  ctx!: Context;

  @Inject()
  ProjectModel!: IProjectModel;

  @Inject()
  gitService!: GitService;

  @Inject()
  userService!: UserService;

  @Inject()
  bucHSF!: BucHSF;

  async create(values: CreateDTO): Promise<IProject> {
    const projectModel = await this.ProjectModel.create(values as any);

    return this.parseData(projectModel);
  }

  async update(values: UpdateDTO): Promise<boolean> {
    const condition = { where: { id: values.id, name: values.name } };

    // 首先尝试查找项目
    const existingRecord = await this.ProjectModel.findOne(condition);
    if (!existingRecord) throw Error(`查询不到 id 为 ${values.id}, name 为 ${values.name} 的项目`);

    // 再更新
    await this.ProjectModel.update(values, condition);

    return true;
  }

  async get(name: string, opts?: IOpts) {
    const res = await this.ProjectModel.findOne({
      where: { name },
    });

    return res && this.parseData(res, opts);
  }

  async getById(id: number | string, opts?: IOpts) {
    const res = await this.ProjectModel.findOne({
      where: { id: String(id) },
    });

    return res && this.parseData(res, opts);
  }

  async delete(id: number | string): Promise<boolean> {
    const destroyResult = await this.ProjectModel.destroy({ where: { id: String(id) } });
    return destroyResult > 0;
  }

  async list(opts?: IOpts): Promise<IProject[]> {
    const res = await this.ProjectModel.findAll();

    return await Promise.all(res.map(item => this.parseData(item, opts)))
  }

  async parseData(model: ProjectModel, opts?: IOpts): Promise<IProject> {
    const {
      id, name, cnName, gitRepo, type, adminWorkidList,
      icon, clientList, dingtalkRobots, deliverConfig, wsyConfig
    } = model;
    const { group, project } = parseGitRepoInfo(gitRepo)!;
    const dynamicReturn = {} as any;

    // 判断是否是管理员
    const isSuperAdmin = await this.userService.isAdmin();
    const isAdmin = isSuperAdmin || adminWorkidList.includes(this.ctx?.user?.workid);

    // 获取管理员buc信息
    const adminsBucInfo = await this.bucHSF.getSimpleUserByEmpIdList(adminWorkidList)

    // 如有需要，将项目的package.json以json格式返回
    if (opts?.needPkgJSON) {
      try {
        const { tmpDirPath, removeTmpDir } = useTmpDir();
        const { projectTmpDirPath } = await useGit(tmpDirPath, {
          branchUrl: gitRepo,
          branchName: 'master',
          cloneSingleBranch: true
        });
        const pkgJSON = await fse.readJSON(path.join(projectTmpDirPath, 'package.json'));

        removeTmpDir();
        dynamicReturn.pkgJSON = {
          branch: 'master',
          json: pkgJSON
        }
      } catch (err: any) {
        this.ctx.logger.error(chalk.red(err.message))
      }
    }

    if (isAdmin || opts?.needDingtalkRobots) {
      dynamicReturn.dingtalkRobots = dingtalkRobots;
    }

    // 如果非管理员，或者不是内部系统发起的需要获取完整打码配置的查询，则将打码秘钥加密返回
    if (!isAdmin && !opts?.needFullWsyConfig && wsyConfig?.accessKey) {
      wsyConfig.accessKey = maskString(wsyConfig.accessKey);
    }

    return {
      id,
      name,
      cnName,
      type,
      icon,
      gitRepo,
      group,
      project,
      clientList,
      isAdmin,
      adminWorkidList,
      adminsBucInfo,
      publishInfo: {
        steps: PROJECT_CONFIG[type]?.steps || [],
        platform: PROJECT_CONFIG[type]?.platform,
      },
      deliverConfig,
      wsyConfig,
      ...dynamicReturn,
    };
  }
}

interface IOpts {
  needPkgJSON?: boolean;
  needDingtalkRobots?: boolean;
  // 获取完整打码配置，无视权限
  needFullWsyConfig?: boolean;
}
