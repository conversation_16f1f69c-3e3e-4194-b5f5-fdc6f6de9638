import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import * as httpClient from 'urllib';
import { IProject } from '@/apis/interface/project';
import CrmHSF from '@/apis/hsf/crm';

const crypto = require('crypto');

interface ISendTextMessageParams {
  /** 消息内容 */
  content: string;
  /** 被@人的用户userid */
  atUserIds?: string[];
  /** 是否@所有人 */
  isAtAll?: boolean;
}

interface ISendLinkMessageParams {
  /** 消息标题 */
  title: string;
  /** 消息内容 */
  text: string;
  /** 点击消息跳转的URL */
  messageUrl: string;
  /** 图片URL */
  picUrl?: string;
}

interface ISendMarkdownMessageParams {
  /** 消息标题 */
  title: string;
  /** 消息内容 */
  text: string;
  /** 被@人的用户userid */
  atUserIds?: string[];
  /** 是否@所有人 */
  isAtAll?: boolean;
}

interface ISendActionCardMessageParams {
  /** 消息标题 */
  title: string;
  /** 消息内容 */
  text: string;
  /** 单个按钮的标题 */
  singleTitle: string;
  /** 点击消息跳转的URL */
  singleURL: string;
  /** 被@人的用户userid */
  atUserIds?: string[];
  /** 是否@所有人 */
  isAtAll?: boolean;
  btns?: Array<{ title: string; actionURL: string; }>
}

@Provide()
export default class DingtalkService {
  @Inject()
  ctx!: Context;

  @Inject()
  crmHSF!: CrmHSF;

  /**
   * 单聊发送通知
   * @param userList 工号list，数组或单项
   * @param title 标题
   * @param content 内容
   * @returns
   */
  async notice(userList: string | (string | undefined)[], title: string, content: string) {
    const users = Array.isArray(userList) ? userList.filter(item => !!item).join(',') : userList;
    if (!users) return false;
    return this.crmHSF.sendDingTalkMessage(users, `[一体化研发平台]${title}`, content)
  }

  async sendTextMsg(params: ISendTextMessageParams, project: IProject): Promise<any> {
    await this.sendMessage({
      msgtype: 'text',
      at: {
        atUserIds: params.atUserIds,
        isAtAll: params.isAtAll,
      },
      text: {
        content: params.content
      }
    }, project);
  }

  async sendLinkMessage(params: ISendLinkMessageParams, project: IProject): Promise<any> {
    await this.sendMessage({
      msgtype: 'link',
      link: {
        text: params.text,
        title: params.title,
        picUrl: params.picUrl,
        messageUrl: params.messageUrl,
      }
    }, project);
  }

  async sendMarkdownMessage(params: ISendMarkdownMessageParams, project: IProject): Promise<any> {
    await this.sendMessage({
      msgtype: 'markdown',
      at: {
        atUserIds: params.atUserIds,
        isAtAll: params.isAtAll,
      },
      markdown: {
        text: params.text,
        title: params.title,
      }
    }, project);
  }

  async sendActionCardMessage(params: ISendActionCardMessageParams, project: IProject): Promise<any> {
    await this.sendMessage({
      msgtype: 'actionCard',
      at: {
        atUserIds: params.atUserIds,
        isAtAll: params.isAtAll,
      },
      actionCard: {
        text: params.text,
        title: params.title,
        btns: params.btns,
        singleTitle: params.singleTitle,
        singleURL: `dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(params.singleURL)}&pc_slide=false`,
      }
    }, project);
  }

  async sendMessage(params: any, project: IProject) {
    if (!project.dingtalkRobots || project.dingtalkRobots.length === 0) {
      this.ctx.logger.warn(`没有找到${project.cnName}对应的钉钉机器人`);
      return;
    }

    return Promise.all(project.dingtalkRobots.map(({ signCode, robotHook }) => {
      const timestamp = Date.now();

      // 使用 HmacSHA256 算法计算签名
      const hmac = crypto.createHmac('sha256', signCode);
      const up = hmac.update(`${timestamp}\n${signCode}`)
      // 输出 base64 格式摘要内容
      const secret = up.digest('base64');

      return new Promise((resolve, reject) => {
        httpClient.request(`${robotHook}&timestamp=${timestamp}&sign=${encodeURIComponent(secret)}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          data: params
        }, (err, data) => {
          if (err) {
            reject(err)
          } else {
            resolve(data)
          }
        })
      });
    }))
  }
}
