/**
 * 代码包相关服务，包括：
 * 1. 包结构分析
 * 2. npm依赖分析
 */
import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import * as path from 'path';
import * as fse from 'fs-extra';
import ProjectService from '@/apis/service/project';
import { IShrinkwrapJson } from '@/apis/interface/helper';
import { downloadFile } from '@/apis/utils/network';
import { unCompress, formatFileSize, calcFileSize } from '@/apis/utils/file';
import { useTmpDir, useGit } from '@/apis/middleware/hooks';
import { catchError } from '@/apis/middleware';
import { uploadToBasement } from '@/apis/utils/file';
import { formatSubpackageSize } from '@/apis/utils/size';
import { getNoticePkgMap, getDiffInfo, handleShrinkwrap } from '@/apis/utils/flat-depencies';
import { getDependenciesDiffTree } from '@/apis/utils/depencies-tree';

@Provide()
export default class PackageService {
  @Inject()
  ctx!: Context;

  @Inject()
  projectService!: ProjectService;

  /**
   * 下载 reportAnalyzed.json，获取分包大小
   * @param reportAnalyzed reportAnalyzed.json地址
   * @param tmpDirPath 临时目录路径
   */
  @catchError
  async getReportAnalyzedFile(reportAnalyzed: string, tmpDirPath: string, branchName: string) {
    if (!reportAnalyzed) throw Error(`report_analyzed.json地址为空，无法下载`)

    // 下载文件
    const { filePath } = await downloadFile(reportAnalyzed, tmpDirPath);

    // 构建产物体积（不含插件）
    const analyzedJSON = await fse.readJSON(filePath);

    let buildSize = 0;
    let buildHumanSize = '';
    const subPackage: any[] = [];
    if (Array.isArray(analyzedJSON)) {
      analyzedJSON.forEach(item => {
        const { name, size } = item;
        // 总包
        if (name === '__FULL__') {
          buildSize = size;
          buildHumanSize = formatFileSize(size);
          return;
        }
        // 主包
        const mainName = name === '__APP__' ? 'main' : '';
        const [_, _sub, subPackageName] = name.split('/');
        subPackage.push({
          subPackageName: mainName || subPackageName,
          buildSize: size,
          buildHumanSize: formatFileSize(size),
        });
      });
    } else {
      buildSize = analyzedJSON.originalSize;
      buildHumanSize = formatFileSize(buildSize);
      analyzedJSON.packages.forEach(item => {
        const { packageName, originalSize } = item;
        const temp = packageName.split('/');
        const subPackageName = temp[temp.length - 1];
        subPackage.push({
          subPackageName,
          buildSize: originalSize,
          buildHumanSize: formatFileSize(originalSize),
        });
      });
    }

    return {
      branchName,
      buildSize,
      buildHumanSize,
      subPackage,
    }
  }

  /**
   * 计算包大小 - def上传产物
   * @param dist 构建产物地址
   * @param tmpDirPath 临时目录路径
   */
  @catchError
  async getSouceSize(dist: string, tmpDirPath: string) {
    const { filePath } = await downloadFile(dist, tmpDirPath);
    // 解压 zip
    const { unzipFilePath } = await unCompress(filePath);
    // 计算包体积
    const unzipFileNames = await fse.readdir(unzipFilePath);
    let buildPath;
    if (unzipFileNames.indexOf('build_wechat') > -1) {
      buildPath = path.join(unzipFilePath, 'build_wechat');
    }
    else if (unzipFileNames.indexOf('build_miniapp') > -1) {
      buildPath = path.join(unzipFilePath, 'build_miniapp');
    }
 
     // 源码体积（小程序源码）
    const sourceSize = await calcFileSize(buildPath);
    return {
      sourceSize,
      sourceHumanSize: formatFileSize(sourceSize),
    }
  }

   /**
   * 计算包大小 - def上传产物
   * @param dist 构建产物地址
   * @param tmpDirPath 临时目录路径
   */
  @catchError
  async getUploadPackageSize(defUploadPkgRes: string, branchName?: string, dist?: string, tmpDirPath?: string) {
    const souceSizeInfo = {};
    if (dist && tmpDirPath) {
      const souceSizeRes = await this.getSouceSize(dist, tmpDirPath);
      Object.assign(souceSizeInfo, souceSizeRes);
    }
    const defUploadPkgSize = JSON.parse(defUploadPkgRes || '');
    const subPackage: any = [];
    let totalInfo = {};

    const formatedSubs = formatSubpackageSize(defUploadPkgSize.uploadResult.subPackageInfo);

    formatedSubs.forEach(pkg => {
      if (pkg.subPackageName === 'total') {
        Object.assign(totalInfo, pkg);
        return;
      }
      subPackage.push(pkg);
    });

    return {
      ...souceSizeInfo,
      ...totalInfo,
      branchName,
      subPackage,
    }
  }

  /** 获取最新的王守义打码返回的体积信息 */
  async getWsyPackageSize(analyzedJSONPath: string, uploadReportAnalyzed: boolean, branchName: string) {
    let reportAnalyzed;

    // 上传 report_analyzed 文件到cdn
    if (uploadReportAnalyzed) {
      const reportAnalyzedFile = await fse.readFile(analyzedJSONPath, { encoding: 'utf-8' });
      // @ts-ignore
      const reportFileName = `${branchName || 'UNKNOWN'}_${Date.parse(new Date())}_report-analyzed.json`;
      const { url } = await uploadToBasement(reportFileName, reportAnalyzedFile);
      reportAnalyzed = url;
    }

    const analyzedJSON = await fse.readJSON(analyzedJSONPath);
    let totalInfo = {};
    const subPackage: any[] = [];
    const formatedSubs = formatSubpackageSize(analyzedJSON);

    formatedSubs.forEach(pkg => {
      if (pkg.subPackageName === 'total') {
        Object.assign(totalInfo, pkg);
        return;
      }
      subPackage.push(pkg);
    });

    return  {
      ...totalInfo,
      subPackage,
      reportAnalyzed,
    };
  }


  /**
   * 计算包大小
   * @param dist 构建产物地址
   * @param tmpDirPath 临时目录路径
   */
  @catchError
  async getPackageSize(dist: string, tmpDirPath: string, uploadReportAnalyzed = false, branchName = 'UNKNOWN') {
    // 下载文件
    const { filePath } = await downloadFile(dist, tmpDirPath);
    
    // 解压 zip
    const { unzipFilePath } = await unCompress(filePath);

    // 计算包体积
    const unzipFileNames = await fse.readdir(unzipFilePath);

    // 最新的王守义打码
    if (unzipFileNames.indexOf('wxSizeInfo.json') > -1) {
      const analyzedPath = path.join(unzipFilePath, 'wxSizeInfo.json');
      return this.getWsyPackageSize(analyzedPath, uploadReportAnalyzed, branchName);
    }

    let buildPath;
    let analyzedJSONPath;
    let reportAnalyzed;
    if (unzipFileNames.indexOf('build_wechat') > -1) {
      buildPath = path.join(unzipFilePath, 'build_wechat')
      analyzedJSONPath = path.join(unzipFilePath, 'run/report_analyzed.json');
    }
    else if (unzipFileNames.indexOf('build_miniapp') > -1) {
      buildPath = path.join(unzipFilePath, 'build_miniapp')
    }

    if (!analyzedJSONPath || !fse.existsSync(analyzedJSONPath)) throw Error(`构建产物 ${dist} 缺少 report_analyzed.json`)

    // 上传 report_analyzed 文件到cdn
    if (uploadReportAnalyzed) {
      const reportAnalyzedFile = await fse.readFile(analyzedJSONPath, { encoding: 'utf-8' });
      // @ts-ignore
      const reportFileName = `${branchName || 'UNKNOWN'}_${Date.parse(new Date())}_report-analyzed.json`;
      const { url } = await uploadToBasement(reportFileName, reportAnalyzedFile);
      reportAnalyzed = url;
    }

    // 源码体积（小程序源码）
    const sourceSize = await calcFileSize(buildPath);
    // 构建产物体积（不含插件）
    const analyzedJSON = await fse.readJSON(analyzedJSONPath) as {
      originalSize: number; // 原本体积，也就是构建产物体积
      optimizableSize: number; // 预期可优化体积
      optimizedSize: number; // 预期优化后的体积
      packages: {
        packageName: string;
        type: 'main' | 'subPackage';
        originalSize: number; // // 原本体积，也就是构建产物体积
        optimizableSize: number; // 预期可优化体积
        optimizedSize: number; // 预期优化后的体积
      }[]
    }

    return {
      reportAnalyzed,
      sourceSize,
      subPackageName: 'total',
      sourceHumanSize: formatFileSize(sourceSize),
      buildSize: analyzedJSON.originalSize,
      buildHumanSize: formatFileSize(analyzedJSON.originalSize),
      subPackage: analyzedJSON.packages.map(({ packageName, type, originalSize }) => {
        let subPackageName;
        if (packageName) {
          const temp = packageName.split('/');
          subPackageName = temp[temp.length - 1];
        } else if (type === 'main') {
          subPackageName = 'main';
        }

        return {
          subPackageName,
          buildSize: originalSize,
          buildHumanSize: formatFileSize(originalSize),
        }
      })
    }
  }

  async getMiniWorkConfig(tmpDirPath: string, branchUrl: string, branchName: string) {
    try {
      const { projectTmpDirPath } = await useGit(tmpDirPath, { branchUrl, branchName, cloneSingleBranch: true});
      return fse.readJSONSync(path.join(projectTmpDirPath, './miniwork.config.json')) || {};
    } catch (e) {
      return {};
    }
  }

  /**
   * 分析依赖
   * @param shrinkwrapUrl shrinkwrap.json 地址
   * @param branchUrl 分支链接
   * @param branchName 分支名
   */
  async analyzeDependencies(shrinkwrapUrl: string, branchUrl: string, branchName: string) {
    const { tmpDirPath, removeTmpDir } = useTmpDir();

    const getShrinkwrapJSON = async () => {
      // 1. 下载依赖
      const { filePath } = await downloadFile(shrinkwrapUrl, tmpDirPath);
      return await fse.readJSON(filePath);
    }

    // 获取小程序研发平台配置，包含依赖白名单，只有白名单的才计数
    const [shrinkwrapJSON, miniWorkConfig] = await Promise.all([getShrinkwrapJSON(), this.getMiniWorkConfig(tmpDirPath, branchUrl, branchName)]);
    const { multiVersionInclude = [], enableNpmInclude = [] } = miniWorkConfig || {};
    // 获取依赖树
    const { diffTree, multiVersionTree, ...other } = getDependenciesDiffTree(shrinkwrapJSON);
    const realMultiVersionTree = multiVersionTree.filter((version) => version.isReal);
    const realMultiVersionPackageCount = realMultiVersionTree.length;
    const realMultiVersionCount = realMultiVersionTree.reduce((prev, current) => prev + (current && current.children && current.children.length || 0), 0);


    // 3. 删除临时文件夹
    removeTmpDir()

    return {
      ...other,
      tree: diffTree,
      multiVersionTree,
      realMultiVersionCount,
      realMultiVersionPackageCount,
      disabledNpmList: [],
      multiVersionInclude,
      enableNpmInclude,
    }
  }

  /**
   * diff 依赖
   * @param shrinkwrapUrl shrinkwrap.json 地址
   * @param ctrlShrinkwrapUrl 对比的 shrinkwrap.json 地址
   */
  async diffDependencies(shrinkwrapUrl: string, ctrlShrinkwrapUrl: string, branchUrl: string, branchName: string) {
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    console.log(2222)
    // 1. 下载依赖
    const [shrinkwrapJSON, ctrlShrinkwrapJSON, miniWorkConfig] = await Promise.all([
      downloadFile(shrinkwrapUrl, tmpDirPath).then(({ filePath }) => fse.readJSON(filePath)),
      downloadFile(ctrlShrinkwrapUrl, tmpDirPath).then(({ filePath }) => fse.readJSON(filePath)),
      this.getMiniWorkConfig(tmpDirPath, branchUrl, branchName)
    ]);
    const { noticeConfig } = miniWorkConfig || {};
    // 获取依赖树
    console.log(444444);
    const diffInfo = getDependenciesDiffTree(shrinkwrapJSON, ctrlShrinkwrapJSON);
    console.log(5555);
    // 获取扁平化的依赖
    const diffFlatInfo = this.diffFlatDependencies(shrinkwrapJSON, ctrlShrinkwrapJSON, noticeConfig);
    // 4. 删除临时文件夹
    removeTmpDir()

    return {
      ...diffInfo,
      diffFlatInfo,
    }
  }

  /** 
   * 获取扁平化的依赖
   * */
  diffFlatDependencies(shrinkwrapJson: IShrinkwrapJson, preShrinkwrapJson: IShrinkwrapJson, noticeConfig?: Record<string, Record<string, string>>) {
    const flatDeps: Record<string, any> = {};
    const noticeInfo: Record<string, Record<string, any>> = {};
    // 通知人信息
    const noticePkgMap = getNoticePkgMap(noticeConfig);

    // 收集当前版本依赖
    handleShrinkwrap(flatDeps, {
      key: 'version',
      depChainStr: ''
    }, shrinkwrapJson);

    // 收集前一个版本的依赖
    handleShrinkwrap(flatDeps, {
      key: 'prevVersion',
      depChainStr: '',
      diffKey: 'version',
    }, preShrinkwrapJson);

    const diffInfo = getDiffInfo(flatDeps, noticeInfo, noticePkgMap);

    return {
      ...diffInfo,
      noticeInfo,
    };
  }
}