/**
 * 代码包相关服务，包括：
 * 1. 包结构分析
 * 2. npm依赖分析
 */
import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import * as path from 'path';
import * as fse from 'fs-extra';
import ProjectService from '@/apis/service/project';
import { IShrinkwrap } from '@/apis/interface/helper';
import { downloadFile } from '@/apis/utils/network';
import { unCompress, formatFileSize, calcFileSize } from '@/apis/utils/file';
import { useTmpDir, useGit } from '@/apis/middleware/hooks';
import { catchError } from '@/apis/middleware';
import { uploadToBasement } from '@/apis/utils/file';
import { formatSubpackageSize } from '@/apis/utils/size';
import { getNoticePkgMap, collectDeps, getDiffInfo } from '@/apis/utils/flat-depencies';

@Provide()
export default class PackageService {
  @Inject()
  ctx!: Context;

  @Inject()
  projectService!: ProjectService;

  /**
   * 下载 reportAnalyzed.json，获取分包大小
   * @param reportAnalyzed reportAnalyzed.json地址
   * @param tmpDirPath 临时目录路径
   */
  @catchError
  async getReportAnalyzedFile(reportAnalyzed: string, tmpDirPath: string, branchName: string) {
    if (!reportAnalyzed) throw Error(`report_analyzed.json地址为空，无法下载`)

    // 下载文件
    const { filePath } = await downloadFile(reportAnalyzed, tmpDirPath);

    // 构建产物体积（不含插件）
    const analyzedJSON = await fse.readJSON(filePath);

    let buildSize = 0;
    let buildHumanSize = '';
    const subPackage: any[] = [];
    if (Array.isArray(analyzedJSON)) {
      analyzedJSON.forEach(item => {
        const { name, size } = item;
        // 总包
        if (name === '__FULL__') {
          buildSize = size;
          buildHumanSize = formatFileSize(size);
          return;
        }
        // 主包
        const mainName = name === '__APP__' ? 'main' : '';
        const [_, _sub, subPackageName] = name.split('/');
        subPackage.push({
          subPackageName: mainName || subPackageName,
          buildSize: size,
          buildHumanSize: formatFileSize(size),
        });
      });
    } else {
      buildSize = analyzedJSON.originalSize;
      buildHumanSize = formatFileSize(buildSize);
      analyzedJSON.packages.forEach(item => {
        const { packageName, originalSize } = item;
        const temp = packageName.split('/');
        const subPackageName = temp[temp.length - 1];
        subPackage.push({
          subPackageName,
          buildSize: originalSize,
          buildHumanSize: formatFileSize(originalSize),
        });
      });
    }

    return {
      branchName,
      buildSize,
      buildHumanSize,
      subPackage,
    }
  }

  /**
   * 计算包大小 - def上传产物
   * @param dist 构建产物地址
   * @param tmpDirPath 临时目录路径
   */
  @catchError
  async getSouceSize(dist: string, tmpDirPath: string) {
    const { filePath } = await downloadFile(dist, tmpDirPath);
    // 解压 zip
    const { unzipFilePath } = await unCompress(filePath);
    // 计算包体积
    const unzipFileNames = await fse.readdir(unzipFilePath);
    let buildPath;
    if (unzipFileNames.indexOf('build_wechat') > -1) {
      buildPath = path.join(unzipFilePath, 'build_wechat');
    }
    else if (unzipFileNames.indexOf('build_miniapp') > -1) {
      buildPath = path.join(unzipFilePath, 'build_miniapp');
    }
 
     // 源码体积（小程序源码）
    const sourceSize = await calcFileSize(buildPath);
    return {
      sourceSize,
      sourceHumanSize: formatFileSize(sourceSize),
    }
  }

   /**
   * 计算包大小 - def上传产物
   * @param dist 构建产物地址
   * @param tmpDirPath 临时目录路径
   */
  @catchError
  async getUploadPackageSize(defUploadPkgRes: string, branchName?: string, dist?: string, tmpDirPath?: string) {
    const souceSizeInfo = {};
    if (dist && tmpDirPath) {
      const souceSizeRes = await this.getSouceSize(dist, tmpDirPath);
      Object.assign(souceSizeInfo, souceSizeRes);
    }
    const defUploadPkgSize = JSON.parse(defUploadPkgRes || '');
    const subPackage: any = [];
    let totalInfo = {};

    const formatedSubs = formatSubpackageSize(defUploadPkgSize.uploadResult.subPackageInfo);

    formatedSubs.forEach(pkg => {
      if (pkg.subPackageName === 'total') {
        Object.assign(totalInfo, pkg);
        return;
      }
      subPackage.push(pkg);
    });

    return {
      ...souceSizeInfo,
      ...totalInfo,
      branchName,
      subPackage,
    }
  }

  /** 获取最新的王守义打码返回的体积信息 */
  async getWsyPackageSize(analyzedJSONPath: string, uploadReportAnalyzed: boolean, branchName: string) {
    let reportAnalyzed;

    // 上传 report_analyzed 文件到cdn
    if (uploadReportAnalyzed) {
      const reportAnalyzedFile = await fse.readFile(analyzedJSONPath, { encoding: 'utf-8' });
      // @ts-ignore
      const reportFileName = `${branchName || 'UNKNOWN'}_${Date.parse(new Date())}_report-analyzed.json`;
      const { url } = await uploadToBasement(reportFileName, reportAnalyzedFile);
      reportAnalyzed = url;
    }

    const analyzedJSON = await fse.readJSON(analyzedJSONPath);
    let totalInfo = {};
    const subPackage: any[] = [];
    const formatedSubs = formatSubpackageSize(analyzedJSON);

    formatedSubs.forEach(pkg => {
      if (pkg.subPackageName === 'total') {
        Object.assign(totalInfo, pkg);
        return;
      }
      subPackage.push(pkg);
    });

    return  {
      ...totalInfo,
      subPackage,
      reportAnalyzed,
    };
  }


  /**
   * 计算包大小
   * @param dist 构建产物地址
   * @param tmpDirPath 临时目录路径
   */
  @catchError
  async getPackageSize(dist: string, tmpDirPath: string, uploadReportAnalyzed = false, branchName = 'UNKNOWN') {
    // 下载文件
    const { filePath } = await downloadFile(dist, tmpDirPath);
    
    // 解压 zip
    const { unzipFilePath } = await unCompress(filePath);

    // 计算包体积
    const unzipFileNames = await fse.readdir(unzipFilePath);

    // 最新的王守义打码
    if (unzipFileNames.indexOf('wxSizeInfo.json') > -1) {
      const analyzedPath = path.join(unzipFilePath, 'wxSizeInfo.json');
      return this.getWsyPackageSize(analyzedPath, uploadReportAnalyzed, branchName);
    }

    let buildPath;
    let analyzedJSONPath;
    let reportAnalyzed;
    if (unzipFileNames.indexOf('build_wechat') > -1) {
      buildPath = path.join(unzipFilePath, 'build_wechat')
      analyzedJSONPath = path.join(unzipFilePath, 'run/report_analyzed.json');
    }
    else if (unzipFileNames.indexOf('build_miniapp') > -1) {
      buildPath = path.join(unzipFilePath, 'build_miniapp')
    }

    if (!analyzedJSONPath || !fse.existsSync(analyzedJSONPath)) throw Error(`构建产物 ${dist} 缺少 report_analyzed.json`)

    // 上传 report_analyzed 文件到cdn
    if (uploadReportAnalyzed) {
      const reportAnalyzedFile = await fse.readFile(analyzedJSONPath, { encoding: 'utf-8' });
      // @ts-ignore
      const reportFileName = `${branchName || 'UNKNOWN'}_${Date.parse(new Date())}_report-analyzed.json`;
      const { url } = await uploadToBasement(reportFileName, reportAnalyzedFile);
      reportAnalyzed = url;
    }

    // 源码体积（小程序源码）
    const sourceSize = await calcFileSize(buildPath);
    // 构建产物体积（不含插件）
    const analyzedJSON = await fse.readJSON(analyzedJSONPath) as {
      originalSize: number; // 原本体积，也就是构建产物体积
      optimizableSize: number; // 预期可优化体积
      optimizedSize: number; // 预期优化后的体积
      packages: {
        packageName: string;
        type: 'main' | 'subPackage';
        originalSize: number; // // 原本体积，也就是构建产物体积
        optimizableSize: number; // 预期可优化体积
        optimizedSize: number; // 预期优化后的体积
      }[]
    }

    return {
      reportAnalyzed,
      sourceSize,
      subPackageName: 'total',
      sourceHumanSize: formatFileSize(sourceSize),
      buildSize: analyzedJSON.originalSize,
      buildHumanSize: formatFileSize(analyzedJSON.originalSize),
      subPackage: analyzedJSON.packages.map(({ packageName, type, originalSize }) => {
        let subPackageName;
        if (packageName) {
          const temp = packageName.split('/');
          subPackageName = temp[temp.length - 1];
        } else if (type === 'main') {
          subPackageName = 'main';
        }

        return {
          subPackageName,
          buildSize: originalSize,
          buildHumanSize: formatFileSize(originalSize),
        }
      })
    }
  }

  async getMiniWorkConfig(tmpDirPath: string, branchUrl: string, branchName: string) {
    try {
      const { projectTmpDirPath } = await useGit(tmpDirPath, { branchUrl, branchName, cloneSingleBranch: true});
      return fse.readJSONSync(path.join(projectTmpDirPath, './miniwork.config.json')) || {};
    } catch (e) {
      return {};
    }
  }

  /**
   * 分析依赖
   * @param shrinkwrapUrl shrinkwrap.json 地址
   * @param branchUrl 分支链接
   * @param branchName 分支名
   */
  async analyzeDependencies(shrinkwrapUrl: string, branchUrl: string, branchName: string) {
    const { tmpDirPath, removeTmpDir } = useTmpDir();

    const getShrinkwrapJSON = async () => {
      // 1. 下载依赖
      const { filePath } = await downloadFile(shrinkwrapUrl, tmpDirPath);
      return await fse.readJSON(filePath);
    }

    // 获取小程序研发平台配置，包含依赖白名单，只有白名单的才计数
    const [shrinkwrapJSON, miniWorkConfig] = await Promise.all([getShrinkwrapJSON(), this.getMiniWorkConfig(tmpDirPath, branchUrl, branchName)]);
    const { multiVersionInclude = [], enableNpmInclude = [] } = miniWorkConfig || {};

    // 2. 分析依赖
    let totalCount = 0;
    let realCount = 0;
    // 标记不受支持的npm依赖
    const disabledNpmList: string[] = [];

    const parseDependenciesRecursively = (dependencies: IShrinkwrap['dependencies'] | undefined) => {
      // 1. dependencies 为空，则返回空
      if (!dependencies) return [];

      // 2. 分析依赖
      const dependenciesList = Object.entries(dependencies).map(([name, item]) => {
        // 如果 npm 包以 “_” 开头，或者没有 version，或者 version 不是合法地址，则直接返回
        if (name.charAt(0) === '_' || item.optional || !item.version) {
          return;
        }

        // 如果 version 是下载地址，则直接返回
        if (/^http/.test(item.version)) {
          realCount++;
          return;
        }

        // 判断依赖是否可以使用
        const isEnabled = enableNpmInclude.findIndex(include => name.match(new RegExp(include, 'ig'))) >= 0;

        if (!isEnabled) {
          disabledNpmList.push(name);
        }

        // 深度遍历
        const childrenRes = parseDependenciesRecursively(item.dependencies);

        return {
          key: `${++totalCount}-${name}`,
          title: `${name}@${item.version}`,
          isLeaf: childrenRes.length === 0,
          isEnabled,
          name,
          version: item.version,
          children: childrenRes
        }
      })

      // 3. 过滤
      return dependenciesList.filter(item => !!item)
    }
    const tree = parseDependenciesRecursively(shrinkwrapJSON.dependencies);

    // 记录所有依赖
    const dependenciesMap = this.getMultiVersionDependencies(shrinkwrapJSON.dependencies);

    // 生成最终的多版本依赖树
    let multiVersionTree: any[] = [];
    Object.entries(dependenciesMap).forEach(([dependName, dependVersions]) => {
      const dependTree = {
        isLeaf:false, 
        name: `${dependName}-${dependVersions.length}`, 
        title: dependName, 
        key: dependName,
        isReal: multiVersionInclude.findIndex(include => dependName.match(new RegExp(include, 'ig'))) >= 0,
        children: dependVersions.map((depend) => ({
          isLeaf: false,
          name: depend.version,
          title: depend.version,
          key: `${dependName}-${depend.version}`,
          isVersion: true,
          children: depend.chain.map((chain, index) => ({
            isLeaf: true,
            name: chain,
            title: chain, 
            key: `${dependName}-${depend.version}-${index}`,
            isChain: true,
          })),
        })),
      };
      multiVersionTree.push(dependTree);
    });

    const realMultiVersionTree = multiVersionTree.filter((version) => version.isReal);

    const multiVersionPackageCount = multiVersionTree.length;
    const realMultiVersionPackageCount = realMultiVersionTree.length;

    const multiVersionCount = multiVersionTree.reduce((prev, current) => prev + (current && current.children && current.children.length || 0), 0);
    const realMultiVersionCount = realMultiVersionTree.reduce((prev, current) => prev + (current && current.children && current.children.length || 0), 0);

    // 对多版本依赖进行排序
    multiVersionTree = multiVersionTree.sort((a, b) => {
      if(a.isReal && !b.isReal) {
        return -1;
      } else if (!a.isReal && b.isReal) {
        return 1;
      } else {
        return b.children.length - a.children.length;
      }
    });

    // 3. 删除临时文件夹
    removeTmpDir()

    return {
      tree,
      multiVersionTree,
      totalCount,
      realCount,
      multiVersionCount,
      multiVersionPackageCount,
      realMultiVersionCount,
      realMultiVersionPackageCount,
      disabledNpmList,
    }
  }

  /**
   * diff 依赖
   * @param shrinkwrapUrl shrinkwrap.json 地址
   * @param ctrlShrinkwrapUrl 对比的 shrinkwrap.json 地址
   */
  async diffDependencies(shrinkwrapUrl: string, ctrlShrinkwrapUrl: string, branchUrl: string, branchName: string) {
    const { tmpDirPath, removeTmpDir } = useTmpDir();

    // 1. 下载依赖
    const [shrinkwrapJSON, ctrlShrinkwrapJSON, miniWorkConfig] = await Promise.all([
      downloadFile(shrinkwrapUrl, tmpDirPath).then(({ filePath }) => fse.readJSON(filePath)),
      downloadFile(ctrlShrinkwrapUrl, tmpDirPath).then(({ filePath }) => fse.readJSON(filePath)),
      this.getMiniWorkConfig(tmpDirPath, branchUrl, branchName)
    ]);
    const { noticeConfig } = miniWorkConfig || {};

    // 2. diff 依赖
    let addCount = 0;
    let deleteCount = 0;
    let modifyCount = 0;
    let key = 0;
    const diffDependenciesRecursively = (dependencies: IShrinkwrap['dependencies'] | undefined, ctrlDependencies: IShrinkwrap['dependencies'] | undefined) => {
      // 1. dependencies 为空，则 diff 结果也为空
      if (!dependencies) return [];

      // 2. 先 diff 出“新增”、“修改”
      const dependenciesList = Object.entries(dependencies).map(([name, item]) => {
        // 如果 npm 包以 “_” 开头，或者没有 version，或者 version 不是合法地址，则直接返回
        if (name.charAt(0) === '_' || item.optional || !item.version || /^http/.test(item.version)) {
          return;
        }

        // 删除对照组同名依赖，以便执行第3步
        const ctrlItem = ctrlDependencies && ctrlDependencies[name];
        if (ctrlDependencies) delete ctrlDependencies[name];

        // 深度遍历
        const childrenDiffRes = diffDependenciesRecursively(item.dependencies, ctrlItem?.dependencies);

        // 与参照组进行 diff
        let diffType;
        let diffVersion;
        if (ctrlItem) {
          if (ctrlItem.version !== item.version) { // 如果版本号不同
            diffType = 'modify';
            modifyCount++;
            diffVersion = ctrlItem.version;
          } else if (childrenDiffRes.length === 0) { // 如果版本号相同，切子节点无 diff，则返回空
            return;
          }
        } else { // 参照组无同名依赖，则认为是新增的
          diffType = 'add';
          addCount++;
        }

        return {
          key: `${++key}-${name}`,
          title: `${name}@${item.version}`,
          isLeaf: childrenDiffRes.length === 0,
          name,
          version: item.version,
          diffType,
          diffVersion,
          children: childrenDiffRes
        }
      });

      // 3. 再 diff 出“删除”
      if (!ctrlDependencies) return dependenciesList.filter(item => !!item);
      const ctrlDependenciesList = Object.entries(ctrlDependencies).map(([name, ctrlItem]) => {
        // 如果 npm 包以 “_” 开头，或者没有 version，或者 version 不是合法地址，则直接返回
        if (name.charAt(0) === '_' || ctrlItem.optional || !ctrlItem.version || /^http/.test(ctrlItem.version)) {
          return;
        }

        deleteCount++;
        return {
          key: `${++key}-${name}`,
          title: `${name}@${ctrlItem.version}`,
          isLeaf: true,
          name: name,
          version: ctrlItem.version,
          diffType: 'delete',
        }
      })

      return dependenciesList.concat(ctrlDependenciesList).filter(item => !!item);
    }

    const duplicatedVersions = this.getMultiVersionDependencies(shrinkwrapJSON.dependencies);
    const ctrlDuplicatedVersions = this.getMultiVersionDependencies(ctrlShrinkwrapJSON.dependencies);
    const diffFlatInfo = this.diffFlatDependencies(shrinkwrapJSON.dependencies, ctrlShrinkwrapJSON.dependencies, noticeConfig);

    let multiVersionTree:any[] = [];

    Object.entries(duplicatedVersions).forEach(([dependName, dependVersions]) => {
      // 对比的依赖
      const ctrlVersions = ctrlDuplicatedVersions[dependName];
      // 说明是新的重复依赖
      if (!ctrlVersions) {
        multiVersionTree.push({
          isLeaf: false,
          name: dependName,
          title: dependName,
          key: dependName,
          diffType: 'add',
          children: dependVersions.map((depend) => ({
            isLeaf: false,
            name: depend.version,
            title: depend.version,
            key: `${dependName}-${depend.version}`,
            isVersion: true,
            children: depend.chain.map((chain, index) => ({
              isLeaf: true,
              name: chain,
              title: chain,
              key: `${dependName}-${depend.version}-${index}`,
              isChain: true,
            })),
          }))
        });
      } else {
        const dependTree: any = {
          isLeaf: false,
          name: dependName,
          title: dependName,
          key: dependName,
          children: []
        };
        dependVersions.forEach((version) => {
          const ctrlVersionIndex = ctrlVersions.findIndex(v => version.version === v.version);
          // 如果没有找到新的版本，则认为是新增的依赖版本
          if (ctrlVersionIndex < 0) {
            dependTree.diffType = 'modify';
            dependTree.children.push({
              isLeaf: false,
              name: version.version,
              title: version.version,
              diffType: 'add',
              key: `${dependName}-${version.version}`,
              isVersion: true,
              children: version.chain.map((chain, index) => ({
                isLeaf: true,
                name: chain,
                title: chain,
                key: `${dependName}-${version.version}-${index}`,
                isChain: true,
              })),
            });
          } else {
            ctrlVersions.splice(ctrlVersionIndex, 1);
            // 说明是相同版本的依赖，则只展示。TODO: 判断依赖链的diff
            dependTree.children.push({
              isLeaf: false,
              name: version.version,
              title: version.version,
              key: `${dependName}-${version.version}`,
              isVersion: true,
              children: version.chain.map((chain, index) => ({
                isLeaf: true,
                name: chain,
                title: chain,
                key: `${dependName}-${version.version}-${index}`,
                isChain: true,
              })),
            });
          }
        });
        // 获取已经没有的版本
        if (ctrlVersions && ctrlVersions.length) {
          dependTree.diffType = 'modify';
          // 将被删除的依赖添加进入版本中
          dependTree.children.push(...ctrlVersions.map((version) => ({
            isLeaf: false,
            name: version.version,
            title: version.version,
            key: `${dependName}-${version.version}`,
            isVersion: true,
            diffType: 'delete',
            children: version.chain.map((chain, index) => ({
              isLeaf: true,
              name: chain,
              title: chain,
              key: `${dependName}-${version.version}-${index}`,
              isChain: true,
            }))
          })));
        }

        multiVersionTree.push(dependTree);
      }
    });

    const diffTree = diffDependenciesRecursively(shrinkwrapJSON.dependencies, ctrlShrinkwrapJSON.dependencies);

    const multiVersionPackageCount = multiVersionTree.length;
    const multiVersionCount = multiVersionTree.reduce((prev, current) => prev + (current && current.children && current.children.length || 0), 0);
    // 4. 删除临时文件夹
    removeTmpDir()

    // 对多版本依赖进行排序
    multiVersionTree = multiVersionTree.sort((a, b) => {
      if(a.isReal && !b.isReal) {
        return -1;
      } else if (!a.isReal && b.isReal) {
        return 1;
      } else {
        return b.children.length - a.children.length;
      }
    });

    return {
      diffTree,
      multiVersionTree,
      addCount,
      deleteCount,
      modifyCount,
      multiVersionCount,
      multiVersionPackageCount,
      diffFlatInfo,
    }
  }

  /**
   * 查找多版本依赖
   * @param dependencies 依赖树
   * @param parent 父依赖链
   * @param record 依赖记录
   * @returns 多版本依赖
   */
  getMultiVersionDependencies(dependencies: IShrinkwrap['dependencies']) {
    const record: Record<string, any> = {};

    const traverseDependencies = (depends: IShrinkwrap['dependencies'], parent: string | null, record: Record<string, any>) => {
      if (!depends) return record;
      Object.entries(depends).forEach(([name, item]) => {        // 如果 npm 包以 “_” 开头，或者没有 version，或者 version 不是合法地址，则直接返回
        if (name.charAt(0) === '_' || item.optional || !item.version || /^http/.test(item.version)) {
          return;
        }
        const title = `${name}@${item.version}`;
        // 生成当前依赖的依赖链
        const currentChain = parent ? `${parent} -> ${title}` : `${title}`;
        // 记录当前依赖的版本号和依赖链
        if (record[name]) {
          // 如果是相同版本依赖，则更新依赖链，否则插入新版本
          let sameVersion = false;
          record[name].forEach(depend => {
            // 如果该版本已存在，则判断依赖链
            if (depend.version === item.version) {
              sameVersion = true;
              const dependIndex = depend.chain.findIndex((c: string) => c.includes(currentChain) || currentChain.includes(c));
              if (dependIndex >= 0) {
                // 如果是子母依赖链，取最长的依赖链版本
                depend.chain[dependIndex] = depend.chain[dependIndex].length > currentChain.length ? depend.chain[dependIndex] : currentChain;
              } else {
                // 否则认为是全新依赖链
                depend.chain.push(currentChain);
              }
            }
          });
          // 如果不是相同版本，则认为是新增依赖
          if (!sameVersion) {
            record[name].push({ version: item.version, chain: [currentChain] });
          }
        } else {
          record[name] = [{ version: item.version, chain: [currentChain] }];
        }

        // 深度遍历子依赖
        traverseDependencies(item.dependencies, currentChain, record);
      });
      return record;
    };

    traverseDependencies(dependencies, null, record);

    // 如果只有1个版本，则删除记录
    Object.keys(record).forEach((dependName) => {
      const versions = record[dependName];
      if (versions.length <= 1) {
        delete record[dependName];
      }
    });
    return record;
  }

    /** 
     * 获取扁平化的依赖
     * */
    diffFlatDependencies(dependencies: IShrinkwrap, preDependencies: IShrinkwrap, noticeConfig?: Record<string, Record<string, string>>) {
      const flatDeps: Record<string, any> = {};
      const noticeInfo: Record<string, Record<string, any>> = {};
      // 通知人信息
      const noticePkgMap = getNoticePkgMap(noticeConfig);

      // 收集当前版本依赖
      collectDeps(flatDeps, {
        deps: dependencies,
        key: 'version',
        depChainStr: ''
      });
      // 收集前一个版本的依赖
      collectDeps(flatDeps, {
        deps: preDependencies,
        key: 'prevVersion',
        depChainStr: '',
        diffKey: 'version'
      });
  
      const diffInfo = getDiffInfo(flatDeps, noticeInfo, noticePkgMap);
  
      return {
        ...diffInfo,
        noticeInfo,
      };
    }
}