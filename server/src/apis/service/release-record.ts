import { Inject, Provide } from "@midwayjs/core";
import { Context } from "@midwayjs/faas";

import { formatDate } from "@/apis/utils";
import { IReleaseRecord } from "@/apis/interface/release-record";
import { IReleaseRecordModel } from "@/apis/model/release-record";

@Provide()
export default class ReleaseRecordService {
  @Inject()
  ctx!: Context;

  @Inject()
  ReleaseRecordModel!: IReleaseRecordModel;

  async update(values: IReleaseRecord): Promise<boolean> {
    const updateResult = await this.ReleaseRecordModel.update(values, {
      where: { id: values.id },
    });
    return updateResult[0] > 0;
  }

  async updateByIds(ids: Array<number>): Promise<boolean> {
    const updateResult = await this.ReleaseRecordModel.update(
      {
        status: 1,
        gmt_modified: formatDate(new Date()) as any,
      },
      {
        where: {
          id: ids,
        },
      }
    );
    return updateResult[0] > 0;
  }

  async getUnRelease(params: any): Promise<any> {
    const {env} = params;
    const res = await this.ReleaseRecordModel.findAll({
      where: { status: 0, diamond_env: env },
      raw: true,
    });
    return res;
  }

  async getLatestRelease(params: any): Promise<any> {
    const {env} = params;
    const res = await this.ReleaseRecordModel.findOne({
      where: {diamond_env: env},
      order: [["id", "DESC"]], // 按id降序排列
      raw: true,
    });
    return res;
  }

  async create(values: IReleaseRecord | any): Promise<any> {
    const curEnv = this.ctx.aliEnv === 'dev' ? 'daily' : this.ctx.aliEnv === 'pre' ? 'pre' : 'center'
    const service = await this.ReleaseRecordModel.create({...values, diamond_env: curEnv});
    return service;
  }

  async getAndUpdateRecord(): Promise<any> {
    try {
      // 一定要先获取diamond，后获取sql，这样子还能更新到新版本，否则会回退
      const curEnv = this.ctx.aliEnv === 'dev' ? 'daily' : this.ctx.aliEnv === 'pre' ? 'pre' : 'center';
      const envParams = { unit: curEnv };
      const diamondRes = await this.ctx.diamond.getConfig("route-map","render-html", envParams);

      console.log("getAndUpdateRecord-diamondRes", diamondRes);

      const diamondObj = JSON.parse(diamondRes);
      let newDiamond = { ...diamondObj };
      // 1. 获取当前登录用户的类型
      const response = await Promise.all([
        this.getUnRelease({env: curEnv}),
        this.getLatestRelease({env: curEnv}),
      ]);

      // 未完成条目,最新条目
      const [unReleaseRecord, latestRecord] = response;

      console.log("getAndUpdateRecord-unReleaseRecord", unReleaseRecord);
      console.log("getAndUpdateRecord-latestRecord", latestRecord);
      console.log("getAndUpdateRecord-if", unReleaseRecord.length, Date.now() - new Date(latestRecord.gmt_create).getTime());

      // 1、如果有未完成的条目，且最新一条的更新时间距离现在10s以上
      if ( unReleaseRecord.length > 0 && Date.now() - new Date(latestRecord.gmt_create).getTime() > 10000 ) {
        // 2、数据进行合并
        unReleaseRecord.forEach((element) => {
          newDiamond[element.diamond_key] = element.diamond_value;
        });

        console.log("getAndUpdateRecord-newDiamond", newDiamond);

        const unReleaseRecordIds = unReleaseRecord.map((element) => element.id);

        console.log("getAndUpdateRecord-unReleaseRecordIds", unReleaseRecordIds);

        // 3、更新diamond，然后更改这一波数据的状态
        // TODO:先测整体数据写入是否有问题，再解开diamond的获取

        // const updateRes = await this.ctx.diamond.publishSingle('route-map', 'render-html', JSON.stringify(newDiamond), envParams);
        const updateDB = await this.updateByIds(unReleaseRecordIds);

        // 2. 返回当前登录用户信息
        return {
          success: true,
          data: {
            // updateRes,
            updateDB,
          },
        };
      } else {
        return {
          success: false,
          data: {
            unReleaseRecord,
            latestRecord,
          },
          message:
            "无未完成的条目，或最新一条的更新时间距离现在不到10s，延后执行",
        };
      }
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err?.message,
      };
    }
  }
}
