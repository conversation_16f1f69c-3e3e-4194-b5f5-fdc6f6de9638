import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { convertsCollectionToCamel } from '@/apis/utils';
import { IShortLink } from '@/apis/interface/short-link';
import { IShortLinkModel } from '@/apis/model/short-link';

@Provide()
export default class ShortLinkService {
  @Inject()
  ctx!: Context;

  @Inject()
  ShortLinkModel!: IShortLinkModel;

  async create(values: IShortLink): Promise<IShortLink> {
    const data = await this.ShortLinkModel.create(values as any, {
      fields: ['long_link', 'short_key']
    });
    const res = data.toJSON();

    return convertsCollectionToCamel(res);
  }
}