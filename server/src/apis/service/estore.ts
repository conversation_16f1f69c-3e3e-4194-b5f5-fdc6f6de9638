/**
 * 王守义 Open API
 */
import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import axios from 'axios';
import { formatDate } from '@/apis/utils';
import { IBuildTask } from '@/apis/interface/build';
import ProjectService from '@/apis/service/project';
import { EBuildPlatform } from '../const/build';

// const ESTORE_DOMAIN = isPre ? 'pre-wsy.alibaba-inc.com' : 'wsy.alibaba-inc.com';
const ESTORE_DOMAIN = 'wsy.alibaba-inc.com';

@Provide()
export default class EStoreService {
  @Inject()
  ctx!: Context;

  @Inject()
  projectService!: ProjectService;

  /** 批量获取构建任务 */
  async getTasks(projectName: string, estoreParams: IEstoreParams): Promise<{ list: IBuildTask[]; total: number; }> {
    // 查询归属项目信息
    const project = await this.projectService.get(projectName, { needFullWsyConfig: true });
    if (!project) throw Error(`查询不到 projectName 为 ${projectName} 的项目`);

    // 查询打码配置
    let accessKey: string;
    let scene: string;
    if (project.wsyConfig) {
      accessKey = project.wsyConfig.accessKey;
      scene = project.wsyConfig.scene;
    } else {
      throw Error(`${projectName} 不支持王守义打码`);
    }

    const query = Object.entries({ scene, ...estoreParams }).map(([key, val]) => `${key}=${val}`).join('&');

    // this.ctx.logger.info('获取打码任务 >>> req: %j', query);

    const originRes = await axios({
      method: 'GET',
      url: `https://${ESTORE_DOMAIN}/wsy-next/api/cloudbuild/tasks?${query}`,
      headers: {
        'x-access-key': accessKey,
      }
    })
    const data = originRes?.data?.data;

    // this.ctx.logger.info('获取打码任务 >>> res: %j', data);

    return {
      list: data.list?.map(task => this.parseData(task)) || [],
      total: data.total || 0
    };
  }

  /** 获取单个构建任务 */
  async getTask(projectName: string, estoreParams: IEstoreParams): Promise<IBuildTask | null> {
    const { list } = await this.getTasks(projectName, estoreParams);

    return list[0] || null;
  }

  /** 构建 */
  async build(projectName: string, { branch, pagePath, pageQuery, env, platform, userInfo, customArgv }: IBuildParams): Promise<IBuildTask | null> {
    // 查询归属项目信息
    const project = await this.projectService.get(projectName, { needFullWsyConfig: true });
    if (!project) throw Error(`查询不到 projectName 为 ${projectName} 的项目`);

    // 查询打码配置
    let accessKey: string;
    let scene: string;
    if (project.wsyConfig) {
      accessKey = project.wsyConfig.accessKey;
      scene = project.wsyConfig.scene;
      if (!platform) { // 如果没有指定打码平台，则取配置里的第一项
        platform = project.wsyConfig.platformList[0];
      } else if (!project.wsyConfig.platformList.includes(platform)) { // 如果配置里不包含指定的打码平台，则报错提示
        throw Error(`${projectName} 暂不支持 ${platform} 平台的打码，请检查配置`);
      }
    } else {
      throw Error(`${projectName} 不支持王守义打码`);
    }

    const data = {
      userInfo,
      platform, scene, compileType: 'mini',
      command: 'pack', env: env || 'online', branch, customArgv: customArgv && JSON.stringify({
        ...customArgv,
        fastbuild: customArgv?.minifyBuild ? 0 : 1 // 开启按需构建时需要关闭快速构建
      }),
      pagePath: pagePath && (`${pagePath}?${pageQuery || ''}`)
    };

    this.ctx.logger.info('创建打码任务 >>> req: %j', data);

    const originRes = await axios(
      {
        url: `https://${ESTORE_DOMAIN}/wsy-next/api/cloudbuild`,
        method: 'POST',
        headers: {
          'x-access-key': accessKey
        },
        data
      });
    const task = originRes?.data?.data;

    this.ctx.logger.info('创建打码任务 >>> ', originRes);
    if (task) {
      this.ctx.logger.info('创建打码任务 >>> 成功: %j', task);
      return this.parseData({
        ...task,
        status: 0,
      })
    } else {
      this.ctx.logger.error('创建打码任务 >>> 失败: %j', originRes?.data);
      throw Error('创建 estore 构建任务失败')
    }
  }

  parseData(data): IBuildTask | null {
    if (!data) return null;

    const {
      branch, commitId,
      env, platform, pagePath, pageQuery,
      status, imgurl, dist,
      creatorName, gmtCreate,
      error, taskId, customArgv
    } = data;

    return {
      branchName: branch, commitId,
      env, platform, pagePath, pageQuery: pageQuery && decodeURIComponent(pageQuery),
      status, dist, error,
      imgUrl: status === 0 ? 'https://img.alicdn.com/imgextra/i3/O1CN01Qa0qBB1Y9NFZWM5UY_!!6000000003016-2-tps-200-200.png' : imgurl,
      creator: creatorName, gmtCreate: gmtCreate && formatDate(new Date(gmtCreate)),
      taskId,
      customArgv: customArgv ? JSON.parse(customArgv) : {}
    }
  }
}

interface IEstoreParams {
  /** 代码分支 */
  branch?: string;
  /** 构建状态：-1 - 失败、0 - 进行中、1 - 成功 */
  // status?: number;
  /** 创建者工号 */
  creatorWorkid?: string;
  /** 请求页数 */
  page?: number;
  /** 每页条数 */
  perPage?: number;
  /** 任务id */
  taskId?: string;
  /** git 提交 id */
  commitId?: string;
  /** 环境 */
  env?: string;
}

interface IBuildParams {
  /** 代码分支 */
  branch: string;
  /** 页面路径 */
  pagePath?: string;
  /** 页面参数 */
  pageQuery?: string;
  /** 构建环境 */
  env?: string;
  /** 构建平台 */
  platform?: EBuildPlatform;
  /** 用户信息 */
  userInfo: string;
  /** 自定义向云端构建传入的参数。可以通过process.env.customArgv获取 */
  customArgv?: {
    /** 启用按需构建 */
    minifyBuild?: boolean;
    /** 参与按需构建的包 */
    minifyBuildPackages?: string[];
    /** 启用快速构建，只对微信小程序有效。与按需构建冲突，按需构建优先 */
    fastbuild?: 1 | 0;
    /** 备注 */
    remark?: string;
  };
  callback?: string;
}