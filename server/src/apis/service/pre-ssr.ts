import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
const dayjs = require('dayjs');
const ALY = require('aliyun-sdk');
const sls = new ALY.SLS({
  accessKeyId: 'LTAI5tDwKExnH8vud8AB1DGw',
  secretAccessKey: '******************************',
  endpoint: 'http://cn-wulanchabu.log.aliyuncs.com',
  // 这是 sls sdk 目前支持最新的 api 版本, 不需要修改
  apiVersion: '2015-06-01',
  // 以下是可选配置
  httpOptions: {
    timeout: 2000  // 1秒, 默认没有timeout
  },
});

@Provide()
export default class PressrService {
  @Inject()
  ctx!: Context;

  async queryMain() {
    // 统计过去10分钟的数据
    const targetSlsOptions = {
      query: `* and logType : summary | select __topic__, __source__, count(*) as count from log where url like '%_fl_auto_preload_spm%' GROUP by __topic__, __source__ order by count DESC`,
      projectName: 'fliggy-rax-ssr',
      logStoreName: 'ssr_log',
      from: dayjs().subtract(10, 'minute').unix(),
      to: dayjs().subtract(1, 'minute').unix()
    };
    
    const targetRes: any = await new Promise((resolve, reject) => sls.getLogs(targetSlsOptions, function (err: any, data: any) {
      if(err){
        reject(err)
      }
      resolve(data);
    }));

    const surceSlsOptions = {
      query: `* and logType : summary | select split_part(split_part(url, '_fl_auto_preload_spm=', 2), '&', 1) as source,  count(*) as count from log where url like '%_fl_auto_preload_spm%' GROUP by source order by count DESC`,
      projectName: 'fliggy-rax-ssr',
      logStoreName: 'ssr_log',
      from: dayjs().subtract(10, 'minute').unix(),
      to: dayjs().subtract(1, 'minute').unix()
    };
    
    const sourceRes: any = await new Promise((resolve, reject) => sls.getLogs(surceSlsOptions, function (err: any, data: any) {
      if(err){
        reject(err)
      }
      resolve(data);
    }));
    
    const targetList = Object.keys(targetRes.body || {}).map(item => {
      return targetRes.body[item]
    });

    const sourceList = Object.keys(sourceRes.body || {}).map(item => {
      return sourceRes.body[item]
    });

    return {
      target: targetList,
      source: sourceList
    };
  }

  async queryDetail(params) {
    // 统计过去10分钟的数据
    const targetSlsOptions = params.type === 'target' ? {
      query: `* and logType : summary and __topic__: ${params.__topic__} and __source__: ${params.__source__} | select date_format(date_trunc('minute', __time__), '%m-%d %H:%i') as time, split_part(split_part(url, '_fl_auto_preload_spm=', 2), '&', 1) as source, count(*) as count from log where url like '%_fl_auto_preload_spm%' GROUP by time, source order by time ASC limit 1000 `,
      projectName: 'fliggy-rax-ssr',
      logStoreName: 'ssr_log',
      from: params.time ? dayjs(params.time).subtract(10, 'minute').unix() : dayjs().subtract(10, 'minute').unix(),
      to: params.time ? dayjs(params.time).subtract(1, 'minute').unix() : dayjs().subtract(1, 'minute').unix()
    } : {
      query: `* and logType : summary | select date_format(date_trunc('minute', __time__), '%m-%d %H:%i') as time, concat(__topic__, '/', __source__) as page, count(*) as count from log where url like '%_fl_auto_preload_spm=${params.source}%' GROUP by time, page order by time ASC limit 1000`,
      projectName: 'fliggy-rax-ssr',
      logStoreName: 'ssr_log',
      from: params.time ? dayjs(params.time).subtract(10, 'minute').unix() : dayjs().subtract(10, 'minute').unix(),
      to: params.time ? dayjs(params.time).subtract(1, 'minute').unix() : dayjs().subtract(1, 'minute').unix()
    };
    
    const res: any = await new Promise((resolve, reject) => sls.getLogs(targetSlsOptions, function (err: any, data: any) {
      if(err){
        reject(err)
      }
      resolve(data);
    }));

    const resList = Object.keys(res.body || {}).map(item => {
      return {...res.body[item], count: parseFloat((parseInt(res.body[item].count) / 60).toFixed(2))}
    });

    // 同一个时间合并总数
    let allList:any = [];
    resList.forEach((item) => {
      if (allList.length === 0) {
        allList.push({
          ...item,
          source: 'All',
          page: 'All'
        })
      } else {
        if (allList[allList.length - 1].time === item.time) {
          allList[allList.length - 1].count = parseFloat((allList[allList.length - 1].count + item.count).toFixed(2));
        } else {
          allList.push({
            ...item,
            source: 'All',
            page: 'All'
          })
        }
      }
    })

    return {
      data: allList.concat(resList),
      params
    };
  }

}
