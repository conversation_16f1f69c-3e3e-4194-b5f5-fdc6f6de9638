import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { convertsCollectionToCamel, convertsCollectionToSnake } from '@/apis/utils';
import { IUser, UserType } from '@/apis/interface/user';
import { IUserModel } from '@/apis/model/user';
import { removeWorkidPrefixZero } from '@/apis/utils';

@Provide()
export default class UserService {
  @Inject()
  ctx!: Context;

  @Inject()
  UserModel!: IUserModel;

  async list(): Promise<IUser[]> {
    const res = await this.UserModel.findAll({
      where: {},
      raw: true
    });

    // 扩展字段
    const extendRes = res.map(item => extendUser(item))

    return convertsCollectionToCamel(extendRes);
  }

  async create(values: IUser): Promise<IUser> {
    values.workid = removeWorkidPrefixZero(values.workid);
    const dbValues = convertsCollectionToSnake(values);
    const userModel = await this.UserModel.create(dbValues, {
      fields: ['user_type', 'userid', 'workid', 'name', 'last_name', 'avatar_url']
    });
    const res = userModel.toJSON();

    return convertsCollectionToCamel(res);
  }

  async update(values: IUser): Promise<boolean> {
    values.workid = removeWorkidPrefixZero(values.workid);
    const dbValues = convertsCollectionToSnake(values);
    const updateResult = await this.UserModel.update(dbValues, {
      where: { workid: values.workid },
      fields: ['user_type']
    });

    return updateResult[0] > 0;
  }

  async get(workid: string): Promise<IUser | null> {
    workid = removeWorkidPrefixZero(workid);
    const res = await this.UserModel.findOne({
      where: { workid },
      raw: true
    });

    // 扩展字段
    const extendRes = extendUser(res)

    return convertsCollectionToCamel(extendRes);
  }

  async delete(workid: string): Promise<boolean> {
    const workidWithoutZero = removeWorkidPrefixZero(workid);
    const destroyResult = await this.UserModel.destroy({
      where: {
        workid: [workid, workidWithoutZero]
      }
    });
    return destroyResult > 0;
  }

  async isAdmin(): Promise<boolean> {
    if (!this.ctx.user) return false;
    const loginUser = await this.get(this.ctx.user.workid);
    return loginUser?.userType === UserType.ADMIN;
  }
}


function extendUser(user) {
  if (!user) return user;

  let userTypeText = '';
  switch (user.user_type) {
    case UserType.ADMIN: userTypeText = '管理员'; break;
    case UserType.DEVELOPER: userTypeText = '开发者'; break;
    case UserType.QA: userTypeText = '测试'; break;
  }
  return {
    ...user,
    userTypeText,
    htmlUrl: `https://work.alibaba-inc.com/nwpipe/u/${user.workid}`
  }
}