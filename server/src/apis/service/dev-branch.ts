import { Inject, Provide } from '@midwayjs/core';
import { IDevBranch } from '@/apis/interface/dev-branch';
import BaseDevBranchService, { IDevBranchCreateParams, IBaseDevBranchUpdateParams } from '@/apis/service/base-dev-branch';
import { DevBranchDetailModel } from '@/apis/model/dev-branch-detail';

@Provide()
export default class DevBranchService extends BaseDevBranchService {
  @Inject()
  DevBranchDetailModel!: typeof DevBranchDetailModel;

  getBranchModel() {
    return this.DevBranchDetailModel;
  }

  async get(devId: number) {
    return await this._get(devId) as IDevBranch | null;
  }

  async list(pageSize: number, pageNum: number, conditions: { projectName?: string; iterId?: number; }) {
    return await this._list(pageSize, pageNum, conditions) as { list: IDevBranch[]; total: number; };
  }

  async create(params: IDevBranchCreateParams) {
    return await this._create(params) as IDevBranch;
  }

  async update(devId: number, params: IBaseDevBranchUpdateParams, recordOperator = true) {
    return await this._update(devId, params, recordOperator) as IDevBranch;
  }

  async delete(devId: number) {
    return await this._delete(devId) as IDevBranch;
  }

  async discard(devId: number) {
    return await this._discard(devId) as IDevBranch;
  }

  async ready(devId: number) {
    return await this._switchReady(devId, true) as IDevBranch;
  }

  async cancelReady(devId: number) {
    return await this._switchReady(devId, false) as IDevBranch;
  }
}
