import * as path from 'path';
import * as child_process from 'child_process';
import { tmpdir } from 'os';
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { convertsCollectionToCamel, convertsCollectionToSnake, formatDate, filterOutNull } from '@/apis/utils';
import { IIterDeliverTask, IAlipayVersionInfo, IMiniAppVersionInfo } from '@/apis/interface/iter-deliver';
import { IIterDeliverTaskModel } from '@/apis/model/iter-deliver';
import UserService from '@/apis/service/user';
import IterBranchService from '@/apis/service/iter-branch';
import DingtalkService from '@/apis/service/dingtalk';
import MiniAppVersionInfoService from '@/apis/service/miniapp-version-info';
import { uploadToBasement } from '@/apis/utils/file';
import { getOriginalPlatform } from '@/apis/utils/deliver';
import Ali<PERSON>y<PERSON>penApiHTTP from '@/apis/http/alipay-open-api';
import { EClient, EMiniAppUploadStatus, EMiniAppVersionStatus, MINI_APP_VERSION_STATUS_TEXT, DELIVER_CLIENT_STATIC_CFG, MINI_APP_UPLOAD_STATUS_TEXT, BUNDLE_ID_CONFIG } from '@/apis/const/iter-deliver';
import { useTmpDir, useGit } from '@/apis/middleware/hooks';
import * as chalk from 'chalk';

@Provide()
export default class IterDeliverService {
  @Inject()
  ctx!: Context;

  @Inject()
  IterDeliverTaskModel!: IIterDeliverTaskModel;

  @Inject()
  userService!: UserService;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  miniAppVersionInfoService!: MiniAppVersionInfoService;

  @Inject()
  alipayOpenApiHTTP!: AlipayOpenApiHTTP;

  async create({ iterId, clientName, miniAppId, uploadStatus }: IIterDeliverCreateParams) {
    const dbValues = this._covertToDBData({
      iterId,
      clientName,
      miniAppId,
      uploadStatus,
      creatorWorkid: this.ctx.user.workid,
      gmtCreate: formatDate(new Date()),
    });
    const iterDeliverTaskModel = await this.IterDeliverTaskModel.create(dbValues);

    return this._handleReturnData(this._formatDBData(iterDeliverTaskModel.toJSON()));
  }

  async findOrCreate({ iterId, clientName, miniAppId, miniAppVersion, uploadStatus }: IIterDeliverFindOrCreateParams) {
    const dbValues = this._covertToDBData({
      iterId,
      clientName,
      miniAppId,
      miniAppVersion,
      uploadStatus,
      creatorWorkid: this.ctx.user.workid,
      gmtCreate: formatDate(new Date()),
    });
    const [iterDeliverTaskModel, created] = await this.IterDeliverTaskModel.findOrCreate({
      where: Object.assign({
        iter_id: iterId,
        mini_app_id: miniAppId,
        mini_app_version: miniAppVersion
      }, clientName && { client_name: clientName }),
      defaults: dbValues
    });
    if (!created) throw Error(`已有同条件(${JSON.stringify({ iterId, clientName, miniAppId, miniAppVersion })})的迭代投放任务`)

    return this._handleReturnData(this._formatDBData(iterDeliverTaskModel.toJSON()));
  }

  async update(id: number, { miniAppVersion, uploadLog, uploadStatus, clientList }: IIterDeliverUpdateParams) {
    const dbValues = this._covertToDBData({
      miniAppVersion,
      uploadLog,
      uploadStatus,
      clientList: clientList && clientList.join(','),
      modifierWorkid: this.ctx.user.workid,
      gmtModified: formatDate(new Date()),
    });
    const updateResult = await this.IterDeliverTaskModel.update(dbValues, {
      where: { id },
    });

    if (updateResult[0] === 0) throw Error(`更新迭代投放记录(id: ${id})失败`);

    return true;
  }

  async delete(id: number) {
    const destroyResult = await this.IterDeliverTaskModel.destroy({ where: { id } });
    return destroyResult > 0;
  }

  async getById(id: number, opts?: { needVersionInfo?: boolean }) {
    const res = await this.IterDeliverTaskModel.findOne({
      where: { id },
      raw: true
    });

    if (res) {
      return this._handleReturnData(this._formatDBData(res), {
        needVersionInfo: opts?.needVersionInfo
      });
    } else {
      return null;
    }
  }

  async list(conditions: { iterId?: number; clientName?: string, miniAppId?: string; }, opts?: { needVersionInfo?: boolean, }) {
    const whereOptions: any = {};

    if (conditions.iterId) whereOptions.iter_id = conditions.iterId;
    if (conditions.clientName) whereOptions.client_name = conditions.clientName;
    if (conditions.miniAppId) whereOptions.mini_app_id = conditions.miniAppId;

    const { rows, count } = await this.IterDeliverTaskModel.findAndCountAll({
      where: whereOptions,
      order: [['gmt_create', 'DESC']],
      raw: true
    });

    const list = await Promise.all(rows.map(item => this._handleReturnData(this._formatDBData(item), {
      needVersionInfo: opts?.needVersionInfo
    })))

    return {
      list,
      total: count
    }
  }

  /** 构建上传 */
  async upload(iterDeliverTask: IIterDeliverTask) {
    const iterBranch = await this.iterBranchService.get(iterDeliverTask.iterId);

    // 判断迭代是否可构建上传
    if (!iterBranch) throw Error(`查询不到 iterId 为 ${iterDeliverTask.iterId} 的迭代`)
    else if (!iterBranch.rcGitBranch) throw Error(`${iterBranch.projectName} 的迭代 ${iterBranch.version} 缺少 rc 分支，无法构建上传`)

    // 拉取代码
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const { projectTmpDirPath } = await useGit(tmpDirPath, {
      branchUrl: iterBranch.rcGitBranch.url,
      branchName: iterBranch.rcGitBranch.name,
      cloneSingleBranch: true
    });

    // 分端上传
    let miniAppVersion: string | undefined;
    let uploadSuccess: boolean;
    let logText: string;
    if (iterDeliverTask.clientName === EClient.ALIPAY) {
      const uploadRes = await this.uploadAlipaySeries(iterDeliverTask.clientName, iterDeliverTask.miniAppId, projectTmpDirPath)
      miniAppVersion = uploadRes.miniAppVersion;
      uploadSuccess = uploadRes.uploadSuccess;
      logText = uploadRes.logText;
    } else {
      throw Error('暂时只支持支付宝端小程序的云构建上传')
    }

    // 上传完毕，删除临时目录
    removeTmpDir();

    // 上传日志
    const logFileName = `${iterDeliverTask.clientName}_${iterDeliverTask.miniAppId}_${iterDeliverTask.id}_${miniAppVersion || 'unknown_version'}_upload_log.txt`;
    const { url: uploadLog } = await uploadToBasement(logFileName, logText);
    this.ctx.logger.info(chalk.cyan(`上传支系小程序 >>> 小程序构建&上传日志：${uploadLog}`))

    // 处理上传结果消息
    let msg: string;
    if (uploadSuccess) {
      msg = `${iterBranch.projectName} 的迭代 ${iterBranch.version} 上传 ${iterDeliverTask.clientName} 端成功，版本 v${miniAppVersion}`;
      this.ctx.logger.info(chalk.green(msg))
    } else {
      msg = `${iterBranch.projectName} 的迭代 ${iterBranch.version} 上传 ${iterDeliverTask.clientName} 端失败，${uploadLog ? ('详见日志：' + uploadLog) : '日志记录失败'}`;
      this.ctx.logger.error(chalk.red(msg))
    }

    return {
      miniAppVersion,
      uploadSuccess,
      uploadLog,
      msg,
    }
  }

  /** 上传支系小程序 */
  async uploadAlipaySeries(clientName: EClient, miniAppId: string, projectTmpDirPath: string): Promise<{ miniAppVersion?: string, uploadSuccess: boolean, logText: string }> {
    this.ctx.logger.info(chalk.cyan('上传支系小程序 >>> 开始'))

    // 安装依赖包
    try {
      await this._exec('tnpm install --by=yarn', {
        cwd: projectTmpDirPath,
      }, '上传支系小程序 >>> 安装依赖包')
    } catch (err: any) {
      return {
        uploadSuccess: false,
        logText: err.message
      }
    }

    // clam 构建
    const binPath = path.join(tmpdir(), 'node_modules/.bin');
    try {
      let successTag = false;
      await this._exec(`${binPath}/clam build:miniapp`, {
        maxBuffer: 1024 * 1024 * 5,
        cwd: projectTmpDirPath,
        env: {
          ...process.env,
          PATH: `${process.env.PATH}:${binPath}`
        }
      }, '上传支系小程序 >>> clam 构建', (std: string) => {
        // 检测到下方字句，认为构建成功
        if (std.match('hook_post_build')) {
          successTag = true;
        }

        // 再检测到下方字句，认为构建完成
        if (successTag && std.match('Total')) {
          return { success: true };
        }

        return null;
      })
    } catch (err: any) {
      return {
        uploadSuccess: false,
        logText: err.message
      }
    }

    // 获取最新版本号，并 patch 版本号 +1
    // 注意要等上面的构建结束再获取版本号，因为构建时间过长，版本号中途可能有新增
    const { appVersion } = await this.alipayOpenApiHTTP.lastQuery({
      clientName,
      miniAppId,
    });
    if (!appVersion) {
      const errMsg = '上传支系小程序 >>> 获取最新版本号失败';
      this.ctx.logger.error(errMsg);
      return {
        uploadSuccess: false,
        logText: errMsg
      }
    }

    /** 通过 mini 脚本上传小程序到指定端，https://yuque.antfin-inc.com/volans/mini/mini#71552929 */
    let reUploadedForOld = false; // 因为版本号太旧而重新上传
    let reUploadedForMax = false; // 因为版本号太多而重新上传
    const upload = async (currentVersion: string) => {
      const miniAppVersionArr = currentVersion.split('.');
      const patchNum = Number(miniAppVersionArr[miniAppVersionArr.length - 1]);
      miniAppVersionArr[miniAppVersionArr.length - 1] = String(patchNum + 1);
      const miniAppVersion = miniAppVersionArr.join('.');

      this.ctx.logger.info(chalk.cyan(`上传支系小程序 >>> 当前最新版本号${currentVersion},目标版本号${miniAppVersion}`))

      try {
        const { logText, stderr } = await this._exec(`DEBUG=minidev-cli ${binPath}/mini upload -a ${miniAppId} --packageVersion '${miniAppVersion}' -b '${BUNDLE_ID_CONFIG[clientName]}'`, {
          maxBuffer: 1024 * 1024 * 5,
          cwd: path.resolve(projectTmpDirPath, 'build_miniapp'),
          env: {
            ...process.env,
            PATH: `${process.env.PATH}:${binPath}`
          }
        }, '上传支系小程序 >>> 小程序构建&上传')


        if (stderr) {
          let matchRes = null as RegExpMatchArray | null;

          // 检测报错是否为开发中版本超过最大数量
          if (!reUploadedForMax && (matchRes = stderr.match('关联普通小程序当前开发中版本超过最大数量'))) {
            reUploadedForMax = true;
            this.ctx.logger.info(chalk.cyan('上传支系小程序 >>> 检测到当前开发中版本超过最大数量，尝试删除后重新上传'))

            // 取第 15 - 20 数据
            const { miniVersionBaseInfoList } = await this.alipayOpenApiHTTP.batchQuery({
              miniAppId,
              clientName,
              versionStatus: EMiniAppVersionStatus.INIT,
              pageSize: 5,
              pageNum: 4
            });

            // 若有则删除后再尝试重新上传，若无则直接尝试重新上传
            if (miniVersionBaseInfoList) {
              const promiseSettledResList = await Promise.allSettled(miniVersionBaseInfoList.map(async item => {
                const res = await this.alipayOpenApiHTTP.infoDelete({
                  miniAppId,
                  clientName,
                  appVersion: item.appVersion
                })

                if (res.code === '10000') {
                  // 【异步】通知操作者
                  this.dingtalkService.notice(this.ctx.user.workid, '自动删除历史版本', `上传小程序 ${miniAppId} 到 ${clientName} 时，由于检测到当前开发中版本超过最大数量，已自动删除历史版本 ${item.appVersion}`)
                  return true;
                } else {
                  return false;
                }
              }))

              // 只要成功删除一个，就可以尝试重新上传
              if (promiseSettledResList.some(data => data.status === 'fulfilled' && data.value)) {
                return await upload(appVersion)
              }
            } else {
              return await upload(appVersion)
            }
          }
          // 检测报错是否为版本号不够新
          else if (!reUploadedForOld && (matchRes = stderr.match(/开始构建错误 您的新版本号必须大于(\d+)\.(\d+)\.(\d+)/))) {
            reUploadedForOld = true;
            this.ctx.logger.info(chalk.cyan('上传支系小程序 >>> 检测到有更新版本号，重新上传'))
            return await upload(`${matchRes[1]}.${matchRes[2]}.${matchRes[3]}`)
          }
        }

        return {
          miniAppVersion,
          uploadSuccess: stderr ? false : true,
          logText
        }
      } catch (err: any) {
        return {
          uploadSuccess: false,
          logText: err.message
        }
      }
    }

    return await upload(appVersion);
  }

  /** 执行指令 */
  async _exec(
    command: string, options: child_process.ExecOptions, actionTitle: string,
    activeCheck?: (std: string) => { success: boolean; message?: string; } | null
  ): Promise<{ logText: string; stdout: string; stderr: string; }> {
    return new Promise((resolve, reject) => {
      this.ctx.logger.info(chalk.cyan(`开始执行【${actionTitle}】`));
      const startTime = Date.now();
      let logText = '';
      let stdout = '';
      let stderr = '';

      const workerProcess = child_process.exec(command, {
        ...options,
        timeout: 20 * 60 * 1000,
      })

      const successCallback = () => {
        this.ctx.logger.info(chalk.green(`【${actionTitle}】执行成功(耗时 ${Date.now() - startTime}ms)`))
        resolve({ stdout, stderr, logText });
      }

      const failCallback = (msg = '未知错误') => {
        const message = `【${actionTitle}】执行失败(耗时 ${Date.now() - startTime}ms): ${msg} \n ${logText}`
        this.ctx.logger.error(chalk.red(message));
        reject(new Error(message));
      }

      // 主动检测逻辑。主要解决 clam build 指令在云上机器不会触发 exit 或 error 的问题
      const check = (data: string) => {
        if (!activeCheck) return;

        const checkRes = activeCheck(data);
        if (!checkRes) return;

        // 先移除监听
        workerProcess.removeAllListeners();
        // 再杀掉进程
        workerProcess.kill()

        // 根据结果执行对应回调
        if (checkRes.success) {
          successCallback()
        } else {
          failCallback(checkRes.message)
        }
      }

      workerProcess.stdout?.on('data', data => {
        if (!data) return;

        logText += data;
        stdout += data;

        if (activeCheck) check(data)
      });

      workerProcess.stderr?.on('data', data => {
        if (!data) return;

        logText += data;
        stderr += data;

        if (activeCheck) check(data)
      });

      workerProcess.on('exit', (code, _signal) => {
        if (code === 0) {
          successCallback()
        } else {
          failCallback(`指令终止，退出码(${code})`)
        }
      });

      workerProcess.on('error', error => {
        failCallback(error.message)
      });
    })
  }

  /** 转换成 db 数据 */
  _covertToDBData(data: any) {
    return convertsCollectionToSnake(data, 1);
  }

  /** 格式化 db 数据 */
  _formatDBData(dbData: any): IIterDeliverTask {
    // 过滤掉 null 值，并转驼峰
    const deliverTask = convertsCollectionToCamel(filterOutNull(dbData), 1);

    return {
      ...deliverTask,
      clientList: deliverTask.clientList && deliverTask.clientList.split(','),
      gmtCreate: deliverTask.gmtCreate && formatDate(deliverTask.gmtCreate),
      gmtModified: deliverTask.gmtModified && formatDate(deliverTask.gmtModified),
    }
  }

  /** 处理返回的数据 */
  async _handleReturnData(deliverTask: IIterDeliverTask, opts?: { needVersionInfo?: boolean }) {
    let miniAppVersionInfo = undefined as IMiniAppVersionInfo | IAlipayVersionInfo | undefined;
    if (opts?.needVersionInfo && deliverTask.miniAppVersion && deliverTask.uploadStatus === EMiniAppUploadStatus.SUCCESS) {
      // 获取小程序版本信息
      miniAppVersionInfo = await this._getMiniAppVersionInfo({
        clientName: deliverTask.clientName,
        miniAppId: deliverTask.miniAppId,
        miniAppVersion: deliverTask.miniAppVersion
      });
    }

    return {
      ...deliverTask,
      miniAppVersionInfo,
      uploadStatusText: MINI_APP_UPLOAD_STATUS_TEXT[deliverTask.uploadStatus],
    } as IIterDeliverTask;
  }

  /** 获取小程序版本信息 */
  async _getMiniAppVersionInfo(singleMatchConditions: ISingleMatchConditions) {
    // 分端逻辑
    if (singleMatchConditions.clientName === EClient.ALIPAY) {
      let miniAppVersionInfo = await this.alipayOpenApiHTTP.infoQuery({
        miniAppId: singleMatchConditions.miniAppId,
        clientName: singleMatchConditions.clientName,
        appVersion: singleMatchConditions.miniAppVersion,
      }) as IAlipayVersionInfo;
      if (!miniAppVersionInfo) throw Error(`查询不到符合条件(${JSON.stringify(singleMatchConditions)})的小程序版本信息`);

      // 扩展版本信息
      return this._extendsMiniAppVersionInfo(singleMatchConditions.clientName, singleMatchConditions.miniAppVersion, miniAppVersionInfo) as IAlipayVersionInfo;
    } else {
      const miniAppVersionInfo = await this.miniAppVersionInfoService.getByConditions(singleMatchConditions);
      if (!miniAppVersionInfo) throw Error(`查询不到符合条件(${JSON.stringify(singleMatchConditions)})的小程序版本信息`);

      // 扩展版本信息
      return this._extendsMiniAppVersionInfo(singleMatchConditions.clientName || '', singleMatchConditions.miniAppVersion, miniAppVersionInfo) as IMiniAppVersionInfo;
    }
  }

  /** 扩展小程序版本信息 */
  _extendsMiniAppVersionInfo(clientName: EClient | '', miniAppVersion: string, miniAppVersionInfo: IMiniAppVersionInfo | IAlipayVersionInfo) {
    // 匹配当前灰度策略
    const grayStrategyList = miniAppVersionInfo.grayStrategy?.split(',') || [];
    let curGrayStrategy: string | undefined;
    if (clientName) {
      DELIVER_CLIENT_STATIC_CFG[clientName].graySteps?.forEach(item => {
        if (grayStrategyList.indexOf(item.strategy) > -1) {
          curGrayStrategy = item.strategy;
        }
      });
    }

    // 小程序原投放平台
    let platformUrl = clientName ? getOriginalPlatform(miniAppVersionInfo.miniAppId, clientName) : '';
    // 支端小程序开发状态，直接跳提审页面
    if (clientName === EClient.ALIPAY && miniAppVersionInfo.status === EMiniAppVersionStatus.INIT) {
      platformUrl = `https://openhome.alipay.com/mini/dev/version-audit?version=${miniAppVersion.split('.').join('_')}&appId=${miniAppVersionInfo.miniAppId}&bundleId=${BUNDLE_ID_CONFIG[clientName]}`
    }

    // 回滚状态
    let statusText = MINI_APP_VERSION_STATUS_TEXT[miniAppVersionInfo.status] || `未知(${miniAppVersionInfo.status})`;
    if (clientName === EClient.ALIPAY && miniAppVersionInfo.status === EMiniAppVersionStatus.OFFLINE && miniAppVersionInfo.rollbackTime) {
      statusText = `${MINI_APP_VERSION_STATUS_TEXT[miniAppVersionInfo.status]}(回滚)`
    }

    return {
      ...miniAppVersionInfo,
      statusText,
      curGrayStrategy,
      graySteps: clientName ? DELIVER_CLIENT_STATIC_CFG[clientName].graySteps : null,
      platformUrl,
    } as typeof miniAppVersionInfo;
  }
}

interface IIterDeliverCreateParams {
  /** 迭代分支id */
  iterId: number;
  /** 投放端 */
  clientName?: EClient;
  /** 小程序id */
  miniAppId: string;
  /** 上传状态 */
  uploadStatus: EMiniAppUploadStatus;
}

interface IIterDeliverFindOrCreateParams extends IIterDeliverCreateParams {
  /** 小程序上传版本 */
  miniAppVersion: string;
}

interface IIterDeliverUpdateParams {
  /** 小程序上传版本 */
  miniAppVersion?: string;
  /** 上传日志 */
  uploadLog?: string;
  /** 上传状态 */
  uploadStatus?: EMiniAppUploadStatus;
  /** 投放端列表 */
  clientList?: EClient[];
}

// 单一匹配条件
interface ISingleMatchConditions {
  /** 投放端 */
  clientName?: EClient;
  /** 小程序id */
  miniAppId: string;
  /** 小程序版本 */
  miniAppVersion: string;
}