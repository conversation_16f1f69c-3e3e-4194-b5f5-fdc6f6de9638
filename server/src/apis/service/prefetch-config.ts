import { Context } from '@midwayjs/faas';
import { Inject, Provide } from '@midwayjs/core';
import { IPrefetchConfig } from '@/apis/interface/prefetch-config';
import { IPrefetchConfigModel } from '@/apis/model/prefetch-config';

import MtopHsf from '@/apis/hsf/mtop';
import PrefetchHsf from '@/apis/hsf/prefetch';

@Provide()
export default class PrefetchConfigService {
  @Inject()
  ctx!:Context;

  @Inject()
  PrefetchConfigModel!: IPrefetchConfigModel;

  @Inject()
  mtopHSF!: MtopHsf;

  @Inject()
  prefetchHSF!: PrefetchHsf;

  async create(values: IPrefetchConfig): Promise<IPrefetchConfig> {
    const res: any = await this.prefetchHSF.createPrefetchItemHandler(values);
    return res.data
  }

  async update(values: IPrefetchConfig): Promise<boolean> {
    const res: any = await this.prefetchHSF.updatePrefetchItem(values);
    return res.data
  }

  async get(path: string, projectName: string): Promise<IPrefetchConfig | null> {
    const res: any = await this.prefetchHSF.getPrefetchByPathAndProj(path, projectName);
    return res.data
  }

  async getById(id: number): Promise<IPrefetchConfig | null> {
    const res: any = await this.prefetchHSF.getPrefetchById(id);
    return res.data
  }

  async delete(id: number): Promise<boolean> {
    const res: any = await this.prefetchHSF.deletePrefetchItem(id);
    return res.data
  }

  async list(): Promise<IPrefetchConfig[]> {
    const res: any = await this.prefetchHSF.getAllPrefetch();
    return res.data
  }

  async findByKeyValue(key, value): Promise<IPrefetchConfig[]> {
    const res: any = await this.prefetchHSF.getPrefetchItemByKV(key, value);
    return res.data
  }

  async getMtopData(api: string, version: string): Promise<any> {
    const res = await this.mtopHSF.getMtopApiData(api, version);
    return res;
  }

  async getPrefetchData(params: any): Promise<any> {
    const res = await this.prefetchHSF.getPrefetchData(params);
    return res;
  }
}