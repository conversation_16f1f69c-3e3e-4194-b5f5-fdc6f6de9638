import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import * as _ from 'lodash';

const Core = require('@alicloud/pop-core');

const accessKeyId = "LTAI5tB6dFamy3LirLMcQF1R";
const accessKeySecret = "******************************";
const endpoint = "https://arms.cn-hangzhou.aliyuncs.com";

// armsKeys 初始化参数
const armsKeys = {
  accessKeyId,
  accessKeySecret,
  endpoint,
  apiVersion: "2019-08-08",
  opts: { timeout: 60000 },
};

// armsSlsKeys 初始化参数
const armsSlsKeys = {
  accessKeyId,
  secretAccessKey: accessKeySecret,
  endpoint: "http://cn-hangzhou.log.aliyuncs.com", //日志服务的域名。更多信息，请参见服务入口。此处以杭州为例，其它地域请根据实际情况填写。
  apiVersion: "2015-06-01", //SDK版本号，固定值。
};

const qliveSlsKeys = {
  accessKeyId: 'LTAIMHs86eSeCvvT',
  secretAccessKey: '******************************',
  endpoint: 'http://cn-shanghai-corp.sls.aliyuncs.com',
  apiVersion: '2015-06-01',
  httpOptions: {
    timeout: 2000, // 1秒, 默认没有timeout
  },
}

interface ILogParams {
  startTime: string | number;
  endTime: string | number;
  query: string; pid: string;
  projectName?: string;
}

/**
 * sls及arms日志
 */
@Provide()
export default class SlsService {
  @Inject()
  ctx!: Context;

  ALY = require("aliyun-sdk");

  armsClient = new Core(armsKeys);
  sls = new this.ALY.SLS(armsSlsKeys);
  qliveSls = new this.ALY.SLS(qliveSlsKeys);

  /**
   * 基础获取日志
   * @param
   */
  async getSlsLogs(slsParams, useQlive = false, method = 'getLogs') {
    return new Promise((resolve, reject) => {
      this[useQlive ? 'qliveSls' : 'sls'][method](slsParams, function (error, data) {
        if (error) {
          reject(error);
        } else {
          resolve(data);
        }
      });
    });
  }

  /**
   * 获取日志store
   * @param
   */
  async GetRetcodeLogstore(pid): Promise<any> {
    return this.armsClient.request(
      "GetRetcodeLogstore",
      { RegionId: "cn-hangzhou", Pid: pid },
      {
        method: "POST",
        formatParams: false,
        timeout: 60000,
      }
    );
  }

  /**
   * 获取日志详情，适用于非高并发场景
   * @param event.startTime - 开始时间（毫秒）
   * @param event.endTime - 结束时间（毫秒）
   * @param event.query - 查询字符串
   * @param event.pid - 进程ID
   * @param event.logStore - 日志库名（可选，默认值：proj-rum-c8c4c7de99b49b5b4bc0a07a87d8ced）
   */
  async getSLsLogDetail(event: ILogParams) {
    const {
      startTime,
      endTime,
      query,
      pid,
      projectName = "proj-rum-c8c4c7de99b49b5b4bc0a07a87d8ced",
    } = event;
    const from = _.floor(Number(startTime) / 1000);
    const to = _.floor(Number(endTime) / 1000);
    const armsInfo = await this.GetRetcodeLogstore(pid);
    const logStoreName = _.get(armsInfo, "Data.RetcodeSLSConfig.Logstore");

    return this.getSlsLogs({
      projectName: projectName, // 必选，Project名称。
      logStoreName,
      query,
      from,
      to,
      powerSql: true,
    });
  }

  /** 
   * 批量获取日志
   * @param list - 日志查询参数数组
   * */
  async getBatchLog(list: ILogParams[]) {
    if (!list.length) {
      return {
        success: false,
        message: '参数错误',
      }
    }
    const res = await Promise.allSettled(
      list.map((item) => {
        return this.getSLsLogDetail(item)
          .then((res: any) => res && res.body)
          .catch((err) => ({
            message: err,
          }))
      })
    );
    return {
      data: res,
      success: true,
    };
  }
}

