import { Inject, Provide } from '@midwayjs/core';
import BaseDevBranchService, { IFreeDevBranchCreateParams, IBaseDevBranchUpdateParams } from '@/apis/service/base-dev-branch';
import ComponentDeliverService from '@/apis/service/component-deliver';
import { formatDate } from '@/apis/utils';
import { IFreeDevBranch } from '@/apis/interface/free-dev-branch';
import { FreeDevBranchDetailModel } from '@/apis/model/free-dev-branch-detail';
import { mergeBranch } from '@/apis/utils/git';
import { IterStatus } from '@/apis/const/iter-branch';
import { EFreeDevMountStatus } from '@/apis/const/free-dev-branch';
import { EDevStatus, EMergeCode, EBranchType } from '@/apis/const/dev-branch';
import { EDeliverBranchType } from '@/apis/const/component-deliver';

@Provide()
export default class FreeDevBranchService extends BaseDevBranchService {
  @Inject()
  FreeDevBranchDetailModel!: typeof FreeDevBranchDetailModel;

  @Inject()
  componentDeliverService!: ComponentDeliverService;

  isFreeDevBranch = true;

  getBranchModel() {
    return this.FreeDevBranchDetailModel;
  }

  async get(devId: number) {
    return await this._get(devId) as IFreeDevBranch | null;
  }

  async list(pageSize: number, pageNum: number, conditions: { projectName?: string; iterId?: number; filterMine?: boolean; searchBranchName?: string; }) {
    return await this._list(pageSize, pageNum, conditions) as { list: IFreeDevBranch[]; total: number; };
  }

  async create(params: IFreeDevBranchCreateParams) {
    return await this._create(params) as IFreeDevBranch;
  }

  async update(devId: number, params: IBaseDevBranchUpdateParams, recordOperator = true) {
    return await this._update(devId, params, recordOperator) as IFreeDevBranch;
  }

  async updateByBranchName(branchName: string, projectName: string, params: IBaseDevBranchUpdateParams, recordOperator = true) {
    const res = await this.getBranchModel().findOne({
      where: { branch_name: branchName,  project_name: projectName },
      raw: true,
    });
    if (!res) throw Error(`查询不到 branchName 为 ${branchName}、projectName 为 ${projectName} 的游离开发分支`);
    
    return await this._update(res.dev_id, params, recordOperator) as IFreeDevBranch;
  }

  async delete(devId: number ) {
    return await this._delete(devId) as IFreeDevBranch;
  }

  async discard(devId: number) {
    return await this._discard(devId) as IFreeDevBranch;
  }

  async ready(devId: number) {
    return await this._switchReady(devId, true) as IFreeDevBranch;
  }

  async cancelReady(devId: number) {
    return await this._switchReady(devId, false) as IFreeDevBranch;
  }


  /**
   * 挂载
   * @param devId 开发分支id
   * @param iterId 迭代分支id
   */
  async mount(devId: number, iterId: number) {
    // 1. 前置判断
    const freeDevBranch = await this._get(devId, true);

    if (freeDevBranch.iterId) throw Error(`游离开发分支 ${freeDevBranch.branchName} 已挂载到迭代（iterId: ${freeDevBranch.iterId}）`);

    const iterBranch = await this.iterBranchService.get(iterId);

    if (!iterBranch) throw Error(`查询不到 iterId 为 ${iterId} 的迭代`)
    else if (iterBranch.status !== IterStatus.PLAN) throw Error(`迭代 ${iterBranch.version} 处于 ${iterBranch.statusText} 状态，不可挂载`)

    // 2. 如果集成模式是“合并代码”，则使用开发分支 merge 迭代分支
    if (freeDevBranch.mergeCode === EMergeCode.YES) {
      await mergeBranch(freeDevBranch.branchName, freeDevBranch.gitBranch.url, iterBranch.gitBranch.name);
    }

    // 3. 写库
    const dbValues = this._covertToDBData({
      mountStatus: EFreeDevMountStatus.MOUNTED,
      iterId,
      modifier: this.ctx.user.name,
      gmtModified: formatDate(new Date()),
      modifierWorkid: this.ctx.user.workid
    });
    const updateResult = await this.FreeDevBranchDetailModel.update(dbValues, {
      where: { dev_id: devId }
    });

    if (updateResult[0] === 0) throw Error(`SQL: 挂载失败`);

    // 4. 建立和迭代的关联关系
    await this.iterBranchService.insertFreeDevBranch(iterId, devId);

    return await this._get(devId, true) as IFreeDevBranch;
  }

  /**
   * 取消挂载
   * @param devId 开发分支id
   */
  async unmount(devId: number) {
    // 1. 前置判断
    const freeDevBranch = await this._get(devId, true);

    if (!freeDevBranch.iterId) throw Error(`游离开发分支 ${freeDevBranch.branchName} 未挂载迭代`);
    else if (freeDevBranch.status === EDevStatus.READY) throw Error(`游离开发分支 ${freeDevBranch.branchName} 处于 ${freeDevBranch.statusText} 状态，不能取消挂载`);

    // 3. 写库
    const dbValues = this._covertToDBData({
      mountStatus: EFreeDevMountStatus.UNMOUNT,
      iterId: null,
      modifier: this.ctx.user.name,
      gmtModified: formatDate(new Date()),
      modifierWorkid: this.ctx.user.workid
    });
    const updateResult = await this.FreeDevBranchDetailModel.update(dbValues, {
      where: { dev_id: devId }
    });
    if (updateResult[0] === 0) throw Error(`SQL: 取消挂载失败`);

    // 4. 移除和迭代的关联关系
    const iterBranch = await this.iterBranchService.get(freeDevBranch.iterId);
    if (iterBranch) {
      const freeDevBranchList = iterBranch.freeDevBranchList || [];
      const index = freeDevBranchList.indexOf(devId);
      if (index >= 0) {
        freeDevBranchList.splice(index, 1);
        await this.iterBranchService.update({
          iterId: freeDevBranch.iterId,
          freeDevBranchList
        });
      }
    }

    return await this._get(devId, true) as IFreeDevBranch;
  }

  /**
   * 如果项目类型是模块，需要更新投放列表里当前开发分支所挂载的迭代id
   * @param devId 开发分支id
   * @param iterId 迭代分支id
   */
  async mountComponent(devId: number, iterId: number) {
    // 1. 前置判断
    const freeDevBranch = await this._get(devId, true);

    if (freeDevBranch.iterId) throw Error(`游离开发分支 ${freeDevBranch.branchName} 已挂载到迭代（iterId: ${freeDevBranch.iterId}）`);

    if (freeDevBranch.branchType === EBranchType.COMPONENT) {
      const freeDevBranchRes = await this.componentDeliverService.list({ pageSize: 10, pageNum: 1, conditions: { devId, branchType: EDeliverBranchType.FREE_DEV_BRANCH } });
      if (freeDevBranchRes?.total) {
        freeDevBranchRes.list.forEach(async (item) => {
          // DB更新所挂载的iterId
          const updateResult = await this.componentDeliverService.update({ deliverId: item.deliverId, iterId });

          if (updateResult[0] === 0) throw Error(`SQL: 更新小程序游离开发分支所挂载的iterId失败`);
        })
      }
    }
  }

  /**
   * 如果项目类型是模块，需要移除投放列表里当前开发分支所挂载的迭代id
   */
  async unmountComponent(devId: number) {
    // 1. 前置判断
    const freeDevBranch = await this._get(devId, true);

    if (!freeDevBranch.iterId) throw Error(`游离开发分支 ${freeDevBranch.branchName} 未挂载迭代`);
    else if (freeDevBranch.status === EDevStatus.READY) throw Error(`游离开发分支 ${freeDevBranch.branchName} 处于 ${freeDevBranch.statusText} 状态，不能取消挂载`);

    if (freeDevBranch.branchType === EBranchType.COMPONENT) {
      const freeDevBranchRes = await this.componentDeliverService.list({ pageSize: 10, pageNum: 1, conditions: { devId, branchType: EDeliverBranchType.FREE_DEV_BRANCH } });
      if (freeDevBranchRes?.total) {
        freeDevBranchRes.list.forEach(async (item) => {
          // DB更新所挂载的iterId
          const updateResult = await this.componentDeliverService.update({ deliverId: item.deliverId, iterId: null });

          if (updateResult[0] === 0) throw Error(`SQL: 更新小程序游离开发分支所挂载的iterId失败`);
        })
      }
    }
  }
}
