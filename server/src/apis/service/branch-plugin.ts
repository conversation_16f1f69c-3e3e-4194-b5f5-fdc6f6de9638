import { SimpleGit } from 'simple-git';
import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import TowerHSF from '@/apis/hsf/tower';
import { uploadToBasement } from '@/apis/utils/file';
import { getAppStructure } from '@/apis/utils/package';
import { decodeQrCode } from '@/apis/utils/qrcode';
import EStoreService from '@/apis/service/estore';
import PackageService from '@/apis/service/package';
import ProjectService from '@/apis/service/project';
import IterBranchService from '@/apis/service/iter-branch';
import DingtalkService from '@/apis/service/dingtalk';
import DefService from '@/apis/service/def';
import { parseGitRepoInfo } from '@/apis/utils/git';

import { EProjectType } from '@/apis/const/project';
import { IGitRepoInfo } from '@/apis/interface/git-opts';
import { IBuildTask, ICreateTask } from '@/apis/interface/build';
import { IPackageInfo } from '@/apis/interface/package';
import { BIZ_LINE } from '@/apis/const';
import { useTmpDir, useGit } from '@/apis/middleware/hooks';
import * as chalk from 'chalk';
import { exec } from 'child_process';
import * as fse from 'fs-extra';
import * as path from 'path';
import { IIterBranch } from '@/apis/interface/iter-branch';
import { IDevBranch } from '@/apis/interface/dev-branch';
import { IFreeDevBranch } from '@/apis/interface/free-dev-branch';
import { EDefBuildTaskType } from '../const/def';
import { formatSubpackageSize, getPackageDiffInfo, getSizeLimitInfo, getBranchLimitNotice } from '@/apis/utils/size';


@Provide()
export default class BranchPluginService {
  @Inject()
  ctx!: Context;

  @Inject()
  towerHSF!: TowerHSF;

  @Inject()
  eStoreService!: EStoreService;

  @Inject()
  packageService!: PackageService;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  defService!: DefService;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  dingtalkService!: DingtalkService;

  /**
   * 获取 shrinkwrap.json
   */
  async getShrinkwrap({ projectTmpDirPath, gitHandler, gitRepoInfo, branchName }: IGetShrinkwrapParams): Promise<string> {
    this.ctx.logger.info(`开始生成 ${gitRepoInfo.project} 的 ${branchName} 分支的 shrinkwrap.json`);

    await gitHandler.checkout([branchName]);

    return new Promise((resolve, reject) => {
      // 生成 shrinkwrap.json
      exec('rm -rf node_modules && tnpm install --production && tnpm shrinkwrap', {
        cwd: projectTmpDirPath
      }, async err => {
        if (err) {
          reject(err);
          return;
        }

        try {
          // 上传 basement
          const remote = await gitHandler.listRemote(['--heads', gitRepoInfo.sshUrl, branchName]);
          const commitId = remote && remote.replace(/\W/, ' ').split(' ')[0];
          const shrinkwrapFile = await fse.readFile(path.join(projectTmpDirPath, 'npm-shrinkwrap.json'), { encoding: 'utf-8' });
          this.ctx.logger.info(chalk.green('getShrinkwrap fetch start'));
          const uploadRes = await uploadToBasement(`${gitRepoInfo.project}__${branchName}__${commitId}__npm-shrinkwrap.json`, shrinkwrapFile);
          if (!uploadRes?.url) throw Error('shrinkwrap 上传 basement 失败');

          this.ctx.logger.info(`生成 ${gitRepoInfo.project} 的 ${branchName} 分支的 shrinkwrap.json 成功: ${uploadRes.url}`);

          // 返回 shrinkwrap 地址
          resolve(uploadRes.url);
        } catch (err) {
          reject(err)
        }
      })
    })
  }

  /**
   * 自动化测试 - 打码环节
   */
  async build(
    branch: IIterBranch | IDevBranch | IFreeDevBranch,
    { gitHandler, gitRepoInfo }: { gitHandler: SimpleGit; gitRepoInfo: IGitRepoInfo; }
  ): Promise<IBuildTask> {
    const { projectName, branchName } = getBranchInfo(branch);
    const logName = `${gitRepoInfo.project} 的 ${branchName}分支`;
    this.ctx.logger.info(`自动化测试前置构建 >> 触发 ${logName}的构建`);

    // 1. 获取指定分支（最新提交）的 estore 打码任务
    const remote = await gitHandler.listRemote(['--heads', gitRepoInfo.sshUrl, branchName]);
    const commitId = remote && remote.replace(/\W/, ' ').split(' ')[0];

    // todo lingyue 启动了王守义的打码，

    let task = await this.eStoreService.getTask(projectName, { commitId, branch: branchName, env: 'online' });
    this.ctx.logger.info(chalk.green('自动化测试前置构建 >> 查询已有任务 ', commitId), task, logName);

    // 2. 如果没有打码任务，或任务失败，则新打码
    if (!task || task.status === -1) task = await this.eStoreService.build(projectName, {
      branch: branchName,
      userInfo: JSON.stringify({
        name: this.ctx.user.name,
        empId: this.ctx.user.workid
      }),
    });
    if (!task) throw Error(`自动化测试前置构建 >> ${logName} 创建打码任务失败`);

    // 3. 轮询构建结果，直至构建结束（成功或失败）或者超过 20 分钟
    const taskId = task.taskId;
    if (task.status === 1) {
      return task;
    } else if (task.status === 0) {
      return await new Promise((resolve, reject) => {
        let maxTime = 1000 * 60 * 20;
        const intervalId = setInterval(async () => {
          this.ctx.logger.info(`自动化测试前置构建 >> ${logName}轮询剩余期限 `, maxTime)
          const res = await this.eStoreService.getTask(projectName, { taskId });

          maxTime -= 1000 * 60;
          if (res?.status === -1) {
            clearInterval(intervalId);
            reject(Error(`自动化测试前置构建 >> ${logName}打码任务失败，任务id: ${taskId}`));
          } else if (res?.status === 1) {
            if (res.imgUrl) {
              this.ctx.logger.info(`自动化测试前置构建 >> ${logName}构建成功`);
              resolve(res);
            } else {
              reject(Error(`自动化测试前置构建 >> ${logName}打码成功但是没有生成对应的二维码，任务id: ${taskId}`));
            }
            clearInterval(intervalId);
          } else if (maxTime < 0) {
            clearInterval(intervalId);
            reject(Error(`自动化测试前置构建 >> ${logName}轮询打码结果超时，超时 20 分钟`));
          }
        }, 1000 * 60)
      });
    } else {
      throw Error(`自动化测试前置构建 >> ${logName}打码任务失败，任务id: ${taskId}`);
    }
  }

  /**
   * 创建自动化测试任务
   * TODO: onlyBuild是临时参数，为了限制只部分场景触发自动化测试，后面需要重构
   */
  async createAutomatedTestTask(
    branch: IIterBranch | IDevBranch | IFreeDevBranch,
    { qrCodeLink, dist, gitRepoInfo, projectTmpDirPath, onlyBuild }: { qrCodeLink: string; dist?: string; gitRepoInfo?: IGitRepoInfo; projectTmpDirPath?: string; onlyBuild?: boolean; }
  ): Promise<ICreateTask> {
    const { projectName, branchName, branchUrl, bizLine } = getBranchInfo(branch);
    const project = await this.projectService.get(projectName);
    if (!project) throw Error(`查询不到名称为 ${projectName} 的项目`); 

    let appType: string;
    if (project.type === EProjectType.WEIXIN) {
      appType = 'WX'
    } else if (project.type === EProjectType.ALIPAY) {
      appType = 'AP'
    } else {
      throw Error(`创建自动化测试任务 >> 不支持 ${project.type} 类型的项目`);
    }

    // 1. 计算包体积
    let packageResult;
    if (dist) {
      const { tmpDirPath, removeTmpDir } = useTmpDir();
      // 没有传入 projectTmpDirPath 或 gitRepoInfo，则需要先拉取一下代码
      if (!projectTmpDirPath || !gitRepoInfo) {
        const res = await useGit(tmpDirPath, {
          branchUrl,
          branchName,
          cloneSingleBranch: true
        });
        projectTmpDirPath = res.projectTmpDirPath;
        gitRepoInfo = res.gitRepoInfo;
      }

      try {
        const [packageSize, appStructure] = await Promise.all([
          this.packageService.getPackageSize(dist, tmpDirPath, true, branchName),
          getAppStructure({
            projectTmpDirPath,
            gitRepoInfo,
            branchName,
          })
        ])
        const packageInfoMap = appStructure.packages.reduce((per, cur) => {
          per[cur.packageName] = cur;
          return per;
        }, {} as { [key: string]: IPackageInfo })
        packageResult = {
          ...packageSize,
          subPackage: packageSize.subPackage.map(item => {
            return {
              ...item,
              bizLine: packageInfoMap[item.subPackageName]?.bizType?.name || 'unknown'
            }
          })
        }
      } catch (err) {
        this.ctx.logger.info(`创建自动化测试任务 >> 构建产物 ${dist} 计算包体积失败`)
      }
      removeTmpDir();
    } else {
      this.ctx.logger.info(`创建自动化测试任务 >> 无构建产物`)
    }

    // 2. 解析二维码
    let miniAppPageUrl = '';
    try {
      miniAppPageUrl = await decodeQrCode(qrCodeLink);
    } catch (err) {
      throw Error(`创建自动化测试任务 >> 解析二维码 ${qrCodeLink} 失败`)
    }

    // 3. 创建塔台任务
    let towerId = '';
    if (!onlyBuild) {
      try {
        towerId = await this.towerHSF.createTowerTask({
          appType,
          creator: this.ctx.user.name,
          branchName,
          testManager: '',
          url: qrCodeLink,
          miniAppPageUrl,
          businessLine: bizLine,
          packageResult: packageResult ? JSON.stringify(packageResult) : '',
        });
      } catch(err) {
        this.ctx.logger.info(err);
      }
    }

    return {
      towerId,
      packageResult,
    }
  }

  /**
   * 构建并创建自动化测试任务
   * TODO: onlyBuild是临时参数，为了限制只部分场景触发自动化测试，后面需要重构
   */
  async buildAndCreateAutomatedTestTask(branch: IIterBranch | IDevBranch | IFreeDevBranch, onlyBuild = true) {
    const { branchName, branchUrl } = getBranchInfo(branch);
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const { gitHandler, gitRepoInfo, projectTmpDirPath } = await useGit(tmpDirPath, {
      branchUrl,
      branchName,
      cloneSingleBranch: true
    });

    // 构建
    console.log('lingyue buildAndCreateAutomatedTestTask >>>>>>>> ', branch)

    const buildTask = await this.build(branch, {
      gitHandler,
      gitRepoInfo
    });

    console.log('buildAndCreateAutomatedTestTask buildTask end >>>>>>>> ', gitRepoInfo.project, branchName, buildTask)

    // 创建自动化测试任务
    let taskData: any = {};
    try {
      taskData = await this.createAutomatedTestTask(branch, {
        qrCodeLink: buildTask.imgUrl,
        dist: buildTask.dist,
        projectTmpDirPath,
        gitRepoInfo,
        onlyBuild
      })
    } catch (err) {
      this.ctx.logger.info(err);
    }

    removeTmpDir();

    return {
      dist: buildTask.dist,
      towerId: taskData.towerId,
      packageResult: taskData.packageResult,
    }
  }

  /** 添加体积分析文件 */
  async getReportAnalyze(dist: string, branchName: string, uploadReportAnalyzed: boolean = true) {
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const res = await this.packageService.getPackageSize(dist, tmpDirPath, uploadReportAnalyzed, branchName);
    removeTmpDir();
    return res;
  }

  async defBuildAndCreateAutomatedTestTask(rcBranchName: string, aoneIdList: any, iterBranch: IIterBranch, project: any) {
    const miniType = project.type;
    const miniAppId = project.clientList[0].miniAppId;
    // lingyue 查询该分支是否存在，如果分支已存在，check分支是否符合规范，如果不规范则删除，若规范则创建发布任务
    // 需要新建的分支
    const { group, project: gitProject } = parseGitRepoInfo(iterBranch.gitBranch.url)!;
    const repo = `${group}/${gitProject}`

    // 检查分支要用git的分支名
    const checkRes = await this.defService.getIterationId({
      repo, 
      branch: rcBranchName
    })

    let needCreateDefBranch = true;
    let newIterId = checkRes?.data?.id;

    if (checkRes?.success) {
      if (checkRes?.data?.name !== checkRes?.data?.version) {
        const delRes = await this.defService.abandonIteration({
          iterationId: checkRes?.data?.id
        })
        if (delRes?.success) {
          newIterId = '';
        }
        console.log(`lingyue delRes >>>>>>>>>>> ${JSON.stringify(delRes)}`)
      } else {
        needCreateDefBranch = false;
      }
    } 
    
    if (needCreateDefBranch) {
      // 更新已有迭代
      const existDefIteraRes = await this.defService.createIteration({
        repo,
        name: iterBranch.version,
        description: iterBranch.description,
        branch: rcBranchName,
        version: iterBranch.version,
        branchType: 'exist',
        aoneBinds: aoneIdList
      });
      newIterId = existDefIteraRes?.data?.id;
      console.log(`lingyue existDefIteraRes >>>>>>>>>> ${JSON.stringify(existDefIteraRes)}`);
      // 更新迭代失败则重新创建迭代
      if (!existDefIteraRes?.success) {
        const createDefIteraRes = await this.defService.createIteration({
          repo,
          name: iterBranch.version,
          description: iterBranch.description,
          branch: rcBranchName,
          version: iterBranch.version,
          trunk: 'master',
          branchType: 'new',
          aoneBinds: aoneIdList
        });
        newIterId = createDefIteraRes?.data?.id;
      }
    }

    // 设置小程序版本号，
    const setRes = await this.defService.setMiniAppVersion({
      iterId: newIterId,
      version: iterBranch.version,
      miniappId: miniAppId,
      type: `APP_${miniType.toUpperCase()}`
    });

    console.log(`lingyue def setres >>>>>>>>>> ${JSON.stringify(setRes)} >>>> ${JSON.stringify(newIterId)}`)

    if (setRes.success) {
      // 创建发布任务
      const publishRes = await this.defService.createIterPublishTask({
        updateDB: true,
        taskType: EDefBuildTaskType.ITERATION,
        iterId: iterBranch.iterId,
        pub_env: 3,
        iterationId: newIterId,
      });
      console.log(`lingyue def publish >>>>>>>>>> ${JSON.stringify(publishRes)}`)
      return publishRes.success;
    }
    return false
  }

  /** 分支体积分析 */
  async analyzeBranchSize(dist: string, projectName: string, branchName: string, gmtModified?: string, noticeUsers?: string) {
    const lastPublishedIter = await this.iterBranchService.getPrevPublishedIter(projectName, gmtModified);
    if (!lastPublishedIter?.dist && !lastPublishedIter?.defUploadPkgRes) {
      return {
        msg: '上次已发布的迭代体积信息缺失',
      };
    }
    const getIterSize = async () => {
      if (lastPublishedIter?.dist) {
        return await this.getReportAnalyze(lastPublishedIter.dist, '', false)
      }
      const uploadRes = JSON.parse(lastPublishedIter.defUploadPkgRes);
      const formatedSubs = formatSubpackageSize(uploadRes.uploadResult.subPackageInfo);
      let totalInfo = {};
      const subPackage: any[] = [];
      formatedSubs.forEach(pkg => {
        if (pkg.subPackageName === 'total') {
          Object.assign(totalInfo, pkg);
          return;
        }
        subPackage.push(pkg);
      });
      return {
        ...totalInfo,
        subPackage,
      };
    }
    const [branchSize, iterSize] = await Promise.all([
      this.getReportAnalyze(dist, '', false),
      getIterSize(),
    ]);
    const prevIterVersion = lastPublishedIter.version;
    const packageInfo = getPackageDiffInfo(iterSize, branchSize, prevIterVersion, branchName);
    const limitInfo = getSizeLimitInfo(packageInfo);
    const { limit, warnMsg } = limitInfo;
    // 发送钉钉通知
    if ((limit || warnMsg.length) && noticeUsers) {
      const userList = Array.from(new Set(noticeUsers.split(',')));
      const content = getBranchLimitNotice(limitInfo, projectName, branchName);
      this.dingtalkService.notice(userList, '小程序体积告警', content)
    }

    return {
      packageInfo,
      limitInfo,
      version: branchName,
      success: true,
      prevIterVersion,
    }
  }

  async getBuildTasks(projectName: string, branchName: string) {
    return this.eStoreService.getTasks(projectName, { branch: branchName, env: 'online' });
  }
}

function getBranchInfo(branch: IIterBranch | IDevBranch | IFreeDevBranch) {
  let branchName: string;
  let branchUrl: string;
  let bizLine: BIZ_LINE | 'all';
  let projectName: string;

  // 如果有 publishDay 则判断是迭代分支
  if ((branch as IIterBranch).publishDay) {
    const iterBranch = branch as IIterBranch;
    if (!iterBranch.rcGitBranch) throw Error(`迭代 ${iterBranch.version} 没有 rc 分支`)

    projectName = iterBranch.projectName;
    branchName = iterBranch.rcGitBranch.name;
    branchUrl = iterBranch.rcGitBranch.url;
    bizLine = 'all';
  }
  // 否则是开发分支
  else {
    const devBranch = branch as IDevBranch;
    if (!devBranch.projectName) throw Error(`开发分支 ${devBranch.branchName} projectName 为空`)
    if (!devBranch.bizLine) throw Error(`开发分支 ${devBranch.branchName} bizLine 为空`)

    branchName = devBranch.gitBranch.name;
    branchUrl = devBranch.gitBranch.url;
    projectName = devBranch.projectName;
    bizLine = devBranch.bizLine;
  }

  return {
    branchName,
    branchUrl,
    projectName,
    dist: branch.dist,
    bizLine
  }
}

interface IGetShrinkwrapParams {
  projectTmpDirPath: string;
  gitHandler: SimpleGit;
  gitRepoInfo: IGitRepoInfo;
  branchName: string;
}