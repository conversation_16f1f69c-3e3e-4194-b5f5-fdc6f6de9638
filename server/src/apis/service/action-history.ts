import { Context } from '@midwayjs/faas';
import { Inject, Provide } from '@midwayjs/core';
import { IActionRecord } from '@/apis/interface/action-history';

@Provide()
export default class ActionHistoryService {
  @Inject()
  ctx!: Context

  init<T = string>(action: T, title: string) {
    return [{
      action,
      title,
      timestamp: Date.now(),
      userName: this.ctx.user.name,
      workid: this.ctx.user.workid,
    }]
  }

  add<T = string>(action: T, title: string, history: IActionRecord<T>[]) {
    return history.concat({
      action,
      title,
      timestamp: Date.now(),
      userName: this.ctx.user.name,
      workid: this.ctx.user.workid,
    })
  }
}