import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import simpleGit, { SimpleGit } from 'simple-git';
import * as path from 'path';
import * as fse from 'fs-extra';
import * as chalk from 'chalk';
import { platform, tmpdir } from 'os';
import axios from 'axios';
import { isServer } from '@/apis/utils';
import * as gitUtils from '@/apis/utils/git';
import { modifyAppVersion, modifyPackageVersion } from '@/apis/utils/package';
import { requireNoCache } from '@/apis/utils/require';
import {
  IGitRepoInfo, IGitOptResult,
  IGitIterCreateParams,
  IGitIterAltersMergeParams,
  IPkgJSONParams,
  IGitOptTypeConf
} from '@/apis/interface/git-opts';
import { INpmMergeError } from '@/apis/interface/npm';
import { IProject } from '@/apis/interface/project';
import { USER_CONFIG } from '@/apis/config/git';
import { useTmpDir, useGit } from '@/apis/middleware/hooks';
import { EMergeCode } from '@/apis/const/dev-branch';

const pwd = process.cwd();
// 应用目录是只读的不允许写文件和目录, Read-only file system. 用系统临时目录 /tmp
const repoPlaceDir = tmpdir();
const binDir = path.join(pwd, isServer ? 'bin' : '../server/bin');
const privateKeyFile = path.join(binDir, './ssh/private');
const GIT_SSH_COMMAND = `ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no -i ${privateKeyFile}`;
const gitBin = path.join(binDir, './git');
const gitConfig = {
  baseDir: repoPlaceDir,
  binary: platform() === 'darwin' ? 'git' : gitBin,
  maxConcurrentProcesses: 6,
};
const COMMIT_PREFIX = '【一体化研发平台】'

@Provide()
export default class GitService {
  @Inject()
  ctx!: Context;

  GIT_OPT_MAPS = {
    CREATE_ITER: {
      code: 'GIT_NEW_ITER_ERR',
      errorMessageCtx: 'create iteration error'
    },
    CREATE_ALTER: {
      code: 'GIT_NEW_ALTER_ERR',
      errorMessageCtx: 'create alter error'
    },
    UPDATE_ALTER: {
      code: 'GIT_UPDATE_ALTER_ERR',
      errorMessageCtx: 'update alter error'
    },
    DELETE_ALTER: {
      code: 'GIT_DEL_ALTER_ERR',
      errorMessageCtx: 'delete alter error'
    },
    CREATE_RC: {
      code: 'GIT_NEW_RC_ERR',
      errorMessageCtx: 'new rc error'
    },
    PUBLISH_RC: {
      code: 'GIT_PUB_RC_ERR',
      errorMessageCtx: 'publish rc error'
    },
    GET_REPO_INFO: {
      code: 'GIT_GET_REPO_ERR',
      errorMessageCtx: 'get repo info error'
    }
  }

  getErrorMessage(type, msg) {
    const conf = this.GIT_OPT_MAPS[type];
    const { errorMessageCtx: prefix, code: errorCode } = conf;
    const errorMessage = `${prefix}, ${msg || '-_-'}`;
    return {
      success: false,
      errorCode,
      errorMessage
    };
  }

  tryCatchRun(type: IGitOptTypeConf, fn) {
    try {
      return fn();
    } catch (e: any) {
      let typeConf;
      if (typeof type === 'string') {
        typeConf = {
          type,
        }
      } else {
        typeConf = type;
      }

      const res = this.getErrorMessage(typeConf, e && e.message);
      const { errorMessage } = res;
      console.error(chalk.bgRed(' GIT_OPT_ERR ') + chalk.red(errorMessage));
      console.error(e);
      return {
        ...res,
        ...(typeConf.errRes || {})
      };
    }
  }

  async createIteration(params: IGitIterCreateParams, project: IProject): Promise<IGitOptResult> {
    return this.tryCatchRun('CREATE_ITER', async () => {
      const { gitRepoUrl, gitBranch, description: iterationDesc } = params;
      const { git, sshUrl, projectRepoDir, repoName, repoInfo } = initGit(gitRepoUrl);
      const version = getIterVersion(gitBranch);

      // check remote branch exists.
      const isBranchExist = await git.listRemote(['--heads', sshUrl, gitBranch]);
      if (isBranchExist) {
        throw new Error(`branch ${gitBranch} already exists`);
      }

      // check if local repo is exists.
      const isExists = await fse.pathExists(projectRepoDir);
      if (isExists) {
        await fse.remove(projectRepoDir);
      }

      // new branch
      const commitDesc = iterationDesc || `新建迭代 ${gitBranch}`;
      await git.clone(sshUrl, repoName, {
        '--depth': 1,
        '--no-single-branch': null,
        // '--shallow-since': '4.weeks.ago'
      });
      await git
        .cwd(projectRepoDir)
        .addConfig('user.name', USER_CONFIG.NAME)
        .addConfig('user.email', USER_CONFIG.EMAIL);
      await git.checkoutBranch(gitBranch, 'HEAD');

      // update version of package.json
      const pkgJSONPath = path.join(projectRepoDir, './package.json');
      const pkgJSON = requireNoCache(pkgJSONPath);
      pkgJSON.version = version;
      await fse.outputJSON(pkgJSONPath, pkgJSON, { spaces: 2 });

      // 注入版本号到 app.js
      await modifyAppVersion(version, projectRepoDir, repoInfo);

      // commit and push remote
      await git
        .add('-A')
        .commit(`${COMMIT_PREFIX}${commitDesc}`)
        .push(['-u', 'origin', gitBranch]);

      console.log(chalk.green('\n>> new iteration branch success'));
      return {
        success: true,
      };
    });
  }

  async mergeIterAlters(gitHandler: SimpleGit, {
    iterBranchName,
    rcBranchName,
    gitRepoInfo,
    projectTmpDirPath,
    alterBranchList,
    isComponentProject,
    version,
  }: IGitIterAltersMergeParams): Promise<{
    errorMsg?: string;
    /** 所属开发分支的 merge 结果列表 */
    singleResList?: IGitOptResult[];
  }> {
    // 检测 rc 分支是否存在，存在的话先删除
    const isRcBranchExists = await gitHandler.listRemote(['--heads', gitRepoInfo.sshUrl, rcBranchName]);
    if (isRcBranchExists) {
      console.log(chalk.yellow(`\n>> rc branch ${rcBranchName} is exists, delete it.`));
      await gitHandler.push(['origin', '-d', rcBranchName]);
    }

    // 检出 rc 分支
    await gitHandler
      .checkoutBranch(rcBranchName, 'HEAD');
    console.log(chalk.green(`\n>> rc branch ${rcBranchName} checkout from ${iterBranchName} ok`));
    const pkgJSONPath = path.join(projectTmpDirPath, 'package.json');
    // const initialPkgJSON = await fse.readJSON(pkgJSONPath);
    // const initialVersion = initialPkgJSON.version;
    const pkgAppDevPath = path.join(projectTmpDirPath, './src/app.dev.json');

    // TODO:增加“合并代码”模式和“更新依赖包”模式 npm 版本号冲突的判断

    // 处理集成模式为“合并代码”的开发分支
    const needMergeBrList = alterBranchList.filter(br => br.mergeCode === EMergeCode.YES);
    const mergeBrSingleResList = await needMergeBrList.reduce(async function (previousPromise, alterBranchInfo): Promise<IGitOptResult[]> {
      const accumulator = await previousPromise;
      const { branch } = alterBranchInfo;
      console.log(`>> merge ${branch} to ${rcBranchName} start`);
      try {
        // 判断app.dev.json是否存，如果存在，则删除
        if (fse.existsSync(pkgAppDevPath)) {
          await fse.unlink(pkgAppDevPath);
        }
        // 如果项目是模块类型，需要先在rc分支上改version为其他变更的version，这样才不会冲突。之后会再改回真正版本
        if (isComponentProject) {
          const matchRes1 = /^free-dev\/\d{8}-(\d+\.\d+\.\d+)-/ig.exec(branch);
          const matchRes2 = /^dev\/(?:\d+\.\d+\.\d+)-(\d+\.\d+\.\d+)-/ig.exec(branch);
          if (matchRes1?.length || matchRes2?.length) {
            const ver = matchRes1?.[1] || matchRes2?.[1];
            ver && await modifyPackageVersion(ver, projectTmpDirPath);
            await gitHandler.add('-A').commit(`${COMMIT_PREFIX}【冻结集成】临时解决版本号冲突，后续会改回真正版本`);
          }
        }
        await gitHandler.merge([`origin/${branch}`, '--allow-unrelated-histories', '--no-ff', '--no-edit', '-m', `${COMMIT_PREFIX}【冻结集成】Merge branch 'origin/${branch}' into ${rcBranchName}`]);
        console.log(chalk.green(`>> merge ${branch} to ${rcBranchName} success`));

        return [
          ...accumulator,
          {
            success: true,
            branch,
          }
        ];
      } catch (err: any) {
        const mergeSummary = err.git;
        const conflicts = mergeSummary?.conflicts || [];
        const errMessage = conflicts.length ? `merge ${branch} to ${rcBranchName} failed, ${conflicts.length} conflicts` : err.message;
        console.error(chalk.red(`\n>> ${errMessage}`));

        return [
          ...accumulator,
          {
            success: false,
            branch,
            errorMessage: errMessage
          }
        ];
      }
    }, Promise.resolve([] as IGitOptResult[]));

    // 如果失败则直接返回错误
    if (mergeBrSingleResList.some(o => !o.success)) {
      const errorMsg = mergeBrSingleResList.find(o => !o.success)?.errorMessage || 'merge dev branch to rc failed';
      return {
        errorMsg,
        singleResList: mergeBrSingleResList
      };
    }

    // 再做一次判断，如果 app.dev.json存在，则删除
    if (fse.existsSync(pkgAppDevPath)) {
      await fse.unlink(pkgAppDevPath);
    }

    // 处理集成模式为“更新依赖包”的开发分支
    const justNpmBrList = alterBranchList.filter(br => br.mergeCode === EMergeCode.NO);
    const mergeNpmRes = mergeBranchNpmList(justNpmBrList);
    const mergeNpmSingleResList = mergeNpmRes.singleResList;

    if (!mergeNpmRes.success) {
      return {
        errorMsg: mergeNpmRes.errorMessage,
        singleResList: mergeNpmSingleResList,
      };
    }

    const npmDeps = Object.keys(mergeNpmRes.npmDeps).reduce((res, name) => {
      res[name] = mergeNpmRes.npmDeps[name].version;
      return res;
    }, {});
    const npmResolutions = Object.keys(mergeNpmRes.npmResolutions).reduce((res, name) => {
      res[name] = mergeNpmRes.npmResolutions[name].version;
      return res;
    }, {});
    if (Object.keys(npmDeps).length || Object.keys(npmResolutions).length) {
      await updatePkgJSON(gitHandler, projectTmpDirPath, { dependencies: npmDeps, resolutions: npmResolutions }, `${COMMIT_PREFIX}【冻结集成】更新依赖`);
    }

    // commit and push remote
    const statusRes = await gitHandler.status(['-s']);
    if (!statusRes.isClean()) {
      await gitHandler
        .add('-A')
        .commit(`${COMMIT_PREFIX}【冻结集成】集成到 ${rcBranchName}`);
    }

    // 如果最终版本和初始版本不一致，则做一次修正
    const finallyPkgJSON = await fse.readJSON(pkgJSONPath);
    const finallyVersion = finallyPkgJSON.version;
    if (version !== finallyVersion) {
      await modifyPackageVersion(version, projectTmpDirPath);
      await modifyAppVersion(version, projectTmpDirPath, gitRepoInfo);
      await gitHandler.add('-A').commit(`${COMMIT_PREFIX}【冻结集成】修正版本号`);
    }

    await gitHandler.push(['-u', 'origin', rcBranchName]);
    console.log(chalk.green('\n>> new rc branch success'));

    return {
      singleResList: [...mergeNpmSingleResList, ...mergeBrSingleResList]
    };
  }

  /**
   * 发布
   * @param rcBranchName rc 分支名
   * @param rcBranchUrl rc 分支地址
   * @returns
   */
  async publishIter(rcBranchName: string, rcBranchUrl: string,): Promise<void> {
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const { gitHandler, projectTmpDirPath } = await useGit(tmpDirPath, { branchUrl: rcBranchUrl, branchName: 'master' })

    // 1. Merge rc 分支到 master
    const mergeCommitMessage = `${COMMIT_PREFIX}Merge branch 'origin/${rcBranchName}' into master`;
    try {
      await gitHandler.merge([`origin/${rcBranchName}`,'--allow-unrelated-histories', '-m', mergeCommitMessage]);
    } catch (err) {
      // 如果 merge 失败，则删除 shrinkwrap.json 再试
      const shrinkwrapJSONPath = path.join(projectTmpDirPath, 'shrinkwrap.json');
      if (fse.existsSync(shrinkwrapJSONPath)) {
        fse.unlinkSync(shrinkwrapJSONPath);
        await gitHandler.reset(['--hard']).add('-A').commit(`${COMMIT_PREFIX}临时删除 shrinkwrap.json，防止 merge 时冲突`);
        await gitHandler.merge([`origin/${rcBranchName}`, '--allow-unrelated-histories', '-m', mergeCommitMessage]);
      } else {
        throw err;
      }
    }

    // 2. 打标
    const version = getIterVersion(rcBranchName);
    const tagName = `v${version}`;
    const isTagExist = await gitHandler.listRemote(['--tags', 'origin', `refs/tags/${tagName}`]);
    if (isTagExist) {
      await gitHandler.push(['origin', '-d', 'tag', tagName]);
      await gitHandler.tag(['-d', tagName]);
    }
    await gitHandler.addAnnotatedTag(tagName, `${COMMIT_PREFIX}publish ${tagName}`);
    await gitHandler.push(['origin', 'master', '--tags']);

    // 3. 删除临时文件
    removeTmpDir()

    console.log(chalk.green(`\n>> publish ${tagName} success`));
  }

  /**
   * 同步分支
   * @param branchName 分支名
   * @param branchUrl 分支地址
   * @param targetBranchName 目标分支名
   * @returns
   **/
  async syncBranch(branchName: string, branchUrl: string, targetBranchName: string): Promise<void> {
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const { gitHandler, projectTmpDirPath, gitRepoInfo } = await useGit(tmpDirPath, { branchUrl })

    // 1. 检查分支是否存在
    const branchIsExist = await gitHandler.listRemote(['--heads', gitRepoInfo.sshUrl, branchName]);
    if (!branchIsExist) throw Error(`代码分支 ${branchName} 不存在`)

    // 2. 检查目标分支是否存在
    const targetBranchIsExist = await gitHandler.listRemote(['--heads', gitRepoInfo.sshUrl, targetBranchName]);
    if (!targetBranchIsExist) throw Error(`目标代码分支 ${targetBranchName} 不存在`)

    // 3. 先 checkout 到目标分支，获取版本号
    const pkgJSONPath = path.join(projectTmpDirPath, 'package.json');
    await gitHandler.checkout([targetBranchName])
    const targetBranchPkgJSON = await fse.readJSON(pkgJSONPath);
    const targetBranchVersion = targetBranchPkgJSON.version;

    // 4. 再 checkout 到源分支，临时修改版本号，防止 merge 时冲突
    await gitHandler.checkout([branchName])
    const sourceBranchPkgJSON = fse.readJSONSync(pkgJSONPath);
    const sourceBranchVersion = sourceBranchPkgJSON.version;
    await modifyPackageVersion(targetBranchVersion, projectTmpDirPath);
    await modifyAppVersion(targetBranchVersion, projectTmpDirPath, gitRepoInfo);
    await gitHandler.add('-A').commit(`${COMMIT_PREFIX}临时修改版本号，防止 merge 时冲突`);

    // 5. merge 到目标分支
    const mergeCommitMessage = `${COMMIT_PREFIX}Merge branch '${targetBranchName}' into ${branchName}`;
    try {
      await gitHandler.merge([targetBranchName, '-m', mergeCommitMessage]);
    } catch (err) {
      // 如果 merge 失败，则删除 shrinkwrap.json 再试
      const shrinkwrapJSONPath = path.join(projectTmpDirPath, 'shrinkwrap.json');
      if (fse.existsSync(shrinkwrapJSONPath)) {
        fse.unlinkSync(shrinkwrapJSONPath);
        await gitHandler.reset(['--hard']).add('-A').commit(`${COMMIT_PREFIX}临时删除 shrinkwrap.json，防止 merge 时冲突`);
        await gitHandler.merge([targetBranchName, '-m', mergeCommitMessage]);
      } else {
        throw err;
      }
    }

    // 6. 改回原版本号
    await modifyPackageVersion(sourceBranchVersion, projectTmpDirPath);
    await modifyAppVersion(sourceBranchVersion, projectTmpDirPath, gitRepoInfo);

    // 7. commit & push
    const statusRes = await gitHandler.status(['-s']);
    if (!statusRes.isClean()) {
      await gitHandler
        .add('-A')
        .commit(`${COMMIT_PREFIX}${branchName} 同步 ${targetBranchName} 成功`);
    }
    await gitHandler.push(['-u', 'origin', branchName]);

    // 8. 删除临时文件
    removeTmpDir()
  }

  /**
   * 查询gitlab现有分支(被删除的查不到)
   * @param params { projectId: git项目id; page: 页码; per_page: 每页个数（默认20）; }
   * @returns
   */
   async getBranches({ projectId, page, per_page }: { projectId: number; page?: number; per_page?: number; }) {
    try {
      const res = await axios({
        method: 'GET',
        url: `https://gitlab.alibaba-inc.com/api/v3/projects/${projectId}/repository/branches?private_token=WRgySzoAcxjYWCCVoazs`,
        data: {
          projectId,
          page: page || 1,
          per_page: per_page || 20,
        }}
        )
      if (res.data && res.status === 200) {
        return res.data;
      } else {
        return {
          errorMsg: res.statusText || "git服务请求失败，请联系丝木",
        };
      }
    } catch (err: any) {
      return {
        errorMsg: err?.message,
      };
    }
  }

  /**
   * 查询git branch -a 分支
   * @param params
   * @returns
   */
  async getRemoteBranches({ url }: { url: string }) {
    try {
      // 拉取代码
      const { tmpDirPath, removeTmpDir } = useTmpDir();
      const { gitHandler } = await useGit(tmpDirPath, {
        branchUrl: url,
      });

      const branchList = await gitHandler.branch();
      // console.log('branchList', JSON.stringify(branchList.all));
      removeTmpDir();
      return branchList.all;
    } catch (err: any) {
      return {
        errorMsg: err?.message,
      };
    }
  }

}

export function initGit(gitRepoUrl: string) {
  const repoInfo = getRepoInfo(gitRepoUrl);
  const { group = '', project = '', sshUrl } = repoInfo;
  const repoName = `${group}-${project}`;
  const projectRepoDir = path.join(repoPlaceDir, repoName);
  const git = simpleGit(gitConfig).env({ GIT_SSH_COMMAND });
  return {
    git,
    sshUrl,
    projectRepoDir,
    repoName,
    repoInfo,
  };
}

function getRepoInfo(repoUrl): IGitRepoInfo {
  const res = gitUtils.parseGitRepoInfo(repoUrl);
  if (!(res && res.group && res.project)) {
    throw new Error(`parse git repo info fail`);
  }
  return res;
}

function getIterVersion(gitBranch) {
  // 去除前缀，提取迭代分支的版本号
  const res = gitBranch.match(/^(\w+\/)(.+)$/);
  if (res && res[2]) return res[2];
  throw new Error(`the version ${gitBranch} is illegal`);
}

async function updatePkgJSON(git, projectRepoDir, json: IPkgJSONParams = {}, message = '') {
  const pkgJSONPath = path.join(projectRepoDir, 'package.json');
  const pkgJSON = requireNoCache(pkgJSONPath);

  if (json.version) {
    pkgJSON.version = json.version;
  }

  if (json.dependencies) {
    pkgJSON.dependencies = {
      ...pkgJSON.dependencies,
      ...json.dependencies
    }
  }

  if (json.resolutions) {
    pkgJSON.resolutions = {
      ...pkgJSON.resolutions,
      ...json.resolutions
    }
  }

  await fse.outputJSON(pkgJSONPath, pkgJSON, { spaces: 2 });

  await git
    .add('-A')
    .commit(message || `${COMMIT_PREFIX}更新 package.json`);
  return git;
}

function mergeBranchNpmList(brList) {
  const res = {
    success: true,
    errorMessage: '',
    npmDeps: {},
    npmResolutions: {},
  };
  const singleResList: IGitOptResult[] = [];
  const npmDeps = {};
  const npmResolutions = {};
  const mergeNpm = (npmList, resVar, branch, filedName: string) => {
    let success = true;
    const mergeErrors: INpmMergeError[] = [];
    npmList.map(({ name, value }) => {
      if (!resVar[name]) {
        resVar[name] = {
          version: value,
          branch
        }
      } else {
        // 目前直接判断相同，视情况做 semver 支持
        if (resVar[name].version !== value) {
          success = false;
          mergeErrors.push({
            errorMessage: `${filedName} ${name}@${value} (in branch ${branch}) is conflict with ${name}@${resVar[name].version} (in branch ${resVar[name].branch})`,
            npm: name,
            version: value,
            conflictBranch: resVar[name].branch,
            conflictVersion: resVar[name].version
          })
        }
      }
    });

    return {
      success,
      mergeErrors
    };
  };
  const mergeResList = brList.map(br => {
    const { npmList, npmResolutionList, branch } = br;
    const { success: mergeNpmDepsSuccess, mergeErrors: mergeNpmDepsErrors } = mergeNpm(npmList, npmDeps, branch, 'dependencies');
    const { success: mergeNpmResolutionsSuccess, mergeErrors: mergeNpmResolutionsErrors } = mergeNpm(npmResolutionList, npmResolutions, branch, 'resolutions');

    singleResList.push({ branch, success: mergeNpmDepsSuccess && mergeNpmResolutionsSuccess })
    return mergeNpmDepsErrors.concat(mergeNpmResolutionsErrors);
  });

  const errorList = mergeResList.reduce((res, mergeError) => {
    if (mergeError.length) {
      res = res.concat(mergeError);
    }
    return res;
  }, []);

  if (errorList.length) {
    res.success = false;
    res.errorMessage = errorList.map(err => err.errorMessage).join(' \n');
  } else {
    res.npmDeps = npmDeps;
    res.npmResolutions = npmResolutions;
  }

  return { ...res, singleResList };
}
