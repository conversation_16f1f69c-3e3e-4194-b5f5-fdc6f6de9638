import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { Op } from 'sequelize';
import { convertsCollectionToCamel, convertsCollectionToSnake, formatDate } from '@/apis/utils';
import { IIterBranch, EChecked } from '@/apis/interface/iter-branch';
import { IProject } from '@/apis/interface/project';
import { IUser } from '@/apis/interface/user';
import DevBranchService from '@/apis/service/dev-branch';
import ProjectService from '@/apis/service/project';
import DefService from '@/apis/service/def';
import UserService from '@/apis/service/user';
import { IIterBranchDetailModel } from '@/apis/model/iter-branch-detail';
import { ITER_STATUS_TEXT, IterStatus, EBranchType } from '@/apis/const/iter-branch';
import { EClient } from '@/apis/const/iter-deliver';
import { EProjectType } from '@/apis/const/project';
import { EDefBuildStatus, EPubTypeEnv } from '@/apis/const/def';
import GitService from './git';
import * as gitUtils from '../utils/git';

@Provide()
export default class IterBranchService {
  @Inject()
  ctx!: Context;

  @Inject()
  IterBranchDetailModel!: IIterBranchDetailModel;

  @Inject()
  devBranchService!: DevBranchService;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  defService!: DefService;

  @Inject()
  userService!: UserService;

  async list(pageSize: number, pageNum: number, conditions: {
    /** 项目名称 */
    projectName?: string;
    /** 状态 */
    statusList?: IterStatus[];
    /** 迭代版本 */
    version?: string;
  }) {
    const whereOptions = convertsCollectionToSnake(conditions);

    if (whereOptions.status_list) {
      whereOptions.status = { [Op.in]: whereOptions.status_list };
      delete whereOptions.status_list;
    }

    const { rows, count } = await this.IterBranchDetailModel.findAndCountAll({
      where: whereOptions,
      order: [['iter_id', 'DESC']],
      limit: pageSize,
      offset: pageSize * (pageNum - 1),
      raw: true
    });

    return {
      list: rows.map(item => this.parseData(item)),
      total: count
    }
  }

  async create(values: IIterBranchCreateValues): Promise<IIterBranch> {
    let defIterId: number | undefined;
    const isComponentProject = values.project.type === EProjectType.COMPONENT;

    // 1. 先判断 version / publishDay 是否已存在
    const exitIterBranch = await this.IterBranchDetailModel.findOne({
      where: {
        [Op.or]: [
          {
            version: values.version
          },
          {
            publish_day: values.publishDay
          }
        ],
        [Op.and]: [
          {
            project_name: values.projectName
          }
        ]
      },
      raw: true,
    });

    if (exitIterBranch) throw new Error(`迭代${values.version}已存在或${values.publishDay}已有迭代`);

    // 2. git 操作
    const gitOpts = new GitService();
    const gitBranch = `stable/${values.version}`;
    const gitRepoUrl = values.project.gitRepo;
    const gitOptRes = await gitOpts.createIteration({
      gitRepoUrl,
      gitBranch,
      description: values.description
    }, values.project);
    if (!gitOptRes.success) throw new Error(gitOptRes.errorMessage);

    // 3. 如果是组件形式，需要为组件创建def迭代
    if (isComponentProject) {
      const res = await this.defService.createIteration({
        repo: `${values.project.group}/${values.project.project}`,
        name: `${values.publishDay}发布迭代（分支号${gitBranch}）`,
        description: values.description,
        // stable分支不需要绑定到迭代上，后面冻结会绑定rc分支
        // branch: gitBranch,
        version: values.version,
        trunk: 'master',
      });

      if (res?.success && res?.data) {
        defIterId = res.data.id;
      } else {
        throw Error(`${values.projectName} 项目创建def迭代失败（${res.errorMsg}）`);
      }
    }

    // 初始化投放端列表
    const project = await this.projectService.get(values.projectName);
    const deliverClientList = project?.clientList.map(({ clientName, miniAppId }) => {
      return {
        clientName,
        miniAppId,
        // 是否跳过投放，既指当前迭代不投放该端，初始为 false
        skip: false,
        // 绑定的小程序投放任务id，初始为 null
        deliverTaskId: null
      };
    });

    // 3. 写库
    const dbValues = convertsCollectionToSnake(values);
    dbValues.branch_type = isComponentProject ? EBranchType.COMPONENT : EBranchType.APP;
    dbValues.status = IterStatus.PLAN;
    dbValues.git_branch = JSON.stringify({
      name: gitBranch,
      url: gitUtils.getBranchUrl(gitRepoUrl, gitBranch),
    });
    dbValues.deliver_client_list = (deliverClientList && deliverClientList.length > 0) ? JSON.stringify(deliverClientList) : null;
    dbValues.def_build_status = EDefBuildStatus.NOT_START;
    dbValues.pkg_initial_version = values.version || null;
    dbValues.def_iter_id = defIterId || null;
    dbValues.qa_list &&= (dbValues.qa_list.length > 0 ? JSON.stringify(dbValues.qa_list) : null);
    const iterBranchDetailModel = await this.IterBranchDetailModel.create(dbValues, {
      fields: ['branch_type', 'version', 'description', 'creator', 'gmt_create', 'status', 'git_branch', 'dev_branch_list', 'free_dev_branch_list', 'publish_day', 'project_name', 'deliver_client_list', 'def_build_status', 'pkg_initial_version', 'def_iter_id', 'qa_list']
    });
    const res = iterBranchDetailModel.toJSON();

    return this.parseData(res);
  }

  async update(values: IIterBranchUpdateValues, force?: boolean): Promise<IIterBranch> {
    // 1. 前置判断
    const iterBranch = await this.get(values.iterId);
    const isComponentIterBranch = iterBranch?.branchType === EBranchType.COMPONENT;
    if (!iterBranch) {
      throw new Error(`id 为 ${values.iterId} 的迭代分支不存在`)
    } else if (!force && (isComponentIterBranch ? [IterStatus.ABANDON] : [IterStatus.ABANDON, IterStatus.PUBLISHED]).includes(iterBranch.status)) {
      throw Error(`迭代 ${iterBranch.version} 处于“${iterBranch.statusText}”状态，不能更新`)
    }

    // 2. 写库
    const dbValues = convertsCollectionToSnake(values, 1);
    dbValues.dev_branch_list &&= (dbValues.dev_branch_list.join(',') || null);
    dbValues.free_dev_branch_list &&= (dbValues.free_dev_branch_list.join(',') || null);
    const updateResult = await this.IterBranchDetailModel.update(dbValues, {
      where: { iter_id: values.iterId },
      fields: ['branch_type', 'description', 'modifier', 'gmt_modified', 'status', 'gray_status', 'git_branch', 'dev_branch_list', 'free_dev_branch_list', 'shrinkwrap', 'tower_id', 'dist', 'report_analyzed', 'pkg_initial_version', 'pkg_publish_version', 'def_iter_id', 'def_task_id', 'def_build_status', 'def_branch_id', 'def_env_type', 'auto_notice_status']
    });
    if (updateResult[0] === 0) throw Error(`更新迭代 ${iterBranch.version} 失败`);

    const newIterBranch = await this.get(values.iterId);
    if (!newIterBranch) throw ('')

    return newIterBranch;
  }

  /**
   * 插入开发分支
   * @param iterId 迭代分支 id
   * @param devId 待插入的开发分支 id
   */
  async insertDevBranch(iterId: number, devId: number, devBranchListField = 'dev_branch_list'): Promise<boolean> {
    // 查询迭代分支
    const iterBranch = await this.IterBranchDetailModel.findOne({
      where: { iter_id: iterId },
      raw: true,
    });
    if (!iterBranch) throw Error(`SQL:查询不到迭代 ${iterId}`);

    // 插入开发分支
    const devBranchList = iterBranch[devBranchListField]?.split(',') ?? [];
    devBranchList.push(String(devId));

    // 写库
    const updateResult = await this.IterBranchDetailModel.update({
      [devBranchListField]: devBranchList.join(',')
    }, {
      where: { iter_id: iterId }
    });
    if (updateResult[0] === 0) throw Error(`SQL: 向迭代 ${iterId} 中插入开发分支 ${devId} 失败`);

    return true;
  }

  /**
   * 插入开发分支
   * @param iterId 迭代分支 id
   * @param devId 待插入的开发分支 id
   */
  async insertFreeDevBranch(iterId: number, devId: number): Promise<boolean> {
    return this.insertDevBranch(iterId, devId, 'free_dev_branch_list')
  }

  /**
   * 添加构建产物
   * @param iterId 迭代分支 id
   * @param dist 构建产物地址
   */
  async addDist(iterId: number, dist: string) {
    // 查询迭代分支
    const iterBranch = await this.IterBranchDetailModel.findOne({
      where: { iter_id: iterId },
      raw: true,
    });
    if (!iterBranch) throw Error(`SQL:查询不到迭代 ${iterId}`);

    // 限制状态
    if (![IterStatus.MERGE, IterStatus.DELIVERING, IterStatus.PUBLISHED].includes(iterBranch.status)) throw Error('只有集成回归或投放中或已发布状态才能添加构建产物');

    // 写库
    await this.IterBranchDetailModel.update({
      dist
    }, {
      where: { iter_id: iterId },
      fields: ['dist']
    });
  }

  /**
   * 添加report_analyzed.json
   * @param iterId 迭代分支 id
   * @param reportAnalyzed 构建产物地址
   */
  async addReportAnalyzed(iterId: number, reportAnalyzed: string) {
    // 查询迭代分支
    const iterBranch = await this.IterBranchDetailModel.findOne({
      where: { iter_id: iterId },
      raw: true,
    });
    if (!iterBranch) throw Error(`SQL:查询不到迭代 ${iterId}`);

    // 限制状态
    if (![IterStatus.MERGE, IterStatus.DELIVERING, IterStatus.PUBLISHED].includes(iterBranch.status)) throw Error('只有集成回归或投放中或已发布状态才能添加report_analyzed.json');

    // 写库
    await this.IterBranchDetailModel.update({
      report_analyzed: reportAnalyzed,
    }, {
      where: { iter_id: iterId },
      fields: ['report_analyzed']
    });
  }

  /**
   * 绑定自动化测试任务
   * @param iterId 迭代分支 id
   * @param towerId 自动化测试任务 id
   */
  async bindTowerId(iterId: number, towerId: string) {
    // 查询迭代分支
    const iterBranch = await this.IterBranchDetailModel.findOne({
      where: { iter_id: iterId },
      raw: true,
    });
    if (!iterBranch) throw Error(`SQL:查询不到迭代 ${iterId}`);

    // 限制状态
    if (![IterStatus.MERGE, IterStatus.DELIVERING, IterStatus.PUBLISHED].includes(iterBranch.status)) throw Error('只有集成回归或投放中或已发布状态才能绑定自动化测试任务');

    // 写库
    await this.IterBranchDetailModel.update({
      tower_id: towerId
    }, {
      where: { iter_id: iterId },
      fields: ['tower_id']
    });
  }

  async get(iterId: number | string): Promise<IIterBranch | null>
  async get(iterId: number | string, assertExist: true): Promise<IIterBranch>
  async get(iterId: number | string, assertExist?: boolean): Promise<IIterBranch | null> {
    const res = await this.IterBranchDetailModel.findOne({
      where: { iter_id: iterId },
      raw: true,
    });

    if (res) {
      return this.parseData(res);
    } else if (assertExist) {
      throw Error(`查询不到 iterId 为 ${iterId} 的迭代`)
    } else {
      return null;
    }
  }

  /**
   * 查找指定项目的已发布完成的迭代列表
   * @param projectName 项目名称
   * @returns
   */
  async getPublishedByProjectList(projectName: string, offset = 20) {
    const res = await this.IterBranchDetailModel.findAll({
      where: { project_name: projectName, status: IterStatus.PUBLISHED },
      order: [['iter_id', 'DESC']],
      limit: offset,
      raw: true,
    });

    if (res) {
      const arr = res.map(item => this.parseData(item));
      return arr;
    } else {
      return [];
    }
  }

  /**
   * 查找指定项目的最近一次已发布完成的迭代
   * @param projectName 项目名称
   * @returns
   */
  async getLatestPublishedByProject(projectName: string): Promise<IIterBranch | null> {
    const res = await this.IterBranchDetailModel.findAll({
      where: { project_name: projectName, status: IterStatus.PUBLISHED },
      order: [['iter_id', 'DESC']],
      limit: 1,
      raw: true,
    });

    if (res && res[0]) {
      return this.parseData(res[0]);
    } else {
      return null;
    }
  }

  /**
   * 获取指定迭代的上一次已发布完成迭代
   * @param projectName 项目名称
   * @returns
   */
  async getPrevPublishedByProject(iterId: string | number): Promise<IIterBranch | null> {
    const iterBranchModel = await this.IterBranchDetailModel.findOne({
      where: { iter_id: iterId },
      raw: true,
    });

    if (!iterBranchModel) return null;

    const res = await this.IterBranchDetailModel.findAll({
      where: {
        project_name: iterBranchModel.project_name,
        status: IterStatus.PUBLISHED,
        iter_id: {
          [Op.lt]: iterBranchModel.iter_id
        }
      },
      order: [['iter_id', 'DESC']],
      limit: 1,
      raw: true,
    });

    if (res && res[0]) {
      return this.parseData(res[0]);
    } else {
      return null;
    }
  }

  async discard(iterId: number): Promise<IIterBranch> {
    // 1. 前置判断
    const iterBranch = await this.get(iterId);
    if (!iterBranch) {
      throw new Error(`迭代分支${iterId}不存在`)
    } else if (iterBranch.status !== IterStatus.PLAN) {
      throw Error('只有“计划发布”状态，才能废弃')
    }

    // 2. 废弃下属开发分支
    // await this.discardDevBranch(iterBranch.devBranchList);

    // 3. 废弃迭代分支
    return await this.update({
      modifier: this.ctx.user.name,
      gmtModified: formatDate(new Date()),
      iterId,
      status: IterStatus.ABANDON
    });
  }

  // async discardDevBranch(devBranchIdList: number[]): Promise<boolean> {
  //   devBranchIdList = devBranchIdList.filter(item => !!item);

  //   // 1. 获取开发分支详情
  //   const devBranchList = await Promise.all(devBranchIdList.map(item => this.devBranchService.get(item)))

  //   // 2. 遍历判断开发分支是否都能废弃
  //   const checkRes = devBranchList.filter((item): item is IDevBranch => {
  //     if (item) {
  //       return !this.devBranchService.canDiscard(item);
  //     }
  //     return false;
  //   });
  //   if (checkRes.length > 0) throw Error(`开发分支${checkRes[0].branchName}处于“${checkRes[0].statusText}”状态，不能废弃`)

  //   // 3. 再真实废弃
  //   await Promise.all(devBranchIdList.map(devId => this.devBranchService.discard(devId)));

  //   return true;
  // }

  async delete(iterId: number): Promise<IIterBranch> {
    // 1. 前置判断
    const iterBranch = await this.get(iterId);
    if (!iterBranch) {
      throw new Error(`迭代分支${iterId}不存在`)
    } else if (iterBranch.status !== IterStatus.ABANDON) {
      throw Error('只有“废弃”状态，才能删除')
    }

    // 2. 删除下属开发分支
    // await this.deleteDevBranch(iterBranch.devBranchList);

    // 3. 删除迭代分支
    const destroyCount = await this.IterBranchDetailModel.destroy({ where: { iter_id: iterId } });
    if (destroyCount === 0) throw Error('删除失败');

    return iterBranch;
  }

  /**
   * 如果项目类型是组件，需要废弃def迭代
   * @param iterId 迭代id
   */
  async deleteComponent(iterId: number) {
    // 1. 前置判断
    const iterBranch = await this.get(iterId);
    if (!iterBranch) {
      throw new Error(`迭代分支${iterId}不存在`)
    } else if (iterBranch.status !== IterStatus.ABANDON) {
      throw Error('只有“废弃”状态，才能删除')
    }

    if (iterBranch.branchType === EBranchType.COMPONENT) {
      const res = await this.defService.abandonIteration({
        iterationId: iterBranch.defIterId!
      })
      if (!res?.success){
        throw Error(`项目废弃def迭代(${iterBranch.defIterId})失败`);
      }
    }
  }

  // async deleteDevBranch(devBranchIdList: number[]): Promise<boolean> {
  //   // 1. 获取开发分支详情
  //   const devBranchList = await Promise.all(devBranchIdList.map(item => this.devBranchService.get(item)))

  //   // 2. 遍历判断开发分支是否都能删除
  //   const checkRes = devBranchList.filter((item): item is IDevBranch => {
  //     if (item) {
  //       return !this.devBranchService.canDelete(item);
  //     }
  //     return false;
  //   });
  //   if (checkRes.length > 0) throw Error(`开发分支${checkRes[0].branchName}处于“${checkRes[0].statusText}”状态，不能删除`)

  //   // 3. 再真实删除
  //   await Promise.allSettled(devBranchIdList.map(item => this.devBranchService.delete(item)));

  //   return true;
  // }

  async getDeliverClient(
    iterId: number,
    conditions: {
      miniAppId?: string; clientName?: EClient; clientList?: EClient[];
    }
  ) {
    // 查询迭代详情
    const iterBranch = await this.get(iterId);
    if (!iterBranch) throw Error('未查询到迭代')

    // 匹配指定投放端
    const deliverClientList = iterBranch.deliverClientList;
    if (!deliverClientList) return null;

    // 匹配投放端
    return deliverClientList.filter(item => {
      if (conditions.miniAppId && conditions.miniAppId !== item.miniAppId) return false;
      if (conditions.clientName && conditions.clientName !== item.clientName) return false;
      if (conditions.clientList && !conditions.clientList.includes(item.clientName)) return false;
      return true;
    })
  }

  /** 更新迭代投放端信息 */
  async updateDeliverClient(
    iterId: number,
    conditions: { miniAppId?: string; clientName?: EClient; clientList?: EClient[]; },
    values: { skip?: boolean; deliverTaskId?: number | null; }
  ) {
    // 查询迭代详情
    const iterBranch = await this.get(iterId);
    if (!iterBranch) throw Error('未查询到迭代')

    // 匹配指定投放端
    const deliverClientList = iterBranch.deliverClientList;
    if (!deliverClientList) throw Error('当前迭代没有配置投放端')

    // 匹配指定投放端，并更新投放端信息
    let hasDiff = false;
    let matchRes = false;
    deliverClientList.forEach(item => {
      if (conditions.miniAppId && conditions.miniAppId !== item.miniAppId) return;
      if (conditions.clientName && conditions.clientName !== item.clientName) return;
      if (conditions.clientList && !conditions.clientList.includes(item.clientName)) return;

      if (values.skip !== undefined && item.skip !== values.skip) {
        item.skip = values.skip;
        hasDiff = true;
      }

      if (values.deliverTaskId !== undefined && item.deliverTaskId !== values.deliverTaskId) {
        item.deliverTaskId = values.deliverTaskId;
        hasDiff = true;
      }

      matchRes = true;
    });
    if (!matchRes) throw Error(`匹配不到符合条件的(${JSON.stringify(conditions)})投放端`)

    // 没有差异返回 false
    if (!hasDiff) return false;

    // 有差异进行更新
    const updateResult = await this.IterBranchDetailModel.update({
      deliver_client_list: JSON.stringify(deliverClientList),
      // 只要有一个投放端处于投放中，则将迭代的状态置为投放中，否则为集成回归
      status: deliverClientList.some(item => !item.skip && item.deliverTaskId) ? IterStatus.DELIVERING : IterStatus.MERGE
    }, {
      // 若迭代状态为已发布，则不更新
      where: { iter_id: iterId, status: { [Op.not]: IterStatus.PUBLISHED } },
    });
    if (updateResult[0] === 0) throw Error(`更新迭代投放端信息失败`);

    return true;
  }

  /**
   * 获取上一次已发布完成迭代，根据发布时间筛选
   * @param projectName 项目名称
   * @param gmtModified 迭代修改时间，用来查上次迭代的数据
   * @returns
   */
  async getPrevPublishedIter(projectName: string, gmtModified?: string): Promise<IIterBranch | null> {
    const condition = gmtModified ? { gmt_modified: { [Op.lt]: new Date(gmtModified) } } : {} as any;
    const res = await this.IterBranchDetailModel.findAll({
      where: {
        project_name: projectName,
        status: IterStatus.PUBLISHED,
        ...condition,
      },
      order: [['gmt_modified', 'DESC']],
      limit: 1,
      raw: true,
    });

    if (res && res[0]) {
      return this.parseData(res[0]);
    } else {
      return null;
    }
  }

  /**
   * 基于def版本查询迭代id
   * @param version 迭代分支 id
   */
  async findIterIdByVersion(version: string, project: string) {
    // 查询迭代分支
    const iterBranch = await this.IterBranchDetailModel.findOne({
      where: {
        version,
        project_name: { 
          [Op.like]: `%${project}%`
        } },
      raw: true,
    });
    if (!iterBranch) throw Error(`SQL:查询不到 ${version} 版本对应的迭代`);
    return iterBranch.iter_id;
  }

   /**
   * 切换测试回归状态
   * @param iterId 分支id
   * @param checked 是否已测试回归
   * @returns
   */
   async switchChecked(iterId: number, checked: number) {
    // 1. 前置判断
    const iterBranch = await this.get(iterId, true);
    const isInQaList = !!iterBranch.qaList?.find(item => item.workid === this.ctx.user.workid);

    if (!isInQaList) {
      const isAdmin = await this.userService.isAdmin();
      if (!isAdmin) throw Error('仅测试成员或管理员有权限操作')
    }

    // 限制状态
    if (IterStatus.MERGE !== iterBranch.status) throw Error('只有集成回归状态才能修改回归状态');

    // 写库
    await this.IterBranchDetailModel.update({
      checked,
      modifier: this.ctx.user.name,
      gmt_modified: new Date(),
    }, {
      where: { iter_id: iterId },
      fields: ['checked', 'modifier', 'gmt_modified']
    });
    return true;
  }

  /*
  * 获取时间日期范围内的迭代列表
  * @returns
  */
 async getPrevPublishedIterByDateRange(projectName: string, dateRange): Promise<any[]> {
  return this.IterBranchDetailModel.findAll({
    where: {
      project_name: projectName,
      status: IterStatus.PUBLISHED,
      publish_day: {
        [Op.between]: dateRange
      }
    },
    order: [['gmt_modified', 'DESC']],
    raw: true,
  });
 }


  parseData(res): IIterBranch {
    const gitBranch = res.git_branch ? JSON.parse(res.git_branch) : {};
    let rcGitBranch;

    // 是否展示集成分支
    const showRcGitBranch = [IterStatus.MERGE, IterStatus.AUDITING, IterStatus.GRAY, IterStatus.DELIVERING, IterStatus.PUBLISHED].includes(res.status);

    if (showRcGitBranch && gitBranch.url) {
      const rcGitBranchName = `rc/${res.version}`;
      rcGitBranch = {
        name: rcGitBranchName,
        url: gitUtils.getBranchUrl(gitBranch.url, rcGitBranchName),
      }
    }

    return {
      ...convertsCollectionToCamel(res),
      devBranchList: (res.dev_branch_list?.split(',') ?? []).map(item => Number(item)),
      freeDevBranchList: (res.free_dev_branch_list?.split(',') ?? []).map(item => Number(item)),
      gitRepo: gitUtils.parseGitRepoInfo(gitBranch.url),
      gitBranch,
      rcGitBranch,
      gmtCreate: res.gmt_create && formatDate(res.gmt_create),
      gmtModified: res.gmt_modified && formatDate(res.gmt_modified),
      statusText: ITER_STATUS_TEXT[res.status] || '未知',
      deliverClientList: res.deliver_client_list && JSON.parse(res.deliver_client_list),
      qaList: res.qa_list && JSON.parse(res.qa_list),
    }
  }
}

/** 迭代分支创建入参 */
export interface IIterBranchCreateValues {
  branchType?: IIterBranch['branchType'];
  version: IIterBranch['version'];
  description: IIterBranch['description'];
  creator: IIterBranch['creator'];
  gmtCreate: IIterBranch['gmtCreate'];
  publishDay: IIterBranch['publishDay'];
  projectName: IIterBranch['projectName'];
  project: IProject;
  pkgInitialVersion?: string;
  pkgPublishVersion?: string;
  defIterId?: number;
  defTaskId?: number;
  defBuildStatus?: EDefBuildStatus;
  defBranchId?: number;
  defEnvType?: EPubTypeEnv;
  qaList?: IUser[];
  checked?: EChecked;
}

/** 迭代分支更新入参 */
export interface IIterBranchUpdateValues {
  iterId: IIterBranch['iterId'];
  branchType?: IIterBranch['branchType'];
  description?: IIterBranch['description'];
  modifier?: IIterBranch['modifier'];
  gmtModified?: IIterBranch['gmtModified'];
  status?: IIterBranch['status'];
  grayStatus?: IIterBranch['grayStatus'] | null;
  devBranchList?: IIterBranch['devBranchList'];
  freeDevBranchList?: IIterBranch['freeDevBranchList'];
  shrinkwrap?: IIterBranch['shrinkwrap'] | null;
  towerId?: IIterBranch['towerId'] | null;
  dist?: IIterBranch['dist'] | null;
  reportAnalyzed?: IIterBranch['reportAnalyzed'] | null;
  pkgInitialVersion?: string;
  pkgPublishVersion?: string;
  defIterId?: number;
  defTaskId?: number;
  defBuildStatus?: EDefBuildStatus;
  defBranchId?: number | null;
  defEnvType?: EPubTypeEnv;
  autoNoticeStatus?: number;
}
