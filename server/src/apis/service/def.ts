import { Inject, Provide, Init } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { IDevBranch } from '@/apis/interface/dev-branch';
import { IFreeDevBranch } from '@/apis/interface/free-dev-branch';
import { DefClient } from '@ali/def-open-client';
import { convertsCollectionToSnake, formatDate } from '@/apis/utils';
import { EPubTypeEnv } from '@/apis/const/def';
import { IterStatus } from '@/apis/const/iter-branch';
import { EDefBuildStatus, EDefBuildTaskType, DEF_ENV_TYPE_WORDING, DEF_BUILD_STATUS_WORDING } from '@/apis/const/def';
import PROJECT_CONFIG from '@/apis/const/project';
import ProjectService from '@/apis/service/project';
import DingtalkService from '@/apis/service/dingtalk';
import IterBranchService, { IIterBranchUpdateValues } from '@/apis/service/iter-branch';
import FreeDevBranchService from '@/apis/service/free-dev-branch';
import DevBranchService from '@/apis/service/dev-branch';
import { mergeBranch } from '@/apis/utils/git';

interface DefRes {
  success: boolean;
  data?: any;
  errorMsg?: string;
  errorCode?: string;
}
// simu-test
// const APP_ID = 296320;

@Provide()
export default class DefService {
  @Inject()
  ctx!: Context;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  freeDevBranchService!: FreeDevBranchService;

  @Inject()
  devBranchService!: DevBranchService;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  dingtalkService!: DingtalkService;

  client!: DefClient;

  // 自动执行
  @Init()
  async init() {
    if (this.client) return;
    this.client = new DefClient({
      // 枚举值 {'daily', 'pre', 'prod'}，默认为 prod
      env: this.ctx.aliEnv === 'prod' ? 'prod' : 'pre',
      // 请求超时配置，默认10s
      // timeout: 5 * 1000,
      accessKeyId: 'yxqCXObF56PH3B3JuOf2p',
      accessKeySecret:
        '70461e0e4ae62d14c60b2cc0cdab46df1e945a462ae0d5c4403ee20d8a1ed770b877ff0cac603eaf234f77ebd344d258',
      // token: TOKEN,
      // 若提供的默认处理不满足，需要调用者在该函数中进行适配
      resultFormatCallback: (res: any): DefRes => {
        console.log('DefClient调用结果 res', res);
        if (res.data && !res.error) {
          return {
            success: true,
            data: res.data,
          };
        } else {
          return {
            success: false,
            errorMsg: res.error || res.Message || 'def服务请求失败，请联系丝木',
            errorCode: res.Code,
          };
        }
      },
    });
  }

  // 设置微信小程序版本
  async setMiniAppVersion({
    iterId, // 迭代id
    version, // 小程序版本
    miniappId, //小程序应用id
    // tenantId, //租户id
    type, // 小程序类型
  }: {
    iterId: string;
    version: string;
    miniappId: string,
    // tenantId: string,
    type: string
  }): Promise<DefRes> {
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      return await this.client.post(
        `/v1.0/work/api/iteration/${iterId}/setminiappversion?Format=json`,
        {},
        {
          miniapp_id: miniappId,
          set_version: version,
          // tenant_id: tenantId,
          type
        },
        {
          SSO_TICKET: ssoTicket,
        }
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 创建迭代
  async createIteration({
    repo,
    name,
    description,
    branch, // git 分支
    branchType = 'exist',
    version,
    trunk,
    aoneBinds,
  }: {
    repo: string;
    name: string;
    description: string;
    branch?: string;
    branchType?: 'exist' | 'new';
    version: string;
    trunk?: string;
    aoneBinds?: string[];
  }): Promise<DefRes> {
    console.log('\n>> createIteration begin, params:', repo, name, description, branch, version, trunk, aoneBinds);
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      aoneBinds = aoneBinds ? Array.from(new Set(aoneBinds)) : [];
      const aone_binds = aoneBinds?.length ? aoneBinds.map(aoneId => `req|${aoneId}`)?.join(',') : '';
      return await this.client.post(
        `/v1.0/work/apps/${encodeURIComponent(
          encodeURIComponent(repo)
        )}/iteration`,
        {},
        {
          name,
          description,
          branch_type: branchType,
          branch,
          version,
          trunk,
          aone_binds,
        },
        {
          SSO_TICKET: ssoTicket,
        }
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 根据 repo 和 branch 获取迭代基本信息(id 等)
  async getIterationId({ repo, branch }: { repo: string; branch: string }): Promise<DefRes> {
    console.log('\n>> getIterationId begin, params:', repo, branch);
    try {
      return await this.client.get(
        '/v1.0/work/apps/iterations/infos',
        { repo: encodeURIComponent(encodeURIComponent(repo)), branch: encodeURIComponent(branch) },
        {},
        {}
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 根据迭代id获取迭代基本信息
  async getIterationInfoById({ iterationId }: { iterationId: number }):Promise<DefRes> {
    console.log('\n>> getIterationInfoById begin, params:', iterationId);
    try {
      return await this.client.get(
        `/v1.0/work/iterations/${iterationId}/detail`,
        {},
        {},
        {}
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 编辑迭代信息
  async editIteration({ iterationId, name, description }: { iterationId: number; name: string; description: string }):Promise<DefRes> {
    console.log('\n>> editIteration begin, params:', iterationId, description);
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      return await this.client.post(
        `/v1.0/work/iterations/${iterationId}/update`,
        {},
        {
          name,
          description
        },
        {
          SSO_TICKET: ssoTicket
        }
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 废弃迭代
  async abandonIteration({ iterationId }: { iterationId: number }): Promise<DefRes> {
    console.log('\n>> abandonIteration begin, params:', iterationId);
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      return await this.client.post(
        `/v1.0/work/iterations/${iterationId}/abandon`,
        {},
        { iterationId },
        {
          SSO_TICKET: ssoTicket
        }
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 集成区绑定变更
  async bindBranchToIteration({ iterationId, branchIds }: { iterationId: number; branchIds: number[] }): Promise<DefRes> {
    console.log('\n>> abandonIteration begin, params:', iterationId, branchIds);
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      return await this.client.post(
        `/v1.0/work/iterations/${iterationId}/devbranch/bind`,
        {},
        { type: 'exist', branchIds },
        {
          SSO_TICKET: ssoTicket,
        }
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 集成区解绑变更
  async unbindBranchToIteration({ iterationId, id }: { iterationId: number; id: number }): Promise<DefRes> {
    console.log('\n>> unbindBranchToIteration begin, params:', iterationId, id);
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      return await this.client.post(
        `/v1.0/work/iterations/${iterationId}/devbranch/unbind`,
        {},
        { id },
        {
          SSO_TICKET: ssoTicket,
        }
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 创建变更
  async createBranch({ appId, branch, branchType, description, aoneBinds }: { appId: number; branch: string; branchType: string; description: string; aoneBinds?: string[]; }): Promise<DefRes> {
    console.log('\n>> createBranch begin, params:', appId, branch, branchType, description, aoneBinds);
    try {
      if (!appId) {
        return {
          success: false,
          errorMsg: '创建变更失败，因为缺失必须参数appId',
        };
      }

      const ssoTicket = await this.ctx.generateSsoTicket();
      aoneBinds = aoneBinds ? Array.from(new Set(aoneBinds)) : [];
      const aone_binds = aoneBinds?.length ? aoneBinds.map(aoneId => `req|${aoneId}`)?.join(',') : '';
      return await this.client.post(
        `/v1.0/work/apps/${appId}/devbranches/add`,
        {},
        {
          branch,
          branch_type: branchType,
          description,
          aone_binds,
        },
        {
          SSO_TICKET: ssoTicket,
        }
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 删除变更
  async deleteBranch({ appId, branchId }: { appId: number; branchId: number }): Promise<DefRes> {
    console.log('\n>> deleteBranch begin, params:', branchId);
    try {
      if (!appId) {
        return {
          success: false,
          errorMsg: '创建变更失败，因为缺失必须参数appId',
        };
      }

      const ssoTicket = await this.ctx.generateSsoTicket();
      return await this.client.post(
        `/v1.0/work/apps/${appId}/devbranches/${branchId}/delete`,
        {},
        {},
        {
          SSO_TICKET: ssoTicket,
        }
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 修改branch信息
  async editBranch({ appId, branchId, aoneBinds }: { appId: number; branchId: number; aoneBinds: string[]; }): Promise<DefRes> {
    console.log('\n>> editBranch begin, params:', appId, branchId, aoneBinds);
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      aoneBinds = aoneBinds ? Array.from(new Set(aoneBinds)) : [];
      const aone_binds = aoneBinds?.length ? aoneBinds.map(aoneId => `req|${aoneId}`)?.join(',') : '';
      return await this.client.post(
        `/v1.0/work/apps/${appId}/devbranchs/${branchId}/edit`,
        {},
        { aone_binds },
        {
          SSO_TICKET: ssoTicket,
        }
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 创建迭代发布任务(游离分支、分支、迭代三种都能查询并更新DB)
  async createIterPublishTask({
    updateDB = false,
    taskType,
    devId,
    iterId,
    iterationId,
    pub_env,
  }: {
    updateDB?: boolean;
    taskType?: EDefBuildTaskType;
    devId?: number; // 发布后台分支id
    iterId?: number; // 发布后台迭代id
    iterationId: number; // def迭代id
    pub_env: EPubTypeEnv;
  }): Promise<DefRes> {
    console.log('\n>> createIterPublishTask begin, params:', updateDB, taskType, devId, iterId, iterationId, pub_env);
    try {
      // 1. 获取buc登录态
      const ssoTicket = await this.ctx.generateSsoTicket();
      console.log('updateDB', taskType, devId, iterId, iterationId, pub_env);

      // 2. git合并master
      if (taskType && devId && [EDefBuildTaskType.DEV_BRANCH, EDefBuildTaskType.FREE_DEV_BRANCH].indexOf(taskType) !== -1) {
        await this.gitMergeMater({ taskType, devId });
      }

      // 3. 创建def迭代的发布任务
      const { success, data, errorMsg, errorCode }: DefRes = await this.client.post(
        `/v1.0/work/iterations/${iterationId}/task`,
        {},
        { pub_env },
        {
          SSO_TICKET: ssoTicket,
        }
      ) as any;

      // 如果不更新DB直接返回结果数据
      if (!updateDB) {
        return { success, data, errorMsg, errorCode };
      }

      // 4. 更新数据库（devId、任务id、构建状态）
      if (success && data?.id) {
        const isSuccess = await this.updateDefTaskInfoToDB({
          taskType: taskType!,
          defEnvType: pub_env,
          devId,
          iterId,
          defTaskId: data.id,
          defBuildStatus: data.pub_status,
        });

        if (isSuccess) {
          return { success: isSuccess, data };
        } else {
          return { success: isSuccess, errorMsg: `SQL: defService.createIterPublishTask 更新def构建任务id(${data.id})失败` };
        }
      }
      return { success, errorMsg, errorCode };
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 查询迭代发布任务详情(游离分支、分支、迭代三种都能查询并更新DB)
  async getIterPublishTaskDetail({
    updateDB = false,
    taskType,
    devId = 0,
    iterId = 0,
    defTaskId,
    sendNotice = true,
  }: {
    updateDB?: boolean;
    taskType?: EDefBuildTaskType;
    iterId?: number;
    devId?: number;
    defTaskId: number;
    sendNotice?: boolean;
  }): Promise<DefRes> {
    console.log('\n>> getIterPublishTaskDetail begin, params:', updateDB, taskType, iterId, devId, defTaskId);
    try {
      // 1. 查询def迭代的发布任务详情
      const { success, data, errorMsg, errorCode }: DefRes = await this.client.get(
        `/v1.0/work/tasks/${defTaskId}/detail`,
        {},
        {},
        {}
      ) as any;

      // 如果不更新DB直接返回结果数据
      if (!updateDB) {
        return { success, data, errorMsg, errorCode };
      }

      if (success) {
        const pubStatus = data?.task?.pub_status;
        // console.log('getIterPublishTaskDetail=====', JSON.stringify(data));

        // 2. 任务不同构建状态时的处理
        switch(pubStatus) {
          // 构建中时，直接返回构建数据
          case EDefBuildStatus.BUILDING: {
            return { success: true, data };
          }
          // 构建成功/失败时，更新数据库（构建状态、npm版本号）
          case EDefBuildStatus.BUILD_SUCCESS:
          case EDefBuildStatus.BUILD_ERROR: {
            let pkgPublishVersion = null;
            let pkgPublishName = null;
            if (pubStatus === EDefBuildStatus.BUILD_SUCCESS) {
              const jobs = data.runtime.context.stages.publish.jobs;
              const { name, version } = jobs?.['tnpm-deploy']?.steps?.['tnpm-deploy']?.outputs || jobs?.tnpm?.steps?.tnpm?.outputs;
              pkgPublishVersion = version,
              pkgPublishName = name;
            }

            const isSuccess = await this.updateDefTaskInfoToDB(
              {
                taskType: taskType!,
                defEnvType: data.task.pub_env,
                devId,
                iterId,
                defTaskId,
                defBuildStatus: pubStatus,
                pkgPublishVersion,
                recordOperator: false
              });

            // 发送钉钉消息
            if (sendNotice) {
              this.sendDingMessage({ taskType, devId, pub_env: data.task.pub_env, pubStatus, pkgPublishVersion, pkgPublishName });
            }

            if (isSuccess) {
              return { success: isSuccess, data };
            } else {
              return { success: isSuccess, errorMsg: `SQL: defService.getIterPublishTaskDetail 更新def构建任务id(${data.id})失败` };
            }
          }
        }
      }
      return { success, errorMsg, errorCode };
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 发送钉钉通知
  async sendDingMessage({ taskType, devId, pub_env, pubStatus, pkgPublishVersion, pkgPublishName }) {
    if (taskType === EDefBuildTaskType.ITERATION) return;

    let branchInfo = '未知';
    let res: IDevBranch | IFreeDevBranch | null = null;
    switch(taskType) {
      case EDefBuildTaskType.DEV_BRANCH: {
        res = await this.devBranchService.get(devId);
        branchInfo = `分支 ${res?.branchName}`;
        break;
      }
      case EDefBuildTaskType.FREE_DEV_BRANCH: {
        res = await this.freeDevBranchService.get(devId);
        branchInfo = `游离分支 ${res?.branchName}`;
        break;
      }
      // TODO 迭代没有workerid 发不了单聊
      // case EDefBuildTaskType.ITERATION: {
      //   res = await this.iterBranchService.get(iterId);
      //   branchInfo = `迭代 v${res?.version}`;
      //   break;
      // }
    }

    if (!res?.projectName) throw Error(`查询 projectName 为空`)

    const project = await this.projectService.get(res.projectName, { needDingtalkRobots: true });
    if (!project) throw Error(`查询不到 projectName 为 ${res.projectName} 的项目`)

    const content = [
      `![bg](${PROJECT_CONFIG[project.type].dingtalkLogo})`,
      `**${branchInfo}${DEF_ENV_TYPE_WORDING[pub_env]}构建npm包${DEF_BUILD_STATUS_WORDING[pubStatus]}**`,
      `- 所属项目：${project.cnName || '未知'}`,
      pkgPublishName ? `- 构建模块名：${pkgPublishName}` : '',
      pkgPublishVersion ? `- 构建模块版本：${pkgPublishVersion}` : '',
      `- 执行人：${this.ctx.user.name}`,
      `- 执行时间：${formatDate(new Date())}`,
      `- **[点击查看详情](${`https://space.o2.alibaba-inc.com/iteration/${res.defIterId}/basic`})**`
    ].join('\n')

    this.dingtalkService.notice([res.creatorWorkid], `${branchInfo}${DEF_ENV_TYPE_WORDING[pub_env]}构建npm包${DEF_BUILD_STATUS_WORDING[pubStatus]}`, content);
  }

  // 查询迭代关联的门神任务
  async checkMensheng(): Promise<DefRes> {
    try {
      return await this.client.get(
        `/v1.0/work/gog/repo_gog_status`,
        {},
        {},
        {}
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 迭代正式发布前的检查
  async publishCheck({ defIterId }: { defIterId: number; }): Promise<DefRes> {
    console.log('\n>> publishCheck begin, params:', defIterId);
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      return await this.client.get(
        `/v1.0/work/iterations/${defIterId}/releasecheckpoint`,
        {},
        {
          SSO_TICKET: ssoTicket,
        },
        {}
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 查询某一迭代下全部发布任务
  async getIterPublishTasks({ appId }: { appId: number }): Promise<DefRes> {
    console.log('\n>> getIterPublishTasks begin, params:', appId);
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      return await this.client.get(
        `/v1.0/work/apps/${appId}/tasks`,
        {},
        {
          SSO_TICKET: ssoTicket,
        },
        {}
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  /**
   * 查询主干是否同步
   * @param defIterId def迭代id
   * @returns isSync 是否同步
   */
  async checkMasterSync(defIterId: number) {
    let isSync = false;
    const res = await this.getLastCommit({
      iterationId: defIterId
    })
    if (res.success) {
      Object.values(res.data).forEach((branch: any) => {
        isSync = branch?.sync_status?.is_sync; // 主干同步状态
      })
    }
    return isSync;
  }

  // 代码合并主干
  async gitMergeMater({ taskType, devId }: { taskType: EDefBuildTaskType; devId: number; }) {
    let res: IDevBranch | IFreeDevBranch | null = null;

    switch(taskType) {
      case EDefBuildTaskType.DEV_BRANCH: {
        res = await this.devBranchService.get(devId);
        break;
      }
      case EDefBuildTaskType.FREE_DEV_BRANCH: {
        res = await this.freeDevBranchService.get(devId);
        break;
      }
    }

    // 1. 查询是否主干同步
    if (res?.defIterId) {
      const isSync = await this.checkMasterSync(res.defIterId);
      if (isSync) { return; }
    }

    // 2. 合并主干
    return mergeBranch(res?.branchName!, res?.gitBranch?.url!, 'master');
  }

  // 更新数据库
  async updateDefTaskInfoToDB({
    taskType,
    defEnvType = EPubTypeEnv.Beta,
    devId,
    iterId,
    defTaskId,
    defBuildStatus,
    pkgPublishVersion,
    recordOperator = true,
    autoNoticeStatus = 0,
  }: {
    taskType: EDefBuildTaskType;
    defEnvType: EPubTypeEnv;
    devId?: number; // DB里的分支id，用于更新DB
    iterId?: number; // DB里的迭代id，用于更新DB
    defTaskId?: number;
    defBuildStatus?: EDefBuildStatus;
    pkgPublishVersion?: string | null;
    recordOperator?: boolean; // 是否更新DB的操作人
    autoNoticeStatus?: number // DB里自动通知的状态
  }): Promise<boolean> {
    if (!taskType) {
      throw Error(`SQL: defService.updateDefTaskInfoToDB 更新def构建任务(taskid: ${defTaskId})失败，缺失必须参数taskType`);
    }

    const shouldUpdateIterBranchStatus = defEnvType === EPubTypeEnv.Prod && defBuildStatus === EDefBuildStatus.BUILD_SUCCESS;
    let params = Object.assign({
      autoNoticeStatus
    },
      defTaskId ? { defTaskId } : {},
      defBuildStatus ? { defBuildStatus } : {},
      pkgPublishVersion ? { pkgPublishVersion } : {}, 
    );

    // 区分游离开发分支/开发分支的更新DB结果
    let updateResult;
    switch(taskType) {
      case EDefBuildTaskType.DEV_BRANCH: {
        updateResult = await this.devBranchService.update(devId!, params, recordOperator);
        break;
      }
      case EDefBuildTaskType.FREE_DEV_BRANCH: {
        updateResult = await this.freeDevBranchService.update(devId!, params, recordOperator);
        break;
      }
      case EDefBuildTaskType.ITERATION: {
        params = Object.assign(params,
          { iterId, },
          defEnvType ? { defEnvType } : {},
          shouldUpdateIterBranchStatus ? { status: IterStatus.PUBLISHED } : {},
          recordOperator ? { modifier: this.ctx.user.name,
            gmtModified: formatDate(new Date()), } : undefined,
        );
        updateResult = await this.iterBranchService.update(params as IIterBranchUpdateValues);
        break;
      }
    }

    console.log('updateDefTaskInfoToDB updateResult===========', updateResult);
    if (updateResult[0] === 0)
      throw Error(`SQL: defService.updateDefTaskInfoToDB 更新def构建任务(taskid: ${defTaskId})失败`);
    return true;
  }

  /** 查询上一次提交以及主干同步状态 */
  async getLastCommit({ iterationId }: { iterationId: number; }): Promise<DefRes> {
    console.log('\n>> getLastCommit begin, params:', iterationId );
    try {
      return await this.client.get(
        `/v1.0/work/iterations/${iterationId}/devbranch/lastcommit?Format=json`,
        {},
        {},
        {}
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  /** 转换成 db 数据 */
  _covertToDBData(data: any) {
    return convertsCollectionToSnake(data, 1);
  }

  // 查询cr
  async getCR({ iterationId }: { iterationId: number }): Promise<DefRes> {
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();

      return await this.client.get(
        `/v1.0/work/iterations/${iterationId}/codereview/status`,
        {},
        {
          SSO_TICKET: ssoTicket,
        },
        {}
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 创建cr
  async createCR({ iterationId, devbranchId, title, description, ids }: { iterationId: number; devbranchId: string; title: string; description: string; ids: string }): Promise<DefRes> {
    try {
      console.log('createCR params', iterationId, devbranchId, title, description, ids);
      const ssoTicket = await this.ctx.generateSsoTicket();
      return await this.client.post(
        `/v1.0/work/iterations/${iterationId}/devbranch/${devbranchId}/cr`,
        {},
        {
          title,
          description,
          assignee_ids: ids
        },
        {
          SSO_TICKET: ssoTicket,
        },
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 修改cr
  async updateCR({ iterationId, title, description, ids }: { iterationId: number; title: string; description: string; ids: string }): Promise<DefRes> {
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      return await this.client.get(
        `/v1.0/work/iterations/${iterationId}/codereview/update`,
        {},
        {
          title,
          description,
          assignee_ids: ids
        },
        {
          SSO_TICKET: ssoTicket,
        },
      ) as any;
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 获取应用信息
  async getAppDetails({ appIdorName }) {
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      const res = await this.client.get(
        `/v1.0/work/apps/${appIdorName}/detail`,
        {},
        {
          SSO_TICKET: ssoTicket,
        },
        {},
      ) as any;

      return {
        data: res,
      };
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

   // 通过def的迭代ID，获取def迭代信息信息
   async getAppIterationById({ defVersionId }) {
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      const res = await this.client.get(
        `/v1.0/work/iterations/${defVersionId}/detail`,
        {},
        {
          SSO_TICKET: ssoTicket,
        },
        {},
      ) as any;

      return {
        data: res,
      };
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

   // 通过def的迭代ID，获取def迭代信息信息
   async getAppIterationByIdNew({ defVersionId }) {
    try {
      const res = await this.client.get(
        `/v1.0/work/iterations/${defVersionId}/detail`,
        {}
      ) as any;

      return {
        data: res,
      };
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 获取应用对应的迭代信息
  async getIterationDetails({ appId, status = '2', pn = 0, rn = 20 }) {
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      const res = await this.client.get(
        `/v1.0/work/apps/${appId}/iterations`,
        {
          status,
          pn,
          rn,
        },
        {
          SSO_TICKET: ssoTicket,
        },
        {},
      ) as any;

      return {
        data: res,
      };
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 获取灰度页面
  async getGreyPages({ appIdorName }) {
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      const res = await this.client.get(
        `/v1.0/work/apps/${appIdorName}/grey-pages`,
        {},
        {
          SSO_TICKET: ssoTicket,
        },
        {},
      ) as any;

      return {
        data: res,
      };
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 获取页面灰度状态
  async getPageGreyTip({ appIdorName }) {
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      const res = await this.client.get(
        `/v1.0/work/apps/${appIdorName}/pagegreytip`,
        {},
        {
          SSO_TICKET: ssoTicket
        },
        {},
      ) as any;

      return {
        data: res,
      };
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 页面灰度放量
  async setPageGrey({ appIdorName, rate = 0 }) {
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      const res = await this.client.post(
        `/v1.0/work/api/app/${appIdorName}/air/pagesetgrey_open`,
        {},
        { rate },
        {
          SSO_TICKET: ssoTicket,
        },
      ) as any;

      return {
        data: res,
      };
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
        errStr: JSON.stringify(err),
      };
    }
  }

  // 完成灰度
  async finishPageGrey({ appIdorName }) {
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      const res = await this.client.post(
        `/v1.0/work/api/app/${appIdorName}/air/pagefinishgrey_open`,
        {},
        {},
        {
          SSO_TICKET: ssoTicket,
        },
      ) as any;

      return {
        data: res,
      };
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 取消灰度
  async cancelPageGrey({ appIdorName }) {
    try {
      const ssoTicket = await this.ctx.generateSsoTicket();
      const res = await this.client.post(
        `/v1.0/work/api/app/${appIdorName}/air/pagecancelgrey_open`,
        {},
        {},
        {
          SSO_TICKET: ssoTicket,
        },
      ) as any;

      return {
        data: res,
      };
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }

  // 获取成员
  async getMember({ appId }) {
    try {
      const res = await this.client.get(
        `/v1.0/work/apps/${appId}/members`,
        {}
      ) as any;
      return {
        data: res,
      };
    } catch (err: any) {
      return {
        success: false,
        errorMsg: err.message,
      };
    }
  }
}
