/**
 * 代码包相关服务，包括：
 * 1. 包结构分析
 * 2. npm依赖分析
 */
import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import * as path from 'path';
import * as fse from 'fs-extra';
import ProjectService from '@/apis/service/project';
import IterBranchService from '@/apis/service/iter-branch';
import { downloadFile } from '@/apis/utils/network';
import { unCompress } from '@/apis/utils/file';
import { useTmpDir } from '@/apis/middleware/hooks';
import { catchError } from '@/apis/middleware';
import { uploadToBasement } from '@/apis/utils/file';

@Provide()
export default class ShrinkWrapService {
  @Inject()
  ctx!: Context;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  iterBranchService!: IterBranchService;

  async unzipFile(dist: string) {
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const { filePath } = await downloadFile(dist, tmpDirPath);
    // 解压 zip
    const { unzipFilePath } = await unCompress(filePath);
    return {
      unzipFilePath,
      removeTmpDir,
      tmpDirPath,
    }
  }

  /**
   * 计算包大小 - def上传产物
   * @param dist 构建产物地址
   * @param tmpDirPath 临时目录路径
   */
  @catchError
  async uploadShrinkWrap(iterId: number, dist: string, fPath?: string, notice?: boolean) {
    let unzipFilePath = fPath;
    let removeTmpDir;

    if (!unzipFilePath) {
      const unzipRes = await this.unzipFile(dist);
      unzipFilePath = unzipRes.unzipFilePath;
      removeTmpDir = unzipRes.removeTmpDir;
    }
    const shrinkWrapPath = path.join(unzipFilePath, 'npm-shrinkwrap.json');
    if (!fse.existsSync(shrinkWrapPath)) {
      throw Error('构建产物不包含 npm-shrinkwrap.json 文件');
    }
    const shrinkwrapFile = fse.readFileSync(shrinkWrapPath, { encoding: 'utf-8' });
    const uploadRes = await uploadToBasement(`${iterId}__npm-shrinkwrap.json`, shrinkwrapFile);
    if (!uploadRes?.url) throw Error('shrinkwrap 上传 basement 失败');

    this.ctx.logger.info(`生成 ${iterId} 迭代的 shrinkwrap.json 成功: ${uploadRes.url}`);

    await this.iterBranchService.update({
      iterId,
      shrinkwrap: uploadRes.url
    }, true);
    removeTmpDir?.();
    return uploadRes.url;
  }
}