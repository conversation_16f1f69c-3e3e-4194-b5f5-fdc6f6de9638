/**
 * 代码包相关服务，包括：
 * 1. 包结构分析
 * 2. npm依赖分析
 */
import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import * as path from 'path';
import * as fse from 'fs-extra';
import ProjectService from '@/apis/service/project';
import IterBranchService from '@/apis/service/iter-branch';
import { downloadFile } from '@/apis/utils/network';
import { unCompress } from '@/apis/utils/file';
import { useTmpDir } from '@/apis/middleware/hooks';
import { catchError } from '@/apis/middleware';
import { uploadToBasement } from '@/apis/utils/file';

@Provide()
export default class ShrinkWrapService {
  @Inject()
  ctx!: Context;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  iterBranchService!: IterBranchService;

  async unzipFile(dist: string) {
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const { filePath } = await downloadFile(dist, tmpDirPath);
    // 解压 zip
    const { unzipFilePath } = await unCompress(filePath);
    return {
      unzipFilePath,
      removeTmpDir,
      tmpDirPath,
    }
  }

  /**
   * 获取 shrinkwrap.json 内容
   * @param remoteUrl shrinkwrap 远程地址
   * @param defDist def构建产物
   * @returns 
   */
  async getShrinkwrapInfo(defDist?: string, remoteUrl?: string) {
    // 从构建产物中读取
    if (defDist) {
      const { unzipFilePath, removeTmpDir } = await this.unzipFile(defDist);
      const [shrinkwrapJson, miniworkConfig] = await Promise.all([
        fse.readJSON(path.join(unzipFilePath, 'npm-shrinkwrap.json')),
        fse.readJSON(path.join(unzipFilePath, 'miniwork.config.json')),
      ]);
      removeTmpDir();
      return {
        shrinkwrapJson,
        miniworkConfig,
      };
    }
    // 从cdn读取
    if (remoteUrl) {
      const { tmpDirPath, removeTmpDir } = useTmpDir();
      const shrinkwrapJson = await downloadFile(remoteUrl, tmpDirPath).then(({ filePath }) => fse.readJSON(filePath));
      removeTmpDir();
      return {
        shrinkwrapJson,
      };
    }
    return null;
  }

  /**
   * 计算包大小 - def上传产物
   * @param dist 构建产物地址
   * @param tmpDirPath 临时目录路径
   */
  @catchError
  async uploadShrinkWrap(iterId: number, dist: string, fPath?: string, notice?: boolean) {
    let unzipFilePath = fPath;
    let removeTmpDir;

    if (!unzipFilePath) {
      const unzipRes = await this.unzipFile(dist);
      unzipFilePath = unzipRes.unzipFilePath;
      removeTmpDir = unzipRes.removeTmpDir;
    }
    const shrinkWrapPath = path.join(unzipFilePath, 'npm-shrinkwrap.json');
    if (!fse.existsSync(shrinkWrapPath)) {
      throw Error('构建产物不包含 npm-shrinkwrap.json 文件');
    }
    const shrinkwrapFile = fse.readFileSync(shrinkWrapPath, { encoding: 'utf-8' });
    const uploadRes = await uploadToBasement(`${iterId}__npm-shrinkwrap.json`, shrinkwrapFile);
    if (!uploadRes?.url) throw Error('shrinkwrap 上传 basement 失败');

    this.ctx.logger.info(`生成 ${iterId} 迭代的 shrinkwrap.json 成功: ${uploadRes.url}`);

    await this.iterBranchService.update({
      iterId,
      shrinkwrap: uploadRes.url
    }, true);
    removeTmpDir?.();
    return uploadRes.url;
  }
}