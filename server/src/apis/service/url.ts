import { Provide, Inject } from '@midwayjs/core';
import * as path from 'path';
import * as fse from 'fs-extra';
import URL_MAP_CONFIG from '@/apis/config/url';
import { IUrl } from '@/apis/interface/url';
import { useTmpDir, useGit } from '@/apis/middleware/hooks';

@Provide()
export default class UrlService {
  @Inject()
  serviceManager: any;

  /** 获取 url 关系表，键值对格式 */
  async getMap(projectName: string): Promise<{ [h5Url: string]: string }> {
    const cfg = URL_MAP_CONFIG[projectName];

    if (!cfg) throw Error(`不支持 ${projectName} 项目`);

    const { gitUrl, filePath, parseFunc } = cfg;
    const { tmpDirPath, removeTmpDir } = useTmpDir();
    const { projectTmpDirPath } = await useGit(tmpDirPath, { branchUrl: gitUrl, branchName: 'master', cloneSingleBranch: true });

    const urlMapFile = await fse.readFile(path.join(projectTmpDirPath, filePath), {
      encoding: 'utf-8'
    });

    // 删除临时文件
    removeTmpDir();

    return parseFunc(urlMapFile);
  }

  /** 获取 url 关系表，数组格式 */
  async getList(projectName: string): Promise<IUrl[]> {
    const [urlMap, titanUrlMap] = await Promise.all([this.getMap(projectName), this.getListByTitan(projectName)]);

    return this.mergeSdkAndTitanList(urlMap, titanUrlMap);
  }

  /** 获取 泰坦配置的url 关系表，数组格式 */
  async getListByTitan(projectName: string): Promise<IUrl[]> {
    const cfg = URL_MAP_CONFIG[projectName];
    if (cfg.titanRid) {
      const res = await this.serviceManager.tbTripfcecoreService.invoke({
        rids: cfg.titanRid
      });

      if (res.success && res.model && res.model.data && res.model.data[cfg.titanRid] && res.model.data[cfg.titanRid].data && res.model.data[cfg.titanRid].data.length) {
        return res.model.data[cfg.titanRid].data;
      }
    }
    
    return [];
  }

  mergeSdkAndTitanList(urlMap, titanUrlMap) {
    // 泰坦的优先级比较高
    const handleUrlMap = Object.entries(urlMap).map(([key, value], index) => {
      const findTitanIndex = titanUrlMap.findIndex(item => item.h5Url === key);
      if (findTitanIndex !== -1) {
        titanUrlMap[findTitanIndex].delete = true;
        return {
          key: index + 1,
          h5Url: titanUrlMap[findTitanIndex].h5Url,
          pagePath: titanUrlMap[findTitanIndex].miniUrl,
          version: titanUrlMap[findTitanIndex].allinoneVersion
        }
      } else {
        return {
          key: index + 1,
          h5Url: key,
          pagePath: value,
          version: '*'
        }
      }
    });

    const handleTaitanUrlMap = titanUrlMap.flatMap((item, index) => {
      if (!item.delete) {
        return [{
          key: index + 1 + handleUrlMap.length,
          h5Url: item.h5Url,
          pagePath: item.miniUrl,
          version: item.allinoneVersion
        }]
      }
      return []
    });

    return [...handleUrlMap, ...handleTaitanUrlMap];
  }
}