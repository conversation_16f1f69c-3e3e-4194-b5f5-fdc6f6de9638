import { Inject, Provide } from '@midwayjs/core';
import { convertsCollectionToCamel, convertsCollectionToSnake } from '@/apis/utils';
import { IIterCalendarItem } from '@/apis/interface/iter-calendar';
import { IIterCalendarModel } from '@/apis/model/iter-calendar';

@Provide()
export default class IterCalendarService {
  @Inject()
  IterCalendarModel!: IIterCalendarModel;

  async create(values: IIterCalendarCreateValues): Promise<IIterCalendarItem> {
    const dbValues = convertsCollectionToSnake(values);

    const iterCalendarModel = await this.IterCalendarModel.create(dbValues, {
      fields: ['iter_id', 'year', 'month', 'day', 'project_name']
    });
    const res = iterCalendarModel.toJSON();

    return convertsCollectionToCamel(res);
  }

  async filter(values: IIterCalendarFilterValues): Promise<IIterCalendarItem[]> {
    const { year, month, day, projectName } = values;
    const condition: any = {};

    if (year) condition.year = year;
    if (month) condition.month = month;
    if (day) condition.day = day;
    if (projectName) condition.project_name = projectName;

    const res = await this.IterCalendarModel.findAll({
      where: condition,
      raw: true
    });

    return convertsCollectionToCamel(res);
  }
}

// 迭代日历新增条目
export interface IIterCalendarCreateValues {
  iterId: number;
  year: number;
  month: number;
  day: number;
  projectName: string;
}

// 迭代日历筛选条目
export interface IIterCalendarFilterValues {
  year?: number;
  month?: number;
  day?: number;
  projectName?: string;
}