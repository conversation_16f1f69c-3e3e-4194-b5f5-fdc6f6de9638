import * as chalk from 'chalk';
import { Context } from '@midwayjs/faas';
import IterDeliverService from './iter-deliver';
import BaseHandler from '@/apis/functions/base';
import { Inject, Provide } from '@midwayjs/core';
import ProjectService from '@/apis/service/project';
import { EClient } from '@/apis/const/iter-deliver';
import DingtalkService from '@/apis/service/dingtalk';
import IterBranchService from '@/apis/service/iter-branch';
import AlipayOpenApiHTTP from '@/apis/http/alipay-open-api';
import MiniworkServiceHTTP from '@/apis/http/miniwork-service';
import { IIterExperienceModel } from '../model/iter-experience';
import { EMiniAppVersionStatus } from '@/apis/const/iter-deliver';
import { formatDate, convertsCollectionToCamel } from '@/apis/utils';
import { IIterExperience, IIterExperienceCreateReq } from '@/apis/interface/iter-experience';

@Provide()
export default class IterExperienceService extends BaseHandler {
  @Inject()
  ctx!: Context;

  @Inject()
  IterExperienceModel!: IIterExperienceModel;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  iterDeliverService!: IterDeliverService;

  @Inject()
  alipayOpenApiHTTP!: AlipayOpenApiHTTP;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  miniworkServiceHTTP!: MiniworkServiceHTTP;

  async get(conditions){
    // experience_id: experienceId,
    const res = await this.IterExperienceModel.findOne({
      where: conditions || {},
      order: [
        ['experience_id', 'DESC']
      ],
      raw: true,
    });
    const resData: IIterExperience | null = res ? this._covertToClientData(res) : null;
    return resData;
  }

  async submitMiniAppVersion({miniAppVersion, miniAppId, clientName}: {miniAppVersion: any, miniAppId: string, clientName: EClient}) {
    const res = await this.alipayOpenApiHTTP.submitAudit({
      miniAppId,
      clientName,
      appVersion: miniAppVersion,
      versionDesc: "飞猪支端小程序迭代，涉及部分功能调整"
    });
    return res;
  }

  async getExperienceQRCode(params: { miniAppId: string, clientName: EClient, miniAppVersion: string, iterId: number, commitId: string, hashK: any }) {
    const { miniAppId, clientName, miniAppVersion, iterId, commitId } = params;
    let temp = {
      miniAppId,
      clientName,
      appVersion: miniAppVersion,
      iterId,
      commitId,
    };

    const getExperienceQR: any = new Promise((resolve, reject) => {
      const res: any = this.queryExperienceInfoData({
        miniAppId,
        clientName,
        appVersion: miniAppVersion
      });
      if(res && res.status === 'expVersionPackged') {
        resolve({
          ...temp, 
          qrCode: res.expQrCodeUrl,
          schemaUrl: res.expSchemaUrl,
          status: 2
        })
      }
    });
    const getAndGenExperienceQR: any = new Promise(async(resolve, reject) => {
      const res1: any = await this.alipayOpenApiHTTP.createExperience({
        miniAppId,
        appVersion: miniAppVersion
      });
      if(res1.code == 10000) {
        const res2: any = await this.queryExperienceInfoData({
          miniAppId,
          clientName,
          appVersion: miniAppVersion
        });
        if(res2 && res2.status === 'expVersionPackged') {
          resolve({
            ...temp, 
            qrCode: res2.expQrCodeUrl,
            schemaUrl: res2.expSchemaUrl,
            status: 2
          })
        }
      }
    });
    const timeoutPromise: any = new Promise((resolve, reject) => {
      const timeIns = setTimeout(() => {
        resolve({
          ...temp,
          qrCode: "",
          schemaUrl: "",
          status: -1
        });
        clearTimeout(timeIns);
      }, 1000 * 60 * 5);
    });
    const res = await Promise.race([
      getExperienceQR,
      getAndGenExperienceQR,
      timeoutPromise
    ]);

    return res;
  }

  async getExperienceInfo(params: { miniAppId: string, clientName: EClient, commitId: string, iterId: number }) {
    const { miniAppId, clientName, commitId, iterId } = params;
    // 任何时候，这4个筛选条件聚合检索至多只有一条数据
    const experienceItem: any = this.get({
      commit_id: commitId,
      iter_id: iterId,
      mini_app_id: miniAppId,
      client_name: clientName,
    });
    return experienceItem;
  }

  async createExperienceTask(params: IIterExperienceCreateReq) {
    const { clientName, miniAppId,  iterId, reBuild=false, branchName, branchUrl } = params;

    // 获取最新版本号，并 patch 版本号 +1
    // 注意要等上面的构建结束再获取版本号，因为构建时间过长，版本号中途可能有新增
    const { appVersion } = await this.alipayOpenApiHTTP.lastQuery({
      clientName,
      miniAppId,
    });
    if (!appVersion) {
      const errMsg = '支系小程序体验码回归 >>> 上传体验版，获取最新版本号失败';
      this.ctx.logger.error(errMsg);
      throw new Error(errMsg);
    }
    await this.clearMiniappInitVersion({ miniAppId, clientName });
    const iterExperienceData = await this.miniworkServiceHTTP.uploadAlipay({
      branchName,
      branchUrl,
      clientName,
      miniAppId,
      appVersion,
      iterId,
      creator: this.ctx.user.name,
      creatorWorkerId: this.ctx.user.workid,
      reBuild
    })
    return iterExperienceData;
  }

  async clearMiniappInitVersion(params: { miniAppId: string; clientName: EClient; }) {
    const { miniAppId, clientName } = params;
    this.ctx.logger.info(chalk.cyan(`支系小程序体验码回归 >>> 上传体验版，开始清理支付宝后台小程序版本`))
    // 获取上传版本列表
    const { miniVersionBaseInfoList } = await this.alipayOpenApiHTTP.batchQuery({
        miniAppId,
        clientName,
        versionStatus: EMiniAppVersionStatus.INIT,
        pageSize: 5,
        pageNum: 4
    });
    // 自动清理版本
    if (miniVersionBaseInfoList && miniVersionBaseInfoList.length > 1) {
      await Promise.race(miniVersionBaseInfoList.map(async item => {
        const res = await this.alipayOpenApiHTTP.infoDelete({
          miniAppId,
          clientName,
          appVersion: item.appVersion
        })
        if (res.code === '10000') { // 成功
          // 【异步】通知操作者
          this.dingtalkService.notice(this.ctx.user.workid, '自动清理历史版本', `上传小程序 ${miniAppId} 到 ${clientName} 时，由于检测到当前开发中版本即将超过最大数量，已自动删除历史版本 ${item.appVersion}`)
            return true;
          } else {
            return false;
          }
      }))
    };
  }

  async cancelExperience(params: { miniAppId: string; clientName: EClient; }) {
    const { miniAppId, clientName } = params;
    const res: any = await this.alipayOpenApiHTTP.cancelExperience({
      miniAppId,
      clientName,
    });
    return res?.code == 10000;
  }

  async queryExperienceInfoData(
    params: { miniAppId: string; clientName: EClient; appVersion: string; }
  ){
    const { miniAppId, clientName, appVersion } = params;
    
    // 首次查询
    this.ctx.logger.info(chalk.green('支系小程序体验码回归 >>> 生成体验版，查询体验码信息 ', miniAppId, clientName, appVersion));
    const initialRes = await this.alipayOpenApiHTTP.queryExperienceInfo({
      miniAppId,
      clientName,
      appVersion: appVersion
    });

    // 如果没有打码任务，或任务已完成，则直接返回
    if (initialRes && initialRes.status !== "expVersionPackaging") {
      return initialRes;
    }

    // 如果正在打包中，则开始轮询等待
    this.ctx.logger.info(chalk.yellow('支系小程序体验码回归 >>> 生成体验版，体验码生成中，开始轮询等待...'));
    
    // 使用Promise处理轮询
    return new Promise((resolve, reject) => {
      const POLLING_INTERVAL = 15 * 1000; // 1分钟
      const MAX_POLLING_TIME = 5 * 60 * 1000; // 分钟
      let elapsedTime = 0;
      
      const intervalId = setInterval(async () => {
        try {
          // 更新已等待时间
          elapsedTime += POLLING_INTERVAL;
          const remainingTime = MAX_POLLING_TIME - elapsedTime;
          
          // 检查是否超时
          if (remainingTime <= 0) {
            clearInterval(intervalId);
            // reject(new Error('查询体验码信息超时，已等待20分钟'));
            resolve({
              err: '查询体验码信息超时，已等待20分钟',
              status: 'expVersionPackageTimeout'
            })
            return;
          }
          
          this.ctx.logger.info(`支系小程序体验码回归 >>> 生成体验版，查询${appVersion}体验码信息轮询剩余期限: ${Math.floor(remainingTime / 60000)}分钟`);
          
          // 查询体验码信息
          const res = await this.alipayOpenApiHTTP.queryExperienceInfo({
            miniAppId,
            clientName,
            appVersion: appVersion
          });
          
          // 如果打包完成，则返回结果
          if (res && res.status !== "expVersionPackaging") {
            clearInterval(intervalId);
            resolve(res);
          }
        } catch (error) {
          clearInterval(intervalId);
          // reject(error);
          resolve({
            err: error,
            status: 'expVersionPackageError'
          })
        }
      }, POLLING_INTERVAL);
    });
  }

  _covertToClientData(dbData){
    return {
      ...convertsCollectionToCamel(dbData, 1),
      gmtCreate: dbData.gmt_create && formatDate(dbData.gmt_create),
      gmtModified: dbData.gmt_modified && formatDate(dbData.gmt_modified),
      isValid: (Date.now() - dbData.gmt_create) < 1000 * 60 * 60 * 24 * 7,
    };
  }
}
