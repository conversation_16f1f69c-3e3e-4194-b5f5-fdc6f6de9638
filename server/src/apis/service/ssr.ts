import * as crypto from 'crypto';
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { HttpService } from '@midwayjs/axios';
import SsrProjectService from '@/apis/service/ssr-project';
import { formatDate, isProd } from "@/apis/utils";
import SsrHSF from '@/apis/hsf/ssr';
const ALY = require('aliyun-sdk');
const Core = require('@alicloud/pop-core');

const sunfireAccessKeyId = 'wbB9cmgSXro4VH6i';
const sunfireAccessKeySecret = '9efc90799571ba07d8a595a2cf141f9f';
const cdnClient = new Core({
  accessKeyId: 'LTAI5t6KVx57uSvLrM7HNzGu',
  accessKeySecret: '******************************',
  endpoint: 'https://dcdn.aliyuncs.com',
  apiVersion: '2018-01-15'
});

const ossCdnClient = new Core({
  accessKeyId: 'LTAI5tCbmXb3iV3cxok5VbP1',
  accessKeySecret: '******************************',
  endpoint: 'https://cdn.aliyuncs.com',
  apiVersion: '2018-05-10'
})

const sls = new ALY.SLS({
  accessKeyId: 'LTAI5tDwKExnH8vud8AB1DGw',
  secretAccessKey: '******************************',
  endpoint: 'http://cn-wulanchabu.log.aliyuncs.com',
  // 这是 sls sdk 目前支持最新的 api 版本, 不需要修改
  apiVersion: '2015-06-01',
  // 以下是可选配置
  httpOptions: {
    timeout: 2000  // 1秒, 默认没有timeout
  },
});

const prefetchSls = new ALY.SLS({
  accessKeyId: 'LTAI5tDwKExnH8vud8AB1DGw',
  secretAccessKey: '******************************',
  endpoint: 'http://cn-zhangjiakou.log.aliyuncs.com',
  // 这是 sls sdk 目前支持最新的 api 版本, 不需要修改
  apiVersion: '2015-06-01',
  // 以下是可选配置
  httpOptions: {
    timeout: 2000  // 1秒, 默认没有timeout
  },
});

const TAIR_USER = '6cf1ed1dbea14839';
// 线上
const FCE_ID = {
  // F3+F4灰度排期
  '4': 1263964,
  // F3+F4正式排期
  '3': 1263963,
  // F1+F2灰度排期
  '2': 1263962,
  // F1+F2正式排期
  '1': 1240777
}
// 预发
// const FCE_ID = {
//   // F3+F4灰度排期
//   '4': 1382443,
//   // F3+F4正式排期
//   '3': 1382442,
//   // F1+F2灰度排期
//   '2': 1382441,
//   // F1+F2正式排期
//   '1': 1382439
// }

@Provide()
export default class SsrService {
  @Inject()
  ctx!: Context;

  @Inject('axios:httpService')
  httpService!: HttpService;

  @Inject()
  ssrHSF!: SsrHSF;

  @Inject()
  ssrProjectService!: SsrProjectService;

  async querySunfireMonitor(query: string) {
    const shaParams = {
      accessKeyId: sunfireAccessKeyId,
      timestamp: String(Date.now()),
      action: 'Mql2.query',
      signatureVersion: '2',
      query,
    };
    const hmac = crypto.createHmac('sha1', sunfireAccessKeySecret);
    const sortedParamsKeys = Object.keys(shaParams).sort();
    hmac.update(
      sortedParamsKeys
        .map((k) => `${k}=${shaParams[k]}`)
        .join('&'),
    );
    const signature = hmac.digest('hex');
    const result = await this.httpService.get(
      `http://api.x.alibaba-inc.com/api/dispatcher.do?${sortedParamsKeys.map((k) => `${k}=${encodeURIComponent(shaParams[k])}`).join('&')}&signature=${encodeURIComponent(signature)}`,
      {
        timeout: 3000
      },
    );
    return result.data;
  }

  async queryPerfData(from, to, pid, projectGroup, projectName, pageName, projectBusiness) {
    try {
      const perfRes = await this.ssrHSF.armsPerfData(from, to, pid, projectGroup, projectName, pageName, projectBusiness);
      return perfRes;
    } catch (e) {
      return {}
    }
  }

  // 查错误日志
  async queryErrorList(from, to, projectName, pageName, errorLogType) {
    const targetSlsOptions = {
      query: `* and logType : summary and success : 0 and __topic__: ${projectName} and __source__: ${pageName} ${errorLogType && errorLogType !== 'ALL' ? ('and errorCode: ' + errorLogType): ' and not errorCode: FILE_ERROR not errorCode: AUTO_DOWNLOAD_FILE_ERROR not errorCode: TEST_ACCOUNT_DOWNGRADE'}`,
      projectName: 'fliggy-rax-ssr',
      logStoreName: 'ssr_log',
      from: parseInt(`${from / 1000}`),
      to: parseInt(`${to / 1000}`),
    };
    const errRes: any = await new Promise((resolve, reject) => sls.getLogs(targetSlsOptions, function (err: any, data: any) {
      if(err){
        resolve([]);
      }
      let list:any = [];
      if (data.body) {
        Object.keys(data.body).forEach(item => {
          list.push(data.body[item]);
        })
      }
      resolve(list);
    }));
    return errRes;
  }

  async queryPerfDetail(pid, projectGroup, projectName, pageName, projectBusiness, target) {
    const perfRes = await this.ssrHSF.armsPerfDetailData(pid, projectGroup, projectName, pageName, projectBusiness, target);
    return perfRes;
  }

  // 查询日志
  async querySSRLogData(params) {
    const {
      projectName,
      logStoreName,
      query,
      from,
      to,
    } = params;

    const slsOpt = {
      projectName,
      logStoreName,
      query,
      from,
      to,
    };

    const res = await new Promise((resolve, reject) => {
      (projectName === 'fl-prefetch' ? prefetchSls : sls).getLogs(slsOpt, function (err: any, data: any) {
        if (err) {
          resolve({
            success: false,
            error: err,
          });
        }
        resolve(data);
      })
    });

    return res;
  }

  async queryList(params: any) {
    const listRes = await this.ssrProjectService.list(params);
    listRes.res = (listRes.res || []).map((item: any) => {
      let isDisabled = false;
      if (params.defPublish || (this.ctx.user && (['205521', '359190','265692','282235'].includes(this.ctx.user.workid)))) {
      } else if (item.admin_list.indexOf(params.workId) < 0) {
        isDisabled = true
      }
      const pList = listRes.pRes.filter((ele: any) => {
        return ele.project_id === item.id
      }).map((ele: any) => {
        return {
          ...ele,
          project_business: item.project_business,
          gmt_create: formatDate(ele.gmt_create),
          gmt_modified: formatDate(ele.gmt_modified),
          isDisabled
        }
      })
      return {
        ...item,
        gmt_create: formatDate(item.gmt_create),
        gmt_modified: formatDate(item.gmt_modified),
        isDisabled,
        children: pList
      }
    })
    return listRes;
  }

  async queryProjectByName(params: any) {
    const res =  await this.ssrProjectService.queryProjectByName({ project_name: params.projectName });
    return res;
  }

  async queryPageById(params: any) {
    const res = await this.ssrProjectService.queryPageById(params);
    const project = res && res.id ? await this.ssrProjectService.queryProjectByName({ project_name: res.project_name }) : {};
    let isDisabled = false;
    if (this.ctx.user && (['205521', '359190','265692','282235'].includes(this.ctx.user.workid))) {
    } else if (project.admin_list && this.ctx.user && project.admin_list.indexOf(this.ctx.user.workid) < 0) {
      isDisabled = true
    }
    let preloadConfig = {};
    const tairClient = await this.ctx.tairManager.getClient({
      username: TAIR_USER,
    });
    const tairRes: any = await tairClient.get('fliggy-ssr-preload-update');
    if (res && tairRes && tairRes.data) {
      const tairData = JSON.parse(tairRes.data);
      if (tairData.open && tairData.list) {
        const targetItem = tairData.list.filter(item => {
          return item.url.includes(`/app/trip/${res.project_name}/pages/${res.page_name}`) && item.isUpdate
        });
        if (targetItem.length > 0) {
          preloadConfig = {
            updateTime: tairData.updateTime,
            ratio: tairData.ratio,
            taskTime: `${tairData.tStart}-${tairData.tEnd}`,
            nextRatio: tairData.ratio === 5 ? 10 : tairData.ratio + 10,
            nextUpdate: tairData.ratio === 5 ? tairData.updateTime + 10 * 60 * 1000 :
            tairData.ratio === 10 ? tairData.updateTime + 20 * 60 * 1000 :
            tairData.updateTime + tairData.timeRange * 60 * 1000
          }
        }
      }
    }
    if (res) {
      res.preloadConfig = preloadConfig;
      res.isDisabled = isDisabled;
      res['gmt_create'] = formatDate(res.gmt_create);
      res['gmt_modified'] = formatDate(res.gmt_modified);
      res['gmt_preload'] = res.gmt_preload ? formatDate(res.gmt_preload) : '';
      res['pid'] = project.pid;
      res['project_business'] = project.project_business;
      if (res.ssr_preload === 6 || res.ssr_preload === 7 || res.ssr_preload === 8 || res.ssr_preload === 9) {
        res.forbiddenTag = (new Date()).valueOf() - (new Date(res.gmt_preload)).valueOf() > 10800000 ? false : true
      }
      return res;
    }
    return {};
  }

  async queryPageByParams(params: any) {
    const res = await this.ssrProjectService.queryPageById(params);
    return res;
  }

  async checkProjectExist(params: any) {
    const res = await this.ssrProjectService.checkProjectExist(params);
    return res;
  }

  async createProject(params: any) {
    const res = await this.ssrProjectService.create(params);
    return res;
  }

  async checkPageExist(params: any) {
    const res = await this.ssrProjectService.checkPageExist(params);
    return res;
  }

  async createPage(params: any) {
    const res = await this.ssrProjectService.createPage(params);
    return res;
  }

  async updateProject(params: any) {
    const res = await this.ssrProjectService.updateProject(params);
    return res;
  }

  async updatePage(params: any) {
    const res = await this.ssrProjectService.updatePage(params);
    return res;
  }

  async delete(params: any) {
    console.log(`删除页面执行人：${this.ctx.user && this.ctx.user.workid}，页面是：${params.pageName},id是：${params.id}`)
    if(!(this.ctx.user && this.ctx.user.workid === '359190')){
      return;
    }
    if (params.pageName) {
      const res = await this.ssrProjectService.deletePage({
        id: params.id
      });
      return res;
    } else {
      const res = await this.ssrProjectService.deleteProject({
        id: params.id
      });
      return res;
    }
  }

  async createLog(params: any) {
    const res = await this.ssrProjectService.createLog(params);
    return res;
  }

  async logList(params: any) {
    let res = await this.ssrProjectService.logList(params);
    res.rows = res.rows.map((item: any) => {
      return {
        ...item,
        gmt_create: formatDate(item.gmt_create),
      }
    })
    return {
      data: res.rows,
      total: res.count
    }
  }

  async setDcdn(params: any, config?: any) {
    const { nameSpace, key, contentKey, contentValue } = params;
    if (nameSpace && key && contentKey && contentValue) {
      const cdnRes = await cdnClient.request('GetDcdnKv', { Namespace: nameSpace, Key: key }, { method: 'GET', formatParams: false });
      if (cdnRes && cdnRes.Value) {
        try {
          const cdnValue = JSON.parse(cdnRes.Value);
          try {
            const parseValue = JSON.parse(contentValue);
            cdnValue[contentKey] = parseValue;
          } catch (e: any) {
            cdnValue[contentKey] = contentValue;
          }
          const setRes = await cdnClient.request('PutDcdnKv', { Namespace: nameSpace, Key: key, Value: JSON.stringify(cdnValue) }, { method: 'POST', formatParams: false });
          console.log("同步pathMapNewOss开始", config)
          try{
            if(config && config.syncOss){
              console.log("同步pathMapNewOss-1", config)
              // 同步路径配置到oss
              const tt = await this.httpService.post(
                `https://rax-stream-new.m.fliggy.com/set_path_map_new_oss`,
                {
                  value: JSON.stringify(cdnValue)
                }
              );
              console.log("同步pathMapNewOss-2", tt)
            }
          }catch(e: any){console.log("同步pathMapNewOss失败",e.message)}
          return setRes;
        } catch (err: any) {
          return {
            success: false,
            errorMsg: err?.message
          }
        }
      } else {
        return {
          success: false,
          errorMsg: 'cdnRes is empty'
        }
      }
    } else {
      return {
        success: false,
        errorMsg: 'params is empty'
      }
    }
  }

  async getDcdnKV(params: any) {
    const { 
      nameSpace, 
      key, 
    } = params;

    if (nameSpace && key) {
      try {
        const cdnRes = await cdnClient.request('GetDcdnKv', { Namespace: nameSpace, Key: key }, { method: 'GET', formatParams: false });
        if (cdnRes && cdnRes.Value) {
          return {
            success: true,
            data: cdnRes.Value,
          };
        } else {
          return {
            success: false,
            errorMsg: 'data is empty',
          }
        }
      } catch (err: any) {
        return {
          success: false,
          errorMsg: err?.message
        }
      }      
    } else {
      return {
        success: false,
        errorMsg: 'params is empty'
      }
    }
  }

  async setDcdnKV(params: any) {
    const { 
      nameSpace, 
      key, 
      value 
    } = params;

    if (nameSpace && key && value) {
      const newValue = typeof value === 'string' ? value : JSON.stringify(value);
      try {
        const setRes = await cdnClient.request('PutDcdnKv', { Namespace: nameSpace, Key: key, Value: newValue }, { method: 'POST', formatParams: false });
        return setRes;
      } catch (err: any) {
        return {
          success: false,
          errorMsg: err?.message
        }
      }
    } else {
      return {
        success: false,
        errorMsg: 'params is empty'
      }
    }
  }

  // 修改预加载定时任务配置
  async changePreConfig(params) {
    const { open, timeRange, reset, whiteList, tStart, tEnd, ratio, startPath, deletePath} = params;
    const tairClient = await this.ctx.tairManager.getClient({
      username: TAIR_USER,
    });
    const tairRes: any = await tairClient.get('fliggy-ssr-preload-update');
    if (reset) {
      if (tairRes.data) {
        await tairClient.put('fliggy-ssr-preload-update', JSON.stringify({
          ...JSON.parse(tairRes.data),
          ratio: 0,
          isRestart: false,
          updateTime: (new Date()).getTime(),
          list: []
        }));
        return true;
      } else {
        return false;
      }
    } else if (timeRange || whiteList || tStart || tEnd) {
      if (tairRes.data) {
        if (ratio) {
          // 改tair
          await tairClient.put('fliggy-ssr-preload-update', JSON.stringify({
            ...JSON.parse(tairRes.data),
            open,
            timeRange,
            whiteList,
            tStart,
            tEnd,
            ratio
          }));
          // 改定投规则
          await this.ssrHSF.updatePlanRule(8449, 'F3+F4灰度专用', JSON.stringify([{"dataList":[{"code":"F4","name":"新飞猪F4会员"},{"code":"F5","name":"新飞猪F5会员"},{"code":"F6","name":"新飞猪F6会员"}],"operate":"equal","type":"memberLevelNew"},{"dataList":[{"code":String(ratio),"name":"utdid"}],"operate":"equal","type":"utdidGray"}]))
          await this.ssrHSF.updatePlanRule(8450, 'F1+F2灰度专用', JSON.stringify([{"dataList":[{"code":"F1","name":"新飞猪F1会员"},{"code":"F2","name":"新飞猪F2会员"},{"code":"F3","name":"新飞猪F3会员"}],"operate":"equal","type":"memberLevelNew"},{"dataList":[{"code":String(ratio),"name":"utdid"}],"operate":"equal","type":"utdidGray"}]))  
        } else if (startPath || deletePath) {
          const targetItem = JSON.parse(tairRes.data).list.filter(item => {
            return item.url === startPath || item.url === deletePath
          })[0];
          // 改tair
          let list = JSON.parse(tairRes.data).list.map(item => {
            if (item.url === startPath) {
              return {
                ...item,
                isUpdate: true
              };
            } else if (item.url === deletePath) {
              return 'delete';
            } else {
              return item;
            }
          });
          list = list.filter(item => {
            return item !== 'delete'
          });
          await tairClient.put('fliggy-ssr-preload-update', JSON.stringify({
            ...JSON.parse(tairRes.data),
            open,
            timeRange,
            whiteList,
            tStart,
            tEnd,
            list
          }));
          // 改灰度的泰坦资源位
          const grayFceRes4 = await this.ssrHSF.getPlanDetailById(FCE_ID['4']);
          const grayFceRes2 = await this.ssrHSF.getPlanDetailById(FCE_ID['2']);
          const grayFceData4 = JSON.parse(grayFceRes4.model.staticData);
          const grayFceData2 = JSON.parse(grayFceRes2.model.staticData);
          // 更新配置数据
          grayFceData4.ssr_preload_config = grayFceData4.ssr_preload_config.map(item => {
            if (item.url.includes(startPath)) {
              return {
                ...item,
                refresh_time: targetItem.refreshTime
              }
            } else if (item.url.includes(deletePath)) {
              return {
                ...item,
                refresh_time: ''
              }
            } else {
              return item;
            }
          })
          grayFceData2.ssr_preload_config = grayFceData2.ssr_preload_config.map(item => {
            if (item.url.includes(startPath)) {
              return {
                ...item,
                refresh_time: targetItem.refreshTime
              }
            } else if (item.url.includes(deletePath)) {
              return {
                ...item,
                refresh_time: ''
              }
            } else {
              return item;
            }
          })
          // 发布资源位
          await this.ssrHSF.savePlanBySceneId(FCE_ID['4'], {
            planRuleDTO: grayFceRes4.model.planRule,
            staticData: JSON.stringify(grayFceData4)
          })
          // 发布资源位
          await this.ssrHSF.savePlanBySceneId(FCE_ID['2'], {
            planRuleDTO: grayFceRes2.model.planRule,
            staticData: JSON.stringify(grayFceData2)
          })
        } else {
          await tairClient.put('fliggy-ssr-preload-update', JSON.stringify({
            ...JSON.parse(tairRes.data),
            open,
            timeRange,
            whiteList,
            tStart,
            tEnd
          }));
        }
        return true;
      } else {
        return false;
      }
    } else {
      return tairRes.data ? JSON.parse(tairRes.data) : {}
    }
  }

  // 预加载定时任务
  async preTask() {
    const nowDate = new Date();
    const nowTime = nowDate.toLocaleTimeString('zh-CN', { hour12: false });
    // 连接tair
    const tairClient = await this.ctx.tairManager.getClient({
      username: TAIR_USER,
    });
    // 获取tair中存的值
    const tairRes: any = await tairClient.get('fliggy-ssr-preload-update');
    if (tairRes && tairRes.data) {
      const tairData = JSON.parse(tairRes.data) || {};
      const tStart = tairData.tStart || '09:00:00';
      const tEnd = tairData.tEnd || '21:00:00';
      // 任务时间 09:00:00 - 21:00:00
      if (nowTime >= tEnd || nowTime < tStart) {
        return;
      }
      // 关了开关，没到更新时间不更新
      if (!tairData.open ||
        (tairData.ratio === 5 && (nowDate.getTime() - tairData.updateTime) < 10 * 60000) ||
        (tairData.ratio === 10 && (nowDate.getTime() - tairData.updateTime) < 20 * 60000) ||
        (tairData.ratio > 10 && (nowDate.getTime() - tairData.updateTime) < tairData.timeRange * 60000)
      ) {
        return;
      }
      let list = tairData.list || [];
      // 筛出需要更新的页面,当前灰度90%筛选出isUpdate为false的页面
      list = tairData.ratio === 90 ? list.filter(item => {
        return !item.isUpdate
      }) : list.filter(item => {
        return item.isUpdate
      }); 
      if (list.length) {
        let ratio = 5;
        if (!tairData.isRestart && tairData.ratio === 5) {
          ratio = 10;
        } else if (!tairData.isRestart && tairData.ratio >= 10 && tairData.ratio <= 80) {
          ratio = tairData.ratio + 10;
        }
        // 当前90%，筛选出来的页面isUpdate改为true
        const newList = tairData.ratio === 90 ? list.map(item => {
          return {
            ...item,
            isUpdate: true
          }
        }) : tairData.list;
        await tairClient.put('fliggy-ssr-preload-update', JSON.stringify({
          ...tairData,
          ratio,
          isRestart: false,
          updateTime: nowDate.getTime(),
          list: newList
        }));
        // 改定投规则和泰坦数据(先灰度后正式)，更新orange时间
        // 改定投规则调用
        await this.ssrHSF.updatePlanRule(8449, 'F3+F4灰度专用', JSON.stringify([{"dataList":[{"code":"F4","name":"新飞猪F4会员"},{"code":"F5","name":"新飞猪F5会员"},{"code":"F6","name":"新飞猪F6会员"}],"operate":"equal","type":"memberLevelNew"},{"dataList":[{"code":String(ratio),"name":"utdid"}],"operate":"equal","type":"utdidGray"}]))
        await this.ssrHSF.updatePlanRule(8450, 'F1+F2灰度专用', JSON.stringify([{"dataList":[{"code":"F1","name":"新飞猪F1会员"},{"code":"F2","name":"新飞猪F2会员"},{"code":"F3","name":"新飞猪F3会员"}],"operate":"equal","type":"memberLevelNew"},{"dataList":[{"code":String(ratio),"name":"utdid"}],"operate":"equal","type":"utdidGray"}]))

        // 灰度为5的时候改灰度资源位和正式资源位
        if (ratio === 5) {
          const grayFceRes4 = await this.ssrHSF.getPlanDetailById(FCE_ID['4']);
          const grayFceRes3 = await this.ssrHSF.getPlanDetailById(FCE_ID['3']);
          const grayFceRes2 = await this.ssrHSF.getPlanDetailById(FCE_ID['2']);
          const grayFceRes1 = await this.ssrHSF.getPlanDetailById(FCE_ID['1']);
          const grayFceData4 = JSON.parse(grayFceRes4.model.staticData);
          const grayFceData2 = JSON.parse(grayFceRes2.model.staticData);
          if (tairData.ratio === 90) {
            // 改正式资源位数据
            await this.ssrHSF.savePlanBySceneId(FCE_ID['3'], {
              planRuleDTO: grayFceRes3.model.planRule,
              staticData: grayFceRes4.model.staticData
            })
            await this.ssrHSF.savePlanBySceneId(FCE_ID['1'], {
              planRuleDTO: grayFceRes1.model.planRule,
              staticData: grayFceRes2.model.staticData
            })
          }
          // 更新灰度资源位数据
          grayFceData4.ssr_preload_config = grayFceData4.ssr_preload_config.map(item => {
            const targetPage = list.filter(ele => {
              return item.url.includes(ele.url)
            });
            if (targetPage.length) {
              return {
                ...item,
                refresh_time: targetPage[0].refreshTime
              }
            } else {
              return item;
            }
          })
          grayFceData2.ssr_preload_config = grayFceData2.ssr_preload_config.map(item => {
            const targetPage = list.filter(ele => {
              return item.url.includes(ele.url)
            });
            if (targetPage.length) {
              return {
                ...item,
                refresh_time: targetPage[0].refreshTime
              }
            } else {
              return item;
            }
          })
          // 发布资源位
          await this.ssrHSF.savePlanBySceneId(FCE_ID['4'], {
            planRuleDTO: grayFceRes4.model.planRule,
            staticData: JSON.stringify(grayFceData4)
          })
          // 发布资源位
          await this.ssrHSF.savePlanBySceneId(FCE_ID['2'], {
            planRuleDTO: grayFceRes2.model.planRule,
            staticData: JSON.stringify(grayFceData2)
          })
        }
        // 更新orange
        this.ssrHSF.orangeFunc({
          appKey: '12381755',
          namespace: 'ssr_preload_publish',
          appVersion: "*",
          content: `update_time="${formatDate(new Date())}"`,
          operator: '205521',
          isNeedReview: false
        }, {
          bussinessName: 'pigeon-platform',
          bussinessToken: isProd ? '88b0dad660a7e3a7' : '9488740eefdee50a',  //线上88b0dad660a7e3a7 
        })
        this.ssrHSF.orangeFunc({
          appKey: '12663307',
          namespace: 'ssr_preload_publish',
          appVersion: "*",
          content: `update_time="${formatDate(new Date())}"`,
          operator: '205521',
          isNeedReview: false
        }, {
          bussinessName: 'pigeon-platform',
          bussinessToken: isProd ? '88b0dad660a7e3a7' : '9488740eefdee50a',  //线上88b0dad660a7e3a7 
        })
      } else {
        if (tairData.list.length > 0) {
          // 改正式资源位
          const grayFceRes4 = await this.ssrHSF.getPlanDetailById(FCE_ID['4']);
          const grayFceRes3 = await this.ssrHSF.getPlanDetailById(FCE_ID['3']);
          const grayFceRes2 = await this.ssrHSF.getPlanDetailById(FCE_ID['2']);
          const grayFceRes1 = await this.ssrHSF.getPlanDetailById(FCE_ID['1']);
          await this.ssrHSF.savePlanBySceneId(FCE_ID['3'], {
            planRuleDTO: grayFceRes3.model.planRule,
            staticData: grayFceRes4.model.staticData
          })
          await this.ssrHSF.savePlanBySceneId(FCE_ID['1'], {
            planRuleDTO: grayFceRes1.model.planRule,
            staticData: grayFceRes2.model.staticData
          })
          // tair数据重置
          await tairClient.put('fliggy-ssr-preload-update', JSON.stringify({
            ...tairData,
            ratio: 0,
            isRestart: false,
            updateTime: nowDate.getTime(),
            list: []
          }));
        }
      }
    } else {
      await tairClient.put(`fliggy-ssr-preload-update`, JSON.stringify({
        // 是否开启自动化任务
        open: true,
        // 10pt灰度时长
        timeRange: 30,
        // 当前灰度比例
        ratio: 0,
        // 是否要回退到从5%开始
        isRestart: false,
        // 当前任务时间戳
        updateTime: nowDate.getTime(),
        // 灰度列表
        list: [],
        // 页面白名单
        whiteList: ''
      }));
    }
  }

  // 页面发布，更新tair中的预加载配置
  async preUpdate(url:string) {
    // 查询泰坦配置中是否包含页面链接
    try {
      const fceRes = await this.ssrHSF.getPlanDetailById(FCE_ID['3']);
      const fceList = (JSON.parse(fceRes.model.staticData).ssr_preload_config || []).filter(item => {
        return item.url.includes(url)
      });
      if (fceList.length) {
        const tairClient = await this.ctx.tairManager.getClient({
          username: TAIR_USER,
        });
        const tairRes: any = await tairClient.get('fliggy-ssr-preload-update');
        if (tairRes && tairRes.data) {
          const tairData = JSON.parse(tairRes.data);
          if (tairData.open && (!tairData.whiteList || tairData.whiteList.includes(url))) {
            let newList = [];
            const tairList = tairData.list;
            // tair列表中是否包含当前页面
            const tairItem = tairList.filter(item => {
              return item.url.includes(url)
            });
            if (tairItem.length) {
              // 包含当前页面,不做任何处理，继续放量
              // if (tairData.ratio < 20) {
              //   // 灰度小于20，更新刷新时间，当前页面进入灰度
              //   newList = tairList.map(item => {
              //     if (item.url.includes(url)) {
              //       return {
              //         url,
              //         refreshTime: formatDate(new Date()),
              //         isUpdate: true
              //       }
              //     }
              //     return item;
              //   });
              //   await tairClient.put('fliggy-ssr-preload-update', JSON.stringify({
              //     ...tairData,
              //     list: newList
              //   }));
              // } else if ((tairData.ratio <= 50 && tairList.length > 3) || (tairData.ratio > 50 && tairList.length > 1)) {
              //   // 灰度小于50且页面大于3个，或灰度大于50且页面大于1个，更新刷新时间，当前页面不灰度
              //   newList = tairList.map(item => {
              //     if (item.url.includes(url)) {
              //       return {
              //         ...item,
              //         refreshTime: formatDate(new Date()),
              //         isUpdate: false
              //       }
              //     }
              //     return item;
              //   });
              //   await tairClient.put('fliggy-ssr-preload-update', JSON.stringify({
              //     ...tairData,
              //     list: newList
              //   }));
              // } else {
              //   // 其他情况，更新刷新时间，灰度进度回退
              //   newList = tairList.map(item => {
              //     if (item.url.includes(url)) {
              //       return {
              //         url,
              //         refreshTime: formatDate(new Date()),
              //         isUpdate: true
              //       }
              //     }
              //     return item;
              //   });
              //   await tairClient.put('fliggy-ssr-preload-update', JSON.stringify({
              //     ...tairData,
              //     isRestart: true,
              //     list: newList
              //   }));
              // }
            } else {
              // 不包含当前页面
              if (tairData.ratio < 20) {
                // 灰度小于20，当前页面进入灰度
                newList = tairList.concat({
                  url,
                  refreshTime: formatDate(new Date()),
                  isUpdate: true
                });
                await tairClient.put('fliggy-ssr-preload-update', JSON.stringify({
                  ...tairData,
                  list: newList
                }));
              } else if (tairData.ratio <= 50 && tairList.length < 5) {
                // 灰度小于等于50且列表中页面少于5个，灰度进度回退
                newList = tairList.concat({
                  url,
                  refreshTime: formatDate(new Date()),
                  isUpdate: true
                });
                await tairClient.put('fliggy-ssr-preload-update', JSON.stringify({
                  ...tairData,
                  isRestart: true,
                  list: newList
                }));
              } else {
                // 灰度小于等于50且列表中页面大于等于3个或灰度大于50，当前页面不灰度
                newList = tairList.concat({
                  url,
                  refreshTime: formatDate(new Date()),
                  isUpdate: false
                });
                await tairClient.put('fliggy-ssr-preload-update', JSON.stringify({
                  ...tairData,
                  list: newList
                }));
              }
            }
          }
        }
      }
      return fceRes;
    } catch(e) {}
  }

  // 页面发布，刷新oss页面
  async updateCdn(projectName, pageName) {
    if (!projectName || !pageName) {
      return;
    }
    await this.ctx.hsfClient.invoke({
      id: 'com.aliyun.fc.open.api.hsf.HsfGatewayService:1.0.0',
      group: 'HSF',
      method: 'invoke',
      args: [
        '207839128012173244',
        'fl-rax-ssr-new',
        'utilsService-handleOssUpdate',
        { 
          groupName: projectName,
          pageName: pageName
        },
        'utilsService-handleOssUpdate__stable',
      ],
      parameterTypes: ['java.lang.String', 'java.lang.String', 'java.lang.String', 'java.lang.Object', 'java.lang.String'],
      responseTimeout: 20000,
    }).catch(err => {});
  }

  // cdn预热
  async cdnHot(list) {
    const hotRes = await ossCdnClient.request('PushObjectCache', { 
      ObjectPath: list.join('\n')
    }, { method: 'POST', formatParams: false });
    return hotRes;
  }
}
