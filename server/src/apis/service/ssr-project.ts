import { Inject, Provide } from '@midwayjs/core';
import { Op } from "sequelize";
import { ISsrProjectModel } from '@/apis/model/ssr-project';
import { formatDate } from "@/apis/utils";
import { ISsrPageModel } from '../model/ssr-page';
import { ISsrLogModel } from '../model/ssr-log';

@Provide()
export default class SsrProjectService {
  @Inject()
  SsrProjectModel!: ISsrProjectModel;

  @Inject()
  SsrPageModel!: ISsrPageModel;

  @Inject()
  SsrLogModel!: ISsrLogModel;

  async checkProjectExist(params: any): Promise<any> {
    const res = await this.SsrProjectModel.findOne({
      where: { project_name: params.projectName },
      raw: true
    });
    if (res) {
      return true;
    }
    return false;
  }

  async create(params: any): Promise<any> {
    const res = await this.SsrProjectModel.create(params);
    return res
  }

  async list(params: any): Promise<any> {
    const whereOptions: any = {};
    if(params.projectName) whereOptions.project_name = params.isSearch ? {
      [Op.substring]: params.projectName
    } : {
      [Op.eq]: params.projectName
    };
    if(params.isMy) whereOptions.admin_list = {
      [Op.substring]: params.workId
    }
    if (params.project_business && params.project_business != 'default') {
      whereOptions.project_business = {
        [Op.substring]: params.project_business
      }
    }
    const res = await this.SsrProjectModel.findAndCountAll({
      where: whereOptions,
      limit: parseInt(params.pageSize || 10),
      offset: parseInt(params.pageSize || 10) * (parseInt(params.current || 1) - 1),
      order: [['gmt_create', 'DESC']],
      raw: true
    });
    const projectList = res.rows.map(item => {
      return item.id;
    })
    if (projectList.length > 0) {
      const pWhereOptions: any = {};
      pWhereOptions.project_id = {
        [Op.or]: projectList
      }
      const pRes = await this.SsrPageModel.findAll({
        where: pWhereOptions,
        order: [['project_id', 'DESC']],
        raw: true
      });
      return {
        res: res.rows,
        total: res.count,
        pRes
      }
    }
    return {
      res: res.rows,
      total: res.count,
    }
  }

  async queryProjectByName(params: any): Promise<any> {
    const res = await this.SsrProjectModel.findOne({
      where: { project_name: params.project_name },
      raw: true
    });
    return res
  }

  async queryPageById(params: any): Promise<any> {
    if (params.id) {
      const res = await this.SsrPageModel.findOne({
        where: { id: params.id },
        raw: true
      });
      return res
    } else if (params.preload_apply_id) {
      const res = await this.SsrPageModel.findOne({
        where: { preload_apply_id: params.preload_apply_id },
        raw: true
      });
      return res
    } else {
      const res = await this.SsrPageModel.findOne({
        where: params,
        raw: true
      });
      return res
    }
  }

  async checkPageExist(params: any): Promise<any> {
    const res = await this.SsrPageModel.findOne({
      where: { project_id: params.projectId, page_name: params.pageName },
      raw: true
    });
    if (res) {
      return true;
    }
    return false;
  }

  async createPage(params: any): Promise<any> {
    const res = await this.SsrPageModel.create(params);
    return res
  }

  async updateProject(params: any): Promise<any> {
    const updateValue:any = {
      gmt_modified: formatDate(new Date()),
      last_operator: params.last_operator,
      pid: params.pid || '',
      project_group: params.projectGroup || 'trip',
      is_associated_def: params.isAssociatedDef || 2,
    };
    if (params.adminList) updateValue.admin_list = params.adminList;
    if (params.projectBusiness) updateValue.project_business = params.projectBusiness;

    const res = await this.SsrProjectModel.update(updateValue, {
      where: { id: params.id }
    });
    return res
  }

  async updatePage(params: any): Promise<any> {
    const updateValue:any = {
      gmt_modified: formatDate(new Date()),
      last_operator: params.last_operator
    };
    if (params.version) updateValue.pre_version = params.version;
    if (`${params.ratio}` === '0' || params.ratio) updateValue.gray_ratio = params.ratio;
    if (params.grayVersion) updateValue.gray_version = params.grayVersion;
    if (params.prodVersion) updateValue.prod_version = params.prodVersion;
    if (params.ratio === -1) {
      updateValue.gray_ratio = 0;
      updateValue.gray_version = ' ';
    }
    if (params.isLogin) updateValue.need_login = params.isLogin;
    if (params.projectGroup) updateValue.project_group = params.projectGroup;
    if (params.pathProjectName) updateValue.path_project_name = params.pathProjectName;
    if (params.pathPageName) updateValue.path_page_name = params.pathPageName;
    if (params.isImmersive) updateValue.is_immersive = params.isImmersive;
    if (params.csrSameSite) updateValue.csr_same_site = params.csrSameSite;
    if (params.ssrPreload) {
      updateValue.ssr_preload = params.ssrPreload;
      updateValue.gmt_preload = formatDate(new Date())
    };
    if (params.processInstanceId) {
      updateValue.preload_apply_id = params.processInstanceId;
    }
    if (params.spmb) {
      updateValue.preload_spm = params.spmb;
    }
    if (params.isTarget) {
      updateValue.target_user_id = params.targetUser;
      updateValue.target_zg_id = params.targetZg;
    }
    if (params.preStreamConfig) {
      updateValue.pre_stream_config = params.preStreamConfig;
    }
    if (params.streamConfig) {
      updateValue.stream_config = params.streamConfig;
    }
    if (params.enforceDowngrade) {
      updateValue.enforce_downgrade = params.enforceDowngrade;
    }
    if (params.alarmConfig) {
      updateValue.alarm_config = params.alarmConfig;
    }
    if (params.cdnConfig) {
      updateValue.cdn_config = params.cdnConfig;
    }
    const res = await this.SsrPageModel.update(updateValue, {
      where: { id: params.id }
    });
    const pageItem = await this.SsrPageModel.findOne({
      where: { id: params.id }
    })
    if (pageItem && pageItem.project_id) {
      const proValue:any = {
        gmt_modified: formatDate(new Date()),
        last_operator: params.last_operator
      };
      await this.SsrProjectModel.update(proValue, {
        where: { id: pageItem.project_id }
      });
    }

    return res
  }

  async deleteProject(params: any): Promise<any> {
    const pageList = await this.SsrPageModel.findAll({
      where: { project_id: params.id },
      raw: true
    });
    const pageListId = pageList.map(item => {
      return item.id;
    })
    const res = await this.SsrProjectModel.destroy({
      where: { id: params.id }
    });
    await this.SsrPageModel.destroy({
      where: { project_id: params.id }
    });
    if (pageListId.length > 0) {
      const pWhereOptions: any = {};
      pWhereOptions.page_id = {
        [Op.or]: pageListId
      }
      await this.SsrLogModel.destroy({
        where: pWhereOptions
      })
    }
    return res
  }

  async deletePage(params: any): Promise<any> {
    const res = await this.SsrPageModel.destroy({
      where: { id: params.id }
    });
    await this.SsrLogModel.destroy({
      where: { page_id: params.id }
    })
    return res
  }

  async createLog(params: any): Promise<any> {
    const res = await this.SsrLogModel.create(params);
    return res
  }

  async logList(params: any): Promise<any> {
    const whereOpt = { page_id: params.id };
    if (params.publish_type) whereOpt['publish_type'] = params.publish_type;
    if (params.version) whereOpt['version'] = params.version;

    const res = await this.SsrLogModel.findAndCountAll({
      where: whereOpt,
      offset: parseInt(params.pageSize || 10) * (parseInt(params.current || 1) - 1),
      limit: parseInt(params.pageSize || 10),
      order: [['gmt_create', 'DESC']],
      raw: true
    });
    return res
  }

}