import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import * as fse from 'fs-extra';
import * as path from 'path';
import IterBranchService from '@/apis/service/iter-branch';
import ShrinkWrapService from '@/apis/service/shrinkwrap';
import { getSizeLimitInfo, getPackageDiffInfo, getSizeReportMsgContent } from '@/apis/utils/size';
import { downloadFile } from '@/apis/utils/network';
import PackageService from '@/apis/service/package';
import DingtalkService from '@/apis/service/dingtalk';
import SlsService from './sls';
import { getDepsPublishReportMsg } from '@/apis/utils/flat-depencies';
import PROJECT_CONFIG from '@/apis/const/project';

// 同步登录态
const SYNC_LOGIN_PATH = '%2Fskip_sessionfilter%2Flogin_token_login.htm';
const PID_CONFIG: Record<string, string> = {
  wechat: 'haa3j9plpi@ff139ef9b51871b', // 微信
  miniapp: 'haa3j9plpi@1c8365874528b74', // 支付宝
};

const PAGES_MAP = {
  wechat: {
    home: 'pages%2Fmain%2Fhome',
    webview: 'pages%2Fmain%2Fwebview',
  },
  miniapp: {
    home: 'pages%2Findex%2Findex',
    webview: 'pages%2Fwebview%2Findex',
  }
}

function getPerfQuerys(version: string, platform) {
  const versionSql = `and release: ${version}`;
  const pageConfig = PAGES_MAP[platform];
  const launchPageSql = Object.keys(pageConfig)
    .map(pageName => `avg(CASE WHEN c3 = '${pageConfig[pageName]}' THEN t4 END) as ${pageName}_time, SUM(IF(c3 = '${pageConfig[pageName]}', 1, 0)) as ${pageName}_count`)
    .join(',');
  const webviewAvgSql = `avg(t6) as webview_time, avg(CASE WHEN c2 != '${SYNC_LOGIN_PATH}' THEN t6 END) as webview_nologin_time, SUM(IF(c2 != '${SYNC_LOGIN_PATH}', 1, 0)) as webview_nologin_count`;
  return [
    // 启动性能
    `t:perf and c1: launch ${versionSql} and t4 >=0 and t4 <= 60000 | select count(*) as total, avg(t4) as launch_time, ${launchPageSql}`,
    // 套壳容器性能
    `t:perf and c1 not launch and t6 >= 0 and t10 >= 0 and t6 <= 60000 ${versionSql} | select count(*) as total, ${webviewAvgSql} `,
    // 网络性能
    `t:perf and c1: weixin-mtop-controller ${versionSql} | count(*) as total, avg(CAST(c2 AS INTEGER)) as wait_time, avg(time) as request_time `,
  ];
}

/**
 * 稳定性保障服务
 */
@Provide()
export default class StabilityService {
  @Inject()
  ctx!: Context;

  @Inject()
  iterBranchService!: IterBranchService;
  
  @Inject()
  shrinkWrapService!: ShrinkWrapService;

  @Inject()
  packageService!: PackageService;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  slsService!: SlsService;

  /** 获取迭代体积信息 */
  async getIterSizeInfo({ gmtModified, projectName, defUploadPkgRes }: { gmtModified: string; projectName: string; defUploadPkgRes: any; }) {
    if (!defUploadPkgRes) throw Error('参数错误');
    // 查询上一个版本的包体积
    // const prevIterBranch = await this.iterBranchService.getPrevPublishedIter(projectName, gmtModified);
    // TODO
    const prevIterBranch = {
      version: '4.5.4',
      "defUploadPkgRes": "{\"version\":\"4.5.4\",\"uploadResult\":{\"subPackageInfo\":[{\"name\":\"/pages/act-play/\",\"size\":317744},{\"name\":\"/pages/async/\",\"size\":767792},{\"name\":\"/pages/bus-offline/\",\"size\":432971},{\"name\":\"/pages/bus/\",\"size\":6786},{\"name\":\"/pages/common/\",\"size\":2959},{\"name\":\"/pages/common2/\",\"size\":91309},{\"name\":\"/pages/flight/\",\"size\":1347693},{\"name\":\"/pages/flight2/\",\"size\":1272213},{\"name\":\"/pages/growth/\",\"size\":331},{\"name\":\"/pages/holiday-async/\",\"size\":818789},{\"name\":\"/pages/holiday/\",\"size\":21914},{\"name\":\"/pages/home/<USER>",\"size\":1448958},{\"name\":\"/pages/hotel-detail/\",\"size\":1631},{\"name\":\"/pages/hotel-search/\",\"size\":2279},{\"name\":\"/pages/hotel/\",\"size\":7897},{\"name\":\"/pages/insurance/\",\"size\":2252},{\"name\":\"/pages/login/\",\"size\":48306},{\"name\":\"/pages/member/\",\"size\":2669},{\"name\":\"/pages/ningxia/\",\"size\":325},{\"name\":\"/pages/qr-travel/\",\"size\":482225},{\"name\":\"/pages/rent-car/\",\"size\":749956},{\"name\":\"/pages/shop/\",\"size\":772},{\"name\":\"/pages/ticket/\",\"size\":573759},{\"name\":\"/pages/train-12306/\",\"size\":107443},{\"name\":\"/pages/train-main/\",\"size\":590948},{\"name\":\"/pages/train-reverse/\",\"size\":2457},{\"name\":\"/pages/train-search/\",\"size\":682485},{\"name\":\"/pages/utils/\",\"size\":617679},{\"name\":\"/pages/vacation/\",\"size\":4131},{\"name\":\"__APP__\",\"size\":1618249},{\"name\":\"__FULL__\",\"size\":12024922}],\"pluginInfo\":[{\"pluginProviderAppid\":\"wxd46b6e9c9b775a56\",\"version\":\"1.0.14\",\"size\":148157},{\"pluginProviderAppid\":\"wx4d2deeab3aed6e5a\",\"version\":\"1.0.8\",\"size\":78784},{\"pluginProviderAppid\":\"wxf3f436ba9bd4be7b\",\"version\":\"2.0.2\",\"size\":133582}]}}",
    }
    const pkgSize = JSON.parse(defUploadPkgRes);
    const version = pkgSize.version;
    const prevIterVersion = prevIterBranch?.version || '';

    // 上次迭代体积
    let prevPkgSize;
    if (prevIterBranch && prevIterBranch.defUploadPkgRes) {
      prevPkgSize = JSON.parse(prevIterBranch.defUploadPkgRes);
    }
  
    const packageInfo = getPackageDiffInfo(prevPkgSize?.uploadResult, pkgSize.uploadResult, prevIterVersion, version, true);
    const limitInfo = getSizeLimitInfo(packageInfo);

    return {
      version,
      packageInfo,
      limitInfo,
      prevIterVersion: prevIterBranch?.version,
      prevDefPkgRes: prevIterBranch?.defUploadPkgRes,
    };
  }

  /** 生成依赖变更信息 */
  async getDepsInfo(iterBranch, buildUrl: string) {
    const { unzipFilePath, tmpDirPath, removeTmpDir } = await this.shrinkWrapService.unzipFile(buildUrl);
    const [prevIter, shrinkwrapJson, miniworkConfig] = await Promise.all([
      // 查询上一次迭代
      // TODO
      Promise.resolve({ shrinkwrap: "http://alipay-rmsdeploy-image.cn-hangzhou.alipay.aliyun-inc.com/fliggy-miniwork/922__npm-shrinkwrap.json" }),
      // this.iterBranchService.getPrevPublishedIter(iterBranch.projectName, iterBranch.gmtModified || iterBranch.gmtCreate), 
      fse.readJSON(path.join(unzipFilePath, 'npm-shrinkwrap.json')),
      fse.readJSON(path.join(unzipFilePath, 'miniwork.config.json')),
    ]);
    const { noticeConfig } = miniworkConfig;
    let depsDiffResult;
    if (noticeConfig && prevIter && prevIter.shrinkwrap) {
      const prevShrinkwrapJson = await downloadFile(prevIter.shrinkwrap, tmpDirPath).then(({ filePath }) => fse.readJSON(filePath));
      depsDiffResult = this.packageService.diffFlatDependencies(
        shrinkwrapJson.dependencies,
        prevShrinkwrapJson.dependencies,
        noticeConfig
      );
    } 
    removeTmpDir();
    return depsDiffResult;
  }

  /** 获取性能数据 */
  async getPerfInfo(platform: string, version: string) {
    const now = Date.now();
    const params = {
      startTime: now,
      endTime: now,
      pid: PID_CONFIG[platform],
      query: '',
    };
    getPerfQuerys()
    const data = await Promise.all([
      this.slsService.getSLsLogDetail(params),
    ]);
    // todo
  }

  /** 生成小程序发版报告 */
  async generatePublishReport(buildUrl, iterBranch, project) {
    const { gmtModified, defUploadPkgRes, iterId, version } = iterBranch;
    const [depsDiffRes, iterSize] = await Promise.all([
      // 依赖变更
      this.getDepsInfo(iterId, buildUrl).catch(() => null),
      // 体积变更
      this.getIterSizeInfo({ gmtModified, projectName: iterBranch, defUploadPkgRes }).catch(() => null),
      // TODO性能变更
    ]);
    // 发送卡片
    const depsInfo = getDepsPublishReportMsg(depsDiffRes, iterId);
    const sizeInfo = getSizeReportMsgContent(iterSize?.limitInfo, iterId, project.workid);
    if (!depsInfo.text && !sizeInfo.text) {
      return;
    }
    const title = `[${project.cnName}]迭代 v${version} 发版报告`;
    // 文案
    const textList = [
      `![bg](${PROJECT_CONFIG[project.type].dingtalkBigLogo})`,
      `## ${title}`,
      sizeInfo.text,
      depsInfo.text,
    ];
    const msgContent: any = {
      title,
      atUserIds: [...depsInfo.atUserIds, ...sizeInfo.atUserIds],
      isAtAll: false,
      text: textList.join('\n\n --- \n\n'),
      btns: [...sizeInfo.actions, ...depsInfo.actions],
      // singleTitle: '查看详情',
      // singleURL: `${isProd ? PROD_URL : PRE_URL}/#/iter/detail?iterId=${iterId}`
    }
    this.dingtalkService.sendActionCardMessage(msgContent, project);
  }
}

