import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import IterBranchService from '@/apis/service/iter-branch';
import ShrinkWrapService from '@/apis/service/shrinkwrap';
import { getSizeLimitInfo, getPackageDiffInfo, getSizeReportMsgContent } from '@/apis/utils/size';
import PackageService from '@/apis/service/package';
import DingtalkService from '@/apis/service/dingtalk';
import SlsService from '@/apis/service/sls';
import IterDeliverService from '@/apis/service/iter-deliver';
import { getDepsPublishReportMsg, getDepsChangeNoticeInfo } from '@/apis/utils/flat-depencies';
import PROJECT_CONFIG from '@/apis/const/project';
import { IIterBranch, IDeliverClient } from '@/apis/interface/iter-branch';
import { getPerfLogParams, formatPerfLog, getPublishReportMsg } from '@/apis/utils/perf';

/**
 * 稳定性保障服务
 */
@Provide()
export default class StabilityService {
  @Inject()
  ctx!: Context;

  @Inject()
  iterBranchService!: IterBranchService;
  
  @Inject()
  shrinkWrapService!: ShrinkWrapService;

  @Inject()
  packageService!: PackageService;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  slsService!: SlsService;

  @Inject()
  iterDeliverService!: IterDeliverService;

  /** 获取迭代体积信息 */
  async getIterSizeInfo({ gmtModified, projectName, defUploadPkgRes }: { gmtModified?: string; projectName: string; defUploadPkgRes: any; }, prevIter?: IIterBranch | null) {
    if (!defUploadPkgRes) throw Error('参数错误');
    // 查询上一个版本的包体积
    const prevIterBranch = prevIter || await this.iterBranchService.getPrevPublishedIter(projectName, gmtModified);
    const pkgSize = JSON.parse(defUploadPkgRes);
    const version = pkgSize.version;
    const prevIterVersion = prevIterBranch?.version || '';

    // 上次迭代体积
    let prevPkgSize;
    if (prevIterBranch && prevIterBranch.defUploadPkgRes) {
      prevPkgSize = JSON.parse(prevIterBranch.defUploadPkgRes);
    }
  
    const packageInfo = getPackageDiffInfo(prevPkgSize?.uploadResult, pkgSize.uploadResult, prevIterVersion, version, true);
    const limitInfo = getSizeLimitInfo(packageInfo, version);

    return {
      version,
      packageInfo,
      limitInfo,
      prevIterVersion: prevIterBranch?.version,
      prevDefPkgRes: prevIterBranch?.defUploadPkgRes,
    };
  }

  /** 生成依赖变更信息 */
  async getDepsInfo(iterBranch: IIterBranch, prevIterBranch?: IIterBranch | null, defDist?: string) {
    const getPrevIterBranch = () => {
      if (prevIterBranch) return Promise.resolve(prevIterBranch);
      return this.iterBranchService.getPrevPublishedIter(iterBranch.projectName, iterBranch.gmtModified || iterBranch.gmtCreate);
    }
    const [prevIter, shrinkwrapInfo] = await Promise.all([
      // 查询上一次迭代
      getPrevIterBranch(), 
      this.shrinkWrapService.getShrinkwrapInfo(defDist || iterBranch.defDist, iterBranch.shrinkwrap),
    ]);
    const { noticeConfig } = shrinkwrapInfo?.miniworkConfig || {};
    let depsDiffResult;
    if (noticeConfig && prevIter && prevIter.shrinkwrap && shrinkwrapInfo) {
      const prevShrinkwrapInfo = await this.shrinkWrapService.getShrinkwrapInfo('', prevIter.shrinkwrap);
      depsDiffResult = this.packageService.diffFlatDependencies(
        shrinkwrapInfo?.shrinkwrapJson,
        prevShrinkwrapInfo?.shrinkwrapJson,
        noticeConfig
      );
    } 
    return depsDiffResult;
  }

  /**
   * 查询开始投放时间
   * @param platform 端信息
   * @param deliverClientList 投放端列表
   * @returns 
   */
  async getDeliverStartTime(platform: string, deliverClientList?: IDeliverClient[] | null) {
    // 为空直接返回
    const deliverClient = deliverClientList?.find(item => item.clientName === platform);
    if (!deliverClient || !deliverClient.deliverTaskId) return null;

    // 根据投放端查询投放状态
    const deliverInfo = await this.iterDeliverService.getById(deliverClient.deliverTaskId, { needVersionInfo: true }).catch(() => null);
    return deliverInfo?.gmtCreate;
  }

  /** 获取性能数据 */
  async getPerfInfo(platform: string, iterBranch: IIterBranch, prevIterBranch: IIterBranch | null) {
    // 查询开始时间，用于计算对应迭代体验码的有效时间
    const [deliverStartTime, prevDeliverStartTime] = await Promise.all([
      this.getDeliverStartTime(platform, iterBranch?.deliverClientList),
      prevIterBranch ? this.getDeliverStartTime(platform, prevIterBranch?.deliverClientList) : Promise.resolve(null),
    ]);
    const [curPerfData, prevPerfData] = await Promise.all([
      this.slsService.getBatchLog(getPerfLogParams(iterBranch, platform, deliverStartTime)),
      prevIterBranch ? this.slsService.getBatchLog(getPerfLogParams(prevIterBranch, platform, prevDeliverStartTime)) : Promise.resolve({}),
    ]);
    return formatPerfLog(curPerfData, prevPerfData);
  }

  /** 
   * 生成小程序发版报告
   * 暂时只支持微信
   **/
  async generatePublishReport(iterBranch: IIterBranch | null, project, defDist?: string) {
    if (!iterBranch || !project || project.type !== 'weixin') return;
    const { gmtModified, defUploadPkgRes, iterId, version } = iterBranch;
    const prevIterBranch = await this.iterBranchService.getPrevPublishedIter(project.name, gmtModified);
    const [depsDiffRes, iterSize, perfRes] = await Promise.all([
      // 依赖变更
      this.getDepsInfo(iterBranch, prevIterBranch, defDist).catch(() => null),
      // 体积变更
      this.getIterSizeInfo({ gmtModified, projectName: project.name, defUploadPkgRes }, prevIterBranch).catch(() => null),
      // 性能变更
      this.getPerfInfo(project.type, iterBranch, prevIterBranch).catch(() => null),
    ]);
    // 发送卡片
    const depsInfo = getDepsPublishReportMsg(depsDiffRes, iterId);
    const sizeInfo = getSizeReportMsgContent(iterSize?.limitInfo, iterId, project.workid);
    const perfInfo = getPublishReportMsg(perfRes, project.type, [prevIterBranch?.version || '', version]);
    const depsNoticeInfo = getDepsChangeNoticeInfo(depsDiffRes, iterId);

    // 钉钉通知依赖变更
    if (depsNoticeInfo?.length) {
      depsNoticeInfo.forEach((item) => {
        const noticeTitle = `${project.cnName}迭代v${version}依赖变更通知`;
        const content = `#### **${noticeTitle}** \n ${item.content}`;
        this.dingtalkService.notice(item.userList, noticeTitle, content);
      });
    }

    if (!depsInfo.text && !sizeInfo.text && !perfInfo.text) {
      return;
    }
    // 发版报告
    const title = `${project.cnName}迭代 v${version} 发版报告`;
    const noticeText = [perfInfo.text,sizeInfo.text,depsInfo.text].filter(item => !!item).join(`<br>\n`)
    const textList = [
      `![bg](${PROJECT_CONFIG[project.type].dingtalkBigLogo})`,
      `### ${title}📝`,
      noticeText,
    ];
    const msgContent: any = {
      title,
      atUserIds: [...depsInfo.atUserIds, ...sizeInfo.atUserIds],
      isAtAll: false,
      text: textList.join('\n\n'),
      btns: [...perfInfo.actions,  ...sizeInfo.actions, ...depsInfo.actions].map(item => ({
        ...item,
        // 关闭侧边栏打开
        actionURL: `dingtalk://dingtalkclient/page/link?url=${encodeURIComponent(item.actionURL)}&pc_slide=false`
      })),
    }
    this.dingtalkService.sendActionCardMessage(msgContent, project);
  }
}

