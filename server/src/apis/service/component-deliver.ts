import { Op } from 'sequelize';
import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { convertsCollectionToCamel, convertsCollectionToSnake, formatDate, isProd } from '@/apis/utils';
import { IComponentDeliver, IMiniappInfo } from '@/apis/interface/component-deliver';
import { INpm } from '@/apis/interface/npm';
import { IProject } from '@/apis/interface/project';
import ProjectService from '@/apis/service/project';
import UserService from '@/apis/service/user';
import DefService from '@/apis/service/def';
import GitService from '@/apis/service/git';
import IterBranchService from '@/apis/service/iter-branch';
import FreeDevBranchService from '@/apis/service/free-dev-branch';
import DevBranchService from '@/apis/service/dev-branch';
import DingtalkService from '@/apis/service/dingtalk';
import  { IFreeDevBranchCreateParams, IDevBranchCreateParams } from '@/apis/service/base-dev-branch';
import { IComponentDeliverDetailModel } from '@/apis/model/component-deliver-detail';
import { parseGitRepoInfo, getBranchDeps, updateBranchDeps, createDeliverBranch } from '@/apis/utils/git';
import { getBiggestVersion, mergeBranch } from '@/apis/utils/git';
import { EDefBuildStatus, EPubTypeEnv, DEF_ENV_TYPE_WORDING, DEF_BUILD_STATUS_WORDING } from '@/apis/const/def';
import PROJECT_CONFIG, { EProjectType } from '@/apis/const/project';
import { PRE_URL, PROD_URL } from '@/apis/const';
import { EDeliverType, EDeliverBranchType, EMinaStatus } from '@/apis/const/component-deliver';
import { EMergeCode } from '@/apis/const/dev-branch';

@Provide()
export default abstract class ComponentDeliverService {
  @Inject()
  ctx!: Context;

  @Inject()
  projectService!: ProjectService;

  @Inject()
  userService!: UserService;

  @Inject()
  defService!: DefService;

  @Inject()
  gitService!: GitService;

  @Inject()
  iterBranchService!: IterBranchService;

  @Inject()
  freeDevBranchService!: FreeDevBranchService;

  @Inject()
  devBranchService!: DevBranchService;

  @Inject()
  dingtalkService!: DingtalkService;

  @Inject()
  ComponentDeliverDetailModel!: IComponentDeliverDetailModel;

  isFreeDevBranch = false;

  /**
   * 查询单条投放信息
   * @param deliverId 投放id
   * @returns
   */
   async get(deliverId: number, assertExist?: boolean): Promise<IComponentDeliver | null> {
    const res = await this.ComponentDeliverDetailModel.findOne({
      where: { deliver_id: deliverId },
      raw: true,
    });

    if (res) {
      return this._covertToClientData(res);
    } else if (assertExist) {
      throw Error(`查询不到 deliverId 为 ${deliverId} 的投放信息`);
    } else {
      return null;
    }
  }

  /**
   * 查询投放列表信息
   * @param pageSize 每页条数
   * @param pageNum 页码
   * @param conditions 查询条件
   * @returns
   */
  async list({ pageSize, pageNum, conditions }: {pageSize: number, pageNum: number, conditions: {
    devId?: number;
    iterId?: number;
    devIdList?: number[];
    deliverType?: EDeliverType;
    branchType?: EDeliverBranchType;
    projectName?: string;
    defEnvType?: EPubTypeEnv;
    modifier?: string;
  }}) {
    const whereOptions: any = {};
    pageSize = pageSize || 10;
    pageNum = pageNum || 1;

    if (conditions.projectName) whereOptions.project_name = { [Op.like]: `%${conditions.projectName}%` };
    if (conditions.devId) whereOptions.dev_id = conditions.devId;
    if (conditions.iterId) whereOptions.iter_id = conditions.iterId;
    if (conditions.devIdList) whereOptions.dev_id = { [Op.in]: conditions.devIdList };
    if (conditions.deliverType) whereOptions.deliver_type = conditions.deliverType;
    if (conditions.branchType) whereOptions.branch_type = conditions.branchType;
    if (conditions.defEnvType) whereOptions.def_env_type = conditions.defEnvType;
    if (conditions.modifier) whereOptions.modifier = this.ctx.user.workid;

    const { rows, count } = await this.ComponentDeliverDetailModel.findAndCountAll({
      where: whereOptions,
      order: [['deliver_id', 'DESC']],
      limit: pageSize,
      offset: pageSize * (pageNum - 1),
      raw: true
    });

    return {
      list: rows.map(item => this._covertToClientData(item)),
      total: count
    };
  }

  /**
   * 更新单条投放信息
   * @param values
   * @returns
   */
  async update(values: IComponentDeliverUpdateValues, recordOperator = true) {
    // 1. 前置判断
    const deliverItem = await this.get(values.deliverId);
    if (!deliverItem) {
      throw new Error(`id 为 ${values.deliverId} 的投放信息不存在`)
    }

    // 2. 写库
    const dbValues = this._covertToDBData(Object.assign(values,
      recordOperator ? {
        modifier: this.ctx.user.name,
        gmtModified: formatDate(new Date()),
      } : undefined
    ));
    const updateResult = await this.ComponentDeliverDetailModel.update(dbValues, {
      where: { deliver_id: values.deliverId },
    });
    if (updateResult[0] === 0) throw Error(`SQL: 更新投放 ${values.deliverId} 失败`);

    const newDeliverItem = await this.get(values.deliverId);
    if (!newDeliverItem) throw ('')

    return newDeliverItem;
  }

  /**
   * 初始化H5投放（创建git分支、迭代、写入投放数据库）
   * @param params
   * @returns
   */
  async init({ deliverId, devId, iterId, deliverType, branchType, pub_env, projectName, gitRepo, gitProjectId, npmList, aoneBinds, description }: {
    deliverId: number;
    devId: number;
    iterId: number;
    deliverType: EDeliverType;
    branchType: EDeliverBranchType;
    pub_env: EPubTypeEnv;
    projectName: string;
    gitRepo: string;
    gitProjectId: number;
    npmList?: INpm[];
    aoneBinds: string[];
    description: string;
  }): Promise<IComponentDeliver> {
    let defIterId: number | undefined;
    // 1. 前置判断
    // 校验投放是否已存在
    if (deliverId) {
      const existingDeliver = await this.get(deliverId, true);
      if (existingDeliver) throw new Error(`${projectName} 下已存在投放记录不可以新增`);
    }

    // 2. git 操作
    const { group: deliverGroup, project: deliverProject, url } = parseGitRepoInfo(gitRepo)!;
    if (!deliverGroup || !deliverProject) {
      throw Error(`项目的git仓库地址不完整，请检查配置`);
    }

    // 查询投放项目的最新git分支号
    const pkgVersion = await this.getNewAndBiggestVersion({ url, type: Action.Create });
    if (!pkgVersion) {
      throw new Error(`${projectName} 初始化投放失败，因为查询git分支号失败`);
    }
    const branchName = `daily/${pkgVersion}`;

    // 创建投放项目的git分支
    await createDeliverBranch(branchName, gitRepo, {
      baseBranchName: 'master',
      npmList,
      pkgVersion,
    });

    // 3. 创建def迭代
    const res = await this.defService.createIteration({
      repo: `${deliverGroup}/${deliverProject}`,
      name: branchName,
      description,
      branch: branchName,
      version: pkgVersion!,
      trunk: 'master',
      aoneBinds,
    });

    if (res?.success && res?.data) {
      defIterId = res.data.id;
    } else {
      throw Error(`${deliverProject} 项目创建def迭代失败 (${res.errorMsg})`);
    }

    // 4. 写库
    const dbValues = this._covertToDBData(
      Object.assign(
        {
          devId,
          iterId,
          deliverType,
          branchType,
          defEnvType: pub_env,
          projectName,
          projectVersion: pkgVersion,
          // status: EDeliverStatus.INITIAL,
          npmList,
          defBuildStatus: EDefBuildStatus.NOT_START,
          creator: this.ctx.user.name,
          gmtCreate: formatDate(new Date()),
          // creatorWorkid: this.ctx.user.workid,
        },
        defIterId ? { defIterId } : {}
      )
    );

    const componentDeliverDetailModel = await this.ComponentDeliverDetailModel.create(dbValues);
    const componentDeliver = this._covertToClientData(componentDeliverDetailModel.toJSON());

    return componentDeliver;
  }

  /**
   * 更新依赖并开始H5投放
   * @param params 入参
   * @returns
   */
  async createPublish({ deliverId, pub_env, gitRepo, npmList, defIterId }: {
    deliverId: number;
    pub_env: EPubTypeEnv;
    gitRepo: string;
    npmList?: INpm[];
    defIterId: number;
  }) {
    // 1. 前置判断
    const componentDeliver = await this.get(deliverId, true);
    let defBranchId: number | undefined;

    if (!componentDeliver) { throw Error(`查询不到deliverId(${deliverId})`) }

    if (!componentDeliver.defIterId) { throw Error(`deliverId(${deliverId})缺少def迭代id，无法创建构建任务`) }

    const branchName = `daily/${componentDeliver.projectVersion}`;

    // 2. git合并master
    await this.mergeMaster({ defIterId, npmList, branchName, gitRepo,  });

    // 3. 更新依赖包，需同步操作 git
    await updateBranchDeps(branchName, gitRepo, npmList, [], [], []);

    // 4. 创建def构建任务
    const publishRes = await this.defService.createIterPublishTask({
      updateDB: false,
      pub_env,
      iterationId: defIterId,
    });

    if (publishRes?.success && publishRes?.data) {
      // 5. 查询迭代信息 (如branchId：后面创建CR要使用)
      const iterInfoRes = await this.defService.getIterationInfoById({
        iterationId: defIterId!
      })
      if (iterInfoRes?.success && iterInfoRes?.data) {
        defBranchId = iterInfoRes.data.iteration.devbranches?.[0].branch_id;
      }

      // 6. 写库
      const values = {
        deliverId,
        defBranchId,
        defTaskId: publishRes.data.id,
        defBuildStatus: publishRes.data.pub_status,
        defEnvType: pub_env,
        npmList,
      };
      const updateResult = await this.update(values);

      if (updateResult[0] === 0) {
        throw Error(`SQL: componentDeliverService.createPublish 更新def构建任务(taskid: ${publishRes.data.id})失败`);
      }
    } else {
      throw Error(`项目def构建失败 (${publishRes.errorMsg})`);
    }

    return this.get(deliverId, true);
  }

  /** git合并master */
  async mergeMaster({ defIterId, npmList, branchName, gitRepo }) {
    // 1. 查询是否主干同步
    const isSync = await this.defService.checkMasterSync(defIterId);
    if (isSync) { return; }

    // 2. 先获取主干的需要投放的依赖版本信息
    const pkgName = npmList?.[0]?.name;
    const { findNpmList } = await getBranchDeps('master', gitRepo, [pkgName]);

    // 3. 再回当前分支，更新需要投放的依赖版本为主干依赖，这样就不会合并master冲突了
    await updateBranchDeps(branchName, gitRepo, findNpmList, [], [], []);

    // 4. 合并master
    await mergeBranch(branchName, gitRepo, 'master');
  }

  /**
   * 查询H5放结果并更新到DB
   * @param params 入参
   * @returns
   */
  async getPublishDetail({ project, deliverId, defTaskId, npmList }: {
    project: IProject;
    deliverId: number;
    defTaskId: number;
    npmList?: INpm[];
  }) {
    // 1. 前置判断
    const componentDeliver = await this.get(deliverId, true);

    if (!componentDeliver) { throw Error(`查询不到deliverId(${deliverId})`) }

    if (!componentDeliver.defTaskId) { throw Error(`deliverId(${deliverId})缺少def任务id，无法查询构建任务`) }

    // if ([EDeliverStatus.ABANDON, EDeliverStatus.NOT_INITIAL, EDeliverStatus.PUBLISH_SUCCESS].includes(componentDeliver.status)) {
    //   throw Error(`${DELIVER_STATUS_WORDING[componentDeliver.status]} 状态不可更新`)
    // }

    // 2. 查询def构建任务
    const res = await this.defService.getIterPublishTaskDetail({
      updateDB: false,
      defTaskId,
    });
    // 如果查询失败，或者发布状态是构建中直接返回结果
    if (!res.success || res.data?.task?.pub_status === EDefBuildStatus.BUILDING) {
      return res.data;
    }

    // 4. 写库
    const defBuildStatus = res.data.task.pub_status;
    const defEnvType = res.data.task.pub_env;
    const values = {
      deliverId,
      defTaskId: res.data.task.id,
      defBuildStatus,
      defEnvType,
      npmList,
      // status: ,
    };
    const updateResult = await this.update(values, false);

    if (updateResult[0] === 0) {
      throw Error(`SQL: componentDeliverService.getPublishDetail 更新def构建任务(taskid: ${res.data.id})失败`);
    }

    // 5. 发送钉群消息
    if (defEnvType === EPubTypeEnv.Prod) {
      let branchInfo = '未知'
      switch(componentDeliver.branchType) {
        case EDeliverBranchType.FREE_DEV_BRANCH: {
          const freeDevBranch = await this.freeDevBranchService.get(componentDeliver.devId!);
          branchInfo = `游离分支 ${freeDevBranch?.branchName}`;
          break;
        }
        case EDeliverBranchType.DEV_BRANCH: {
          const devBranch = await this.freeDevBranchService.get(componentDeliver.devId!);
          branchInfo = `分支 ${devBranch?.branchName}`;
          break;
        }
        case EDeliverBranchType.ITERATION: {
          const iterBranch = await this.iterBranchService.get(componentDeliver.iterId!);
          branchInfo = `迭代 v${iterBranch?.version}`;
          break;
        }
      }

      const { name: pkgName = '未知', value: pkgValue = '未知' } = componentDeliver.npmList?.[0] || {} as any;
      this.dingtalkService.sendActionCardMessage({
        title: `[${project.cnName}]${branchInfo} ${DEF_ENV_TYPE_WORDING[defEnvType]}投放H5页面 ${DEF_BUILD_STATUS_WORDING[defBuildStatus]}（更新已有游离分支里的依赖版本）`,
        singleTitle: '跳转DEF查看详情',
        singleURL: `https://space.o2.alibaba-inc.com/iteration/${componentDeliver.defIterId}/basic`,
        text: [
          `![bg](${PROJECT_CONFIG[EProjectType.COMPONENT].dingtalkBigLogo})`,
          `**${branchInfo} ${DEF_ENV_TYPE_WORDING[defEnvType]}投放H5页面 ${DEF_BUILD_STATUS_WORDING[defBuildStatus]}**`,
          `- 所属项目：${project.cnName || '未知'}`,
          `- 投放页面：${componentDeliver.projectName}`,
          `- 页面分支：${componentDeliver.projectVersion}`,
          `- 更新依赖：模块名${pkgName}，版本${pkgValue}`,
          `- 执行人：${componentDeliver.creator}`,
          `- 执行时间：${formatDate(new Date())}`
        ].filter(item => !!item).join('\n')
      }, project)
    }

    return res.data;
    // return this.get(deliverId, true);
  }

  /**
   * 初始化小程序投放（新建小程序git分支，开发分支，写入数据库）
   * @param params 入参
   * @returns
   */
   async initMiniapp({
    // 写入投放db的参数
    deliverId,
    devId,
    iterId,
    deliverType,
    branchType,
    pub_env,
    // 创建小程序的游离开发分支参数
    projectName,
    branchName,
    description,
    mergeCode,
    npmList,
    npmResolutionList,
    bizLine,
    qaList,
    aoneList,
    projectType,
  }: {
    deliverId: number;
    devId: number;
    deliverType: EDeliverType;
    pub_env: EPubTypeEnv;
  } & IFreeDevBranchCreateParams & IDevBranchCreateParams) {
    // 1. 前置判断
    // 校验投放是否已存在
    if (deliverId) {
      const existingDeliver = await this.get(deliverId, true);
      if (existingDeliver)
        throw new Error(`${projectName} 下已存在投放记录不可以新增`);
    }

    // 2. 请求创建小程序的游离开发分支
    const params = {
      projectName,
      branchName,
      description,
      mergeCode,
      npmList,
      npmResolutionList,
      bizLine,
      qaList,
      aoneList,
      projectType,
    };
    const res = await this.freeDevBranchService.create(params);

    const dbValues = this._covertToDBData({
      devId,
      iterId,
      deliverType,
      branchType,
      defEnvType: pub_env,
      projectName,
      // 小程序专有
      miniappInfo: { branchName, projectName, devId: res.devId, status: EMinaStatus.INIT },
      // status: EDeliverStatus.INITIAL,
      npmList,
      creator: this.ctx.user.name,
      gmtCreate: formatDate(new Date()),
    });

    const componentDeliverDetailModel = await this.ComponentDeliverDetailModel.create(dbValues);
    const devBranch = this._covertToClientData(componentDeliverDetailModel.toJSON());

    return devBranch;
  }

  /**
   * 预发环境更新小程序投放（更新当前分支里依赖的版本号，同步git，并更新投放记录）
   * @param params 入参
   * @returns
   */
   async updateBetaMiniapp({
    deliverId,
    npmList,
  }: {
    deliverId: number;
    npmList: INpm[];
  }) {
    // 1. 前置判断 校验投放是否存在
    const componentDeliver = await this.get(deliverId, true);

    if (!componentDeliver) { throw Error(`查询不到deliverId(${deliverId})`) }

    // 2. 更新投放小程序的游离开发分支 在小程序投放记录中的投放分支里的依赖
    await this.update({ deliverId, npmList, miniappInfo: ({ ...componentDeliver.miniappInfo, status: EMinaStatus.BETA }) as IMiniappInfo });

    const { devId, branchName } = componentDeliver.miniappInfo!;
    // 游离开发分支更新依赖并更新git仓库(注意是在原有依赖的基础上更新，不要直接覆盖，否则用户自己加的依赖就没了)
    const freeDevBranch = await this.freeDevBranchService.get(devId);

    if (!freeDevBranch) {
      throw Error(`SQL: componentDeliverService.updateBetaMiniapp 查询devId为${devId}失败`);
    }
    const oldNpmList = freeDevBranch.npmList || [];
    const { name, value } = npmList?.[0];
    const newNpmList = oldNpmList.map(npm => {
      // 只更新npmList组件依赖
      if (npm.name === name) {
        return { name, value }
      }
      // 保留原有依赖
      return npm;
    })

    if (freeDevBranch?.mergeCode === EMergeCode.NO) {
      // 如果是更新依赖模式，则更新npmList
      const updateResult = await this.freeDevBranchService.update(devId, { mergeCode: EMergeCode.NO, npmList: newNpmList });
      if (updateResult[0] === 0) throw Error(`SQL: componentDeliverService.updateBetaMiniapp更新小程序游离开发分支的依赖失败`);
    } else {
      // 如果是合并代码模式，需要提交代码更新依赖
      updateBranchDeps(branchName, freeDevBranch.gitRepo.sshUrl, npmList, [], [], []);
    }

    return await this.get(deliverId, true);
  }

  /**
   * 线上环境更新小程序投放（更新当前迭代下全部分支里小程序依赖的版本号，同步git，并更新投放记录）
   * @param params 入参
   * @returns
   */
   async updateProdMiniapp({
    deliverId,
    iterId,
    deliverType,
    npmList,
    projectName,
  }: {
    deliverId: number;
    iterId: number;
    deliverType: EDeliverType;
    npmList: INpm[];
    projectName: string;
  }) {
    // 2. 查询迭代列表里当前迭代下所绑定的开发分支/游离开发分支id
    const iterBranch = await this.iterBranchService.get(iterId);
    if (!iterBranch) {
      throw new Error(`id 为 ${iterId} 的迭代分支不存在`)
    }
    const { devBranchList, freeDevBranchList } = iterBranch;
    let totalDeliverBranchList = [] as any;

    // 3. 更新开发分支/游离开发分支 在小程序投放记录中的投放分支里的依赖
    if (devBranchList?.length) {
      const devBranchRes = await this.list({ pageSize: 10, pageNum: 1, conditions: { devIdList: devBranchList, branchType: EDeliverBranchType.DEV_BRANCH, deliverType } })
      if (devBranchRes?.total) {
        totalDeliverBranchList = totalDeliverBranchList.concat(devBranchRes.list);
      }
    }
    if (freeDevBranchList?.length) {
      const freeDevBranchRes = await this.list({ pageSize: 10, pageNum: 1, conditions: { devIdList: freeDevBranchList, branchType: EDeliverBranchType.FREE_DEV_BRANCH, deliverType } })
      if (freeDevBranchRes?.total) {
        totalDeliverBranchList = totalDeliverBranchList.concat(freeDevBranchRes.list);
      }
    }

    if (totalDeliverBranchList?.length) {
      totalDeliverBranchList.forEach(async (item) => {
        // DB更新依赖
        await this.update({ deliverId: item.deliverId, iterId, npmList, miniappInfo: { ...item.miniappInfo, status: EMinaStatus.PROD } });

        const { devId, branchName } = item.miniappInfo;

        // 游离开发分支更新依赖并更新git仓库(注意是在原有依赖的基础上更新，不要直接覆盖，否则用户自己加的依赖就没了)
        const freeDevBranch = await this.freeDevBranchService.get(devId);

        if (!freeDevBranch) {
          throw Error(`SQL: componentDeliverService.updateBetaMiniapp 查询devId为${devId}失败`);
        }
        const oldNpmList = freeDevBranch.npmList || [];
        const { name, value } = npmList?.[0];
        const newNpmList = oldNpmList.map(npm => {
          // 只更新npmList组件依赖
          if (npm.name === name) {
            return { name, value }
          }
          // 保留原有依赖
          return npm;
        })

        if (freeDevBranch?.mergeCode === EMergeCode.NO) {
          // 如果是更新依赖模式，则更新npmList
          const updateResult = await this.freeDevBranchService.update(devId, { mergeCode: EMergeCode.NO, npmList: newNpmList });
          if (updateResult[0] === 0) throw Error(`SQL: componentDeliverService.updateProdMiniapp更新小程序游离开发分支的依赖失败`);
        } else {
          // 如果是合并代码模式，需要提交代码更新依赖
          updateBranchDeps(branchName, freeDevBranch.gitRepo.sshUrl, npmList, [], [], []);
        }
      })
    } else {
      throw new Error(`${projectName} 下没有测试投放过的小程序，无需更新`);
    }

    // 4. 获取项目详情 & 发送钉群消息
    const project = await this.projectService.get(iterBranch.projectName, { needDingtalkRobots: true });
    if (!project) throw Error(`查询不到 projectName 为 ${iterBranch.projectName} 的项目`)

    const { name: pkgName = '', value: pkgValue = '' } = npmList?.[0] || {} as any;
    this.dingtalkService.sendActionCardMessage({
      title: `[${projectName}]迭代 v${iterBranch.version} 已投放线上小程序（更新已有游离分支里的依赖版本）`,
      singleTitle: '查看详情',
      singleURL: `${isProd ? PROD_URL : PRE_URL}/#/iter/detail?iterId=${iterBranch.iterId}`,
      text: [
        `![bg](${PROJECT_CONFIG[EProjectType.COMPONENT].dingtalkBigLogo})`,
        `**迭代 v${iterBranch.version} 已投放线上小程序**`,
        `- 迭代描述：${iterBranch.description}`,
        `- 所属项目：${projectName || '未知'}`,
        `- 基线分支：[${iterBranch.gitBranch.name}](${iterBranch.gitBranch.url})`,
        `- 更新依赖：模块名${pkgName}，版本${pkgValue}`,
        `- 执行人：${iterBranch.creator}`,
        `- 执行时间：${formatDate(new Date())}`
      ].filter(item => !!item).join('\n')
    }, project)

    // TODO ??
    return await this.list({ pageSize: 10, pageNum: 1, conditions: { devIdList: devBranchList, branchType: EDeliverBranchType.DEV_BRANCH, deliverType } })
  }

  _covertToDBData(clientData: any) {
    const dbValues = convertsCollectionToSnake(clientData, 1);
    // 数组或对象类型的需处理下再写库
    dbValues.aone_list &&=
      dbValues.aone_list.length > 0 ? JSON.stringify(dbValues.aone_list) : null;
    dbValues.npm_list &&=
      dbValues.npm_list.length > 0 ? JSON.stringify(dbValues.npm_list) : null;
    dbValues.npm_resolution_list &&=
      dbValues.npm_resolution_list.length > 0
        ? JSON.stringify(dbValues.npm_resolution_list)
        : null;
    dbValues.qa_list &&=
      dbValues.qa_list.length > 0 ? JSON.stringify(dbValues.qa_list) : null;
    dbValues.git_branch &&= JSON.stringify(dbValues.git_branch);
    dbValues.miniapp_info &&= JSON.stringify(dbValues.miniapp_info);

    return dbValues;
  }

  _covertToClientData(dbData): IComponentDeliver {
    return {
      ...convertsCollectionToCamel(dbData, 1),
      gmtCreate: dbData.gmt_create && formatDate(dbData.gmt_create),
      gmtModified: dbData.gmt_modified && formatDate(dbData.gmt_modified),
      npmList: dbData.npm_list ? JSON.parse(dbData.npm_list) : [],
      miniappInfo: dbData.miniapp_info ? JSON.parse(dbData.miniapp_info) : [],
    };
  }

  /** 获得最新且最大的git分支号 */
  async getNewAndBiggestVersion({
    url,
    type,
  }: {
    url: string;
    type?: Action;
  }): Promise<string> {
    const res: any = await this.gitService.getRemoteBranches({ url });

    if (res?.length) {
      const gitBranchNameList = res.reduce((list: any[], item) => {
        // TODO 这里可以只保留daily
        const matchRes1 = /^remotes\/origin\/free-dev\/\d{8}-(\d+\.\d+\.\d+)-/ig.exec(item);
        const matchRes2 = /^remotes\/origin\/dev\/(?:\d+\.\d+\.\d+)-(\d+\.\d+\.\d+)-/ig.exec(item);
        const matchRes3 = /^remotes\/origin\/daily\/(\d+\.\d+\.\d+)/ig.exec(item);
        const matchRes4 = /^remotes\/origin\/rc\/(\d+\.\d+\.\d+)/ig.exec(item);
        const matchRes5 = /^remotes\/origin\/stable\/(\d+\.\d+\.\d+)/ig.exec(item);
        const matchRes6 = /def[a-z0-9_-]*\/(\d+\.\d+\.\d+)/ig.exec(item);
        if (matchRes1?.length || matchRes2?.length || matchRes3?.length || matchRes4?.length || matchRes5?.length || matchRes6?.length) {
          list.push(matchRes1?.[1] || matchRes2?.[1] || matchRes3?.[1] || matchRes4?.[1] || matchRes5?.[1] || matchRes6?.[1]);
        }
        return list;
      }, []) as string[];
      console.log('gitBranchNameList', gitBranchNameList);
      let ver = getBiggestVersion(gitBranchNameList)
      if (type === Action.Create) {
        // 如果是新建分支，git版本号要加一
        ver[2]++;
      }
      (ver as any) = ver.join('.');
      console.log('ver', ver);
      return ver as any;
    }
    return '';
  }
}

export enum Action {
  Create = 'create', // 创建
  Update = 'update', // 更新
  Read = 'read', // 查看
}

/** 迭代分支更新入参 */
export interface IComponentDeliverUpdateValues {
  deliverId: number;
  devId?: number;
  iterId?: number | null;
  deliverType?: EDeliverType;
  branchType?: EDeliverBranchType;
  projectName?: string;
  projectVersion?: string;
  modifier?: string;
  gmtModified?: string;
  defIterId?: number;
  defTaskId?: number;
  defBuildStatus?: number;
  defBranchId?: number;
  defEnvType?: EPubTypeEnv;
  npmList?: INpm[];
  miniappInfo?: IMiniappInfo;
}
