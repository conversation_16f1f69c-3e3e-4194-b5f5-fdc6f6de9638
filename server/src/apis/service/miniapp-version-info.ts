import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/faas';
import { Op } from 'sequelize';
import { convertsCollectionToCamel, convertsCollectionToSnake, formatDate, filterOutNull } from '@/apis/utils';
import { IMiniAppVersionInfo } from '@/apis/interface/iter-deliver';
import { IMiniAppVersionInfoModel } from '@/apis/model/miniapp-version-info';
import { EClient, EMiniAppVersionStatus } from '@/apis/const/iter-deliver';
import { filterOutUndefined } from '@/apis/utils';

@Provide()
export default class MiniAppVersionInfoService {
  @Inject()
  ctx!: Context;

  @Inject()
  MiniAppVersionInfoModel!: IMiniAppVersionInfoModel;

  async create({ clientName, miniAppId, miniAppVersion }: IMiniAppVersionInfoCreateParams) {
    const dbValues = this._covertToDBData({
      clientName,
      miniAppId,
      miniAppVersion,
      status: EMiniAppVersionStatus.INIT,
      createTime: formatDate(new Date()),
    });
    const [miniAppVersionInfoModel, created] = await this.MiniAppVersionInfoModel.findOrCreate({
      where: Object.assign({
        mini_app_id: miniAppId,
        mini_app_version: miniAppVersion
      }, clientName && { client_name: clientName }),
      defaults: dbValues
    });

    if (!created) throw Error(`已有同条件(${JSON.stringify({ clientName, miniAppId, miniAppVersion })})的小程序版本信息`);

    return this._handleReturnData(this._formatDBData(miniAppVersionInfoModel.toJSON()));
  }

  async getById(id: number) {
    const res = await this.MiniAppVersionInfoModel.findOne({
      where: { id },
      raw: true
    });

    if (res) {
      return this._handleReturnData(this._formatDBData(res));
    } else {
      return null;
    }
  }

  /** 按条件获取一条数据，若匹配数超过 1 则报错 */
  async getByConditions(conditions: ISingleMatchConditionsByMiniAppVersion | ISingleMatchConditionsByStatus) {
    const { rows } = await this.MiniAppVersionInfoModel.findAndCountAll({
      where: filterOutUndefined(convertsCollectionToSnake(conditions, 1)),
      raw: true
    });

    if (rows.length > 1) throw Error('超过一条数据被匹配到')

    if (!rows[0]) return null;

    return this._handleReturnData(this._formatDBData(rows[0]))
  }

  /** 删除 */
  async delete(singleMatchConditions: ISingleMatchConditionsByMiniAppVersion) {
    // 获取版本信息，并做前置判断
    const miniAppVersionInfo = await this.getByConditions(singleMatchConditions);
    if (!miniAppVersionInfo) throw Error(`查询不到符合条件(${JSON.stringify(singleMatchConditions)})的小程序版本信息`);
    if (miniAppVersionInfo.status !== EMiniAppVersionStatus.INIT) throw Error('只有处于“开发中”状态才能删除');

    const destroyResult = await this.MiniAppVersionInfoModel.destroy({ where: { id: miniAppVersionInfo.id } });
    return destroyResult > 0;
  }

  /** 提审 */
  async audit(singleMatchConditions: ISingleMatchConditionsByMiniAppVersion, clientList?: EClient[]) {
    // 获取版本信息，并做前置判断
    const miniAppVersionInfo = await this.getByConditions(singleMatchConditions);
    if (!miniAppVersionInfo) throw Error(`查询不到符合条件(${JSON.stringify(singleMatchConditions)})的小程序版本信息`);
    if (miniAppVersionInfo.status !== EMiniAppVersionStatus.INIT) throw Error('只有处于“开发中”状态才能提审');

    // 若小程序存在审核中、审核通过、审核拒绝或者灰度中的版本，当前版本不能提审
    const { rows } = await this.MiniAppVersionInfoModel.findAndCountAll({
      where: Object.assign({
        mini_app_id: singleMatchConditions.miniAppId,
        [Op.or]: [
          { status: EMiniAppVersionStatus.AUDITING },
          { status: EMiniAppVersionStatus.WAIT_RELEASE },
          { status: EMiniAppVersionStatus.AUDIT_REJECT },
          { status: EMiniAppVersionStatus.GRAY },
        ]
      }, singleMatchConditions.clientName && { client_name: singleMatchConditions.clientName }),
      raw: true
    });
    if (rows.length > 0) throw Error('该小程序存在审核中、审核通过、审核拒绝或者灰度中的版本，当前版本不能提审')

    // 写库
    const dbValues = this._covertToDBData({
      status: EMiniAppVersionStatus.AUDITING,
      clientList: clientList && clientList.join(',')
    });
    const updateResult = await this.MiniAppVersionInfoModel.update(dbValues, {
      where: { id: miniAppVersionInfo.id },
    });
    if (updateResult[0] === 0) throw Error(`提审版本 ${singleMatchConditions.miniAppVersion} 失败`);

    return true;
  }

  /** 通过审核 */
  async passAudit(singleMatchConditions: ISingleMatchConditionsByMiniAppVersion) {
    // 获取版本信息，并做前置判断
    const miniAppVersionInfo = await this.getByConditions(singleMatchConditions);
    if (!miniAppVersionInfo) throw Error(`查询不到符合条件(${JSON.stringify(singleMatchConditions)})的小程序版本信息`);
    if (miniAppVersionInfo.status !== EMiniAppVersionStatus.AUDITING) throw Error('只有处于“审核中”状态才能通过审核');

    // 写库
    const dbValues = this._covertToDBData({
      status: EMiniAppVersionStatus.WAIT_RELEASE,
    });
    const updateResult = await this.MiniAppVersionInfoModel.update(dbValues, {
      where: { id: miniAppVersionInfo.id },
    });
    if (updateResult[0] === 0) throw Error(`版本 ${singleMatchConditions.miniAppVersion} 通过审核失败`);

    return true;
  }

  /** 撤销审核 */
  async cancelAudit(singleMatchConditions: ISingleMatchConditionsByMiniAppVersion) {
    // 获取版本信息，并做前置判断
    const miniAppVersionInfo = await this.getByConditions(singleMatchConditions);
    if (!miniAppVersionInfo) throw Error(`查询不到符合条件(${JSON.stringify(singleMatchConditions)})的小程序版本信息`);
    if (miniAppVersionInfo.status !== EMiniAppVersionStatus.AUDITING) throw Error('只有处于“审核中”状态才能撤销审核');

    // 写库
    const dbValues = this._covertToDBData({
      status: EMiniAppVersionStatus.INIT,
    });
    const updateResult = await this.MiniAppVersionInfoModel.update(dbValues, {
      where: { id: miniAppVersionInfo.id },
    });
    if (updateResult[0] === 0) throw Error(`版本 ${singleMatchConditions.miniAppVersion} 撤销审核失败`);

    return true;
  }

  /** 退回开发 */
  async backDev(singleMatchConditions: ISingleMatchConditionsByMiniAppVersion) {
    // 获取版本信息，并做前置判断
    const miniAppVersionInfo = await this.getByConditions(singleMatchConditions);
    if (!miniAppVersionInfo) throw Error(`查询不到符合条件(${JSON.stringify(singleMatchConditions)})的小程序版本信息`);
    if (miniAppVersionInfo.status !== EMiniAppVersionStatus.AUDITING
      && miniAppVersionInfo.status !== EMiniAppVersionStatus.WAIT_RELEASE
      && miniAppVersionInfo.status !== EMiniAppVersionStatus.AUDIT_REJECT
    ) throw Error('只有处于“审核中”、“审核通过”或“审核失败”状态才能退回开发');

    // 写库
    const dbValues = this._covertToDBData({
      status: EMiniAppVersionStatus.INIT
    });
    const updateResult = await this.MiniAppVersionInfoModel.update(dbValues, {
      where: { id: miniAppVersionInfo.id },
    });
    if (updateResult[0] === 0) throw Error(`版本 ${singleMatchConditions.miniAppVersion} 退回开发失败`);

    return true;
  }

  /** 灰度 */
  async gray(singleMatchConditions: ISingleMatchConditionsByMiniAppVersion, grayStrategy: string) {
    // 获取版本信息，并做前置判断
    const miniAppVersionInfo = await this.getByConditions(singleMatchConditions);
    if (!miniAppVersionInfo) throw Error(`查询不到符合条件(${JSON.stringify(singleMatchConditions)})的小程序版本信息`);
    if (miniAppVersionInfo.status !== EMiniAppVersionStatus.WAIT_RELEASE && miniAppVersionInfo.status !== EMiniAppVersionStatus.GRAY) throw Error('只有处于“审核通过”或“灰度中”状态才能灰度');

    // 写库
    const dbValues = this._covertToDBData({
      status: EMiniAppVersionStatus.GRAY,
      grayStrategy,
      // 第一次灰度需要记录灰度开始时间
      grayStartTime: miniAppVersionInfo.status === EMiniAppVersionStatus.WAIT_RELEASE ? formatDate(new Date()) : undefined,
    });
    const updateResult = await this.MiniAppVersionInfoModel.update(dbValues, {
      where: { id: miniAppVersionInfo.id },
    });
    if (updateResult[0] === 0) throw Error(`灰度版本 ${singleMatchConditions.miniAppVersion} 失败`);

    return true;
  }

  /** 结束灰度 */
  async finishGray(singleMatchConditions: ISingleMatchConditionsByMiniAppVersion) {
    // 获取版本信息，并做前置判断
    const miniAppVersionInfo = await this.getByConditions(singleMatchConditions);
    if (!miniAppVersionInfo) throw Error(`查询不到符合条件(${JSON.stringify(singleMatchConditions)})的小程序版本信息`);
    if (miniAppVersionInfo.status !== EMiniAppVersionStatus.GRAY) throw Error('只有处于“灰度中”状态才能结束灰度');

    // 写库
    const dbValues = this._covertToDBData({
      status: EMiniAppVersionStatus.WAIT_RELEASE,
      // 重置灰度开始时间
      grayStartTime: null,
      // 重置灰度策略
      grayStrategy: null
    });
    const updateResult = await this.MiniAppVersionInfoModel.update(dbValues, {
      where: { id: miniAppVersionInfo.id },
    });
    if (updateResult[0] === 0) throw Error(`结束版本 ${singleMatchConditions.miniAppVersion} 灰度失败`);

    return true;
  }

  /** 上架 */
  async online(singleMatchConditions: ISingleMatchConditionsByMiniAppVersion, { isRollback, ignoreGray }: { isRollback?: boolean; ignoreGray?: boolean; }) {
    // 获取版本信息，并做前置判断
    const miniAppVersionInfo = await this.getByConditions(singleMatchConditions);
    if (!miniAppVersionInfo) throw Error(`查询不到符合条件(${JSON.stringify(singleMatchConditions)})的小程序版本信息`);
    if (isRollback) {
      if (miniAppVersionInfo.status !== EMiniAppVersionStatus.OFFLINE) throw Error('回滚的目标版本必须处于下架状态');
    } else {
      if (ignoreGray) {
        if (miniAppVersionInfo.status !== EMiniAppVersionStatus.WAIT_RELEASE) throw Error('只有审核通过才能上架');
      } else {
        if (miniAppVersionInfo.status !== EMiniAppVersionStatus.GRAY) throw Error('上架前必须灰度');
      }
    }

    // 非回滚操作，需要查询并下架当前版本
    if (!isRollback) {
      const prevMiniAppVersionInfo = await this.getByConditions({
        clientName: singleMatchConditions.clientName,
        miniAppId: singleMatchConditions.miniAppId,
        status: EMiniAppVersionStatus.RELEASE,
      });

      if (prevMiniAppVersionInfo) {
        this.offline({ ...singleMatchConditions, miniAppVersion: prevMiniAppVersionInfo.miniAppVersion })
      }
    }

    // 上架
    const dbValues = this._covertToDBData({
      status: EMiniAppVersionStatus.RELEASE,
      shelfTime: formatDate(new Date())
    });
    const updateResult = await this.MiniAppVersionInfoModel.update(dbValues, {
      where: { id: miniAppVersionInfo.id },
    });
    if (updateResult[0] === 0) throw Error(`${isRollback ? '回滚至' : '上架'}版本 ${singleMatchConditions.miniAppVersion} 失败`);

    return true;
  }

  /** 下架 */
  async offline(singleMatchConditions: ISingleMatchConditionsByMiniAppVersion, isRollback?: boolean) {
    // 获取版本信息，并做前置判断
    const miniAppVersionInfo = await this.getByConditions(singleMatchConditions);
    if (!miniAppVersionInfo) throw Error(`查询不到符合条件(${JSON.stringify(singleMatchConditions)})的小程序版本信息`);
    if (miniAppVersionInfo.status !== EMiniAppVersionStatus.RELEASE) throw Error('只有处于“已上架”状态才能下架');

    // 下架
    const time = formatDate(new Date());
    const dbValues = this._covertToDBData({
      status: EMiniAppVersionStatus.OFFLINE,
      offlineTime: time,
      rollbackTime: isRollback ? time : undefined
    });
    const updateResult = await this.MiniAppVersionInfoModel.update(dbValues, {
      where: { id: miniAppVersionInfo.id },
    });
    if (updateResult[0] === 0) throw Error(`${isRollback ? '回滚' : '下架'}版本 ${singleMatchConditions.miniAppVersion} 失败`);

    return true;
  }

  /** 回滚 */
  async rollback(singleMatchConditions: ISingleMatchConditionsByMiniAppVersion, miniAppVersion: string) {
    // 获取版本信息，并做前置判断
    const miniAppVersionInfo = await this.getByConditions(singleMatchConditions);
    if (!miniAppVersionInfo) throw Error(`查询不到符合条件(${JSON.stringify(singleMatchConditions)})的小程序版本信息`);
    if (miniAppVersionInfo.status !== EMiniAppVersionStatus.RELEASE) throw Error('只有处于“已上架”状态才能回滚');

    // 回滚到指定版本
    await this.online({ ...singleMatchConditions, miniAppVersion }, { isRollback: true })

    // 下架当前版本
    await this.offline(singleMatchConditions, true)

    return true;
  }

  /** 转换成 db 数据 */
  _covertToDBData(data: any) {
    return convertsCollectionToSnake(data, 1);
  }

  /** 格式化 db 数据 */
  _formatDBData(dbData: any): IMiniAppVersionInfo {
    // 过滤掉 null 值，并转驼峰
    const res = convertsCollectionToCamel(filterOutNull(dbData), 1);

    return {
      ...res,
      createTime: res.createTime && formatDate(res.createTime),
      grayStartTime: res.grayStartTime && formatDate(res.grayStartTime),
      shelfTime: res.shelfTime && formatDate(res.shelfTime),
      offlineTime: res.offlineTime && formatDate(res.offlineTime),
      rollbackTime: res.rollbackTime && formatDate(res.rollbackTime)
    }
  }

  /** 处理返回的数据 */
  _handleReturnData(miniAppVersionInfo: IMiniAppVersionInfo) {
    return miniAppVersionInfo;
  }
}

type IMiniAppVersionInfoCreateParams = ISingleMatchConditionsByMiniAppVersion;

/** 根据小程序版本来唯一匹配 */
interface ISingleMatchConditionsByMiniAppVersion {
  /** 小程序id */
  miniAppId: string;
  /** 小程序版本 */
  miniAppVersion: string;
  /** 投放端，多端单独投放场景必须额外传此参数来唯一匹配 */
  clientName?: EClient;
}

/** 根据小程序状态来唯一匹配 */
interface ISingleMatchConditionsByStatus {
  /** 小程序id */
  miniAppId: string;
  /** 小程序版本状态 */
  status: EMiniAppVersionStatus;
  /** 投放端，多端单独投放场景必须额外传此参数来唯一匹配 */
  clientName?: EClient;
}