{"compilerOptions": {"baseUrl": ".", "outDir": "build", "module": "esnext", "target": "es2021", "lib": ["es2021", "dom"], "sourceMap": false, "jsx": "react-jsx", "allowSyntheticDefaultImports": true, "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noUnusedLocals": true, "allowJs": true, "removeComments": false, "skipLibCheck": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "strict": true, "noImplicitThis": true, "noImplicitAny": false, "importHelpers": true, "strictNullChecks": true, "types": ["node"], "paths": {"@/*": ["./src/*"], "@@/*": ["./src/.umi/*"]}}, "include": ["src"], "exclude": ["node_modules", "build", "dist"], "ts-node": {"compilerOptions": {"module": "commonjs"}}}