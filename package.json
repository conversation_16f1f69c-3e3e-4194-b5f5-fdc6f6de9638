{"name": "miniwork", "version": "3.0.34", "group": "func", "description": "小程序研发平台", "author": {"name": "南麓", "email": "<EMAIL>"}, "private": true, "repository": {"type": "git", "url": "http://gitlab.alibaba-inc.com/func/miniwork"}, "keywords": ["clam", "faas-whale", "miniwork"], "toolkit": "@ali/clam-toolkit-one", "commandType": "clam", "defPublishType": "assets_faas", "scripts": {"client-install": "cd client && tnpm install --no-package-lock", "server-install": "cd server && tnpm install --no-package-lock", "postinstall": "npm run client-install && npm run server-install", "start": "npm run client-start", "client-start": "cd client && npm run start", "build": "npm run client-build", "client-build": "cd client && npm run build", "client-lint": "cd client && npm run lint", "server-lint": "cd server && npm run lint", "lint": "npm run client-lint && npm run server-lint", "publish-def": "npm run build", "prepare": "tnpm-lock-adapter"}, "license": "MIT", "publishConfig": {"registry": "http://registry.npm.alibaba-inc.com"}, "engines": {"node": ">=12.0.0"}, "resolutions": {"cluster-client": "3.6.0", "@types/node": "22.5.1"}, "devDependencies": {"@ali/tnpm-lock-adapter": "^1.9.0", "@types/node": "^16.7.10"}, "__npminstall_done": false}