<img src=https://gw.alipayobjects.com/zos/r/215m6q/20200306202646.png width=200>

# miniwork

> 小程序研发平台，已升级到 fc 和 umi3，前后端目录分离。

- 项目类型: FaaS Whale 一体化中项目
- 创建者: 南麓 <<EMAIL>>
- 项目地址: http://gitlab.alibaba-inc.com/func/miniwork
- Def 发布类型：web 端一体化 assets_faas
- 租户：【FC】飞猪中后台
- 网关：FC通用网关
- 域名：函数组名会作为应用默认三级域名的前缀，比如 `${group}.fc.alibaba-inc.com`（预发地址为 `${group}.pre-fc.alibaba-inc.com`），自定义域名可见[语雀](https://yuque.antfin-inc.com/func/help/fcgnlt)
- 数据库的使用请参考 [FaaS 直连数据库进行增删改查](https://yuque.antfin-inc.com/func/help/sql)
- **以上内容通过 clam init -> 项目 -> 中后台 -> FaaS中后台一体化 自动创建**，更多 FaaS 使用文档可见 [使用文档](https://yuque.antfin-inc.com/func/help)
- 备注：由于需要接入 DEF 发布，需要确保对应 group 下有 tbfed（前端研发平台）的用户为管理员


### 常见命令

```bash
$ clam newbranch // 新建分支
$ clam push // 提交代码

$ clam dev // 本地调试

$ clam prepub // 预发
$ clam publish // 正式发布
```

### 目录结构

```tree
biz
├── client
│   ├── config
│   ├── src
│   ├── mock
│   ├── README.md
│   ├── package.json
│   ├── tsconfig.json
│   ├── tslint.yml
│   └── typings.d.ts
├── server
│   ├── src
│   │   ├── apis
│   │   └── configuration.ts
│   ├── f.yml
│   ├── package.json
│   └── tsconfig.json
├── Gruntfile.js
├── README.md
├── abc.json
└── package.json
```

### 开发说明
- 首次开发，需在项目根目录执行`chmod -R 0700 ./server/bin`
- Antd Pro 和之前大家使用的一致，我这边进行了不少用不到的简化，去掉多语言、权限、认证、测试等不用的，但是没有修改内部原理使用，大家参考[最新文档](https://pro.ant.design/)即可
- FaaS 函数的代码在 `server/src/apis/functions` 目录下面，开发和之前纯函数是一致的，单文件多函数入口可参考 user.ts，函数配置在 f.yml
- 飞猪同学开发可以参考[使用文档](https://yuque.antfin-inc.com/func/help/start)，**更多 Midway FaaS 开发可以参考 [Ali Serverless 官方文档](https://yuque.antfin-inc.com/one-serverless/ali-serverless)，属于你的百科全书**

### Render 函数

- render 很像我们 midway 体系里面的 static index.tpl 能力，渲染一个加载前端资源的壳子，基本上无需改动
- 这一块使用很简单，直接参考 `server/src/apis/functions/render`,`server/src/apis/view/index.ejs.ts` 这两个文件随心所欲去修改成符合自己就好

### 高密度部署

在 f.yml 配置，可以将所有函数部署到一个空间下部署，节约函数资源

```yml
//建议全配置的方式
aggregation:
  all:
    functionsPattern:
      - '*'

// 选择性的，不建议
aggregation:
  all:
    functions:
      - index
      - render
      - user
      - xxx
```

### 中间件能力
- [BUC 能力](https://yuque.antfin-inc.com/one-serverless/ui77tp/ndd3ga)
- [操作数据库](https://yuque.antfin-inc.com/func/help/sql)
- 这一块有其他中间件的需求欢迎猛烈提交过来，一起去推相关同学去实现
