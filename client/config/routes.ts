// 路由配置方法详见文档：
// umi: https://umijs.org/zh-CN/docs/routing
// antd pro: https://pro.ant.design/docs/router-and-nav-cn

export default [
  {
    path: '/',
    redirect: '/iter',
  },
  {
    name: '迭代管理',
    icon: 'table',
    path: '/iter',
    routes: [
      {
        path: '/iter',
        redirect: '/iter/calendar',
      },
      {
        name: '迭代日历',
        title: '迭代管理 - 迭代日历',
        path: '/iter/calendar',
        component: './IterCalendar',
      },
      {
        name: '游离分支',
        title: '迭代管理 - 游离分支',
        path: '/iter/free-branch-list',
        component: './IterFreeBranchList'
      },
      {
        name: '申请加版',
        title: '迭代管理 - 申请加版',
        path: '/iter/extra-version-application-list',
        component: './IterExtraVersionApplicationList'
      },
      {
        name: '申请回滚',
        title: '迭代管理 - 申请回滚',
        path: '/iter/rollback-application-list',
        component: './IterRollbackApplicationList'
      },
      {
        name: '迭代详情',
        title: '迭代管理 - 迭代详情',
        path: '/iter/detail',
        component: './IterDetail',
        hideInMenu: true
      }
    ]
  },
  {
    name: '小程序包分析',
    icon: 'pieChart',
    path: '/package',
    routes: [
      {
        path: '/package',
        redirect: '/package/structure',
      },
      {
        name: '结构分析',
        title: '包管理 - 结构分析',
        path: '/package/structure',
        component: './PackageStructure',
      },
      {
        name: '依赖分析',
        title: '包管理 - 依赖分析',
        path: '/package/dependencies',
        component: './PackageDependencies',
      },
      {
        name: '首页体积',
        title: '包管理 - 首页体积',
        path: '/package/home-size',
        component: './HomeSize',
      }
    ]
  },
  {
    name: '源码SSR',
    icon: 'CloudOutlined',
    path: '/ssr',
    routes: [
      {
        path: '/ssr',
        redirect: '/ssr/list',
      },
      {
        name: '项目列表',
        title: '源码SSR - 项目列表',
        path: '/ssr/list',
        component: './SSRList',
      },
      {
        name: '页面详情',
        title: '源码SSR - 页面详情',
        path: '/ssr/detail',
        component: './SSRDetail',
        hideInMenu: true
      },
      {
        name: 'ER配置',
        title: '源码SSR - ER配置',
        path: '/ssr/erconfig',
        component: './ERConfig',
      },
      {
        name: '预加载更新配置',
        title: '源码SSR - 预加载更新配置',
        path: '/ssr/preUpdate',
        component: './PreUpdate',
      },
      {
        name: '预加载大盘',
        path: '/ssr/pressr',
        component: './PreSsr',
      },
      {
        name: '日志查询',
        path: '/ssr/logQuery',
        component: './SSRLogQuery',
      },
      {
        name: '其他配置',
        path: '/ssr/otherConfig',
        component: './OtherConfig',
      },
      {
        name: '工具',
        path: '/ssr/sstTools',
        component: './SSRTools',
      }
    ]
  },
  {
    name: '开发助手',
    icon: 'experiment',
    path: '/helper',
    routes: [
      {
        path: '/helper',
        redirect: '/helper/capture',
      },
      {
        name: '网络抓包',
        title: '开发助手 - 网络抓包',
        path: '/helper/capture',
        component: './HelperCapture',
      },
      {
        name: 'Atom调试工具',
        title: '开发助手 - Atom调试工具',
        path: '/helper/atomQrCode',
        component: './AtomQrCode',
      },
      {
        name: 'URL统一化',
        title: '开发助手 - URL统一化',
        path: '/helper/url',
        component: './HelperUrl',
      },
      {
        name: '测试报告',
        title: '开发助手 - 测试报告',
        path: '/helper/reportList',
        component: './HelperReportList',
      },
      {
        name: '测试报告详情',
        title: '开发助手 - 测试报告详情',
        path: '/helper/report',
        component: './HelperReport',
        hideInMenu: true
      },
      {
        name: '性能看板',
        title: '开发助手 - 性能看板',
        path: '/helper/performanceDashboard',
        component: './PerformanceDashboard',
        hideInMenu: true // 功能失效，暂时隐藏
      },
      {
        path: '/helper/link',
        redirect: '/route/link',
        hideInMenu: true
      }
    ]
  },
  {
    name: '预加载体系',
    icon: 'thunderbolt',
    path: '/preSystem',
    routes: [
      {
        path: '/preSystem',
        redirect: '/preSystem/prefetch-market',
      },
      {
        name: '数据预加载配置',
        title: '数据预加载 - Prefetch配置',
        path: '/preSystem/prefetch-config',
        component: './PrefetchConfig',
      },
      {
        name: '数据预加载报表',
        title: '数据预加载 - Prefetch报表',
        path: '/preSystem/prefetch-market',
        component: './PrefetchMarket',
      },
      {
        name: '资源预加载报表',
        title: '资源预加载 - Preload报表',
        path: '/preSystem/preload-market',
        component: './PreloadMarket',
      }
    ]
  },
  {
    name: '生链工具',
    icon: 'link',
    path: '/route',
    routes: [
      {
        path: '/route',
        redirect: '/route/link',
        hideInMenu: true
      },
      {
        name: '生成测试链接',
        title: '路由及投放管理 - 生成测试链接',
        path: '/route/link',
        component: './HelperLink',
      },
      {
        name: '多端投放（含套壳）',
        title: '路由及投放管理 - 多端投放',
        path: '/route/onelink',
        component: './OneLinkForMultiClientLink',
      },
      {
        name: '流量海关',
        title: '路由及投放管理 - 流量海关',
        path: '/route/miniapplink',
        component: './TreasureMiniappLink',
      },
      {
        name: '微信外投',
        title: '路由及投放管理 - 微信外投',
        path: '/route/openlink',
        component: './OpenLink'
      },
      {
        name: '短链生成',
        title: '路由及投放管理 - 短链生成',
        path: '/route/shortlink',
        component: './ShortLink',
      },
      {
        path: '/route/url',
        redirect: '/helper/url',
        hideInMenu: true
      }
    ]
  },
  {
    name: '百宝箱',
    icon: 'inbox',
    path: '/treasure',
    routes: [
      {
        path: '/treasure',
        redirect: '/treasure/TreasureData',
      },
      {
        path: '/treasure/shortlink',
        redirect: '/route/shortlink',
        hideInMenu: true
      },
      {
        path: '/treasure/miniapplink',
        redirect: '/route/miniapplink',
        hideInMenu: true
      },
      {
        name: '常用网站',
        title: '百宝箱 - 常用网站',
        path: '/treasure/navigation',
        component: './TreasureNavigation',
      },
      {
        name: '淘口令生成',
        title: '百宝箱 - 淘口令生成',
        path: '/treasure/genTaoPassword',
        component: './GenTaoPassword',
      },
    ]
  },
  {
    name: '监控平台',
    icon: 'monitor',
    path: '/monitor',
    routes: [
      {
        name: '微信小程序监控',
        path: '/monitor/wechat-miniapp',
        routes: [
          {
            name: '监控大盘',
            path: '/monitor/wechat-miniapp/index',
            component: './MonitorWechatMiniapp',
          },
          {
            name: '问题流转大盘',
            path: '/monitor/wechat-miniapp/issue',
            component: './MonitorWechatMiniapp/Issue',
          },
          {
            name: '告警管理',
            path: '/monitor/wechat-miniapp/alarm',
            component: './MonitorWechatMiniapp/Alarm'
          }
        ]
      },
      {
        name: '小程序业务监控',
        title: '监控大盘 - 小程序业务监控',
        path: '/monitor/miniapp-business',
        component: './MonitorMiniappBusiness',
      }
    ]
  },
  {
    name: '项目管理',
    title: '项目管理',
    icon: 'appstore',
    path: '/project',
    component: './Project',
  },
  {
    name: '权限管理',
    title: '权限管理',
    icon: 'user',
    path: '/user',
    component: './User',
  },
  {
    path: '/*',
    title: '404',
    component: './404',
  },
];
