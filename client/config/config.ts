// https://umijs.org/docs/api/config
import path from 'path';
import { defineConfig } from 'umi';
import routes from './routes';

export default defineConfig({
  antd: {
    configProvider: {
      theme: {
        token: {
          colorPrimary: '#1890ff',
        },
      },
    },
  },
  layout: {
    locale: false,
  },
  access: {},
  model: {},
  initialState: {},
  request: {},
  mountElementId: 'root',
  dva: {},
  qiankun: {
    // 需要开启微前端需要打开
    master: {
      sandbox: { strictStyleIsolation: true },
      enable: true,
      prefetch: true,
      apps: [
        {
          name: 'monitor',
          entry: 'https://fl-page-back.fc.alibaba-inc.com',
          credentials: true, // 拉取 entry 时是否需要携带cookie
        },
      ],
    },
  },
  history: { type: 'hash' },
  devtool: false,
  // umi routes: https://umijs.org/docs/routing
  routes,
  locale: {
    default: 'zh-CN',
    antd: true,
    baseNavigator: true,
  },
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  chainWebpack(config, { webpack }) {
    // 云构建场景日志量太大，删除process plugin
    if (process.env.BUILD_ENV === 'cloud') {
      config.plugins.delete('progress');
    }
  },
  extraBabelPlugins: process.env.NODE_ENV === 'production' 
  ? ['babel-plugin-dynamic-import-node'] 
  : [],
  plugins: ['@ali/umi-plugin-faas'],
  faas: {
    outputPath: '../build',
    functionDir: path.resolve('../server'),
    sourceDir: path.resolve('../server/src/apis'),
    isSeparated: true,
  },
  outputPath: '../build/client',
  manifest: {
    basePath: '/',
  }
});
