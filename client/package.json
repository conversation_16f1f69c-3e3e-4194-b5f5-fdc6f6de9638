{"name": "miniwork", "private": true, "description": "小程序研发平台", "keywords": ["clam", "miniwork"], "repository": {"type": "git", "url": "http://gitlab.alibaba-inc.com/func/miniwork"}, "author": {"name": "南麓", "email": "<EMAIL>"}, "scripts": {"build": "max build", "dev": "max dev", "format": "prettier --cache --write .", "setup": "max setup", "start": "npm run dev"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ali/aes-tracker": "^3.0.0", "@ali/aes-tracker-plugin-api": "^3.0.7", "@ali/aes-tracker-plugin-autolog": "^3.0.4", "@ali/aes-tracker-plugin-eventTiming": "^3.0.0", "@ali/aes-tracker-plugin-jserror": "^3.0.3", "@ali/aes-tracker-plugin-longtask": "^3.0.1", "@ali/aes-tracker-plugin-perf": "^3.0.7", "@ali/aes-tracker-plugin-pv": "^3.0.3", "@ali/aes-tracker-plugin-resourceError": "^3.0.2", "@ali/alitrip-mtop": "^1.19.0", "@ali/rxpi-wechat-url-map": "^1.11.16", "@ali/umi-plugin-find-code": "^0.2.4", "@ali/umi-preset-whale": "1.x", "@ant-design/charts": "^1.2.14", "@ant-design/icons": "^4.5.0", "@ant-design/pro-components": "^2.3.24", "@tanstack/react-table": "^8.11.6", "@uiw/codemirror-extensions-basic-setup": "^4.22.2", "@uiw/codemirror-extensions-langs": "^4.22.2", "@uiw/codemirror-theme-atomone": "^4.22.2", "@uiw/react-codemirror": "^4.22.2", "@umijs/max": "^4.0.59", "antd": "^4.19.0", "classnames": "^2.2.6", "form-render": "1.14.10", "lodash": "^4.17.21", "node-fetch": "^2.7.0", "qrcode.react": "^1.0.1", "query-string": "^9.1.1", "react-diff-viewer-continued": "^3.4.0", "react-hooks-global-state": "^2.0.0-rc.0", "react-json-view": "^1.21.3", "svgsaver": "^0.9.0", "table-render": "^1.1.5", "umi-request": "^1.4.0"}, "devDependencies": {"@ali/umi-plugin-faas": "^6.0.0", "@types/lodash": "^4.14.172", "@types/qrcode.react": "^1.0.2", "@types/react": "^16.9.0", "@types/react-dom": "^16.9.0", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "antd-dayjs-webpack-plugin": "^1.0.6", "babel-plugin-import": "^1.13.3", "eslint-plugin-prettier": "^3.4.0", "prettier": "^2.2.1", "react": "17.x", "react-dom": "17.x", "typescript": "^4.1.2"}, "engines": {"node": ">=16"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"], "commandType": "clam", "defPublishType": "assets", "group": "func", "isCloudBuild": true, "toolkit": "@ali/clam-toolkit-one", "__npminstall_done": false}