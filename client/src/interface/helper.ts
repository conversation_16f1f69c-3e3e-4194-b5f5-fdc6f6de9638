export interface IDependenciesVersionNode {
  /** 当前依赖的版本 */
  version: string;
  /** 依赖版本的依赖链 */
  chain: string[];
}

export interface IDependenciesTreeNode {
  /** key值 */
  key: string;
  /** npm包名+版本号 */
  title: string;
  /** 是否是叶子节点 */
  isLeaf?: boolean;
  /** 是否是版本号节点 */
  isVersion?: boolean;
  /** 是否是依赖链节点 */
  isChain?: boolean;
  /** 是否是需要处理的多版本依赖 */
  isReal?: boolean;
  /** 子节点 */
  children: IDependenciesTreeNode[];

  /** npm包名 */  
  name: string;
  /** npm包版本 */  
  version: string;
  /** diff类型 */
  diffType?: DiffType;
  /** npm diff 版本 */  
  diffVersion?: string;
}

export interface IDependencies {
  tree: IDependenciesTreeNode[];
  multiVersionTree: IDependenciesTreeNode[];
  totalCount: number;
  realCount: number;
  multiVersionCount: number;
  realMultiVersionCount: number;
  multiVersionPackageCount: number;
  realMultiVersionPackageCount: number;
}

export interface IFlatDependenciesItem {
  name: string;
  type: 1 | 2 | 3;
  version?: string[];
  prevVersion?: string[];
  [key: string]: any;
}

export interface IDependenciesDiff {
  diffTree: IDependenciesTreeNode[];
  multiVersionTree: IDependenciesTreeNode[];
  noticeUserInfo?: Record<string, string>;
  diffFlatInfo: {
    list: IFlatDependenciesItem[];
    deleteCount: number;
    modifyCount: number;
    addCount: number;
    noticeInfo: Record<string, {
      bizType: string;
      noticeUser: string;
      deps?: Record<string, string[]>;
      version?: string[];
      prevVersion?: string[];
      [key: string]: any;
    }>;
  };
  addCount: number;
  deleteCount: number;
  modifyCount: number;
  multiVersionCount: number;
  multiVersionPackageCount: number;
}

export enum DiffType {
  Add = 'add',
  Delete = 'delete',
  Modify = 'modify',
}
