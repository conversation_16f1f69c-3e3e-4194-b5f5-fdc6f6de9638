/** 用户信息 */
export interface IUser extends IEmployee {
  /** 用户类型 */
  userType: UserType;
  /** 用户类型文案 */
  userTypeText: string;
}

export interface IEmployee {
  /** 用户 id */
  userid: string;
  /** 工号 */
  workid: string;
  /** 花名 */
  name: string;
  /** 真名 */
  lastName: string;
  /** 头像 */
  avatarUrl: string;
  /** 用户页 */
  htmlUrl: string;
}

export enum UserType {
  ADMIN = 'admin',
  DEVELOPER = 'developer',
  QA = 'qa'
}