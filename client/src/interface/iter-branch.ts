import { IGitRepoInfo, IGitBranch } from './git';
import { IterStatus, EBranchType } from '@/const/iter-branch';
import { EClient, EMiniAppVersionStatus } from '@/const/iter-deliver';
import { IIterDeliverTask } from '@/interface/iter-deliver';
import { EDefBuildStatus, EPubTypeEnv } from '@/const/def';
import { IUser } from './user';

/** 迭代分支详情 */
export interface IIterBranch {
  /** 迭代分支id */
  iterId: number;
  /** 迭代类型（项目类型：app、组件类型：component） */
  branchType?: EBranchType;
  /** 分支版本 */
  version: string;
  /** 分支描述 */
  description: string;
  /** def构建产物 */
  defDist: string;
  /** def上传包体积 */
  defUploadPkgRes?: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  gmtCreate: string;
  /** 修改人 */
  modifier?: string;
  /** 修改时间 */
  gmtModified?: string;
  /** 分支状态 */
  status: IterStatus;
  /** 分支状态文案 */
  statusText: string;
  /** 灰度状态 */
  grayStatus?: string;
  /** 发布日期 */
  publishDay: string;
  /** 开发分支列表 */
  devBranchList: any[];
  /** 游离开发分支列表 */
  freeDevBranchList: any[];
  /** 所属项目名称 */
  projectName: string;
  /** Git仓库 */
  gitRepo: IGitRepoInfo;
  /** 代码分支 */
  gitBranch: IGitBranch;
  /** 集成代码分支 */
  rcGitBranch?: IGitBranch;
  /** 塔台id，自动化测试用 */
  towerId?: string;
  /** shrinkwrap.json地址 */
  shrinkwrap?: string;
  /** 构建产物地址 */
  dist?: string;
  /** reportAnalyzed地址 */
  reportAnalyzed?: string;
  /** 投放端列表（如果值为 null 可能是历史数据，没有该字段） */
  deliverClientList: IDeliverClient[] | null
  /** 迭代类型是组件类型时，新建git分支时，npm包的原始版本号 */
  pkgInitialVersion?: string;
  /** 迭代类型是组件类型时，npm包发布后的版本号 */
  pkgPublishVersion?: string;
  /** 迭代类型是组件类型时，def迭代id */
  defIterId: number;
  /** 迭代类型是组件类型时，def构建任务id */
  defTaskId: number;
  /** 迭代类型是组件类型时，def分支id */
  defBranchId?: number;
  /** 迭代类型是组件类型时，def构建状态（1：未构建、2：构建中、3：构建成功、4：构建失败） */
  defBuildStatus?: EDefBuildStatus;
  /** 迭代类型是组件类型时，def构建类型（1：预发、2：线上） */
  defEnvType?: EPubTypeEnv;
  /** 自动通知的状态(0:未通知，1：已通知) */ 
  autoNoticeStatus?: number;
  /** 迭代测试 */
  qaList: IUser[];
  /** 是否回归完成 */
  checked?: EChecked;
}

export interface IDeliverClient {
  /** 小程序id */
  miniAppId: string;
  /** 端名称 */
  clientName: EClient;
  /** 端扩展信息 */
  clientExtInfo: {
    /** 端中文名称 */
    cnName: string;
    /** 端图标 */
    icon: string;
  } | null;
  /** 是否跳过投放（指当前迭代不投放该端） */
  skip: boolean;
  /** 绑定的小程序投放任务id */
  deliverTaskId: number | null;
  /** 原投放平台地址 */
  originalPlatform: string;
}

export interface IDeliverClientWithTask extends IDeliverClient {
  deliverTask: IIterDeliverTask | null;
}

export interface IIterStep {
  matchProcessStatus: (EMiniAppVersionStatus | IterStatus)[];
  matchErrorStatus?: (EMiniAppVersionStatus | IterStatus)[];
  matchFinishStatus?: (EMiniAppVersionStatus | IterStatus)[];
  title: string;
  description?: { [key: string]: string }
}

export enum EChecked {
  /** 未测试回归 */
  NO = 0,
  /** 已测试回归 */
  YES = 1,
}
