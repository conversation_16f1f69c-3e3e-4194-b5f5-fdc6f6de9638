import { BIZ_LINE } from '@/const/biz';
import { EIterQaReviewAction } from '@/const/iter-qa-review';
import { IActionRecord } from '@/interface/action-history';

/** 迭代回归记录 */
export interface IIterQaReview {
  /** 回归记录id */
  id: number;
  /** 迭代分支id */
  iterId: number;
  /** 创建时间 */
  gmtCreate: string;
  /** 行业名称 */
  bizName: BIZ_LINE;
  /** 回归结论 */
  reviewResList: IIterQaReviewRes[];
  /** 历史记录 */
  actionHistory: IActionRecord<EIterQaReviewAction>[];
}

/** 单条回归结论 */
export interface IIterQaReviewRes {
  /** 回归结论id */
  resId: number;
  /** 回归者 */
  reviewer: {
    /** 花名 */
    name: string;
    /** 工号 */
    workid: string;
  };
  /** 是否通过 */
  pass: boolean;
  /** 主要回归内容 */
  title: string;
  /** 回归纪要 */
  comment: string;
}