export interface IPrefetchConfig {
  /** id */
  id: number;
  /** 所属项目名 */
  projectName: string;
  /** 是否停用 */
  isStop?: number;
  /** 页面path */
  path: string;
  /** 是否需要登陆 */
  needLogin?: boolean;
  /** mtop api */
  mtopApi?: string;
  /** mtop version */
  mtopVersion?: string;
  /** 强匹配参数，匹配不上不返回数据 */
  verifyKeys?: IPrefetchVerifyKeys[];
  /** 获取数据时静态参数 */
  staticParams?: string;
  /** 获取数据时需要从query中获取的动态参数 */
  dynamicParams?: IPrefetchDynamicKeys[];
  /** 是否需要定位信息 */
  needGeolocation?: boolean;
  /** 过期时间 */
  expiryTimeMs?: number;
  /** 是否获取完就删除 */
  isNeedGetThenRemove?: boolean;
  /** 获取mtop时的超时时间 */
  mtopTimeOutMs?: number;
  /** 是否有冷启动场景 */
  hasColdStart: boolean;
  /** hsf id（冷启动场景） */
  hsfId?: string;
  /** hsf method（冷启动场景） */
  hsfMethod?: string;
  /** hsf parameterTypes（冷启动场景） */
  hsfParameterTypes?: string;
  /** hsf调用参数结构（冷启动场景） */
  hsfParamsObj?: IPrefetchHsfParams[];
  /** 是否灰度 */
  isGray?: boolean;
  /** 灰度进度 */
  grayNumber?: number;
  /** 入参处理函数 */
  paramsHandleFunc?: string;
  /** 取数据校验参数函数 */
  checkParamsFunc?: string;
  /** mtop信息 */
  mtopData?: IPrefetchMtopData;
  /** 不开启冷启动*/
  notColdPrefetch?: boolean;
}

export interface IPrefetchMtopData {
  /** mtop api */
  mtopApi?: string;
  /** mtop version */
  mtopVersion?: string;
  /** hsf data */
  hsfData?: IPrefetchHsfData;
}

export interface IPrefetchHsfData {
  /** hsf id */
  hsfId?: string;
  /** hsf method */
  hsfMethod?: string;
  /** hsfParameterTypes */
  hsfParameterTypes?: string;
  /** hsf 入参结构 */
  hsfParamsMapping?: any;
  /** mtop 入参结构 */
  mtopParamsMapping?: any;
}

export interface IPrefetchVerifyKeys {
  name: string;
}

export interface IPrefetchDynamicKeys {
  /** url Pramas中key名 */
  urlParamKey: string;
  /** 实际需要key名 */
  actuallyParamKey: string;
}

export interface IPrefetchHsfParams {
  key: string;
  defaultValue: string;
}