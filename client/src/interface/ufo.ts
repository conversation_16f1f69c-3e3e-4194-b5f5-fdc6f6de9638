import { IIterBranch } from './iter-branch';

export interface IPerformanceList {
  success: boolean;
  data: {
    success: boolean;
    data: {
      id: number;
      gmtCreate?: number;
      gmtModified?: number;
      pageName: string;
      bizLine?: string;
      network: string;
      mobileModel: string;
      os: string;
      openTime: string | null;
      mirrorAlgorithmId: number;
      runningType: string;
      pageUrl: string;
      pageType: string;
      // 迭代分支
      h5Version: string;
    }[];
  }[];
}

export interface IPerformanceLineData {
  h5Version: string;
  pageName: string;
  os: string;
  openTime: number;
  iterId: number;
  bizLine: string;
}

export interface IIterBranchResult {
  success: boolean;
  data: Pick<IIterBranch, 'iterId' | 'version'>[];
}

export interface IBaseConfig {
  ufoTaskId: number;
  bizLine: string;
  taskDetail: {
    configName: string;
    configUrl: string;
    os: string;
    configId: number;
    ufoTaskRecordId: number;
  };
  taskType: string;
  taskName: string;
  id: number;
}
[];
