import { EPubTypeEnv } from '@/const/def';
import { EDeliverBranchType, EMinaStatus, EDeliverType } from '@/const/component-deliver';
import { INpm } from '@/interface/base-dev-branch';

export interface IComponentDeliver {
  key: string;
  name: string;
  cnName: string;
  gitRepo: string;
  gitProjectId: number;
  defProjectId: number;
  hasFpackage: true;
  hasZcache: true;
  /** 投放id */
  deliverId: number;
  /** 迭代id（如果是开发分支类型，这里指被挂载到的迭代，如果是迭代类型，就是迭代本身） */
  iterId?: number;
  /** 开发分支id（包含游离开发分支id、开发分支id） */
  devId?: number;
  /** 投放类型（1：h5、2：weixin、3：alipay、4：bytedance） */
  deliverType: EDeliverType;
  /** 开发分支类型（1表示游离开发分支，2表示开发分支，3表示迭代） */
  branchType?: EDeliverBranchType;
  /** 投放应用名称 */
  projectName?: string;
  /** 投放应用分支版本 */
  projectVersion?: string;
  /** 需要更新的npm包，json格式的数组 */
  npmList?: INpm[];
  /** 分支状态（-1：废弃、0：未投放、1：预发投放中、2：预发投放成功、3：预发投放失败、4：线上投放中、5：线上投放成功、6：线上投放失败） */
  // status: EDeliverStatus;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  gmtCreate: Date;
  /** 修改人 */
  modifier?: string;
  /** 修改时间 */
  gmtModified?: Date;
  /** def迭代id */
  defIterId?: number;
  /** def构建任务id */
  defTaskId?: number;
  /** def构建状态（1：未构建、2：构建中、3：构建成功、4：构建失败） */
  defBuildStatus?: number;
  /** def构建分支的branchId（用于绑定） */
  defBranchId?: number;
  /** def构建类型（1：预发、2：线上） */
  defEnvType?: EPubTypeEnv;
  /** 投放的小程序信息 */
  miniappInfo?: IMiniappInfo;
}

export interface IMiniappInfo {
  branchName: string;
  projectName: string;
  devId: number;
  status: EMinaStatus;
}
