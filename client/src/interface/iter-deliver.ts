import { EClient, EMiniAppUploadStatus, EMiniAppVersionStatus } from '@/const/iter-deliver';

/** 迭代投放任务 */
export interface IIterDeliverTask {
  /** 任务id */
  id: number;
  /** 迭代分支id */
  iterId: number;
 /** 投放端，适用多端单独投放 */
 clientName?: EClient;
 /** 投放端列表，适用多端统一投放 */
 clientList?: EClient[];
  /** 小程序id */
  miniAppId: string;
  /** 小程序上传版本 */
  miniAppVersion?: string;
  /** 上传日志 */
  uploadLog?: string;
  /** 创建时间 */
  gmtCreate: string;
  /** 修改时间 */
  gmtModified?: string;
  /** 小程序上传状态 */
  uploadStatus: EMiniAppUploadStatus;
  /** 小程序上传状态文案 */
  uploadStatusText: string;

  /** 小程序版本信息 */
  miniAppVersionInfo?: IAlipayVersionInfo | IMiniAppVersionInfo;
}

/** 支端小程序版本信息 */
export interface IAlipayVersionInfo extends IMiniAppVersionExtInfo {
  /** 端信息 */
  bundleId: string;
  /** 小程序id */
  miniAppId: string;
  /** 小程序版本 */
  appVersion: string;
  /** 小程序版本状态 **/
  status: EMiniAppVersionStatus;
  /** 版本创建时间，如 2019-01-01 12:00:22 */
  createTime: string;
  /** 版本灰度时间 */
  grayStartTime?: string;
  /** 版本回滚时间 */
  rollbackTime?: string;
  /** 版本上架时间 */
  shelfTime?: string;
  /** 版本下架时间 */
  offlineTime?: string;
  /** 版本构建类型，01 - 自创建、02 - 模板构建 */
  buildSource: string;
  /** 版本描述 */
  description: string;
  /** 包地址 */
  packageUrl: string;
  /** 小程序灰度策略，累加值，以英文逗号分割，详见：https://yuque.antfin-inc.com/tinyapp-all/qddncu/zst2ef */
  grayStrategy?: string;
  /** 审核失败原因 */
  rejectReason: string;
  /** 安全扫描结果，true、false */
  scanResult: string;
  /** 小程序版本截图，多个截图以逗号隔开 */
  screenShotList: string;
}

/** 其他小程序版本信息 */
export interface IMiniAppVersionInfo extends IMiniAppVersionExtInfo {
  /** 版本id */
  id: number;
  /** 端信息 */
  clientName: string;
  /** 小程序id */
  miniAppId: string;
  /** 小程序版本 */
  miniAppVersion: string;
  /** 小程序版本状态 **/
  status: EMiniAppVersionStatus;
  /** 版本创建时间，如 2019-01-01 12:00:22 */
  createTime: string;
  /** 版本灰度时间 */
  grayStartTime?: string;
  /** 版本回滚时间 */
  rollbackTime?: string;
  /** 版本上架时间 */
  shelfTime?: string;
  /** 版本下架时间 */
  offlineTime?: string;
  /** 小程序灰度策略，累加值，以英文逗号分割 */
  grayStrategy?: string;
}

interface IMiniAppVersionExtInfo {
  /** 小程序版本状态文案 */
  statusText: string;
  /** 当前灰度策略 */
  curGrayStrategy?: string;
  /** 灰度步骤 */
  graySteps: {
    /** 策略值 */
    strategy: string;
    /** 策略名称 */
    title: string;
  }[];
  /** 投放平台地址 */
  platformUrl: string;
}