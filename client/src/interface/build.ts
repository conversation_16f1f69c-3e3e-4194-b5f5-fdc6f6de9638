import { EBuildPlatform } from "@/const/build";

export interface IBuildTask {
  /** 云构建任务id */
  taskId: string;
  /** 代码分支 */
  branchName: string;
  /** 代码提交id */
  commitId: string;
  /** 构建环境：online - 线上、pre - 预发 */
  env: string;
  /** 小程序平台 */
  platform: EBuildPlatform;
  /** 页面路径 */
  pagePath: string;
  /** 页面参数 */
  pageQuery: string;
  /** 构建状态：-1 - 失败、0 - 进行中、1 - 成功 */
  status: number;
  /** 构建产物地址 */
  dist?: string;
  /** 错误信息 */
  error?: string;
  /** 二维码地址 */
  imgUrl?: string;
  /** 构建人 */
  creator: string;
  /** 构建时间 */
  gmtCreate: string;
  /** 自定义参数 */
  customArgv: {
    /** 启用按需构建，只对支付宝小程序有效 */ 
    minifyBuild?: boolean;
    /** 参与按需构建的包 */
    minifyBuildPackages?: string[];
    /** 启用快速构建，只对微信小程序有效。与按需构建冲突，按需构建优先 */
    fastbuild?: 1 | 0;
    /** 备注 */
    remark?: string;
  };
}
