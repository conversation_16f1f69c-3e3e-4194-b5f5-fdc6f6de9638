import { ISubPackageSize } from '@/interface/package'

/**
 * 集成任务明细
 */

export interface TaskDetail {
  "result": boolean,
  "taskType": string,
  "totalNum": number,
  "successNum": number,
  "ufoTaskId": number,
  "resultUrl": string,
  "taskStatus": number,
  "taskDetail": CaseDetail[] | PackageDetail[],
  "taskName": string,
  "id": number,
  "standardResult": number,
  "realResult": number;
}

/**
 * 自动化case结果明细
 */
export interface CaseDetail {
  "caseName": string,
  "result": boolean,
  "msg": string,
}


/**
 * 包大小结果明细
 */
export interface PackageDetail extends ISubPackageSize {
  "bizLine": string,
}

/**
 * 主任务明细
 */
export interface MainTaskDetail {
  "checkType": string,
  "gmtModified": number,
  "batchNo": string,
  "mainTaskId": number,
  "ufoTaskId": number,
  "branchName": string,
  "gmtCreate": number,
  "checkResult": number,
  "resultInfo": string,
  "url": string,
  "appType": string,
  "id": number,
  "alarmReceiver": string,
  "businessType": string,
  "isMainTask": number,
  "taskStatus": number
}
