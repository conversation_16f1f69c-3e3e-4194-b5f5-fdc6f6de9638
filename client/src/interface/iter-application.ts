import { EProcessInstanceStatus } from '@/const/bpms';
import { IBucSimpleUser } from '@/interface/buc';

export interface IIterApplication {
  /** 实例 id */
  processInstanceId: string;
  /** 标题 */
  title: string;
  /** 申请链接 */
  url: string;
  /** 创建时间 */
  gmtCreate: string;
  /** 申请状态 */ 
  status: EProcessInstanceStatus;
  /** 状态文案 */ 
  statusText: string;
  /** buc的用户信息 */
  bucUser?: IBucSimpleUser;
}