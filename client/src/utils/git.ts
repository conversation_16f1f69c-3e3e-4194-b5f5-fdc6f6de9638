import gitApi from '@/api/git';
import { IGitRepoInfo } from '@/interface/git';

// 得到最大的版本号
export function getBiggestVersion(A) {
  var a = [];
  var r = [];
  if (!A) {
    return [0, 0, 0];
  }
  for (var i = 0; i < A.length; i++) {
    if (A[i].match(/^\d+\.\d+\.\d+$/)) {
      var sp = A[i].split('.');
      a.push([
        Number(sp[0]), Number(sp[1]), Number(sp[2])
      ]);
    }
  }

  var r = findMax(findMax(findMax(a, 0), 1), 2);
  return r[0];
}

// a：二维数组，index，比较第几个
// return：返回保留比较后的结果组成的二维数组
export function findMax(a, index) {
  var t = [];
  var b = [];
  var r = [];
  for (var i = 0; i < a.length; i++) {
    t.push(Number(a[i][index]));
  }
  var max = Math.max.apply(this, t);
  for (var i = 0; i < a.length; i++) {
    if (a[i][index] === max) {
      b.push(i);
    }
  }
  for (var i = 0; i < b.length; i++) {
    r.push(a[b[i]]);
  }
  return r;
}

export enum Action {
  Create = 'create', // 创建
  Update = 'update', // 更新
  Read = 'read', // 查看
}

// 获得最新且最大的git分支号
export const getNewAndBiggestVersion = async (gitProjectId: number, type?: Action): Promise<string> => {
  // simu-test
  // gitProjectId = 2582736;
  if (!gitProjectId) {
    return '';
  }

  const res = await gitApi.getBranches({ projectId: gitProjectId, per_page: 30 });
  if (res.data) {
    const gitBranchNameList = res.data.reduce((list: any[], item) => {
      const matchRes1 = /^free-dev\/\d{8}-(\d+\.\d+\.\d+)-/ig.exec(item.name);
      const matchRes2 = /^dev\/(?:\d+\.\d+\.\d+)-(\d+\.\d+\.\d+)-/ig.exec(item.name);
      const matchRes3 = /^daily\/(\d+\.\d+\.\d+)/ig.exec(item.name);
      const matchRes4 = /^rc\/(\d+\.\d+\.\d+)/ig.exec(item.name);
      const matchRes5 = /^stable\/(\d+\.\d+\.\d+)/ig.exec(item.name);
      const matchRes6 = /def[a-z0-9_-]*\/(\d+\.\d+\.\d+)/ig.exec(item.name);
      if (matchRes1?.length || matchRes2?.length || matchRes3?.length || matchRes4?.length || matchRes5?.length || matchRes6?.length) {
        list.push(matchRes1?.[1] || matchRes2?.[1] || matchRes3?.[1] || matchRes4?.[1] || matchRes5?.[1] || matchRes6?.[1]);
      }
      return list;
    }, []) as string[];
    let ver = getBiggestVersion(gitBranchNameList)
    if (type === Action.Create) {
      // 如果是新建分支，git版本号要加一
      ver[2]++;
    }
    ver = ver.join('.');
    // console.log('ver', ver);
    return ver;
  }
  return '';
}

/** 从gitRepo链接里解析出具体 */
export function parseGitRepoInfo(repoUrl: string): IGitRepoInfo | null {
  let matches;
  if (/^http/.test(repoUrl)) {
    // http://gitlab.alibaba-inc.com/trip/rx-miniwork-test.git
    const regExp = /^http(?:s)?:\/\/([^/]*)\/([^/]*)\/([^/.]*)/i;
    matches = repoUrl.match(regExp);
  }

  if (/^git@/.test(repoUrl)) {
    // **************************:trip/rx-miniwork-test.git
    const regExp = /^git@([^:]*):([^/]*)\/([^/.]*)/i;
    matches = repoUrl.match(regExp);
  }

  if (matches) {
    const host = matches[1] || '';
    const group = matches[2] || '';
    const project = matches[3] || '';
    return {
      url: `http://${host}/${group}/${project}`,
      httpUrl: `http://${host}/${group}/${project}.git`,
      sshUrl: `git@${host}:${group}/${project}.git`,
      host,
      group,
      project
    };
  } else {
    console.error('>> parse git repo info fail, url: ' + repoUrl);
    return null;
  }
}
