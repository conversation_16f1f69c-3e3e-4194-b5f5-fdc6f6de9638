// 格式化日期
export function formateDate(fmt: string, date = new Date()) {
  const opt = {
    "Y+": date.getFullYear().toString(), // 年
    "M+": (date.getMonth() + 1).toString(), // 月
    "D+": date.getDate().toString(), // 日
    "h+": date.getHours().toString(), // 时
    "m+": date.getMinutes().toString(), // 分
    "s+": date.getSeconds().toString() // 秒
  };
  
  Object.entries(opt).map(([key, val]) => {
    const ret = new RegExp(`(${key})`).exec(fmt);
    if (ret) {
      fmt = fmt.replace(ret[1], (ret[1].length === 1) ? val : val.padStart(ret[1].length, '0'))
    };
  })

  return fmt;
}
