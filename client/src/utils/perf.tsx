import { IFreeDevBranch } from '@/interface/free-dev-branch';
import { IIterBranch } from '@/interface/iter-branch';
import freeDevBranchApi from '@/api/free-dev-branch';
import packageApi from '@/api/package';
import buildApi from '@/api/build';
import { Modal } from 'antd';

let showLimit = false;
const SUGGESSTION = '优化建议：1. 检查分支分支是否包含master最新代码，不包含则更新一下；2. 查看构建日志，检查是否有分包异步化失败；3. 超出体积部分是非首屏组件，请配置分包异步化；4. 如果是项目必要代码导致增加，联系桑陆或者芷毓';

function renderSizeMsg(text: string, color: string, msgList?: string[]) {
  if (!msgList || !msgList.length) return null;
  return (
    <div style={{ marginBottom: '12px' }}>
      {text}
      <div style={{ marginLeft: '20px', color }}>
        {msgList.map(item => <div key={item}>- {item}</div>)}
      </div>
    </div>
  );
}

function SizeContent({ warnMsg, limitMsg, isIter, holdUp }: { warnMsg?: string[]; limitMsg?: string[]; isIter?: boolean; holdUp?: boolean }) {
  const branchText = holdUp ? '请及时优化，否则无法就绪参与集成' : '无法就绪参与集成，请优化';
  const limitTitle = `❗️❗️ 体积超限，${isIter ? '请先优化迭代体积' : branchText}：`;
  return (
    <div>
      {renderSizeMsg(limitTitle, 'red', limitMsg)}
      {renderSizeMsg('⚠️⚠️ 以下包体积增加较多，请注意优化：', 'orange', warnMsg)}
      <div style={{ margin: '10px 0', color: '#999' }}>{SUGGESSTION}</div>
    </div>
  )
}

/** 处理体积接口返回 */
function handleSizeResp(res: any, resolve: (v: any) => void, reject: (v: any) => void) {
  if (res?.msg || res.data.msg) {
    return reject({ msg: res?.msg || res.data.msg });
  }
  // 缺少体积信息的情况，直接返回
  if (!res.data.limitInfo) {
    return resolve(true);
  }
  const { limit, limitMsg, warnMsg } = res.data.limitInfo;
  if (!limit && !warnMsg.length) {
    return resolve(true);
  }
  // 体积达限
  if (limit) {
    reject({ limit, warnMsg, limitMsg });
  }
  // 总包警告
  reject({ warnMsg, warn: true });
}

function handleCheckSizeCatch(err: any, callback: () => void, holdUp: boolean = true, isAdmin?: boolean) {
  // 非阻塞场景，继续执行callback
  if (!holdUp) {
    callback();
  }
  // 体积限制
  if (err.limit) {
    Modal.error({
      width: 600,
      title: '包体积超限',
      content: <SizeContent warnMsg={err.warnMsg} limitMsg={err.limitMsg}  />,
      onOk() {
        // 留个后门给管理员操作
        if (isAdmin && showLimit) {
          callback();
        }
        showLimit = true;
      }
    });
    return;
  }
  if (err.warn) {
    Modal.warning({
      title: '包体积告警',
      width: 600,
      content: <SizeContent warnMsg={err.warnMsg} />,
      onOk() {
        holdUp && callback();
      }
    });
    return;
  }
  Modal.error({
    title: '体积校验失败',
    content: err.msg,
  });
}

/**
 * 检查分支体积
 * @param branch 
 * @param projectName 
 * @returns 
 */
export function checkBranchSize(branch: IFreeDevBranch, projectName: string, needNotice?: boolean) {
  return new Promise((resolve, reject) => {
    // 非微信小程序项目，不做校验
    if (projectName !== 'fliggy-weixin') {
      return resolve(true);
    }
    // 就绪前分支详情里没有dist，需要去查一遍构建列表
    const { branchName, modifierWorkid, creatorWorkid } = branch;
    buildApi.list({
      branchName,
      projectName,
      page: 1,
      pageSize: 5,
    }).then((res) => {
      const buildRes = (res?.data?.list || []).find(item => item.status === 1);
      if (!buildRes || !buildRes.dist) {
        return reject({
          msg: '分支构建信息缺失，请先打码上传'
        });
      }
      const { dist, gmtCreate } = buildRes as { dist: string, gmtCreate: string };
      freeDevBranchApi.analyzeSize({
        dist,
        branchName,
        projectName,
        gmtModified: gmtCreate,
        noticeUsers: needNotice ? `${creatorWorkid},${modifierWorkid}` : '',
      }).then((res: any) => {
        handleSizeResp(res, resolve, reject);
      }).catch(err => {
        reject({ msg: err?.msg || err?.data?.msg });
      })
    }).catch(err => {
      reject({ msg: err?.msg || err?.data?.msg });
    });
  })
}

function checkIterSize(iterBranch: IIterBranch) {
  return new Promise((resolve, reject) => {
    const { defUploadPkgRes, projectName, gmtModified } = iterBranch;
    // 非微信小程序项目，不做校验
    if (projectName !== 'fliggy-weixin') {
      return resolve(true);
    }
    if (!defUploadPkgRes) {
      return reject({
        msg: '迭代体积信息缺失，请先上传'
      });
    }
    packageApi.analyzeIterSize({
      defUploadPkgRes,
      projectName,
      gmtModified,
    }).then((res) => {
      handleSizeResp(res, resolve, reject);
    }).catch(err => {
      reject({ msg: err?.msg || err?.data?.msg });
    }) 
  });
}

/**
 * 分支挂载、就绪前检查
 * @param branch 分支信息
 * @param projectName 项目名称
 * @param callback 检查通过后的回调
 * @param holdUp 是否阻塞
 */
export function checkSizeBeforeBranchAction(
  branch: IFreeDevBranch,
  projectName: string,
  callback: () => void,
  extra: { holdUp?: boolean; needNotice?: boolean, isAdmin?: boolean } = { holdUp: true }
) {
  checkBranchSize(branch, projectName, extra.needNotice)
    .then(callback)
    .catch(err => handleCheckSizeCatch(err, callback, extra?.holdUp, extra?.isAdmin));
}

/**
 * 迭代投放时检查
 * @param iterBranch 迭代信息
 * @param projectName 项目名称
 * @param callback 检查通过后的回调
 */
export function checkSizeBeforeIterDeliver(iterBranch: IIterBranch, callback: () => void) {
  checkIterSize(iterBranch)
    .then(callback)
    .catch(err => handleCheckSizeCatch(err, callback, true));
}