// @ts-nocheck
/**
 * AEM 埋点，如有疑惑可联系 @秦渡
 * AEM 站点：https://aem.alibaba-inc.com/workbench
 * AEM 文档：https://yuque.antfin-inc.com/aes/help
 * 数据看板：https://seven.alibaba-inc.com/
 */
import { matchRoutes } from '@umijs/max';
import AES from '@ali/aes-tracker';
import AESPluginPV from '@ali/aes-tracker-plugin-pv';
import AESPluginJSError from '@ali/aes-tracker-plugin-jserror';
import AESPluginAPI from '@ali/aes-tracker-plugin-api';
import AESPluginResourceError from '@ali/aes-tracker-plugin-resourceError';
import AESPluginPerf from '@ali/aes-tracker-plugin-perf';
import AESPluginEventTiming from '@ali/aes-tracker-plugin-eventTiming';
import AESPluginLongTask from '@ali/aes-tracker-plugin-longtask';
import AESPluginAutolog from '@ali/aes-tracker-plugin-autolog';

declare global {
  interface Window {
    currentUser: IUser;
  }
}

/**
 * 如果是 BUC 账号体系应用，按如下设置。
 * - 由于 BUC 应用的用户名和用户 ID 一般在 Cookie 里不透出，得从接口里拿。
 * - 所以在用户信息接口返回的地方加上 `aes.setConfig('uid', 'XXX'); aes.setConfig('username', 'XXX');`
 */
export const aes = new AES({
  pid: 'miniwork',
  user_type: '14',
  requiredFields: ['uid', 'username'],
  uid: window.currentUser.userid,
  username: window.currentUser.name
});

/** 挂载插件，只有挂载上的才会生效 */
aes.use([
  AESPluginJSError,
  AESPluginAPI,
  AESPluginResourceError,
  AESPluginPerf,
  AESPluginEventTiming,
  AESPluginLongTask,
  AESPluginAutolog,
]);

// PV 插件单独处理，需要关闭自动发送 PV
const pluginPV = aes.use(AESPluginPV, { autoPV: false });

// pluginPV 自带的 switchPage 不会自动发送离开埋点，故封装一层，切换页面的时候会发离开埋点
export const switchPage = (...props) => {
  if (aes.getConfig('page_id')) {
    pluginPV.sendLeave();
  }
  return pluginPV.switchPage(...props);
};

export const onRouteChange = ({ clientRoutes, location }) => {
  const matchedRoute = matchRoutes(clientRoutes, location.pathname)?.pop()
    ?.route;

  // 如果这个路由是一个跳转路由，就直接略过
  if (
    !matchedRoute ||
    matchedRoute.redirect ||
    matchedRoute.element?.props?.to
  ) {
    return;
  }
  
  // 获取页面的名称
  const nextPageId = matchedRoute?.name || location.pathname;

  /**
   * 注意：
   * 如果你的项目有接入微前端，或者是你有 iframe 页面并也想做页面区分，
   * 需要在这个位置给 nextPageId 设置需要的值。
   *
   * 举个例子，我有一些 iframe 页面是这样的：
   * - https://x.xx/#/iframe?url=xxx1
   * - https://x.xx/#/iframe?url=xxx2
   * - https://x.xx/#/iframe?url=xxx3
   *
   * 那么我要写下
   * nextPageId = location.query.url;
   */

  if (nextPageId !== aes.getConfig('page_id')) {
    // 到了这里说明路由对应渲染的页面发生了变化
    console.log('[AEM] nextPageId >>> ', nextPageId);
    switchPage({ page_id: nextPageId });
  }
};

/** 这里是把实例暴露给 sdata 使用，便于可视化查看页面数据 */
window.aes = aes;