import _ from 'lodash';

/** 获取def迭代页面链接 */
export const getDefIterationUrl = (defIterId?: number) => {
  return defIterId ? `//space.o2.alibaba-inc.com/iteration/${defIterId}/basic` : '';
}

/** 获取奥特UI测试页面链接 */
export const getAutoUITestUrl = (testId?: number) => {
  return testId ? `//fl-auto-test.fc.alibaba-inc.com/test/browserTest/${testId}` : '';
}

/**
 * 根据bizLine自定义CR人员列表 (投放H5页面时)
 * @param workIdList 分支对应的开发人信息（id、业务线）
 * @param actionWorkId 发布管理员id（下单页是丝木/凌麟/九屻，发布管理员不可以选择自己去CR）
 * @returns
 */
export const getCRUserByBizLine = (workIdList: { creatorWorkid?: string; bizLine?: string; }[], actionWorkId: string) => {
  const crWorkIdList: string[] = [];

  // 不同业务线找对应负责人CR
  workIdList.forEach((user) => {
    const { creatorWorkid = '' } = user;
    switch (user.bizLine) {
      case 'vacation': {
        // 度假 二助 305411 治愚 417065
        crWorkIdList.push(+creatorWorkid !== 417065 ? '417065' : '305411');
        break;
      }
      case 'hotel': {
        // 酒店 贤重 304085 秦渡 208504
        crWorkIdList.push(+creatorWorkid !== 304085 ? '304085' : '208504');
        break;
      }
      case 'train': {
        // 火车票 依里 323058 上白 324152
        if (+creatorWorkid !== 323058) {
          crWorkIdList.push('323058')
        }
        if (+creatorWorkid !== 324152) {
          crWorkIdList.push('324152')
        }
        break;
      }
      case 'grabTrain': {
        // 火车票抢票 上白 324152
        crWorkIdList.push(+creatorWorkid !== 324152 ? '324152' : '323058');
        break;
      }
      case 'flight': {
        // 机票 江渺 353643 化云 123906
        crWorkIdList.push(+creatorWorkid !== 353643 ? '353643' : '123906');
        break;
      }
      case 'vehicle': {
        // 用车 紫猫 250315 蓑雨 415952
        crWorkIdList.push(+creatorWorkid !== 250315 ? '250315' : '415952');
        break;
      }
      case 'bus':
      case 'ship': {
        // 汽车票 船票 如基 417438 溪童 112095
        crWorkIdList.push(+creatorWorkid !== 417438 ? '417438' : '112095');
        break;
      }
      case 'common': {
        // 保险 祁南 288915 晨丝 372979
        if (+creatorWorkid === 288915) {
          crWorkIdList.push('372979');
        } else if (+creatorWorkid === 372979) {
          crWorkIdList.push('288915');
        }
        // 平台 丝木 335299 九屻 134512 凌麟 414542
        if (+creatorWorkid === 335299) {
          crWorkIdList.push(+actionWorkId !== 134512 ? '134512' : '414542');
        } else if (+creatorWorkid === 414542) {
          crWorkIdList.push(+actionWorkId !== 335299 ? '335299' : '414542');
        } else if (+creatorWorkid === 134512) {
          crWorkIdList.push(+actionWorkId !== 335299 ? '335299' : '414542');
        }
        break;
      }
      case 'travelDetail': {
        // 商详 织火 285786 二助 305411
        crWorkIdList.push(+creatorWorkid !== 285786 ? '285786' : '305411');
        break;
      }
      case 'test': {
        // 测试 橙犀 371555
        crWorkIdList.push('371555');
        break;
      }
      default:
        break;
    }
  })

  return crWorkIdList;
}

/**
 * 自定义CR人员列表（发布组件时）
 * @param workIdList 分支对应的开发人信息（id、业务线）
 * @param actionWorkId 发布管理员id（下单页是丝木/凌麟/九屻，发布管理员不可以选择自己去CR）
 * @returns
 */
export const getCRUserList = (
  projectName: string,
  workIdList: { creatorWorkid?: string; bizLine?: string; [key: string]: any }[],
  actionWorkId: string,
  needPlatformCR?: boolean,
) => {
  let crWorkIdList: string[] = [];

  if (['buy-phoenix', 'simu-test'].includes(projectName)) {
    // 下单页
    crWorkIdList = getCRUserByBizLine(workIdList, actionWorkId);

    // 如果需要平台CR且crWorkIdList没有平台的人，则需要再添加一下 丝木 335299 九屻 134512 凌麟 414542
    const defaultCRList = ['335299', '414542', '134512'];
    if (needPlatformCR && !_.intersection(crWorkIdList, defaultCRList)?.length) {
      crWorkIdList.push(+actionWorkId !== 335299 ? '335299' : '414542');
    }
  } else if (projectName === 'travel-detail') {
    // 商详 织火 285786 二助 305411
    crWorkIdList.push(+actionWorkId !== 285786 ? '285786' : '305411');
  }

  return crWorkIdList;
};
