import MTop from '@ali/alitrip-mtop';
import { extend } from 'umi-request';

/**
 * 内部 Http 接口调用
 */
const req = extend({
  prefix: '/api',         // 统一加前缀
  credentials: 'include', // 带上cookie
});
export const runApi = async <IRes>(api: string, params?: any, method = 'get') => {
  const options: any = { method };
  if (method.toUpperCase() === 'GET') {
    options.params = params;
  }
  if (method.toUpperCase() === 'POST') {
    options.data = params;
  }
  return req(api, options)
    .then((res: any) => res as IRes);
};

/**
 * 二方 Http 接口调用
 */
const req2nd = extend({
  credentials: 'include'
});
export const run2ndApi = async <IRes>(api: string, params?: any, method = 'get') => {
  const options: any = { method };
  if (method.toUpperCase() === 'GET') {
    options.params = params;
  }
  if (method.toUpperCase() === 'POST') {
    options.data = params;
  }
  return req2nd(api, options)
    .then((res: any) => res as IRes);
};

//用于http接口转发的场景
export interface ProxyParams {
  url: string;
  method: string;
  params: object;
}

export const mtopProxy = (proxyParams: ProxyParams) => {
  const options = {
    api: 'mtop.fliggy.serverless.fc.api',
    version: '1.0',
    data: {
      fcGroup: 'fl-mtop',
      fcName: 'index',
      fcArgs: JSON.stringify(proxyParams),
    },
  };
  return MTop.request(options);
};

//用于http接口转发使用mtop请求的场景
export const runMtopProxy = async (api: string, params?: any, method = 'get') => {
  return mtopProxy({ url: api, params, method })
    .then((res: any) => res.data);
};

//请求资源
export const fetchReq = async (url: string) => {
  return new Promise((resolve, reject) => {
    fetch(url)
    .then(res => {
      resolve(true)
    }).catch(e =>{
      resolve(false)
    })
  })
  
}
