/**
 * 递归对类型为 string 的值进行 trim
 */
export function trimCollection(collection: any, depth: number = Infinity): any {
  if (depth === 0) return collection;

  if (Array.isArray(collection)) {
    return collection.map(item => trimCollection(item, depth - 1))
  } else if (Object.prototype.toString.call(collection) === '[object Object]') {
    return Object.keys(collection).reduce((pre, key) => {
      pre[key] = trimCollection(collection[key], depth - 1)
      return pre;
    }, {} as { [key: string]: any })
  } else if (typeof collection === 'string') {
    return collection.trim();
  } else {
    return collection;
  }
}

/**
 * 查询url入参
 * @param param 参数名
 * @return string 参数值
 */
export const gerUrlParam = (param: string) => {
  let result:any = {};
  const queryString = window.location.href.split('?') || parent.location.href.split('?');
  if (queryString.length > 1) {
    const queryList = queryString[queryString.length-1].split('&');
    queryList.map(item => {
      const queryItem = item.split('=');
      if (queryItem.length = 2){
        result[queryItem[0]] = queryItem[1];
      }
    })
  }     
  return result[param];
}

// 工号处理，兼容 001234，WB1234这种工号
export function removeWorkidPrefixZero(workid: string) {
  return String(+workid || workid); // 兼容 001234，WB1234这种工号
}

export async function sleepFunc(delay = 10000) {
  return new Promise((res, rej) => {
    setTimeout(() => {
      res(true);
    }, delay);
  });
} 