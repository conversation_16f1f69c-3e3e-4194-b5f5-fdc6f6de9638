/**
 * 生成小程序访问链接
 */

/**
 * 生成微信一体化小程序体验版链接
 * @param page 页面地址
 * @param query 页面参数
 * @returns 
 */
export function makeWechatExpLink(page: string, query?: string) {
  let link = 'https://open.weixin.qq.com/sns/getexpappinfo?appid=wx6a96c49f29850eb5&iswxtpa=1';

  if (!page) return link;
  link += `&path=${page}.html`

  if (query) link += `?${encodeURIComponent(query)}`

  return link
}

/**
 * 生成支付宝一体化小程序正式版链接
 * @param page 页面地址
 * @param pageQuery 页面参数
 * @param appQuery App参数
 * @returns 
 */
export function makeAlipayLink(page: string, pageQuery?: string, appQuery?: string) {
  let link = 'alipays://platformapi/startapp?appId=2018081461095002';

  if (page) {
    const pageParam = pageQuery ? `${page}?${pageQuery}` : page;
    link += `&page=${encodeURIComponent(pageParam)}`
  }

  if (appQuery) link += `&query=${encodeURIComponent(appQuery)}`;

  return link
}

/**
 * 生成淘宝轻应用正式版链接
 * @param page 页面地址
 * @param pageQuery 页面参数
 * @param appQuery App参数
 * @returns 
 */
export function makeTaobaoLink(page: string, pageQuery?: string, appQuery?: string) {
  let link = 'https://outfliggys.m.taobao.com/tb/jump?uniapp_id=1011089&uniapp_page=jump';

  // 拼接打开的页面和参数
  if (page) {
    const pageParam = pageQuery ? `${page}?${pageQuery}` : page;
    link += `&page=${encodeURIComponent(pageParam)}`;
  }

  // 拼接全局参数
  if (appQuery) link += `&query=${encodeURIComponent(appQuery)}`;

  return link;
}