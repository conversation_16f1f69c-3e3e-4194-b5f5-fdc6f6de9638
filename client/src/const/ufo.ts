export const UFO_ALIPAY_TASK_INFO = [
  {
    ufoTaskId: 151,
    bizLine: '度假',
    taskDetail: {
      configName: '支付宝首页-度假tab',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=151&autoConfigId=298',
      os: 'Android',
      configId: 151,
      ufoTaskRecordId: 173732,
    },
    taskType: '性能自动化',
    taskName: 'Android-支付宝首页-度假tab',
    id: 7,
  },
  {
    ufoTaskId: 152,
    bizLine: '公共',
    taskDetail: {
      configName: '支付宝首页-本地化',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=152&autoConfigId=298',
      os: 'Android',
      configId: 152,
      ufoTaskRecordId: 173733,
    },
    taskType: '性能自动化',
    taskName: 'Android-支付宝首页-本地化',
    id: 8,
  },
  {
    ufoTaskId: 153,
    bizLine: '度假',
    taskDetail: {
      configName: '支付宝首页-度假tab',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=153&autoConfigId=300',
      os: 'iOS',
      configId: 153,
      ufoTaskRecordId: 173734,
    },
    taskType: '性能自动化',
    taskName: 'iOS-支付宝首页-度假tab',
    id: 9,
  },
  {
    ufoTaskId: 154,
    bizLine: '公共',
    taskDetail: {
      configName: '支付宝首页-本地化',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=154&autoConfigId=300',
      os: 'iOS',
      configId: 154,
      ufoTaskRecordId: 173736,
    },
    taskType: '性能自动化',
    taskName: 'iOS-支付宝首页-本地化',
    id: 10,
  },
  {
    ufoTaskId: 279,
    bizLine: '行程',
    taskDetail: {
      configName: '支付宝-行程tab',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=279&autoConfigId=298',
      os: 'Android',
      configId: 279,
      ufoTaskRecordId: 173737,
    },

    taskType: '性能自动化',

    taskName: 'Android-支付宝-行程tab',
    id: 11,
  },
  {
    ufoTaskId: 281,
    bizLine: '行程',
    taskDetail: {
      configName: '支付宝-行程tab',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=281&autoConfigId=300',
      os: 'iOS',
      configId: 281,
      ufoTaskRecordId: 173738,
    },
    taskType: '性能自动化',
    taskName: 'iOS-支付宝-行程tab',
    id: 12,
  },
  {
    ufoTaskId: 339,
    bizLine: '酒店',
    taskDetail: {
      configName: '支付宝酒店首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=339&autoConfigId=298',
      os: 'Android',
      configId: 339,
      ufoTaskRecordId: 173739,
    },

    taskType: '性能自动化',

    taskName: 'Android-支付宝酒店首页',
    id: 13,
  },
  {
    ufoTaskId: 340,
    bizLine: '酒店',
    taskDetail: {
      configName: '支付宝酒店列表页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=340&autoConfigId=298',
      os: 'Android',
      configId: 340,
      ufoTaskRecordId: 173740,
    },

    taskType: '性能自动化',

    taskName: 'Android-支付宝酒店列表页',
    id: 14,
  },
  {
    ufoTaskId: 347,
    bizLine: '酒店',
    taskDetail: {
      configName: '支付宝酒店首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=347&autoConfigId=300',
      os: 'iOS',
      configId: 347,
      ufoTaskRecordId: 173741,
    },
    taskType: '性能自动化',
    taskName: 'iOS-支付宝酒店首页',
    id: 15,
  },
  {
    ufoTaskId: 348,
    bizLine: '酒店',
    taskDetail: {
      configName: '支付宝酒店列表页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=348&autoConfigId=300',
      os: 'iOS',
      configId: 348,
      ufoTaskRecordId: 173742,
    },

    taskType: '性能自动化',

    taskName: 'iOS-支付宝酒店列表页',
    id: 16,
  },
  {
    ufoTaskId: 349,
    bizLine: '酒店',
    taskDetail: {
      configName: '支付宝酒店详情页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=349&autoConfigId=300',
      os: 'iOS',
      configId: 349,
      ufoTaskRecordId: 173743,
    },

    taskType: '性能自动化',

    taskName: 'iOS-支付宝酒店详情页',
    id: 17,
  },
  {
    ufoTaskId: 355,
    bizLine: '机票',
    taskDetail: {
      configName: '支付宝机票首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=355&autoConfigId=298',
      os: 'Android',
      configId: 355,
      ufoTaskRecordId: 173744,
    },

    taskType: '性能自动化',

    taskName: 'Android-支付宝机票首页',
    id: 18,
  },
  {
    ufoTaskId: 357,
    bizLine: '机票',
    taskDetail: {
      configName: '支付宝机票OTA',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=357&autoConfigId=298',
      os: 'Android',
      configId: 357,
      ufoTaskRecordId: 173745,
    },

    taskType: '性能自动化',

    taskName: 'Android-支付宝机票OTA',
    id: 19,
  },
  {
    ufoTaskId: 363,
    bizLine: '机票',
    taskDetail: {
      configName: '支付宝机票首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=363&autoConfigId=300',
      os: 'iOS',
      configId: 363,
      ufoTaskRecordId: 173746,
    },

    taskType: '性能自动化',

    taskName: 'iOS-支付宝机票首页',
    id: 20,
  },
  {
    ufoTaskId: 365,
    bizLine: '机票',
    taskDetail: {
      configName: '支付宝机票OTA',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=365&autoConfigId=300',
      os: 'iOS',
      configId: 365,
      ufoTaskRecordId: 173747,
    },

    taskType: '性能自动化',

    taskName: 'iOS-支付宝机票OTA',
    id: 21,
  },
  {
    ufoTaskId: 371,
    bizLine: '火车票',
    taskDetail: {
      configName: '支付宝火车票首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=371&autoConfigId=298',
      os: 'Android',
      configId: 371,
      ufoTaskRecordId: 173748,
    },

    taskType: '性能自动化',

    taskName: 'Android-支付宝火车票首页',
    id: 22,
  },
  {
    ufoTaskId: 372,
    bizLine: '火车票',
    taskDetail: {
      configName: '支付宝火车票列表',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=372&autoConfigId=298',
      os: 'Android',
      configId: 372,
      ufoTaskRecordId: 173749,
    },

    taskType: '性能自动化',

    taskName: 'Android-支付宝火车票列表',
    id: 23,
  },
  {
    ufoTaskId: 379,
    bizLine: '火车票',
    taskDetail: {
      configName: '支付宝火车票首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=379&autoConfigId=300',
      os: 'iOS',
      configId: 379,
      ufoTaskRecordId: 173750,
    },

    taskType: '性能自动化',

    taskName: 'iOS-支付宝火车票首页',
    id: 24,
  },
  {
    ufoTaskId: 380,
    bizLine: '火车票',
    taskDetail: {
      configName: '支付宝火车票列表',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=380&autoConfigId=300',
      os: 'iOS',
      configId: 380,
      ufoTaskRecordId: 173751,
    },

    taskType: '性能自动化',

    taskName: 'iOS-支付宝火车票列表',
    id: 25,
  },
  {
    ufoTaskId: 384,
    bizLine: '汽车票',
    taskDetail: {
      configName: '支付宝_汽车票_首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=384&autoConfigId=300',
      os: 'iOS',
      configId: 384,
      ufoTaskRecordId: 173752,
    },

    taskType: '性能自动化',

    taskName: 'iOS-支付宝_汽车票_首页',
    id: 26,
  },
  {
    ufoTaskId: 397,
    bizLine: '汽车票',
    taskDetail: {
      configName: '支付宝_汽车票_首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=397&autoConfigId=298',
      os: 'Android',
      configId: 397,
      ufoTaskRecordId: 173753,
    },
    taskType: '性能自动化',
    taskName: 'Android-支付宝_汽车票_首页',
    id: 27,
  },
];

export const UFO_WX_TASK_INFO = [
  {
    ufoTaskId: 160,
    bizLine: '公共',
    taskDetail: {
      configName: '微信小程序首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=160&autoConfigId=298',
      os: 'Android',
      configId: 160,
      ufoTaskRecordId: 173770,
    },
    taskType: '性能自动化',
    taskName: 'Android-微信小程序首页',
    id: 3,
  },
  {
    ufoTaskId: 161,
    bizLine: '公共',
    taskDetail: {
      configName: '微信小程序首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=161&autoConfigId=300',
      os: 'iOS',
      configId: 161,
      ufoTaskRecordId: 173771,
    },

    taskType: '性能自动化',

    taskName: 'iOS-微信小程序首页',
    id: 4,
  },
  {
    ufoTaskId: 323,
    bizLine: '度假',
    taskDetail: {
      configName: '微信度假POI详情',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=323&autoConfigId=298',
      os: 'Android',
      configId: 323,
      ufoTaskRecordId: 173772,
    },

    taskType: '性能自动化',

    taskName: 'Android-微信度假POI详情',
    id: 5,
  },
  {
    ufoTaskId: 331,
    bizLine: '度假',
    taskDetail: {
      configName: '微信度假POI详情',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=331&autoConfigId=300',
      os: 'iOS',
      configId: 331,
      ufoTaskRecordId: 173774,
    },

    taskType: '性能自动化',

    taskName: 'iOS-微信度假POI详情',
    id: 7,
  },
  {
    ufoTaskId: 336,
    bizLine: '酒店',
    taskDetail: {
      configName: '微信酒店列表页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=336&autoConfigId=298',
      os: 'Android',
      configId: 336,
      ufoTaskRecordId: 173775,
    },

    taskType: '性能自动化',

    taskName: 'Android-微信酒店列表页',
    id: 8,
  },
  {
    ufoTaskId: 337,
    bizLine: '酒店',
    taskDetail: {
      configName: '微信酒店详情页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=337&autoConfigId=298',
      os: 'Android',
      configId: 337,
      ufoTaskRecordId: 173776,
    },
    taskType: '性能自动化',
    taskName: 'Android-微信酒店详情页',
    id: 9,
  },
  {
    ufoTaskId: 344,
    bizLine: '酒店',
    taskDetail: {
      configName: '微信酒店列表页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=344&autoConfigId=300',
      os: 'iOS',
      configId: 344,
      ufoTaskRecordId: 173777,
    },
    taskType: '性能自动化',
    taskName: 'iOS-微信酒店列表页',
    id: 10,
  },
  {
    ufoTaskId: 345,
    bizLine: '酒店',
    taskDetail: {
      configName: '微信酒店详情页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=345&autoConfigId=300',
      os: 'iOS',
      configId: 345,
      ufoTaskRecordId: 173778,
    },

    taskType: '性能自动化',

    taskName: 'iOS-微信酒店详情页',
    id: 11,
  },
  {
    ufoTaskId: 351,
    bizLine: '机票',
    taskDetail: {
      configName: '微信机票首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=351&autoConfigId=298',
      os: 'Android',
      configId: 351,
      ufoTaskRecordId: 173779,
    },

    taskType: '性能自动化',

    taskName: 'Android-微信机票首页',
    id: 12,
  },
  {
    ufoTaskId: 352,
    bizLine: '机票',
    taskDetail: {
      configName: '微信机票列表',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=352&autoConfigId=298',
      os: 'Android',
      configId: 352,
      ufoTaskRecordId: 173780,
    },

    taskType: '性能自动化',

    taskName: 'Android-微信机票列表',
    id: 13,
  },
  {
    ufoTaskId: 353,
    bizLine: '机票',
    taskDetail: {
      configName: '微信机票OTA',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=353&autoConfigId=298',
      os: 'Android',
      configId: 353,
      ufoTaskRecordId: 173781,
    },

    taskType: '性能自动化',

    taskName: 'Android-微信机票OTA',
    id: 14,
  },
  {
    ufoTaskId: 359,
    bizLine: '机票',
    taskDetail: {
      configName: '微信机票首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=359&autoConfigId=300',
      os: 'iOS',
      configId: 359,
      ufoTaskRecordId: 173782,
    },

    taskType: '性能自动化',

    taskName: 'iOS-微信机票首页',
    id: 15,
  },
  {
    ufoTaskId: 360,
    bizLine: '机票',
    taskDetail: {
      configName: '微信机票列表',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=360&autoConfigId=300',
      os: 'iOS',
      configId: 360,
      ufoTaskRecordId: 173783,
    },

    taskType: '性能自动化',

    taskName: 'iOS-微信机票列表',
    id: 16,
  },
  {
    ufoTaskId: 361,
    bizLine: '机票',
    taskDetail: {
      configName: '微信机票OTA',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=361&autoConfigId=300',
      os: 'iOS',
      configId: 361,
      ufoTaskRecordId: 173784,
    },

    taskType: '性能自动化',

    taskName: 'iOS-微信机票OTA',
    id: 17,
  },
  {
    ufoTaskId: 367,
    bizLine: '火车票',
    taskDetail: {
      configName: '微信火车票首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=367&autoConfigId=298',
      os: 'Android',
      configId: 367,
      ufoTaskRecordId: 173785,
    },

    taskType: '性能自动化',

    taskName: 'Android-微信火车票首页',
    id: 18,
  },
  {
    ufoTaskId: 368,
    bizLine: '火车票',
    taskDetail: {
      configName: '微信火车票列表',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=368&autoConfigId=298',
      os: 'Android',
      configId: 368,
      ufoTaskRecordId: 173786,
    },

    taskType: '性能自动化',

    taskName: 'Android-微信火车票列表',
    id: 19,
  },
  {
    ufoTaskId: 369,
    bizLine: '火车票',
    taskDetail: {
      configName: '微信火车票OTA',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=369&autoConfigId=298',
      os: 'Android',
      configId: 369,
      ufoTaskRecordId: 173787,
    },

    taskType: '性能自动化',

    taskName: 'Android-微信火车票OTA',
    id: 20,
  },
  {
    ufoTaskId: 370,
    bizLine: '火车票',
    taskDetail: {
      configName: '微信火车票下单',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=370&autoConfigId=298',
      os: 'Android',
      configId: 370,
      ufoTaskRecordId: 173788,
    },

    taskType: '性能自动化',

    taskName: 'Android-微信火车票下单',
    id: 21,
  },
  {
    ufoTaskId: 375,
    bizLine: '火车票',
    taskDetail: {
      configName: '微信火车票首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=375&autoConfigId=300',
      os: 'iOS',
      configId: 375,
      ufoTaskRecordId: 173789,
    },

    taskType: '性能自动化',

    taskName: 'iOS-微信火车票首页',
    id: 22,
  },
  {
    ufoTaskId: 376,
    bizLine: '火车票',
    taskDetail: {
      configName: '微信火车票列表',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=376&autoConfigId=300',
      os: 'iOS',
      configId: 376,
      ufoTaskRecordId: 173790,
    },

    taskType: '性能自动化',

    taskName: 'iOS-微信火车票列表',
    id: 23,
  },
  {
    ufoTaskId: 377,
    bizLine: '火车票',
    taskDetail: {
      configName: '微信火车票OTA',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=377&autoConfigId=300',
      os: 'iOS',
      configId: 377,
      ufoTaskRecordId: 173791,
    },

    taskType: '性能自动化',

    taskName: 'iOS-微信火车票OTA',
    id: 24,
  },
  {
    ufoTaskId: 378,
    bizLine: '火车票',
    taskDetail: {
      configName: '微信火车票下单',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=378&autoConfigId=300',
      os: 'iOS',
      configId: 378,
      ufoTaskRecordId: 173792,
    },

    taskType: '性能自动化',

    taskName: 'iOS-IOS微信火车票下单',
    id: 25,
  },
  {
    ufoTaskId: 385,
    bizLine: '汽车票',
    taskDetail: {
      configName: '微信汽车票首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=385&autoConfigId=300',
      os: 'iOS',
      configId: 385,
      ufoTaskRecordId: 173793,
    },

    taskType: '性能自动化',

    taskName: 'iOS-微信汽车票首页',
    id: 26,
  },
  {
    ufoTaskId: 407,
    bizLine: '汽车票',
    taskDetail: {
      configName: '微信汽车票首页',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=407&autoConfigId=298',
      os: 'Android',
      configId: 407,
      ufoTaskRecordId: 173794,
    },

    taskType: '性能自动化',

    taskName: 'Android-微信汽车票首页',
    id: 27,
  },
  {
    ufoTaskId: 424,
    bizLine: '度假',
    taskDetail: {
      configName: '度假首页列表',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=424&autoConfigId=298',
      os: 'Android',
      configId: 424,
      ufoTaskRecordId: 173795,
    },

    taskType: '性能自动化',

    taskName: 'Android-度假首页列表',
    id: 28,
  },
  {
    ufoTaskId: 425,
    bizLine: '度假',
    taskDetail: {
      configName: '度假首页列表',
      configUrl: 'https://ufo2.alitrip.com/autoPerformance/jobList?mirrorAlgorithmId=425&autoConfigId=300',
      os: 'iOS',
      configId: 425,
      ufoTaskRecordId: 173796,
    },
    taskType: '性能自动化',
    taskName: 'iOS-度假首页列表',
    id: 29,
  },
];
