export enum BIZ_LINE {
  Common = 'common',
  Hotel = 'hotel',
  Vacation = 'vacation',
  Bus = 'bus',
  Ship = 'ship',
  Flight = 'flight',
  Train = 'train',
  Vehicle = 'vehicle',
  User = 'user',
  Growth = 'growth',
  Member = 'member',
  Shop = 'shop',
}

// 业务名称
export const BIZ_TITLE = {
  [BIZ_LINE.Common]: '公共',
  [BIZ_LINE.Hotel]: '酒店',
  [BIZ_LINE.Vacation]: '度假',
  [BIZ_LINE.Bus]: '汽车票',
  [BIZ_LINE.Ship]: '船票',
  [BIZ_LINE.Flight]: '机票',
  [BIZ_LINE.Train]: '火车票',
  [BIZ_LINE.Vehicle]: '用车',
  [BIZ_LINE.User]: '用户',
  [BIZ_LINE.Growth]: '用增',
  [BIZ_LINE.Member]: '会员',
  [BIZ_LINE.Shop]: '店铺',
}

// 业务名称
export const BIZ_TITLE_COLOR = {
  [BIZ_LINE.Common]: 'magenta',
  [BIZ_LINE.Hotel]: 'cyan',
  [BIZ_LINE.Vacation]: 'blue',
  [BIZ_LINE.Bus]: 'gold',
  [BIZ_LINE.Ship]: '#108ee9',
  [BIZ_LINE.Flight]: 'volcano',
  [BIZ_LINE.Train]: 'orange',
  [BIZ_LINE.Vehicle]: 'geekblue',
  [BIZ_LINE.User]: 'purple',
  [BIZ_LINE.Growth]: 'green',
  [BIZ_LINE.Member]: 'red',
  [BIZ_LINE.Shop]: 'lime',
}

// 团队维度包大小限制
export const GROUP_SIZE = [
  {
    group: 'platform',
    name: '平台',
    size: 2 * 1024 * 1024,
    bizLines: [BIZ_LINE.Member, BIZ_LINE.Growth, BIZ_LINE.User,],
  },
  {
    group: 'common',
    name: '公共',
    size: 4 * 1024 * 1024,
    bizLines: [BIZ_LINE.Common,],
  },
  {
    group: 'hotel',
    name: '酒店',
    size: 4 * 1024 * 1024,
    bizLines: [BIZ_LINE.Hotel, BIZ_LINE.Shop,],
  },
  {
    group: 'traffic',
    name: '交通',
    size: 6 * 1024 * 1024,
    bizLines: [BIZ_LINE.Flight, BIZ_LINE.Train, BIZ_LINE.Bus, BIZ_LINE.Vehicle,],
  },
  {
    group: 'vacation',
    name: '度假',
    size: 4 * 1024 * 1024,
    bizLines: [BIZ_LINE.Vacation,],
  },
];

export const BIZ_LINE_OPTIONS = Object.keys(BIZ_TITLE).map((key) => ({
  value: key,
  // @ts-ignore
  label: BIZ_TITLE[key]
}));