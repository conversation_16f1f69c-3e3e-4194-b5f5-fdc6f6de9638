export enum EPubTypeEnv {
  /** 日常版本 */
  Beta = 1,
  /** 生产版本 */
  Prod = 2,
  /** 小程序 */ 
  Mini = 3
}

export const DEF_ENV_TYPE_WORDING = {
  [EPubTypeEnv.Beta]: '预发',
  [EPubTypeEnv.Prod]: '线上',
}

export enum EDefBuildStatus {
  /** 未构建 */
  NOT_START = 1,
  /** 构建中 */
  BUILDING = 2,
  /** 构建成功 */
  BUILD_SUCCESS = 3,
  /** 构建失败 */
  BUILD_ERROR = 4,
}

export const DEF_BUILD_STATUS_WORDING = {
  [EDefBuildStatus.NOT_START]: '未构建',
  [EDefBuildStatus.BUILDING]: '构建中',
  [EDefBuildStatus.BUILD_SUCCESS]: '构建成功',
  [EDefBuildStatus.BUILD_ERROR]: '构建失败',
}

export enum EDefBuildTaskType {
  FREE_DEV_BRANCH = 'free-dev-branch',
  DEV_BRANCH = 'dev-branch',
  ITERATION = 'iteration',
}

export enum ECR_STATUS {
  OPENED = 'opened', // 首次打开
  RE_OPENED = 'reopened', // 有变更，重新打开
  ACCEPTED = 'accepted', // 已通过
  MERGED = 'merged', // 已合并
}

export const CR_STATUS_WORDING = {
  [ECR_STATUS.OPENED]: '待通过',
  [ECR_STATUS.RE_OPENED]: '重新打开，待通过',
  [ECR_STATUS.ACCEPTED]: '已通过',
  [ECR_STATUS.MERGED]: '已合并',
}
