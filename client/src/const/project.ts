export enum EProjectType {
  /** 支系小程序 */
  ALIPAY = 'alipay',
  /** 淘系轻应用 */
  TAOBAO = 'taobao',
  /** 字节小程序 */
  BYTEDANCE = 'bytedance',
  /** 微信小程序 */
  WEIXIN = 'weixin',
  /** 组件 */
  COMPONENT = 'component',
  /** 其他类型 */
  OTHER = 'other'
}

export const PROJECT_TYPE_CN_NAME = {
  [EProjectType.ALIPAY]: '支系小程序',
  [EProjectType.TAOBAO]: '淘系轻应用',
  [EProjectType.BYTEDANCE]: '字节小程序',
  [EProjectType.WEIXIN]: '微信小程序',
  [EProjectType.COMPONENT]: '组件',
  [EProjectType.OTHER]: '其他'
}