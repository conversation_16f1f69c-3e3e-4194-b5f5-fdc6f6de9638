import { EProjectType } from "@/const/project";
import { IIterStep } from '@/interface/iter-branch';

/** 投放端 */
export enum EClient {
  /** 支付宝 */
  ALIPAY = 'alipay',
  /** 淘宝 */
  TAOBAO = 'taobao',
  /** 微信 */
  WEIXIN = 'weixin',
  /** 抖音 */
  DOUYIN = 'douyin',
  /** 今日头条 */
  TOUTIAO = 'toutiao',
  /** 抖音lite */
  DOUYIN_LITE = 'douyin_lite',
  /** 今日头条lite */
  TT_LITE = 'tt_lite',
}

/** 投放端中文名称 */
export const CLIENT_CN_NAME = {
  [EClient.ALIPAY]: '支付宝',
  [EClient.TAOBAO]: '淘宝',
  [EClient.WEIXIN]: '微信',
  [EClient.DOUYIN]: '抖音',
  [EClient.TOUTIAO]: '今日头条',
  [EClient.DOUYIN_LITE]: '抖音lite',
  [EClient.TT_LITE]: '今日头条lite',
}

/** 小程序上传状态 */
export enum EMiniAppUploadStatus {
  /** 上传中 */
  UPLOADING = 'UPLOADING',
  /** 上传成功 */
  SUCCESS = 'SUCCESS',
  /** 上传失败 */
  FAILED = 'FAILED',
}

/** 小程序版本状态 */
export enum EMiniAppVersionStatus {
  /** 开发中 */
  INIT = 'INIT',
  /** 审核中 */
  AUDITING = 'AUDITING',
  /** 审核通过 */
  WAIT_RELEASE = 'WAIT_RELEASE',
  /** 审核驳回 */
  AUDIT_REJECT = 'AUDIT_REJECT',
  /** 灰度中 */
  GRAY = 'GRAY',
  /** 已上架 */
  RELEASE = 'RELEASE',
  /** 下架 */
  OFFLINE = 'OFFLINE'
}

/** 小程序投放步骤 */
export const DELIVER_STEPS = [
  {
    matchProcessStatus: [EMiniAppVersionStatus.AUDITING],
    matchFinishStatus: [EMiniAppVersionStatus.WAIT_RELEASE],
    matchErrorStatus: [EMiniAppVersionStatus.AUDIT_REJECT],
    title: '审核',
    description: {
      [EMiniAppVersionStatus.WAIT_RELEASE]: '审核通过',
      [EMiniAppVersionStatus.AUDIT_REJECT]: '审核驳回'
    }
  },
  {
    matchProcessStatus: [EMiniAppVersionStatus.GRAY],
    title: '灰度'
  },
  {
    statusType: 'versionStatus',
    matchFinishStatus: [EMiniAppVersionStatus.RELEASE, EMiniAppVersionStatus.OFFLINE],
    title: '上架',
    description: {
      [EMiniAppVersionStatus.OFFLINE]: '上架后下架',
    }
  }
] as IIterStep[];

export function getDeliverSteps(projectType: EProjectType) {
  // 字节小程序不支持灰度
  if (projectType === EProjectType.BYTEDANCE) {
    return DELIVER_STEPS.filter(item => item.title !== '灰度');
  }
  return DELIVER_STEPS;
}
