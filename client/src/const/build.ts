import { EProjectType } from "@/const/project";
import { IProject } from "@/interface/project";

/**
 * 打码平台
 */
export enum EBuildPlatform {
  /** 支付宝 */
  ALIPAY = 'alipay',
  /** 淘宝 */
  TAOBAO = 'taobao',
  /** 微信 */
  WECHAT = 'wechat',
  /** 抖音 */
  DOUYIN = 'douyin'
}

/** 
 * 打码平台中文名称
 **/
export const BUILD_PLAtFORM_CN_NAME = {
  [EBuildPlatform.ALIPAY]: '支付宝',
  [EBuildPlatform.TAOBAO]: '淘宝',
  [EBuildPlatform.WECHAT]: '微信',
  [EBuildPlatform.DOUYIN]: '抖音',
}

// 是否支持打码
export function isSupportBuild(project: IProject) {
  return !!project.wsyConfig;
}