import { EPubTypeEnv, EDefBuildStatus } from '@/const/def';
import { EProjectType } from '@/const/project';
import { Tag } from 'antd';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
} from '@ant-design/icons';

/** 投放类型 */
export enum EDeliverType {
  H5 = 1,
  WEIXIN = 2,
  ALIPAY = 3,
  BYTEDANCE = 4,
}

/** 小程序投放状态 */
export enum EMinaStatus {
  INIT = 1, // 初始化
  BETA = 2, // 预发投放
  PROD = 3, // 线上投放
}

/** 小程序投放状态文案 */
export const getMinaStatusWording = (status?: EMinaStatus) => {
  const envName = status === EMinaStatus.BETA ? '预发' : '线上' ;
  switch(status) {
    case EMinaStatus.INIT: {
      return <Tag color="processing">已初始化</Tag>;
    }
    case EMinaStatus.BETA:
    case EMinaStatus.PROD: {
      return <Tag icon={<CheckCircleOutlined />} color="success">{`${envName}投放成功`}</Tag>;
    }
    default: {
      return <Tag icon={<ClockCircleOutlined />} color="default">未投放</Tag>;
    }
  }
}

/** 根据配置信息的name值，获得项目类型，项目名，投放类型 */
export const getDeliverTypeInfo = (name) => {
  switch(name) {
    case 'fliggy-weixin': {
      return {
        projectType: EProjectType.WEIXIN,
        projectName: 'fliggy-weixin',
        deliverType: EDeliverType.WEIXIN
      }
    }
    case 'fliggy-allinone': {
      return {
        projectType: EProjectType.ALIPAY,
        projectName: 'fliggy-allinone',
        deliverType: EDeliverType.ALIPAY
      }
    }
    case 'fliggy-bytedance': {
      return {
        projectType: EProjectType.BYTEDANCE,
        projectName: 'fliggy-bytedance',
        deliverType: EDeliverType.BYTEDANCE
      }
    }
    default: {
      return {
        projectType: EProjectType.COMPONENT,
        projectName: 'h5',
        deliverType: EDeliverType.H5
      }
    }
  }
}

/** 投放分支类型 */
export enum EDeliverBranchType {
  FREE_DEV_BRANCH = 1,
  DEV_BRANCH = 2,
  ITERATION = 3,
}

export enum EDeliverStatus {
  /** 废弃 */
  ABANDON = -1,
  /** 待初始化 */
  NOT_INITIAL = 0,
  /** 未投放 */
  INITIAL = 1,
  /** 预发投放中 */
  PREPUBING = 2,
  /** 预发投放成功 */
  PREPUB_SUCCESS = 3,
  /** 预发投放失败 */
  PREPUB_FAIL = 4,
  /** 线上投放中 */
  PUBLISHING = 5,
  /** 线上投放成功 */
  PUBLISH_SUCCESS = 6,
  /** 线上投放失败 */
  PUBLISH_FAIL = 7,
}

export const DELIVER_STATUS_WORDING = {
  [EDeliverStatus.ABANDON]: '废弃',
  [EDeliverStatus.NOT_INITIAL]: '待初始化',
  [EDeliverStatus.INITIAL]: '未投放',
  [EDeliverStatus.PREPUBING]: '预发投放中',
  [EDeliverStatus.PREPUB_SUCCESS]: '预发投放成功',
  [EDeliverStatus.PREPUB_FAIL]: '预发投放失败',
  [EDeliverStatus.PUBLISHING]: '线上投放中',
  [EDeliverStatus.PUBLISH_SUCCESS]: '线上投放成功',
  [EDeliverStatus.PUBLISH_FAIL]: '线上投放失败',
}

export const getDeliverStatusWording = ({ defEnvType, defBuildStatus, defIterId }: {
  defEnvType: EPubTypeEnv;
  defBuildStatus: EDefBuildStatus;
  defIterId: number;
}) => {
  const envName = defEnvType === EPubTypeEnv.Beta ? '预发' : '线上' ;
  switch(defBuildStatus) {
    case EDefBuildStatus.BUILDING: {
      return <Tag icon={<SyncOutlined spin />} color="processing">{`${envName}投放中`}</Tag>;
    }
    case EDefBuildStatus.BUILD_ERROR: {
      return <Tag icon={<CloseCircleOutlined />} color="error">{`${envName}投放失败`}</Tag>;
    }
    case EDefBuildStatus.BUILD_SUCCESS: {
      return <Tag icon={<CheckCircleOutlined />} color="success">{`${envName}投放成功`}</Tag>;
    }
    default: {
      return <Tag icon={<ClockCircleOutlined />} color="default">{defIterId ? '待投放' : '待初始化'}</Tag>;
    }
  }
}

// 投放按钮的可操作类型
export enum EDeliverActionType {
  DELIVER = 'deliver', // 查看 + 投放
  VIEW = 'view', // 仅查看
}
