import { IIterStep } from '@/interface/iter-branch';

export enum EBranchType {
  /** 迭代类型：项目类型 */
  APP = 'app',
  /** 迭代类型：组件类型 */
  COMPONENT = 'component',
}

export enum IterStatus {
  /** 废弃 */
  ABANDON = -1,
  /** 计划发布 */
  PLAN = 0,
  /** 集成回归 */
  MERGE = 1,
  /**
   * @deprecated 新链路中，该状态废弃
   * 审核中
   **/
  AUDITING = 4,
  /**
   * @deprecated 新链路中，该状态废弃
   * 灰度中
   **/
  GRAY = 2,
  /** 投放中 */
  DELIVERING = 5,
  /** 已发布 */
  PUBLISHED = 3
}

export const ITER_STATUS_TEXT = {
  [IterStatus.ABANDON]: '废弃',
  [IterStatus.PLAN]: '计划发布',
  [IterStatus.MERGE]: '集成回归',
  [IterStatus.AUDITING]: '审核中',
  [IterStatus.GRAY]: '灰度中',
  [IterStatus.DELIVERING]: '投放中',
  [IterStatus.PUBLISHED]: '已发布',
}

// 老链路
export const OLD_ITER_STEPS = [
  {
    matchProcessStatus: [IterStatus.PLAN],
    matchErrorStatus: [IterStatus.ABANDON],
    title: '计划发布',
    description: {
      [IterStatus.ABANDON]: '已废弃'
    }
  },
  {
    matchProcessStatus: [IterStatus.MERGE],
    title: '集成回归',
  },
  {
    matchProcessStatus: [IterStatus.AUDITING],
    title: '审核中'
  },
  {
    matchProcessStatus: [IterStatus.GRAY],
    title: '灰度中'
  },
  {
    matchFinishStatus: [IterStatus.PUBLISHED],
    title: '已发布'
  }
] as IIterStep[];

// 新链路
export const ITER_STEPS = [
  {
    matchProcessStatus: [IterStatus.PLAN],
    matchErrorStatus: [IterStatus.ABANDON],
    title: '计划发布',
    description: {
      [IterStatus.ABANDON]: '已废弃'
    }
  },
  {
    matchProcessStatus: [IterStatus.MERGE],
    title: '集成回归',
  },
  {
    matchProcessStatus: [IterStatus.DELIVERING],
    title: '开始投放',
  },
  {
    matchFinishStatus: [IterStatus.PUBLISHED],
    title: '已发布'
  }
] as IIterStep[];

// 项目是模块类型时的发布链路
export const MODULE_ITER_STEPS = [
  {
    matchProcessStatus: [IterStatus.PLAN],
    matchErrorStatus: [IterStatus.ABANDON],
    title: '计划发布',
    description: {
      [IterStatus.ABANDON]: '已废弃'
    }
  },
  {
    matchProcessStatus: [IterStatus.MERGE],
    title: '集成回归',
  },
  {
    matchProcessStatus: [IterStatus.PUBLISHED],
    title: '发布'
  },
  {
    matchProcessStatus: [IterStatus.DELIVERING],
    title: '行业投放'
  },
] as IIterStep[];
