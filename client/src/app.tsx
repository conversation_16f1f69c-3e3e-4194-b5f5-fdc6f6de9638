import { RunTimeLayoutConfig } from 'umi';
import Header from '@/layouts/Header';
import Footer from '@/layouts/Footer';
import '@/utils/aes';
import '@/layouts/index.less';

export { onRouteChange } from "@/utils/aes";

export const layout: RunTimeLayoutConfig = (_initialState) => {
  // 配置详见：https://procomponents.ant.design/components/layout#prolayout
  return {
    title: '一体化研发平台',
    logo: 'https://gw.alicdn.com/imgextra/i2/O1CN01X77U3s1DKj85ttaxW_!!6000000000198-2-tps-200-200.png',
    layout: 'mix',
    rightContentRender: Header,
    footerRender: Footer,
    siderWidth: 208,
    contentStyle: {
      padding: '24px'
    }
  };
};

export async function getInitialState() {
  return {
    currentUser: window.currentUser
  };
} 