import { Avatar, Spin, Space } from 'antd';
import { useModel } from 'umi';

const UserAvatar = () => {
  const currentUser = useModel('user');

  return currentUser.userid ? (
    <Space style={{ marginRight: '12px' }}>
      <Avatar size="small" src={currentUser.avatarUrl} alt="avatar" />
      <span>{currentUser.name}</span>
    </Space>
  ) : (
    <span style={{ marginRight: '12px' }}>
      <Spin size="small" />
    </span>
  );
};

export default UserAvatar;
