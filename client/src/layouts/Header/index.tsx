import { Space, Divider } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import Avatar from './Avatar';

const Header = () => {
  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Space>
        <a href="https://yuque.antfin.com/docs/share/ba09b6d8-a314-4dc6-bd48-d97ca698e073" target="_blank">
          <QuestionCircleOutlined style={{ fontSize: '16px', verticalAlign: 'text-bottom', marginRight: '4px' }} />
          帮助手册
        </a>
        <Divider type="vertical" />
        <Avatar />
      </Space>
    </div>
  );
};

export default Header;
