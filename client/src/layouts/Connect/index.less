.global-connect {
  position: fixed;
  right: 0;
  bottom: 20px;
  width: 60px;
  height: 130px;
  z-index: 99;

  &:hover {
    animation: none;

    .hand {
      transform: rotate(90deg) scaleY(-1) translateY(60px);
    }

    .body {
      transform: scaleX(-1) translateX(70px);
    }

    .tips {
      transform: translate(0)
    }
  }

  .hand {
    position: absolute;
    top: 20px;
    right: -10px;
    transition: transform 0.3s ease-out;
    transform: rotate(90deg) scaleY(-1);
    width: 60px;
    height: 60px;
    cursor: pointer;
  }

  .body {
    position: absolute;
    top: 0;
    right: -80px;
    transition: transform 0.3s ease-out;
    transform: scaleX(-1);
    width: 80px;
    height: 110px;
    cursor: pointer;
  }

  .tips {
    transition: transform 0.3s ease-out;
    transform: translate(220px);
    position: absolute;
    bottom: 0;
    right: 0;
    height: 20px;
    line-height: 20px;
    background-color: rgba(0, 0, 0, 0.65);
    padding: 0 8px;
    border-radius: 4px 0 0 4px;
    font-size: 13px;
    color: #fff;
    white-space: nowrap;
  }
}