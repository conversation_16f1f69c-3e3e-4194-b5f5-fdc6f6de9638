import { useState, useEffect } from 'react';
import projectApi from '@/api/project';
import { IProject } from '@/interface/project';

export default function () {
  const [projectList, setProjectList] = useState<IProject[]>([]);
  const refreshProjectList = () => {
    projectApi.list().then((projectListRes) => {
      setProjectList(projectListRes.data)
    });
  }
  
  useEffect(() => {
    refreshProjectList();
  }, []);

  return [projectList, refreshProjectList] as [IProject[], () => void]
}