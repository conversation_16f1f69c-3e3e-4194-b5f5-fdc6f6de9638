import { useState } from 'react';
import { useLocation } from 'umi';
import queryString from 'query-string';
import PackageStructure from '@/components/PackageStructure';
import ProjectSelect from '@/components/ProjectSelect';
import { IProject } from '@/interface/project';
import './index.less';

export default () => {
  const [project, setProject] = useState<IProject>();
  const location = useLocation();
  const { iterId, devId, freeDevId } = queryString.parse(location.search) as { iterId?: string; devId?: string; freeDevId?: string };
  const showProjectSelect = !iterId && !devId && !freeDevId; // 当没有指定迭代分支或开发分支时，展示项目选择

  function onProjectSelect(project: IProject) {
    setProject(project)
  }

  return (
    <div className="package-structure-container">
      {showProjectSelect && <ProjectSelect onSelect={onProjectSelect} style={{ margin: '18px 24px 18px' }} />}
      <PackageStructure project={project} iterId={iterId} devId={devId} freeDevId={freeDevId} />
    </div>
  );
}
