import { useState } from 'react';
import { useLocation } from 'umi';
import queryString from 'query-string';
import ProjectSelect from '@/components/ProjectSelect';
import AnalyzeDependencies from '@/components/AnalyzeDependencies';
import { IProject } from '@/interface/project';

import './index.less';

export default () => {
  const [project, setProject] = useState<IProject>();
  const location = useLocation();
  const { iterId, fromIterId } = queryString.parse(location.search) as { iterId?: string; fromIterId?: string; };
  const showProjectSelect = !iterId; // 当没有指定迭代分支时，展示项目选择

  function onProjectSelect(project: IProject) {
    setProject(project);
  }

  return (
    <div className="package-dependencies-container">
      {showProjectSelect && <ProjectSelect onSelect={onProjectSelect} style={{ margin: '18px 24px' }} />}

      {/* 依赖分析 */}
      <AnalyzeDependencies projectName={project?.name} iterId={iterId} fromIterId={fromIterId} />
    </div>
  );
}