import { useState } from 'react';
import { <PERSON>ert, Button, Space, Input, message } from 'antd';
import FormRender from '@/components/FR';
import { useForm } from 'form-render';
import FLianApi from '@/api/flian';
import schema from './schema';

import './index.less';

export default () => {
  const form = useForm();
  const [loading, setLoading] = useState(false);
  const [taoPassword, setTaoPassword] = useState<string | null>(null);

  const onFormFinish = async (formData: any, errors: any) => {
    setLoading(true);
    if (errors.length > 0) return;
    const { link, title, image } = formData;
    const res = await FLianApi.generateTbSharePassword({ link, title, image });
    setLoading(false);
    if (res?.success) {
      setTaoPassword(res?.data?.data);
    } else {
      setTaoPassword('遇到网络问题，请重新生成~');
    }
  }

  const onReset = () => {
    form.resetFields();
  };

  function copyLink() {
    navigator.clipboard.writeText(taoPassword || '')
      .then(() => {
        message.success('复制成功')
      }).catch((err) => {
        message.error(err?.message || '复制失败！')
      });
  }

  return (
    <div className="gen-taopassword-container">
      <Alert message="淘口令有效期为30天" type="info" />

      <div className="content">
        <h3>淘口令生成</h3>

        <div className="gen-taopassword-form">
          <FormRender
            form={form}
            schema={schema}
            onFinish={onFormFinish}
            footer={true}
          />
          <Space className="gen-taopassword-btns">
            <Button onClick={onReset}>重置表单</Button>
            <Button type="primary" loading={loading} onClick={form.submit}>生成淘口令</Button>
          </Space>
        </div>
        {
          taoPassword ? <Input.Group compact className="whole-link">
            <span className="label">淘口令:</span>
            <Input disabled={true} style={{ flex: 1 }} value={taoPassword || ''} />
            <Button type="primary" onClick={copyLink}>复制</Button>
          </Input.Group> : null
        }
      </div>
    </div>
  );
}