import { useEffect, useMemo, useState } from 'react';
import { Spin, Tabs, Row, Col, Tag, Result } from 'antd';
import { useLocation } from 'umi';
import queryString from 'query-string';
import homeSizeApi from '@/api/home-size';
import SizePie from './components/Pie';
import SizeColumn from './components/Column';
import DetailTable from './components/DetailTable';
import DifferTable from './components/DifferTable';
import { IChartsItem, IMainSizeDetail } from './types';
import { formatResData, getTableConfig } from './lib/utils'
import './index.less';

export default () => {
  const location = useLocation();
  const { iterId, dist } = queryString.parse(location.search) as { iterId?: string; dist?: string; };
  const [loading, setLoading] = useState(true);
  const [errMsg, setErrMsg] = useState('');
  const [pieList, setPieList] = useState<IChartsItem[]>([]);
  const [columnList, setColumnList] = useState<IChartsItem[]>([]);
  const [chainList, setChainList] = useState<string[]>([]);
  // 业务依赖明细
  const [bizNpmSummary, setBizNpmSummary] = useState(null);
  // 分包依赖明细
  const [pkgDepSummary, setPkgDepSummary] = useState(null);
  // 主包体积变更明细
  const [mainSizeDetail, setMainSizeDetail] = useState<IMainSizeDetail>({ list: [], bizSizeMap: {} });

  const fetchHomeSize = () => {
    homeSizeApi.analyzeHomeSize({ iterId, dist })
      .then(res => {
        // @ts-ignore
        const resData = res.data;
        if (!resData) return Promise.reject(res);

        const formatRes = formatResData(resData);

        setLoading(false);
        setPieList(formatRes.pieList);
        setColumnList(formatRes.columnList);
        setChainList(resData.chainList);
        setBizNpmSummary(resData.bizNpmSummary);
        setPkgDepSummary(resData.pkgDepSummary);
        setMainSizeDetail(resData.mainSizeDetail);
      })
      .catch(err => {
        setErrMsg(err?.errorMsg || '获取首页体积信息失败');
      });
  };

  const detailTables = useMemo(() => {
    return getTableConfig(
      { pkgDepSummary, bizNpmSummary, chainList },
      (color: string, name: string) => (<Tag color={color}>{name}</Tag>)
    );
  }, [pkgDepSummary, bizNpmSummary, chainList])

  useEffect(() => {
    fetchHomeSize();
  }, []);

  if (errMsg) {
    return <Result status={500} title="" subTitle={errMsg} />;
  }

  return (
    <Spin spinning={loading}>
      <Tabs defaultActiveKey="0">
        <Tabs.TabPane tab="首页体积分布" key="0">
          <Row gutter={[10, 10]}>
            {/* 饼图信息 */}
            {pieList.map((pieItem, index) => (
              <Col span={8} key={index}>
                <SizePie {...pieItem} />
              </Col>
            ))}
            {/* 柱状图 */}
            {columnList.map((columnItem, index) => (
              <Col span={8} key={index}>
                <SizeColumn {...columnItem} />
              </Col>
            ))}
          </Row>
        </Tabs.TabPane>

        {detailTables.map((item, index) => (
          <Tabs.TabPane tab={item.tabName} key={index + 1}>
            {item.data && (
              <DetailTable {...item} />
            )}  
          </Tabs.TabPane>
        ))}
        <Tabs.TabPane tab="主包体积变更明细" >
          <DifferTable data={mainSizeDetail} />
        </Tabs.TabPane>
      </Tabs>
    </Spin>
  );
};