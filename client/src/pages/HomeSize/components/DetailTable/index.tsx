import React, { useEffect, useMemo, useState } from 'react';
import { Radio, RadioChangeEvent, Table, Typography, Modal, message, Card } from 'antd';
import IconFont, { iconType } from '@/components/IconFont';
import { formatSize, getNpmDepChain } from '../../lib/utils';
import { ITableItem } from '../../types';
import './index.less';


interface IProps {
  data: Record<string, ITableItem[]>;
  initData: string;
  keyMap?: Record<string, string>;
  extraColumn?: Record<string, any>[];
  chainList: string[];
}

const { Link } = Typography;

const DEFAULT_COLUMNS = [
  {
    title: 'npm包',
    dataIndex: 'npmName',
  },
  {
    title: '体积（KB）',
    dataIndex: 'size',
    render: (_: any, { size }: ITableItem) => formatSize(size)
  },
];

function sortList(list: ITableItem[]) {
  return list.sort((a, b) => b.size - a.size);
}

export default function DetailTable({ data, initData, keyMap, extraColumn, chainList }: IProps) {
  const [biz, setBiz] = useState(initData);
  const [list, setList] = useState<ITableItem[]>([]);
  const [depChain, setDepChain] = useState<string[]>([]);
  const [modalTitle, setModalTitl] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  // 筛选
  const onSelect = (e: RadioChangeEvent) => {
    const selectedBiz = e.target.value;
    setBiz(selectedBiz);
    setList(sortList(data[selectedBiz]));
  };

  const openChainModal = (npmName: string) => {
    const npmChain = getNpmDepChain(chainList, npmName);
    if (!npmChain.length) {
      message.info('当前npm包无前置依赖')
      return;
    }
    setModalTitl(`${npmName}的前置依赖`);
    setIsModalOpen(true);
    setDepChain(npmChain)
  };

  const columns = useMemo(() => {
    return [
      ...DEFAULT_COLUMNS,
      ...(extraColumn || []),
      {
        title: '操作',
        dataIndex: 'size',
        render: (_: any, { npmName }: ITableItem) => <Link onClick={() => openChainModal(npmName)}>查看依赖关系</Link>
      }
    ]
  }, [extraColumn]);

  useEffect(() => {
    setList(sortList(data[biz]));
  }, []);

  const toolbarRender = () => {
    return (
      <Radio.Group defaultValue={initData} size="small" style={{ margin: 16 }} onChange={onSelect}>
        {Object.keys(data).map((key) => (
           <Radio.Button value={key} key={key}>{keyMap? keyMap[key] : key}</Radio.Button>
        ))}
      </Radio.Group>
    );
  };

  return (
    <Card>
      {toolbarRender()}
      <Table
        pagination={false}
        columns={columns}
        dataSource={list}
      />
      <Modal
        title={modalTitle}
        open={isModalOpen}
        footer={null}
        onCancel={() => setIsModalOpen(false)}
      >
        {depChain.map((chain, index) => (
          <div className="dep-chain" key={index}>
            <IconFont type={iconType.chain} className="dep-chain-icon" />
            {chain}
          </div>
        ))}
      </Modal>
    </Card>
  )
}
