import React, { useMemo } from 'react';
import { Table, Typography, Tag, Card, Row, Col, Statistic } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import { formatSizeWithUnit } from '../../lib/utils';
import { BIZ_NAME_MAP, BIZ_FILTERS, BIZ_TAG_COLOR } from '../../lib/constants';
import { ISizeDifferItem, BizMap, IMainSizeDetail } from '../../types';
import './index.less';

// 展示文件/Npm包名
function renderName(item: ISizeDifferItem) {
  const { name, prevSize, size } = item;
  if (!prevSize) {
    return <>{name}<Tag className="small-tag" color="#f50">新增</Tag></>
  }
  if (!size) {
    return <>{name}<Tag className="small-tag" color="#87d068">删除</Tag></>
  }
  return name;
}

function renderSize(version: string, value: number) {
  if (!value) return '-';
  return (
    <div>
      {version ? <Tag>{version}</Tag> : null}
      {formatSizeWithUnit(value)}
    </div>
  );
}

function renderDifferSize(num: number) {
  return (
    <div style={{color: num > 0 ? '#cf1322': '#3f8600'}}>
      {num > 0 ? '+' : ''}
      {formatSizeWithUnit(num)}
    </div>
  );
}

const getColumn: any = (version = '当前', prevVersion: string = '上个') => ([
  {
    title: '文件名或者npm包',
    dataIndex: 'name',
    width: '32%',
    render: (_: any, item: ISizeDifferItem) => renderName(item)
  },
  {
    title: '所属业务',
    dataIndex: 'biz',
    width: '150px',
    render: (_: any, { biz }: { biz: BizMap }) => <Tag color={BIZ_TAG_COLOR[biz]}>{BIZ_NAME_MAP[biz]}</Tag>,
    filterSearch: true,
    filters: BIZ_FILTERS,
    onFilter: (value: string, record: ISizeDifferItem) => record.biz === value,
  },
  {
    title: `${prevVersion}版本体积`,
    dataIndex: 'prevSize',
    render: (_: any, { prevSize, prevVersion: npmPrevVersion }: ISizeDifferItem) => renderSize(npmPrevVersion, prevSize),
  },
  {
    title: `${version}版本体积`,
    dataIndex: 'size',
    render: (_: any, { size, version: npmVersion }: ISizeDifferItem) => renderSize(npmVersion, size)
  },
  {
    title: '体积变更',
    dataIndex: 'differSize',
    render: (_: any, { differSize }: ISizeDifferItem) => renderDifferSize(differSize)
  },
]);

export default function DetailTable({ data }: { data: IMainSizeDetail }) {
  const { list, bizSizeMap, total = 0, prevVersion, version} = data || { list: [], bizSizeMap: {} };
  const columns = useMemo(() => {
    return getColumn(version, prevVersion)
  }, [data]);

  const bizData = useMemo(() => {
    return Object.keys(bizSizeMap).map(biz => {
      const value = bizSizeMap[biz];
      return {
        biz,
        value,
        valueWithUni: formatSizeWithUnit(value),
      };
    }).sort((a, b) => b.value - a.value);
  }, [bizSizeMap]);

  return (
    <>
      {!!total && (<Row gutter={24} style={{ marginBottom: '12px' }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="汇总"
              value={formatSizeWithUnit(total)}
              precision={2}
              valueStyle={{ color: total > 0 ? '#cf1322': '#3f8600' }}
              prefix={total > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
            />
          </Card>
        </Col>
        <Col span={16} style={{ paddingLeft: 0 }}>
          <Card bodyStyle={{ display: 'flex' }}>
            {bizData.map((item) => (
              <div key={item.biz} className="statistic-item">
                {/* @ts-ignore */}
                <span className="statistic-item-label">{BIZ_NAME_MAP[item.biz]}</span>
                <div>{item.value > 0 ? '+' : '' }{formatSizeWithUnit(item.value)}</div>
              </div>
            ))}
          </Card>
        </Col>
      </Row>)}
      <Card>
        <Table
          pagination={false}
          columns={columns}
          dataSource={list}
        />
      </Card>
    </>
  )
}
