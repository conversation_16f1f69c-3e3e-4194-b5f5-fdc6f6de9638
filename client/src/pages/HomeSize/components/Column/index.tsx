import { useState } from 'react';
import { Card, Typography } from 'antd';
import { Column } from '@ant-design/charts';
import { getPercentText } from '../../lib/utils';
import { BizMap, IPieItem } from '../../types';
import { BIZ_TAG_COLOR } from '../../lib/constants';
import './index.less';

interface IProps {
  data: IPieItem[];
  title?: string;
}

const { Title } = Typography;

const COLUMN_CONFIG = {
  xField: 'type',
  yField: 'value',
  height: 300,
  style: {
    fill: ({ type }: IPieItem) => BIZ_TAG_COLOR[type as BizMap],
  },
  label: {
    text: (d: IPieItem) => getPercentText(d.percent),
    offset: 10,
  },
  legend: false,
};


/** 体积饼图 */
export default function SizeColumn({ data, title }: IProps) {
  return (
    <Card>
      <Title level={5}>{title}</Title>
      {/* @ts-ignore */}
      <Column {...COLUMN_CONFIG} data={data} />
    </Card>
  );
}