import React from 'react';
import { Card, Typography, Space, Statistic } from 'antd';
import { Pie } from '@ant-design/charts';
import { getPercentText } from '../../lib/utils';
import { IPieItem } from '../../types';
import './index.less';

interface IProps {
  data: IPieItem[];
  title?: string;
  titleList?: Array<{
    title: string;
    value: string;
  }>;
  style?: React.CSSProperties;
}

const { Title } = Typography;

const basicConfig = {
  width: 200,
  height: 160,
  padding: 0,
  angleField: 'value',
  colorField: 'type',
  radius: 0.7,
  pieStyle: {
    strokeOpacity: 0.7
  },
  label: {
    type: 'spider',
    content: (d: IPieItem) => `${d.type} ${getPercentText(d.percent)}`
  },
  interactions: [{ type: 'element-active' }],
  legend: false
};


/** 体积饼图 */
export default function SizePie({ data, title, titleList, style }: IProps) {
  return (
    <Card className="size-pie" style={style}>
      {title && <Title className="size-pie__title" level={5}>{title}</Title>}

      {/* 数据对比 */}
      {titleList &&(
        <Space size="large">
          {titleList.map((item, index) => (
            <Statistic key={index} title={item.title} value={item.value} />
          ))}
        </Space>
      )}
  
      {/* @ts-ignore */ }
      {<Pie {...basicConfig} data={data} />}
    </Card>
  )
}