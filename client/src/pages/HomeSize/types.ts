export enum BizMap {
  USER = 'user', // 用户
  TRAFFICX = 'trafficx', // 交通公共
  HOTEL = 'hotel', // 交通酒店
  FLIGHT = 'flight', // 机票
  TRAIN = 'train', // 火车票
  BUS = 'bus', // 汽车票
  VEHICLE = 'vehicle', // 度假
  TICKET = 'ticket', // 门票
  COMMON = 'common' // 公共
};

export interface IPieItem {
  type: string;
  value: number;
  percent?: number;
  showValue?: string;
}

export interface IChartsItem {
  data: IPieItem[];
  title?: string;
  titleList?: Array<{
    title: string;
    value: string;
  }>;
}

export interface ICoreInfo {
  totalSize: number;
  homeSize: number;
  mainSize: number;
  mainHomeSize: number;
}

export interface ITableItem {
  npmName: string;
  fromPkg: string;
  size: number;
  biz?: BizMap;
  deps?: string[];
}

export interface ISizeDifferItem {
  name: string;
  biz: BizMap;
  prevSize: number;
  size: number;
  differSize: number;
  prevVersion: string;
  version: string;
}

export interface IMainSizeDetail {
  prevVersion?: string;
  version?: string;
  bizSizeMap: Record<string, number>;
  list: ISizeDifferItem[];
  total?: number;
}
