import { BizMap } from "../types";

export const BIZ_NAME_MAP = {
  [BizMap.COMMON]: '公共',
  [BizMap.HOTEL]: '酒店',
  [BizMap.TRAFFICX]: '交通公共',
  [BizMap.USER]: '平台',
  [BizMap.FLIGHT]: '机票',
  [BizMap.BUS]: '汽车票',
  [BizMap.TRAIN]: '火车票',
  [BizMap.VEHICLE]: '租车',
  [BizMap.TICKET]: '门票'
}

export const BIZ_TAG_COLOR = {
  [BizMap.COMMON]: 'magenta',
  [BizMap.HOTEL]: 'cyan',
  [BizMap.TRAFFICX]: 'blue',
  [BizMap.BUS]: 'gold',
  [BizMap.FLIGHT]: 'volcano',
  [BizMap.TRAIN]: 'orange',
  [BizMap.VEHICLE]: 'geekblue',
  [BizMap.USER]: 'purple',
  [BizMap.TICKET]: 'green',
}

/** 分包标签颜色 */
export const PKG_TAG_COLOR: Record<string, string> = {
  async: 'magenta',
  flight: 'volcano',
  flight2: 'volcano',
  home: 'purple',
  main: 'purple',
  ticket: 'green',
  'train-search': 'orange',
  'rent-car': 'geekblue',
};

/** 业务筛选 */
export const BIZ_FILTERS = Object.keys(BIZ_NAME_MAP).map((value) => ({ value, text: BIZ_NAME_MAP[value as BizMap] }));

/** 分包筛选 */
export const PKG_FILTERS = ['main', 'home', 'async', 'flight', 'flight2', 'ticket', 'train-search', 'rent-car'].map((value) => ({ value, text: value }));