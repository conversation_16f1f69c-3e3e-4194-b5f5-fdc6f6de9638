import { ReactNode } from "react";
import { ICoreInfo, IPieItem, BizMap, ITableItem } from "../types";
import { BIZ_NAME_MAP, BIZ_TAG_COLOR, PKG_TAG_COLOR, PKG_FILTERS, BIZ_FILTERS } from './constants';

export function calcNumber(num: number, digits = 2) {
  return parseFloat(num.toFixed(digits)).toString();
}

export function formatSize(size: number) {
  return calcNumber(size / 1024);
}

export function formatSizeWithUnit(size?: number) {
  if (!size) return '-'
  if (Math.abs(size) > 1024) {
    return calcNumber(size / 1024) + 'KB';
  }
  return size + 'B';
}

/** 获取百分比展示文案 */
export function getPercentText(num: number = 0) {
  return calcNumber(num * 100, 1) + '%';
}

/** 处理汇总体积 */
export function formatAllInfo(coreInfo: ICoreInfo) {
  const { totalSize = 0, homeSize = 0, mainSize = 0, mainHomeSize = 0 } = coreInfo;
  const getItem = (config: Record<string, any>) => {
    const { total, num, totalTitle, numTitle, unit = 'KB', numType } = config;
    return {
      titleList: [
        { title: totalTitle, value: `${total}${unit}` },
        { title: numTitle, value: `${num}${unit}` }
      ],
      data: [
        { type: numType, value: num },
        { type: '其他', value: +calcNumber(total - num) },
      ]
    }
  };
  return {
    total: getItem({
      total: +formatSize(totalSize / 1024),
      num: +formatSize(homeSize / 1024),
      totalTitle: '小程序总体积',
      numTitle: '首页总体积',
      unit: 'MB',
      numType: '首页'
    }),
    main: getItem({
      total: +formatSize(mainSize),
      num: +formatSize(mainHomeSize),
      totalTitle: '小程序主包体积',
      numTitle: '主包首页体积',
      numType: '首页'
    }),
  };
}

/** 处理体积数据 */
export function formatSizeInfo(data: Record<string, number>, valMap?: Record<string, string>) {
  return Object.keys(data).map((key) => {
    return {
      type: valMap ? valMap[key] : key,
      value: +formatSize(data[key]),
    } as IPieItem;
  });
}

/** 处理核心分包数据 */
export function formatCorePkgInfo(data: Record<string, Record<string, number>>) {
  return [
    {
      title: '主包首页体积分布（KB）',
      data: formatSizeInfo(data.main, BIZ_NAME_MAP),
    },
    {
      title: 'home分包体积分布（KB）',
      data: formatSizeInfo(data.home, BIZ_NAME_MAP),
    }
  ];
}

/** 柱状图数据 */
function getColumnData({ bizTotalSize, mainPkgSize, homePkgSize }: any) {
  return [
    { title: '首页业务体积分布明细（KB）', data: bizTotalSize },
    { title: '主包首页体积分布明细（KB）', data: mainPkgSize },
    { title: 'home分包体积分布明细（KB）', data: homePkgSize },
  ];
}

function getPieData({ alloverSizeInfo, bizTotalSize, pkgTotalSize, mainPkgSize, homePkgSize }: any) {
  return [
    { ...alloverSizeInfo.total },
    { title: '首页业务体积分布（KB）', data: bizTotalSize },
    { title: '首页各分包体积分布（KB）', data: pkgTotalSize },
    { ...alloverSizeInfo.main },
    { title: '主包首页体积分布（KB）', data: mainPkgSize },
    { title: 'home分包体积分布（KB）', data: homePkgSize },
  ];
}

/** 处理返回数据 */
export function formatResData(resData: Record<string, any>) {
  const params = {
    alloverSizeInfo: formatAllInfo(resData.allSizeInfo),
    mainPkgSize: formatSizeInfo(resData.corePkgSizeInfo.main, BIZ_NAME_MAP),
    homePkgSize: formatSizeInfo(resData.corePkgSizeInfo.home, BIZ_NAME_MAP),
    pkgTotalSize: formatSizeInfo(resData.pkgSizeInfo),
    bizTotalSize: formatSizeInfo(resData.bizSizeInfo, BIZ_NAME_MAP),
  };
  return {
    pieList: getPieData(params),
    columnList: getColumnData(params),
  }
}

/** 获取明细表格配置 */
export function getTableConfig(
  { pkgDepSummary, bizNpmSummary, chainList, mainSizeDiff, prevVesion }: any,
  renderTag: (color: string, name: string) => ReactNode
) {
  return [
    {
      tabName: '分包体积明细',
      initData: 'main',
      data: pkgDepSummary,
      chainList,
      extraColumn: [
        {
          title: '所属业务',
          dataIndex: 'biz',
          render: (_: any, { biz }: { biz: BizMap }) => renderTag(BIZ_TAG_COLOR[biz], BIZ_NAME_MAP[biz]),
          filterSearch: true,
          filters: BIZ_FILTERS,
          onFilter: (value: string, record: ITableItem) => record.biz === value,
        }
      ]
    },
    {
      tabName: '业务体积明细',
      initData: BizMap.USER,
      data: bizNpmSummary,
      chainList,
      keyMap: BIZ_NAME_MAP,
      extraColumn: [
        {
          title: '所在分包',
          dataIndex: 'fromPkg',
          render: (_: any, { fromPkg }: { fromPkg: string }) => renderTag(PKG_TAG_COLOR[fromPkg], fromPkg),
          filterSearch: true,
          filters: PKG_FILTERS,
          onFilter: (value: string, record: ITableItem) => record.fromPkg === value,
        }
      ]
    },
  ];
}

// 获取npm包的前置依赖关系
export function getNpmDepChain(chainList: string[], npmName: string) {
  const depChain: string[] = [];
  const res: any[] = [];
  chainList.forEach((chain) => {
    const idx = chain.indexOf(npmName);
    const pureChain = chain.substring(0, idx + npmName.length);
    if (idx > 0 && pureChain !== npmName && depChain.indexOf(pureChain) === -1) {
      depChain.push(pureChain);
    }
  });
  return depChain;
}