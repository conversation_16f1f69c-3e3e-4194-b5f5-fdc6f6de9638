import { Alert, Spin, Tabs, TabsProps } from 'antd';
import { ReactNode, useEffect, useMemo, useState } from 'react';
import { useModel } from 'umi';

import './index.less';

const Tab = ({ url, children }: { url: string; children: ReactNode }) => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!loading) return;
    // 超时自动关闭loading，原因是某些情况下 iframe 的 onLoad 不会触发
    setTimeout(() => setLoading(false), 3000);
  }, []);
 
  return (
    <Spin spinning={loading} size="large">
      <Alert showIcon message={children} type="info" />
      <iframe className="iframe" onLoad={() => setLoading(false)} src={url} />
    </Spin>
  );
};

export default () => {
  const currentUser = useModel('user');

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '通用抓包（支/微/抖小程序适用）',
      children: (
        <Tab
          url={`https://open-qa.alibaba-inc.com/noLogin/#/openEasymock/requestDataForFliggy?userId=${currentUser?.workid}`}
        >
          小程序唤起扫码方法：1. 在小程序首页左上角连续快速点击6次； 2. 还可以通过Atom工具；
        </Tab>
      ),
    },
    {
      key: '2',
      label: 'AntMan（仅支付宝小程序适用）',
      children: (
        <Tab
          url="http://antbox-site.antgroup-inc.cn/antBoxWeb"
        >
          建议安装<a href="https://aliyuque.antfin.com/antman/guide/quick-start" target="_blank">AntMan桌面应用</a>，体验和稳定性会更好
        </Tab>
      ),
    },
  ];

  return (
    <div className="helper-capture-container">
      <Tabs items={items} />
    </div>
  );
};
