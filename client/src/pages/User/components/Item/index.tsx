import { useState } from 'react';
import { message, Col, List, Popconfirm, Divider, Spin, Avatar, Row } from 'antd';
import userApi from '@/api/user';
import { IUser } from '@/interface/user';
import EditUserBtn, { Action } from '../EditUserBtn';
import { useAccess, Access } from 'umi';

import './index.less';

interface Props {
  data: IUser;
  onDeleteEnd: Function;
}

export default ({ data, onDeleteEnd }: Props) => {
  const [latestData, setLatestData] = useState(data);
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(true);
  const access = useAccess();

  const Actions = () => {
    return <Access accessible={access.isAdmin}>
      <EditUserBtn
        user={latestData}
        onEditEnd={onEditEnd}
        action={Action.Update}>
        <a>编辑</a>
      </EditUserBtn>
      <Divider type="vertical" />
      <Popconfirm title="确认删除？" okText="是" cancelText="否" onConfirm={deleteUser}>
        <a>删除</a>
      </Popconfirm>
    </Access>;
  };

  function onEditEnd(_action: any, res: { data: IUser[] }) {
    setLatestData(res.data[0]);
  }

  // 删除用户
  function deleteUser() {
    setLoading(true);

    userApi.delete(latestData.workid)
      .then((res) => {
        if (res?.success) {
          setShow(false);
          onDeleteEnd && onDeleteEnd();
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '删除失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  if (!show || !latestData) return null;

  return <Spin spinning={loading} wrapperClassName="user-list-item">
    <Row>
      <Col span={6}>
        <List.Item.Meta
          avatar={
            // <Avatar src={latestData.avatarUrl} /> personalPhotoUrl 图片太大
            <Avatar src={`https://work.alibaba-inc.com/photo/${latestData.workid}.100x100.jpg`} />
          }
          title={<a href={latestData.htmlUrl} target="_blank">{latestData.name}</a>}
          description={`${latestData.lastName}(${latestData.workid})`}
        />
      </Col>
      <Col span={6} className="col">
        <div>{latestData.userTypeText}</div>
      </Col>
      <Col span={12} className="col actions">
        <Actions />
      </Col>
    </Row>
  </Spin>
}