export default {
  "type": "object",
  "properties": {
    "userList": {
      "title": "用户名",
      "type": "array",
      "required": true,
      "items": {
        "type": "string"
      },
      "props": {},
      "width": "90%",
      "widget": "user"
    },
    "userType": {
      "title": "用户类型",
      "type": "string",
      "required": true,
      "default": "developer",
      "enum": ["admin", "developer", "qa"],
      "enumNames": ["管理员", "开发者", "测试"],
      "widget": "radio"
    },
  },
  "labelWidth": 180,
  "displayType": "row"
};
