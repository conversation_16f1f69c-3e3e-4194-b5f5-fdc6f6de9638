import { useState } from 'react';
import { Modal, message, Spin, Avatar } from 'antd';
import { useForm } from 'form-render';
import { IUser } from '@/interface/user';
import FormRender from '@/components/FR';
import userApi, { IUserCreateRes, IUserUpdateRes } from '@/api/user';
import schema from './schema';

import './index.less';

export enum Action {
  Create = 'create', // 新建
  Update = 'update', // 更新
}

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  user?: IUser;
  action: Action;
  onEditEnd?: (action: Action, res: any) => void;
}

export default ({ user, children, action, onEditEnd }: Props) => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const form = useForm();

  // 行动名称
  let actionName = '';
  if (action === Action.Create) actionName = '新建';
  else if (action === Action.Update) actionName = '更新';

  const onFormMount = () => {
    // 当有值时，回填表单
    if (user) {
      const { userid, workid, name, lastName, avatarUrl } = user;
      const value = JSON.stringify({ userid, workid, name, lastName, avatarUrl });

      form.setValues({
        userList: [{
          label: <span style={{ display: 'inline-flex', alignItems: 'center' }}><Avatar key={workid} src={`https://work.alibaba-inc.com/photo/${workid}.100x100.jpg`} size={18} style={{ marginRight: '6px' }} />{name}（{lastName}-{workid}）</span>,
          value
        }],
        userType: user.userType,
      })
    }

    // 各 action 专属逻辑
    if (action === Action.Update) {
      form.setSchemaByPath('userList', { disabled: true }); // 禁止修改用户
    }
  }

  const onFormSubmit = () => {
    form.submit();
  };

  const onFormCancel = () => {
    form.resetFields();
    setShowModal(false);
  };

  const onFormFinish = (formData: any, errors: any) => {
    if (errors.length > 0) return;

    setLoading(true);

    userApi[action === Action.Update ? 'updateMulti' : 'createMulti'](formData).then((res: void | IUserCreateRes | IUserUpdateRes) => {
      if (res?.success) {
        message.info(`${actionName}成功`);
        setShowModal(false);
        setTimeout(() => form.resetFields(), 500); // 迟一点清空，省得闪一下
        onEditEnd && onEditEnd(action, res);
      } else {
        message.error(res?.errorMsg || `${actionName}失败`);
      }
    })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => setLoading(false));
  }

  return <>
    <div onClick={() => setShowModal(true)}>
      {children}
    </div>

    <Modal
      title={`${actionName}用户`}
      open={showModal}
      onOk={onFormSubmit}
      onCancel={onFormCancel}
      maskClosable={false}
      okButtonProps={{ disabled: form.errorFields?.length > 0 }}
      width={700}
      destroyOnClose
    >
      <Spin spinning={loading} wrapperClassName="form-wrapper">
        <FormRender
          form={form}
          schema={schema}
          removeHiddenData={false}
          onMount={onFormMount}
          onFinish={onFormFinish}
          displayType="row" />
      </Spin>
    </Modal>
  </>
}
