import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>er, Spin, Button, List } from 'antd';
import { useAccess, Access } from 'umi';
import userApi from '@/api/user';
import { IUser } from '@/interface/user';
import EditUserBtn, { Action } from './components/EditUserBtn';
import Item from './components/Item';

import './index.less';

export default () => {
  const [userList, setUserList] = useState<IUser[]>([]);
  const [loading, setLoading] = useState(true);
  const access = useAccess();

  useEffect(refresh, [])

  function refresh() {
    setLoading(true);

    userApi.list().then(res => {
      if (!res || !res.data) return;
      setUserList(res.data)
    })
      .finally(() => setLoading(false))
  }

  return (
    <div style={{ background: '#fff' }}>
      <PageHeader
        ghost={false}
        title="权限管理"
        extra={
          <Access accessible={access.isAdmin}>
            <EditUserBtn action={Action.Create} onEditEnd={refresh}><Button type="primary">添加用户</Button></EditUserBtn>
          </Access>
        }>
      </PageHeader>

      <Spin size="large" spinning={loading}>
        <List
          style={{ padding: '0 24px 24px' }}
          itemLayout="horizontal"
          size="large"
          pagination={{
            pageSize: 10,
          }}
          dataSource={userList}
          renderItem={item => (
            <List.Item key={item.workid}>
              <Item data={item} onDeleteEnd={refresh} />
            </List.Item>
          )}
        />
      </Spin>
    </div>
  );
}
