import { useEffect, useState } from 'react';
import { Input, Form, Radio, RadioChangeEvent, Select, Button, message } from 'antd';
import QRCode from 'qrcode.react';
import queryString from 'query-string';
import { endOptions, flowCustomOptions, alipayWebviewPath, taobaoWebviewPath, alipayMiniappRouteOptions, taobaoMiniappRouteOptions, nowCollectHomeOptions, flowCustomWebviewOptions, homeTabOptions, noDynamicOptions, staticJumpTypeOptions, IMiniappRoute } from './formOptions';
import './index.less';

const { TextArea } = Input;
const { Option } = Select;


export default () => {
  const [end, setEnd] = useState('alipay');
  const [flowCustom, setFlowCustom] = useState(false);
  const [miniappRouteIndex, setMiniappRouteIndex] = useState<number>(0);
  const [homeTab, setHomeTab] = useState('');
  const [nowCollectHome, setNowCollectHome] = useState(true);
  const [noDynamic, setNoDynamic] = useState(false);
  const [staticJumpType, setStaticJumpType] = useState(false);
  const [flowCustomWebview, setFlowCustomWebview] = useState(false);
  const [h5Url, setH5Url] = useState('');
  const [endUrl, setEndUrl] = useState('');
  const [canWorking, setCanWorking] = useState(false);
  const [realUrl, setRealUrl] = useState('');
  const [ttid, setTtid] = useState('');
  const [fpt, setFpt] = useState('');
  // 海关页的spm， 只有套壳页可以自己填写
  const [customPageSpm, setCustomPageSpm] = useState<string>('');

  const miniappRouteOptions = end === 'alipay' ? alipayMiniappRouteOptions : taobaoMiniappRouteOptions;
  const webviewPath = end === 'alipay' ? alipayWebviewPath : taobaoWebviewPath;
  const miniappRoute: IMiniappRoute = miniappRouteOptions[miniappRouteIndex].value;

  // 投放端change
  const onEndChange = ({ target: { value } }: RadioChangeEvent) => {
    setEnd(value);
  };

  // 是否流量海关change
  const flowCustomChange = ({ target: { value } }: RadioChangeEvent) => {
    setFlowCustom(value);
  };

  // 小程序route change
  const miniappRouteChange = (value: number) => {
    setMiniappRouteIndex(value);
  };

  // 首页tab change
  const homeTabChange = (value: string) => {
    setHomeTab(value);
  };

  // 是否设首强拉change
  const nowCollectHomeChange = ({ target: { value } }: RadioChangeEvent) => {
    setNowCollectHome(value);
  };

  // 承接页是否套壳change
  const flowCustomWebviewChange = ({ target: { value } }: RadioChangeEvent) => {
    setFlowCustomWebview(value);
  };

  // 是否承接页不走定投
  const noDynamicChange = ({ target: { value } }: RadioChangeEvent) => {
    setNoDynamic(value);
  };

  // 是否海关页不走定投
  const staticJumpTypeChange = ({ target: { value } }: RadioChangeEvent) => {
    setStaticJumpType(value);
  };

  // 套壳链接 change
  const h5UrlChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setH5Url(e.target.value);
  };

  // 承接页链接 change
  const endUrlChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setEndUrl(e.target.value);
  };

  // 海关页 ttid change
  const ttidChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setTtid(e.target.value);
  };

  // 海关页spm change
  const customPageSpmChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setCustomPageSpm(e.target.value);
  };

  // 海关页fpt change
  const fptChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFpt(e.target.value);
  };

  const generateUrl = () => {
    const urlData = queryString.parseUrl(end === 'alipay' ? 'alipays://platformapi/startapp?appId=2018081461095002' : 'https://m.duanqu.com?_ariver_appid=6579080');
    if (flowCustom) {
      // 启用流量海关能力
      // 生成海关页参数处理
      const customPage = queryString.parseUrl(miniappRoute.path);
      customPage.query['flowCustomChannel'] = 'true';
      if (ttid) customPage.query['ttid'] = ttid;
      if (miniappRoute?.isWebview && h5Url) {
        // 海关页为套壳页
        customPage.query['url'] = h5Url;
      } else {

        // 是首页并且指定首页tab
        if (end === 'alipay' && miniappRoute && miniappRoute.path === 'pages/index/index' && homeTab) {
          customPage.query['tab'] = homeTab;
        }
        if (fpt) {
          customPage.query['fpt'] = `ftid(${fpt})`;
        }
      }
      urlData.query['page'] = queryString.stringifyUrl(customPage, {strict: false});

      // 处理全局参数
      const queryData : Record<string, string> = {};

      // 兼容承接页没有兜底的情况
      if (!endUrl) {
        queryData['noPlaceHoldUrl'] = 'true';
      } else {
        // 拼接上海关页的spm
        let endPage = queryString.parseUrl(endUrl);
        if (end === 'taobao' && customPageSpm) {
          // 先兼容手淘静态跳转
          endPage.query['spm'] = `${customPageSpm}.flow-custom.static-jump`;
        }
        queryData['jumpUrl'] = queryString.stringifyUrl(endPage, {strict: false});
        if (noDynamic) {
          queryData['noDynamicJumpUrl'] = 'true';
        }
      }

      if (staticJumpType) {
        queryData['staticJumpType'] ='true';
      }

      if (end === 'alipay' && nowCollectHome) {
        queryData['nowCollectHome'] ='true';
      }
      if (flowCustomWebview && endUrl) {
        queryData['flowCustomWebview'] ='true';
      }
      urlData.query['query'] = queryString.stringify(queryData);
      setRealUrl(queryString.stringifyUrl(urlData, {strict: false}));
    } else {
      urlData.query['page'] = `${webviewPath}?url=${encodeURIComponent(endUrl)}`;
    }
    setRealUrl(queryString.stringifyUrl(urlData, {strict: false}));
  }

  const copyLink = () => {
    navigator.clipboard.writeText(realUrl)
      .then(() => {
        message.success('复制成功')
      }).catch((err) => {
        message.error(err?.message || '复制失败！')
      });
  }

  useEffect(() => {
    console.log(flowCustom, miniappRoute, flowCustom && miniappRoute && endUrl)
    if (flowCustom) {
      if (miniappRoute) {
        setCanWorking(true);
        setCustomPageSpm(miniappRoute.spm || '');
      }
    } else {
      if (endUrl) {
        setCanWorking(true);
      }
    }
  }, [flowCustom, miniappRoute, nowCollectHome, endUrl]);

  return (
    <div className="miniapp-link-container">

      <div className="content">
        <h3>小程序链接生成</h3>
        {
          realUrl ? <QRCode className='qrcode' size={150} value={realUrl} /> : null
        }
        <Form className="form" layout="vertical">
          <Form.Item label="投放端：" required>
            <Radio.Group options={endOptions} onChange={onEndChange} value={end} />
          </Form.Item>
          <Form.Item label="是否需要流量海关能力：" tooltip="增加小程序海关页，承接页返回可进小程序页" required>
            <Radio.Group options={flowCustomOptions} onChange={flowCustomChange} value={flowCustom} />
          </Form.Item>
          {
            flowCustom ? <Form.Item label="小程序海关页：" required>
              <Select
                showSearch
                placeholder="选择小程序页面"
                optionFilterProp="children"
                onChange={miniappRouteChange}
                value={miniappRouteIndex}
                filterOption={(input, option) =>
                  (option!.children as unknown as string).toLowerCase().includes(input.toLowerCase())
                }
              >
                {
                  miniappRouteOptions.map((item, index) => {
                    return <Option value={index} key={item.label}>{item.label}</Option>
                  })
                }
              </Select>
            </Form.Item> : null
          }
          {
            end === 'alipay' && flowCustom && miniappRoute && miniappRoute.path === 'pages/index/index' ? <Form.Item label="海关首页锚定tab">
              <Select
                placeholder="选择首页tab"
                optionFilterProp="children"
                onChange={homeTabChange}
                value={homeTab}
                filterOption={(input, option) =>
                  (option!.children as unknown as string).toLowerCase().includes(input.toLowerCase())
                }
              >
                {
                  homeTabOptions.map(item => {
                    return <Option value={item.value} key={item.value}>{item.label}</Option>
                  })
                }
              </Select>
            </Form.Item> : null
          }
          {
            flowCustom ? <Form.Item label="海关页spm：">
              <Input
                placeholder="在此提供海关页的SPM"
                onChange={customPageSpmChange}
                value={customPageSpm}
                disabled={!miniappRoute?.isWebview}
                required
              />
            </Form.Item> : null
          }
          {
            flowCustom ? <Form.Item label="小程序海关页ttid：">
              <Input
                placeholder="在此粘贴ttid"
                onChange={ttidChange}
                value={ttid}
              />
            </Form.Item> : null
          }
          {
            flowCustom && miniappRoute && !miniappRoute.isWebview ? <Form.Item label="小程序海关页fpt：">
              <Input
                placeholder="在此粘贴fpt"
                onChange={fptChange}
                value={fpt}
              />
            </Form.Item> : null
          }
          {
            flowCustom && miniappRoute && miniappRoute.isWebview ? <Form.Item label="套壳页H5链接：" required>
              <Input
                placeholder="在此粘贴H5链接,请带上ttid or fpt"
                onChange={h5UrlChange}
                value={h5Url}
                size={'large'}
              />
            </Form.Item> : null
          }
          {
            flowCustom ? <Form.Item label="承接页是否套壳：" required>
              <Radio.Group options={flowCustomWebviewOptions} onChange={flowCustomWebviewChange} value={flowCustomWebview} />
            </Form.Item> : null
          }
          {
            end === 'alipay' && flowCustom ? <Form.Item label="是否强拉设首：" required>
              <Radio.Group options={nowCollectHomeOptions} onChange={nowCollectHomeChange} value={nowCollectHome} />
            </Form.Item> : null
          }
          {
            flowCustom ? <Form.Item label={end === 'alipay' ? '是否不获取动态数据（适用需要快速二跳的场景，例机票listing）' : '是否关闭动态化能力（默认不关闭，即开启动态化能力）'} required>
              <Radio.Group options={staticJumpTypeOptions} onChange={staticJumpTypeChange} value={staticJumpType} />
            </Form.Item> : null
          }
          {
            end === 'alipay' && flowCustom ? <Form.Item label="承接页是否固定，不定投（适用触达场景）" required>
              <Radio.Group options={noDynamicOptions} onChange={noDynamicChange} value={noDynamic} />
            </Form.Item> : null
          }
          <Form.Item label={flowCustom ? "承接页链接：" : "套壳页链接"} required={flowCustom && end === 'alipay' ? false : true}>
            <Input
              placeholder="在此粘贴链接,请带上ttid or fpt"
              onChange={endUrlChange}
              value={endUrl}
              size={'large'}
            />
          </Form.Item>
        </Form>
        <TextArea
          rows={4}
          disabled={true}
          value={realUrl}
        />
        <Button
          type="primary"
          onClick={generateUrl}
          disabled={canWorking ? false : true}
          className="do-btn"
        >
          生成
        </Button>
        <Button
          onClick={() => {
            copyLink();
          }}
          className="copy-btn"
        >
          复制链接
        </Button>
      </div>
    </div>
  );
}
