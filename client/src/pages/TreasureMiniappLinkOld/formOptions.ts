export interface IMiniappRoute {
  path: string;
  isWebview?: boolean;
  spm?: string;
}

export const endOptions = [
  { label: '支付宝', value: 'alipay' },
  { label: '淘宝', value: 'taobao' }
];

export const flowCustomOptions = [
  { label: '是', value: true },
  { label: '否', value: false },
];

export const noDynamicOptions = [
  { label: '是', value: true },
  { label: '否', value: false },
];

export const staticJumpTypeOptions = [
  { label: '是', value: true },
  { label: '否', value: false },
];

export const alipayWebviewPath = 'pages/webview/index';
export const taobaoWebviewPath = 'pages/webview/index';

export const alipayMiniappRouteOptions = [
  { label: '首页', value: { path: 'pages/index/index', spm: '181.22633512' } },
  { label: '福利中心', value: { path: 'pages/weal-center/index', spm: '181.24050330' } },
  { label: '套壳页', value: { path: alipayWebviewPath, isWebview: true } },
  { label: '酒店列表', value: { path: 'hotel/searchlist/index', spm: '181.7437890' } },
];

export const taobaoMiniappRouteOptions = [
  { label: '首页', value: { path: 'pages/index/index', spm: '181.7474825' } },
  { label: '福利中心', value: { path: 'pages/lottery/index', spm: '181.26989430' } },
  { label: '套壳页', value: { path: taobaoWebviewPath, isWebview: true } },
];

export const homeTabOptions = [
  { label: '酒店', value: 'hotel' },
  { label: '机票', value: 'flight' },
  { label: '火车票', value: 'train' },
  { label: '门票', value: 'ticket' },
  { label: '汽车票', value: 'bus' },
  { label: '租车', value: 'vehicle' }
]

export const nowCollectHomeOptions = [
  { label: '是', value: true },
  { label: '否', value: false },
];

export const flowCustomWebviewOptions = [
  { label: '是', value: true },
  { label: '否', value: false },
];