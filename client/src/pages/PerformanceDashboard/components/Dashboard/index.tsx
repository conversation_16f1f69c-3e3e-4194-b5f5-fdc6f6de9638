import React, { useEffect, useMemo, useState } from 'react';
import { Pie<PERSON>hartOutlined, AreaChartOutlined } from '@ant-design/icons';
import { Select, Space, Spin, Tabs } from 'antd';
import { IProject } from '@/interface/project';
import ufoApi from '@/api/ufo';
import HistoryPerformance from '../HistoryPerformance';
import PerformanceTable from '../PerformanceTable';
import { UFO_ALIPAY_TASK_INFO, UFO_WX_TASK_INFO } from '@/const/ufo';
import { IPerformanceList, IBaseConfig, IPerformanceLineData } from '@/interface/ufo';
import { IIterBranch } from '@/interface/iter-branch';
import ProjectSelect from '@/components/ProjectSelect';

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('0');
  const [performanceData, setPerformanceData] = useState<IPerformanceList>();
  const [branchList, setBranchList] = useState<Pick<IIterBranch, 'version' | 'iterId'>[]>([]);
  const [project, setProject] = useState<IProject>();
  const [selectedOS, setSelectedOS] = useState<string>('all');
  const [bizLine, setBizLine] = useState<string>('酒店');
  const iosArray: IPerformanceLineData[] = [];
  const androidArray: IPerformanceLineData[] = [];
  let finalArray: IPerformanceLineData[] = [];

  const taskInfo = project?.type === 'weixin' ? UFO_WX_TASK_INFO : UFO_ALIPAY_TASK_INFO;
  const array: IPerformanceLineData[] = [];

  taskInfo.forEach((config, idx) => {
    const jobList = performanceData?.data.map((item) => item.data)[idx];
    const jobMap = new Map(jobList?.map((job, idx) => [job.h5Version, job]));

    if (jobList?.length) {
      const result = branchList?.map((branch) => {
        if (jobMap.has(branch.version)) {
          return {
            pageName: config.taskDetail.configName,
            os: config.taskDetail.os,
            h5Version: branch.version,
            iterId: branch.iterId,
            bizLine: config.bizLine,
            openTime: Number(jobMap.get(branch.version)?.openTime?.split('|')[1] || '0'),
            ufoUrl: `https://ufo2.alitrip.com/autoPerformance/videoResult?id=${
              jobMap.get(branch.version)?.id
            }&mirrorAlgorithmId=${config.ufoTaskId}`,
          };
        } else {
          // console.log(jobMap.get(branch.version)?.bizLine);
          return {
            pageName: config.taskDetail.configName,
            os: config.taskDetail.os,
            h5Version: branch.version,
            openTime: 0,
            bizLine: config.bizLine,
            iterId: branch.iterId,
            ufoUrl: `https://ufo2.alitrip.com/autoPerformance/videoResult?id=${
              jobMap.get(branch.version)?.id
            }&mirrorAlgorithmId=${config.ufoTaskId}`,
          };
        }
      });
      array.push(...result);
    }
  });

  const fetchData = (project: IProject) => {
    const taskInfo = project?.type === 'weixin' ? UFO_WX_TASK_INFO : UFO_ALIPAY_TASK_INFO;
    const taskIds = taskInfo.map((item) => item.ufoTaskId);

    Promise.all([ufoApi.getBranchList(project.name), ufoApi.getAllListByPage(taskIds, 1, 40, project?.name)])
      .then((res) => {
        setLoading(false);
        setBranchList(res[0].data.map((item) => ({ iterId: item.iterId, version: 'rc/' + item.version })));
        setPerformanceData(res[1]);
      })
      .catch((err) => {
        console.log(err);
      });
    // setLoading(false);
    // setBranchList(mockData[1].data.map((item) => ({ iterId: item.iterId, version: 'rc/' + item.version })));
    // setPerformanceData(mockData[1]);
  };

  function onProjectSelect(project: IProject) {
    setProject(project);
    fetchData(project);
    setLoading(true);
  }

  array.forEach((item) => {
    if (item.os === 'iOS') {
      iosArray.push(item);
    } else {
      androidArray.push(item);
    }
  });

  if (bizLine !== '全部') {
    if (selectedOS === 'iOS') {
      finalArray = iosArray.filter((item) => item.bizLine === bizLine);
    } else if (selectedOS === 'Android') {
      finalArray = androidArray.filter((item) => item.bizLine === bizLine);
    } else {
      finalArray = array.filter((item) => item.bizLine === bizLine);
    }
  } else {
    if (selectedOS === 'iOS') {
      finalArray = iosArray;
    } else if (selectedOS === 'Android') {
      finalArray = androidArray;
    } else {
      finalArray = array;
    }
  }

  return (
    <Spin spinning={loading} tip="请求数据中, 请耐心等待">
      <ProjectSelect onSelect={onProjectSelect} style={{ margin: '18px 16px 18px' }} />
      <div>
        <span style={{ marginLeft: '16px' }}>系统：</span>
        <Select
          // style={{ marginLeft: '16px' }}
          defaultValue="all"
          options={[
            { label: 'iOS（iPhone13ProMax）', value: 'iOS' },
            { label: 'Android（小米10）', value: 'Android' },
            { label: '全部', value: 'all' },
          ]}
          onChange={(value) => setSelectedOS(value)}
        ></Select>
        <span style={{ marginLeft: '16px' }}>行业：</span>
        <Select
          defaultValue="酒店"
          options={[
            { label: '全部', value: '全部' },
            { label: '首页', value: '公共' },
            { label: '酒店', value: '酒店' },
            { label: '机票', value: '机票' },
            { label: '火车票', value: '火车票' },
            { label: '汽车票', value: '汽车票' },
            { label: '度假', value: '度假' },
          ]}
          onChange={(value) => setBizLine(value)}
        ></Select>
      </div>

      {project?.name && array.length && branchList.length ? (
        <>
          <h2 style={{ margin: '18px 16px 0' }}>性能趋势</h2>
          <HistoryPerformance
            project={project}
            taskInfo={taskInfo}
            branchList={branchList}
            finalArray={finalArray}
          ></HistoryPerformance>
          <PerformanceTable
            project={project}
            taskInfo={taskInfo}
            branchList={branchList}
            finalArray={finalArray}
            bizLine={bizLine}
            selectedOS={selectedOS}
          ></PerformanceTable>
        </>
      ) : null}
    </Spin>
  );
};
export default Dashboard;
