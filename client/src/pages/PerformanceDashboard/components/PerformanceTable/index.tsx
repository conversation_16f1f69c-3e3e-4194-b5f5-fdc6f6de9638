import React, { useState } from 'react';
import { Table, TableColumnsType, Tooltip } from 'antd';
import { IBaseConfig, IPerformanceLineData, IPerformanceList } from '@/interface/ufo';
import { IProject } from '@/interface/project';
import { IIterBranch } from '@/interface/iter-branch';
import { QuestionCircleFilled } from '@ant-design/icons';

interface IProps {
  project: IProject;
  taskInfo: IBaseConfig[];
  performanceData?: IPerformanceList;
  branchList: Pick<IIterBranch, 'version' | 'iterId'>[];
  finalArray: IPerformanceLineData[];
  bizLine: string;
  selectedOS: string;
}

interface DataType {
  [key: string]: string;
}

interface GroupData {
  [key: string]: IPerformanceLineData[];
}

const PerformanceTable: React.FC<IProps> = (props) => {
  const { finalArray, branchList, taskInfo, bizLine, selectedOS } = props;

  let selectedTaskInfo = taskInfo;

  if (selectedOS === 'all') {
    selectedTaskInfo = bizLine !== '全部' ? taskInfo.filter((item) => item.bizLine === bizLine) : taskInfo;
  } else {
    if (selectedOS !== 'all') {
      selectedTaskInfo =
        bizLine !== '全部'
          ? taskInfo.filter((item) => item.bizLine === bizLine && item.taskDetail.os === selectedOS)
          : taskInfo.filter((item) => item.taskDetail.os === selectedOS);
    }
  }

  const columns: TableColumnsType<DataType> = branchList.map((branch) => ({
    key: branch.iterId,
    title: branch.version,
    dataIndex: branch.version,
    width: 150,
    render: (value, record) => {
      const number = value.split('ms')[0];
      let color = '';
      if (
        (record.pageName.endsWith('（iOS）') && +number >= 1000) ||
        (record.pageName.endsWith('（Android）') && +number >= 1800)
      ) {
        color = 'red';
      }
      return <span style={{ color: color }}>{value}</span>;
    },
  })).reverse();

  columns.unshift({
    title: '页面',
    dataIndex: 'pageName',
    key: 'pageName',
    width: 160,
    fixed: 'left',
  });

  const tableMap = new Map();

  // 分组
  const finalArrayGroup = finalArray.reduce<GroupData>(
    (acc, item) => ({
      ...acc,
      [`${item.pageName}（${item.os}）`]: [...(acc[`${item.pageName}（${item.os}）`] || []), item],
    }),
    {},
  );

  for (let key of Object.keys(finalArrayGroup)) {
    const map = new Map();
    finalArrayGroup[key].forEach((item) => {
      map.set(item.h5Version, `${String(item.openTime)}ms`);
    });
    tableMap.set(key, map);

  }

  const dataSource = selectedTaskInfo.map((task) => {
    const obj = branchList?.reduce<DataType>((acc, item) => {
      acc.key = String(item.iterId + task.id);
      acc.pageName = task.taskDetail.configName + `（${task.taskDetail.os}）`;
      acc[item.version] = tableMap.get(`${task.taskDetail.configName}（${task.taskDetail.os}）` )?.get(item.version) || '0ms';
      return acc;
    }, {});
    return obj;
  }, []).reverse();

  return (
    <div style={{ marginLeft: '1rem' }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <h1 style={{ display: 'inline' }}>页面耗时</h1>
        <Tooltip title="FSP首屏高端机达标<=1000ms，低端机<=1800ms">
          <QuestionCircleFilled style={{ position: 'relative', top: '-0.5rem', left: '0.1rem' }}></QuestionCircleFilled>
        </Tooltip>
      </div>
      <Table
        pagination={{ position: ['bottomCenter'] }}
        columns={columns}
        dataSource={dataSource}
        scroll={{ x: 1300 }}
      ></Table>
    </div>
  );
};

export default PerformanceTable;
