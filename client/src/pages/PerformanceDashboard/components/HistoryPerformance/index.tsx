import React from 'react';
import { Line } from '@ant-design/plots';
import { IPerformanceLineData, IPerformanceList, IBaseConfig } from '@/interface/ufo';
import { IProject } from '@/interface/project';
import { IIterBranch } from '@/interface/iter-branch.js';
import './index.less';

interface IProps {
  project: IProject;
  taskInfo: IBaseConfig[];
  performanceData?: IPerformanceList;
  branchList?: Pick<IIterBranch, 'version' | 'iterId'>[];
  finalArray: IPerformanceLineData[];
}

const HistoryPerformance: React.FC<IProps> = (props) => {
  const { project, branchList, finalArray} = props;

  const dataSource = finalArray.map(item => {
    let pageName = item.pageName + `（${item.os}）`;
    let openTime = item.openTime < 0 ? 0 : item.openTime;
    return {
      ...item,
      openTime,
      pageName
     }
  })

  const performanceDataConfig = {
    data: dataSource,
    height: 400,
    renderer: 'svg',
    xField: 'h5Version',
    seriesField: 'pageName',
    yField: 'openTime',
    xAxis: {
      label: {
        style: {
          fill: '#1890ff',
        },
      },
    },
    yAxis: {
      label: {
        // 数值格式化为千分位
        formatter: (v: string) => `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`),
      },
    },
    point: {
      size: 5,
      shape: 'diamond',
      style: {
        fill: 'white',
        stroke: '#5B8FF9',
        lineWidth: 2,
      },
    },
    interactions: [
      {
        type: 'marker-active',
      },
    ],
    tooltip: {
      follow: true,
      enterable: true,
      customContent: (name, items) => {
        const container = document.createElement('div');
        container.className = 'g2-tooltip';
        const title = `<div class="g2-tooltip-title" style="margin-top: 12px;margin-bottom: 12px;">${name}</div>`;
        let listItem = '';
        items.forEach((item) => {
          listItem += `<li class="g2-tooltip-list-item" data-index={index} style="margin-bottom:4px;display:flex;align-items: center;">
              <span style="background-color:${
                item?.mappingData?.color || item?.color
              };" class="g2-tooltip-marker"></span>
              <span style="display:inline-flex;flex:1;justify-content:space-between">
              <a style="margin-right: 16px;" href=${item.data.ufoUrl}>${item?.name}:</a><span>${item?.value}ms</span>
              </span>
          </li>`;
        });
        container.innerHTML = title + listItem;
        return container;
      },
    },
  };

  function clickLine(element: any) {
    const target = element.target || {};
    const id = target.id || '';
    const offsetBranch = id.indexOf('rc/');
    const offsetPage = id.indexOf(project?.name === 'fliggy-weixin' ? '微信' : '支付宝');

    let branchName = '';
    if (offsetBranch > -1) {
      branchName = id.slice(offsetBranch);
    }
    const { iterId = '' } = branchList?.find((item) => item.version === branchName) || {};
    iterId ? window.open(`/#/iter/detail?iterId=${iterId}`) : null;

    let pageName = '';
    if (offsetPage > -1) {
      pageName = id.slice(offsetPage);
    }
  }

  return (
    <div onClick={(e) => clickLine(e)}>
      <div className="line-container">
        <Line {...performanceDataConfig}></Line>
      </div>
    </div>
  );
};

export default HistoryPerformance;
