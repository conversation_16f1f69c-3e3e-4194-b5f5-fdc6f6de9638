import { useState, useEffect } from 'react';
import { Table, Alert } from 'antd';
import { SortOrder } from 'antd/es/table/interface';
import { IUrl } from '@/interface/url';
import ProjectSelect from '@/components/ProjectSelect';
import { IProject } from '@/interface/project';
import urlApi from '@/api/url';

import './index.less';

const defaultColumns = [
  {
    title: '序号',
    dataIndex: 'key',
    width: '10%',
  },
  {
    title: '小程序页面地址',
    dataIndex: 'pagePath',
    sorter: (a: IUrl, b: IUrl) => a.h5Url.length - b.h5Url.length,
    sortDirections: ['descend', 'ascend'] as SortOrder[],
    width: '25%',
  },
  {
    title: 'h5地址',
    dataIndex: 'h5Url',
    sorter: (a: IUrl, b: IUrl) => a.h5Url.length - b.h5Url.length,
    sortDirections: ['descend', 'ascend'] as SortOrder[],
    width: '45%',
  },
];

const versionColumn = {
  title: '最低使用版本',
  dataIndex: 'version',
  width: '20%',
}

export default () => {
  const [urlList, setUrlList] = useState<IUrl[]>([]);
  const [loading, setLoading] = useState(true);
  const [columns, setColumns] = useState(defaultColumns);
  const [project, setProject] = useState<IProject>();

  useEffect(() => {
    setUrlList([])
    if (project?.name === 'fliggy-allinone') {
      setColumns([...defaultColumns, versionColumn])
    } else {
      setColumns(defaultColumns)
    }
    fetch()
  }, [project?.name])

  function fetch() {
    if (!project?.name) return;
    setLoading(true);

    urlApi.list({ projectName: project.name }).then(res => {
      if (!res || !res.data) throw Error('empty');
      setUrlList(res.data)
    })
      .catch(err => {
        setUrlList([])
      })
      .finally(() => setLoading(false))
  }

  function onProjectSelect(project: IProject) {
    setProject(project)
  }

  return (
    <div className="helper-url-container">
      <ProjectSelect onSelect={onProjectSelect} style={{ margin: '18px 0' }} />

      <Table
        loading={loading}
        columns={columns}
        dataSource={urlList} />
    </div>
  );
}
