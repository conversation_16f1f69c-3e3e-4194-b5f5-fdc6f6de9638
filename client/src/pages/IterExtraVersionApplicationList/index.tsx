import { useState, useEffect } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { <PERSON><PERSON>, PageHeader, Space, message, Table, Avatar } from 'antd';
import { LinkOutlined, PauseCircleOutlined, LoadingOutlined, CheckCircleOutlined, CloseCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { IIterApplication } from '@/interface/iter-application';
import IterApplicationApi from '@/api/iter-application';
import { AlignType } from 'rc-table/es/interface';
import { EProcessInstanceStatus } from '@/const/bpms';
import CreateApplicationBtn from './components/CreateApplicationBtn';

import './index.less';

const PAGE_SIZE = 10;

export default () => {
  const [refreshTag, setRefreshTag] = useState<number>();
  const [loading, setLoading] = useState(true);
  const [pageNum, setPageNum] = useState(1);
  const [pageTotal, setPageTotal] = useState(0);
  const [dataSource, setDataSource] = useState<IDataSource[]>([])
  const columns = [
    {
      title: '状态',
      dataIndex: 'statusText',
      align: 'center' as AlignType,
      render: (statusText: string, record: IDataSource) => {
        let icon;
        switch (record.status) {
          case EProcessInstanceStatus.NEW: icon = <PauseCircleOutlined style={{ color: '#fa8c16' }} className="status-icon" />; break;
          case EProcessInstanceStatus.RUNNING: icon = <LoadingOutlined style={{ color: '#1890ff' }} className="status-icon" />; break;
          case EProcessInstanceStatus.AGREE: icon = <CheckCircleOutlined style={{ color: '#5b8c00' }} className="status-icon" />; break;
          case EProcessInstanceStatus.DISAGREE:
          case EProcessInstanceStatus.TERMINATED:
          case EProcessInstanceStatus.ERROR:
            icon = <CloseCircleOutlined style={{ color: '#f5222d' }} className="status-icon" />; break;
        }
        return <div className='status'>{icon}{statusText}</div>
      },
    },
    {
      title: '申请标题',
      dataIndex: 'title',
      align: 'center' as AlignType,
      render: (title: string) => <div style={{ textAlign: 'left' }}>{title}</div>
    },
    {
      title: '申请人',
      key: 'applicant',
      align: 'center' as AlignType,
      render: (_title: string, record: IDataSource) => {
        if (record.bucUser) {
          return <Space><Avatar size="small" src={`https://work.alibaba-inc.com/photo/${record.bucUser.empId}.220x220.jpg`} alt="avatar" />{record.bucUser.nickNameCn}</Space>
        }
        return '未知'
      }
    },
    {
      title: '申请时间',
      dataIndex: 'gmtCreate',
      align: 'center' as AlignType,
    },
    {
      title: '申请详情',
      dataIndex: 'url',
      align: 'center' as AlignType,
      render: (url: string) => <a href={url} target="_blank"><LinkOutlined style={{ fontSize: '18px' }} /></a>,
    },
  ];

  useEffect(() => {
    setDataSource([]);
    fetchIterApplicationList();
  }, [pageNum, refreshTag])

  function setRefresh() {
    setRefreshTag(Date.now());
  }

  function fetchIterApplicationList() {
    setLoading(true);

    IterApplicationApi.listExtraVersion({
      pageNum,
      pageSize: PAGE_SIZE
    }).then(res => {
      if (!res.data) throw Error(res.errorMsg || '获取加版申请列表失败');

      unstable_batchedUpdates(() => {
        setLoading(false);
        setDataSource(res.data.list.map(item => ({ key: item.processInstanceId, ...item })));
        setPageTotal(res.data.total);
      })
    }).catch((err) => {
      message.error(err.message || '加载失败');

      unstable_batchedUpdates(() => {
        setDataSource([]);
        setLoading(false);
      })
    })
  }


  return <div className="iter-application-list-container">
    <PageHeader
      className="page-header"
      title="加版申请"
      extra={
        <Space className="actions">
          <CreateApplicationBtn
            onCreateEnd={() => setRefresh()}
          >
            <Button type="primary">新建申请</Button>
          </CreateApplicationBtn>
        </Space>
      }
    />

    <Table
      className="list"
      loading={loading}
      columns={columns}
      dataSource={dataSource}
      pagination={{
        current: pageNum,
        pageSize: PAGE_SIZE,
        total: pageTotal,
        onChange: (pageNum) => setPageNum(pageNum)
      }} />
  </div >
};

interface IDataSource extends IIterApplication {
  key: string;
}