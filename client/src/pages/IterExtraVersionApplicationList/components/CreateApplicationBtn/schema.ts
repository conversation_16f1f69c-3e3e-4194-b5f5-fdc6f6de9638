export default {
  "type": "object",
  "properties": {
    "projectName": {
      "title": "项目名称",
      "type": "string",
      "required": true,
      "enum": ['fliggy-allinone', 'fliggy-weixin', 'fliggy-bytedance'],
      "enumNames": ['支付宝一体化小程序', '飞猪微信小程序', '飞猪字节小程序'],
      "widget": "select",
    },
    "reason": {
      "title": "加版原因",
      "type": "string",
      "widget": "textarea",
      "required": true
    },
    "effect": {
      "title": "不加版的影响",
      "type": "string",
      "widget": "textarea",
      "required": true
    },
    "publishDay": {
      "title": "期望发版日期",
      "type": "string",
      "widget": "date",
      "required": true
    }
  },
  "labelWidth": 120,
  "displayType": "row"
};
