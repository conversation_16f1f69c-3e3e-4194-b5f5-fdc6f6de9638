import { useState } from 'react';
import { Modal, message, Spin } from 'antd';
import { useForm } from 'form-render';
import { IIterApplication } from '@/interface/iter-application';
import FormRender from '@/components/FR';
import IterApplicationApi from '@/api/iter-application';
import schema from './schema';

import './index.less';

interface Props {
  onCreateEnd?: (data: IIterApplication) => void;
}

const Comp: React.FC<Props> = ({ children, onCreateEnd }) => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const form = useForm();

  const onFormSubmit = () => {
    form.submit();
  };

  const onFormCancel = () => {
    form.resetFields();
    setShowModal(false);
  };

  const onFormFinish = (formData: any, errors: any) => {
    if (errors.length > 0) return;

    setLoading(true);

    IterApplicationApi.applyExtraVersion({
      projectName: formData.projectName,
      reason: formData.reason,
      effect: formData.effect,
      publishDay: formData.publishDay
    })
      .then(res => {
        if (res?.success) {
          message.info('创建加版申请成功');
          setShowModal(false);
          setTimeout(() => form.resetFields(), 500); // 迟一点清空，省得闪一下
          onCreateEnd && onCreateEnd(res.data);
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '创建失败');
      })
      .finally(() => setLoading(false));
  }

  return <>
    <div onClick={() => setShowModal(true)}>
      {children}
    </div>

    <Modal
      title="提交加版申请"
      open={showModal}
      onOk={onFormSubmit}
      onCancel={onFormCancel}
      maskClosable={false}
      okButtonProps={{ disabled: form.errorFields?.length > 0 }}
      width={800}
      destroyOnClose
    >
      <Spin spinning={loading} wrapperClassName="form-wrapper">
        <FormRender
          form={form}
          schema={schema}
          removeHiddenData={false}
          onFinish={onFormFinish}
          displayType="row" />
      </Spin>
    </Modal>
  </>
}

export default Comp;
