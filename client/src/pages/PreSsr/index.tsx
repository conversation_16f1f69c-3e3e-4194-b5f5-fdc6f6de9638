import { useEffect, useState, useRef } from 'react';
import { AndroidFilled, AppleFilled, AppstoreFilled } from '@ant-design/icons';
import moment from 'moment';
import {
  Col, Row, Card, Spin, Progress, Tag, Space,
  Tooltip, Pagination, PageHeader, Button, DatePicker, Statistic
} from 'antd';
import { get } from 'lodash';
import ProCard from '@ant-design/pro-card';
import { Area, Line } from '@ant-design/plots';
import ssrDetail from '@/api/ssr-detail';

import './index.less';

export default () => {
  const [spinning, setSpinning] = useState(true);
  const [targetList, setTargetList] = useState<any>([]);
  const [sourceList, setSourceList] = useState([]);
  const [targetNum, setTargetNum] = useState(1);
  const [sourceNum, setSourceNum] = useState(1);
  const [loading, setLoading] = useState(false);
  const [detail, setDetail] = useState<any>({});
  const [time, setTime] = useState<any>(null);
  const [date, setDate] = useState<any>(moment().subtract(1, 'day'));

  const TYPE_MAP: any = {
    "target": "目标页来源",
    "source": "触发页去向"
  }

  useEffect(() => {
    getData();
  }, [])

  const getData = () => {
    setSpinning(true);
    ssrDetail.queryPressr({}).then(res => {
      setTargetList(get(res, 'data.target', []));
      setSourceList(get(res, 'data.source', []))
      setSpinning(false);
    }).catch(e => {
      setSpinning(false);
    })
  }

  const handleClick = (data: any, type: any, time?: any) => {
    if (loading) return;
    setLoading(true);
    if (!time) {
      setTime(null);
    };
    ssrDetail.queryPressrDetail({
      ...data,
      type,
      time
    }).then(res => {
      setLoading(false);
      const chartConfig = {
        data: res.data.data || [],
        xField: 'time',
        yField: 'count',
        seriesField: type === 'target' ? 'source' : 'page',
        meta: {
          count: {
            alias: 'QPS'
          }
        },
        yAxis: {
          rate: {
            min: 0,
          }
        }
      }
      setDetail({ ...res.data, chartConfig });
    })
  }

  const timeChange = (moment: any, dateStr: any) => {
    setTime(moment);
    handleClick(detail.params, detail.params.type, dateStr)
  }

  const dateChange = (moment: any, dateStr: any) => {
    setDate(moment);
    console.log(moment, dateStr)
  }

  const getUrl = (group: any, page: any) => {
    if (group === 'pcraft') {
      return `https://outfliggys.m.taobao.com/wow/pone/${group}/${page}`;
    }
    return `https://outfliggys.m.taobao.com/app/trip/${group}/pages/${page}`;
  }

  const renderContent = (list: any, type: any) => {
    return (
      <>
        <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
          {
            list.map((item: any, index: any) => {
              if ((type === 'target' && (index + 1) <= targetNum * 10 && (index + 1) > (targetNum - 1) * 10) ||
                (type === 'source' && (index + 1) <= sourceNum * 10 && (index + 1) > (sourceNum - 1) * 10)) {
                return (
                  <Tooltip title={type === 'target' ?
                    `${getUrl(item.__topic__, item.__source__)}` : item.source}>
                    <div className='row' onClick={() => handleClick(item, type)}>
                      <Tag color="#87d068">{index + 1}</Tag>
                      <div className='title'>{type === 'target' ? `${item.__topic__}/${item.__source__}` : item.source}</div>
                      <Progress
                        percent={(item.count / list[0].count) * 100}
                        format={() => { return (item.count / 60 / 10).toFixed(2) }}
                      />
                    </div>
                  </Tooltip>
                )
              }
            })
          }
        </Space>
        <div className='page-wrap'>
          {list.length > 10 ? <Pagination
            size="small"
            defaultCurrent={1}
            total={list.length}
            showSizeChanger={false}
            onChange={(e) => {
              if (type === 'target') {
                setTargetNum(e);
              } else {
                setSourceNum(e);
              }
            }}
          /> : null}
        </div>
      </>
    )
  }

  const renderDetail = () => {
    if (detail?.params?.__source__ || detail?.params?.source) {
      const title = detail?.params?.type === 'target' ? getUrl(detail?.params?.__topic__, detail?.params?.__source__) : detail?.params?.source
      return (
        <Card
          title={`${TYPE_MAP[detail?.params?.type]}QPS详情：${title}`}
          extra={
            <DatePicker
              showTime
              onChange={timeChange}
              value={time}
              placeholder="10分钟内数据"
              disabledDate={(current) => { return current && (current > moment().endOf('day') || current < moment().subtract(29, 'day').startOf('day'))}}
            />}
        >
          <Row gutter={12}>
            <Col span={24}>
              <Line
                {...detail.chartConfig}
              />
            </Col>
          </Row>
        </Card>
      )
    }
  }

  const renderInfo = () => {
    if (detail?.params?.__source__) {
      return (
        <Card
          title="预加载信息（T+1）"
          extra={
            <DatePicker
              onChange={dateChange}
              value={date}
              disabledDate={(current) => { return current && (current > moment().subtract(1, 'day').endOf('day') || current < moment().subtract(30, 'day').startOf('day'))}}
            />
          }
        >
          <Row gutter={12}>
            <Col span={4}>
              <Card>
                <Statistic title="资源利用率" prefix={<AppstoreFilled style={{ color: 'orange' }} />} value={9.3} precision={2} suffix="%" />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic title="资源利用率" prefix={<AppleFilled style={{ color: 'gray' }} />} value={99.3} precision={2} suffix="%" />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic title="资源利用率" prefix={<AndroidFilled style={{ color: 'green' }} />} value={99.3} precision={2} suffix="%" />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic title="预加载命中率" prefix={<AppstoreFilled style={{ color: 'orange' }} />} value={99.3} precision={2} suffix="%" />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic title="预加载命中率" prefix={<AppleFilled style={{ color: 'gray' }} />} value={99.3} precision={2} suffix="%" />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic title="预加载命中率" prefix={<AndroidFilled style={{ color: 'green' }} />} value={99.3} precision={2} suffix="%" />
              </Card>
            </Col>
          </Row>
        </Card>
      )
    }

  }

  return (
    <>
      <PageHeader
        title="预加载监控大盘"
        style={{ backgroundColor: '#ffffff', marginBottom: '12px' }}
        extra={[
          <Button loading={spinning} onClick={getData}>刷新</Button>
        ]}
      />
      <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
        <Spin spinning={spinning}>
          <Row gutter={12}>
            <Col span={12}>
              <Card
                title="最近10分钟目标页QPS排行"
                extra="点击可查看单页面详情"
                bodyStyle={{ minHeight: '450px' }}
              >
                {targetList.length > 0 ?
                  renderContent(targetList, 'target') :
                  <span>{!spinning ? '暂无数据' : ''}</span>
                }
              </Card>
            </Col>
            <Col span={12}>
              <Card
                title="最近10分钟触发页任务QPS排行"
                extra="点击可查看单页面详情"
                bodyStyle={{ minHeight: '450px' }}
              >
                {sourceList.length > 0 ?
                  renderContent(sourceList, 'source') :
                  <span>{!spinning ? '暂无数据' : ''}</span>
                }
              </Card>
            </Col>
          </Row>
        </Spin>

        <Spin spinning={loading}>
          <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
            {/* {renderInfo()} */}
            <Row>
              <Col span={24}>
                {renderDetail()}
              </Col>
            </Row>
          </Space>
        </Spin>
      </Space>
    </>
  )
}
