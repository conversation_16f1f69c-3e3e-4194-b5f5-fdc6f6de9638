import { Image, Typography, Divider } from 'antd';

import './index.less';

const { Title, Text, Link } = Typography;

export default () => {
  return (
    <div className='atom-container'>
      <Title>Atom调试工具</Title>
      <Link href='https://aliyuque.antfin.com/remtwr/cvk271/yo3dnz5r6qlgb1c6'>使用说明书</Link>
      <Text>触发方式: 在支、微小程序首页左上角"飞猪旅行"快速点击6次触发扫码</Text>
      <Divider />
      <Image
        width={200}
        src="https://gw.alicdn.com/imgextra/i3/O1CN01BZGv0J1hLaZ5AuuVQ_!!6000000004261-1-tps-116-116.gif"
      />
      <Text>二维码</Text>
    </div>
  );
}
