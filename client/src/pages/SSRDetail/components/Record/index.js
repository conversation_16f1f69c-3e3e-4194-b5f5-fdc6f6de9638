import { useEffect, useState, useRef } from 'react';
import { ProTable } from '@ant-design/pro-components';
import { Tag } from 'antd';
import ssrDetail from '@/api/ssr-detail';
import { gerUrlParam } from '@/utils/base';

export default() => {
  const tableRef = useRef();

  const COLOR_MAP = {
    1: 'green',
    2: 'orange',
    3: 'gray',
    4: 'red'
  }
  const TYPE_MAP = {
    1: '预发',
    2: '灰度',
    3: '取消灰度',
    4: '线上'
  }

  const columns = [{
    title: '发布时间',
    dataIndex: 'gmt_create',
    hideInSearch: true,
  }, {
    title: '操作人',
    dataIndex: 'operator',
    hideInSearch: true,
  },  {
    title: '版本号',
    dataIndex: 'version'
  }, {
    title: '发布类型',
    dataIndex: 'publish_type',
    valueEnum: TYPE_MAP,
    render(text, record) {
      const publish_type = record.publish_type || '';
      return (
        <Tag color={COLOR_MAP[publish_type]}>{TYPE_MAP[publish_type]}</Tag>
      )
    }
  }, {
    title: '灰度比例',
    dataIndex: 'gray_ratio',
    hideInSearch: true,
    render(text, record) {
      return (
        <div>{record.publish_type === 2 ? text + '%' : '-'}</div>
      )
    }
  }]

  return (
    <>
    <ProTable
      rowKey="id"
      search={true}
      pagination={{
        showQuickJumper: true,
        pageSize: 10
      }}
      columns={columns}
      actionRef={tableRef}
      request={async (params, sorter) => {
        const resData = await ssrDetail.logList({
          ...params,
          id: gerUrlParam('id')
        }).then(res => {
          if (res.success && res.data) {
            return res.data
          }
        }).catch(e => {})
        return resData;
      }}
    />
    </>
  )
}