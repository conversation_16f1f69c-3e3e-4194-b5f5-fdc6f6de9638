.ssr-monitor-container {
  padding-bottom: 24px;
  min-height: 600px;

  .ssr-search {
    padding: 24px;
    padding-bottom: 12px;
    background-color: #ffffff;
  }

  .list {
    margin: 18px 24px 0;
  }

  .ssr-monitor-title-wrap {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .ssr-monitor-title-button {
    margin-right: 24px;
  }

  .ssr-monitor-title {
    padding: 0 24px;
  }

  .ssr-monitor-pd32 {
    padding: 0 32px; 
  }

  .ssr-monitor-content {
    background-color: #ffffff;
    margin-top: 12px;
    padding: 12px 0;

    .ssr-monitor-arms-container {
      // background-color: #f3f3f3;
      border: 1px solid transparent;
      padding: 12px;
      padding-bottom: 0;
      border-top: 4px solid transparent;
      border-bottom: 1px solid #f3f3f3;
      cursor: pointer;
      background-color: rgba(0,0,0,0.01);
      border-radius: 12px;

      .arms-title {
        font-size: 12px;
        font-weight: normal;
        color: gray;
        margin-left: 6px;
      }
    }

    .arms-selected {
      border-top: 4px solid orange;
    }

    .ssr-monitor-arms {
      display: flex;
      flex-direction: row;
      overflow-x: scroll;
      padding-bottom: 12px;
    }

    .ssr-monitor-arms-item {
      display: flex;
      flex: 1;
      flex-direction: column;
      margin-right: 6px;
      min-width: 120px;

      .arms-label {
        color: gray;
      }

      .arms-value {
        font-size: 24px;
        font-weight: bold;
      }

      .arms-suf {
        font-size: 16px;
        font-weight: normal;
      }
    }

    .ssr-monitor-arms-item-selected {
      border-left: 4px solid orange;
    }
  }
}