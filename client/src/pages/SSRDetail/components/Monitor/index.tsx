import { useEffect, useState } from 'react';
import { Button, Form,Select, Row, Col, Input, DatePicker, message, Spin, Statistic, Empty, Table, Tooltip } from 'antd';
import { get } from 'lodash'
import { Area, Line } from '@ant-design/plots';
import ProCard from '@ant-design/pro-card';
import SSRDetailApi from '@/api/ssr-detail';
import moment from 'moment';
import { gerUrlParam } from '@/utils/base';
import { formateDate } from '@/utils/date';
import Alarm from '../Alarm';
import './index.less';

const { RangePicker } = DatePicker;
const timeFormatter = (timestamp: any, duration: any) => {
  // 7天内按小时维度统计
  if (duration <= 604800000) {
    return moment(timestamp).format('MM-DD HH:mm');
  }
  // 30天内按照天维度统计
  return moment(timestamp).format('YYYY-MM-DD');
};

const TITLE_MAP = ['QPS', '错误率']

export default (props: any) => {
  const [loading, setLoading] = useState(false);
  const [chartConfig, setChartConfig] = useState<any>([]);
  const [disabledChange, setDisableChange] = useState(false);
  const [armsData, setArmsData] = useState<any>(null);
  const [errorList, setErrorList] = useState<any>([]);
  const [armsSelectedIndex, setArmsSelectedIndex] = useState(-1);
  const [armsLoading, setArmsLoading] = useState(false);
  const [detailChart, setDetailChart] = useState<any>({});
  const [rtLineChart, setRTLineChart] = useState<any>({});
  const [showAlarmPad, setShowAlarmPad] = useState({
    show: false,
    data: props.pageInfo
  })

  const [form] = Form.useForm();

  const ARMS_MAP = [{
    key: 'total',
    label: '整体'
  }, {
    key: 'lx',
    label: '飞猪'
  }, {
    key: 'tb',
    label: '淘宝'
  }, {
    key: 'ap',
    label: '支付宝'
  }, {
    key: 'other',
    label: '其他'
  }];

  const ERROR_COLUMNS = [{
    title: '时间',
    dataIndex: '__time__',
    key: '__time__',
    fixed: 'left',
    render: text => <span>{formateDate('YYYY-MM-DD hh:mm:ss', new Date(parseInt(text) * 1000))}</span>,
  }, {
    title: 'traceId',
    dataIndex: 'traceId',
    key: 'traceId',
    fixed: 'left'
  }, {
    title: '错误分类',
    dataIndex: 'errorCode',
    key: 'errorCode',
    filters: [
      {
        text: 'TIMEOUT',
        value: 'TIMEOUT',
      },
      {
        text: 'RENDER_ERROR',
        value: 'RENDER_ERROR',
      },
      {
        text: 'BUNDLE_NOT_FOUND',
        value: 'BUNDLE_NOT_FOUND'
      }
    ],
    onFilter: (value: string, record:any) => record.errorCode && record.errorCode.indexOf(value) === 0
  },  {
    title: '错误详情',
    dataIndex: 'errorMsg',
    key: 'errorMsg'
  }, {
    title: '用户ID',
    dataIndex: 'userId',
    key: 'userId'
  }, {
    title: '客户端',
    dataIndex: 'appName',
    key: 'appName',
    width: 100,
    filters: [
      {
        text: 'LX',
        value: 'LX',
      },
      {
        text: 'TB',
        value: 'TB',
      },
      {
        text: 'AP',
        value: 'AP'
      }
    ],
    onFilter: (value: string, record:any) => record.appName && record.appName.indexOf(value) === 0
  }, {
    title: '操作系统',
    dataIndex: 'osName',
    key: 'osName',
    width: 80,
    filters: [
      {
        text: 'IOS',
        value: 'IOS',
      },
      {
        text: 'Android',
        value: 'Android',
      }
    ],
    onFilter: (value: string, record:any) => record.osName && record.osName.indexOf(value) === 0
  }, {
    title: '页面链接',
    dataIndex: 'url',
    key: 'url',
    render: (text:any) => (
      <a onClick={() => {
      const url = text.split('/app');
        navigator.clipboard
      .writeText(`https://outfliggys.m.taobao.com/app${url[1]}`)
      .then(() => {
        message.success('复制成功');
      })
      .catch((err) => {
        message.error(err?.message || '复制失败！');
      });
      }}>复制</a>
    ),
  }]

  useEffect(() => {
    const projectGroup = gerUrlParam('projectGroup') || props.pageInfo.project_group; 
    const projectName = gerUrlParam('projectName') || props.pageInfo.path_project_name || props.pageInfo.project_name;
    const pageName = gerUrlParam('pageName') || props.pageInfo.path_page_name || props.pageInfo.page_name;
    const projectBusiness = gerUrlParam('projectBusiness') || props.pageInfo.project_business;
    const errorLogType = gerUrlParam('errorLogType') || props.pageInfo.errorLogType;
    if (projectName && pageName) {
      const initialForm = {
        projectGroup,
        projectName,
        pageName,
        projectBusiness,
        dateRange: [moment().subtract(31, 'minute'), moment().subtract(1, 'minute')],
        errorLogType: errorLogType || 'ALL'
      }
      form.setFieldsValue(initialForm);
      setLoading(true);
      fetchMonitorData(initialForm);
      handleSelectArms(0);
      setDisableChange(true);
    }
  }, [])

  const fieldsFormatter = {
    rate(v: any) {
      return (v).toFixed(2).replace(/[0]+$/, '').replace(/\.$/, '') + '%';
    },
    duration(v: any) {
      return v.toFixed(2) + 'ms';
    },
    count(v: any) {
      return v;
    },
  };

  // 处理arms数据
  const handleArmsData = (list: any) => {
    if (!list) {
      return;
    }
    const result: any = {
      total: {
        total: 0,
        one: 0,
        avg_t2: 0,
        cache_total: 0,
        cache_avg_t2: 0,
        cache_one_pass_rate: 0,
        cache_rate: 0,
        static_total: 0,
        static_avg_t2: 0,
        static_one_pass_rate: 0,
        static_rate: 0
      },
      lx: {
        total: 0,
        one: 0,
        avg_t2: 0,
        pressr_total: 0,
        pressr_avg_t2: 0,
        pressr_rate: 0,
        pressr_one_pass_rate: 0,
        cache_total: 0,
        cache_avg_t2: 0,
        cache_one_pass_rate: 0,
        cache_rate: 0,
        static_total: 0,
        static_avg_t2: 0,
        static_one_pass_rate: 0,
        static_rate: 0
      },
      tb: {
        total: 0,
        one: 0,
        avg_t2: 0,
        cache_total: 0,
        cache_avg_t2: 0,
        cache_one_pass_rate: 0,
        cache_rate: 0,
        static_total: 0,
        static_avg_t2: 0,
        static_one_pass_rate: 0,
        static_rate: 0
      },
      ap: {
        total: 0,
        one: 0,
        avg_t2: 0,
        cache_total: 0,
        cache_avg_t2: 0,
        cache_one_pass_rate: 0,
        cache_rate: 0,
        static_total: 0,
        static_avg_t2: 0,
        static_one_pass_rate: 0,
        static_rate: 0
      },
      other: {
        total: 0,
        one: 0,
        avg_t2: 0,
        cache_total: 0,
        cache_avg_t2: 0,
        cache_one_pass_rate: 0,
        cache_rate: 0,
        static_total: 0,
        static_avg_t2: 0,
        static_one_pass_rate: 0,
        static_rate: 0
      }
    }
    // 端外&总数据
    for (const i in list) {
      const cTotal = parseInt(list[i].cache_count);
      const cT2 = list[i].cache_avg_t2 === 'null' ? 0 : parseFloat(list[i].cache_avg_t2);
      const cOne = list[i].cache_one_pass_rate === 'NaN' ? 0 : parseFloat(list[i].cache_one_pass_rate);
      const cRate = parseInt(list[i].cache_count) / parseInt(list[i].count) * 100;
      const sTotal = parseInt(list[i].static_count);
      const sT2 = list[i].static_avg_t2 === 'null' ? 0 : parseFloat(list[i].static_avg_t2);
      const sOne = list[i].static_one_pass_rate === 'NaN' ? 0 : parseFloat(list[i].static_one_pass_rate);
      const sRate = parseInt(list[i].static_count) / parseInt(list[i].count) * 100;
      if (result[list[i].detector_app]) {
        result[list[i].detector_app] = {
          ...list[i],
          pressr_total: parseInt(list[i].pressr_count),
          pressr_avg_t2: list[i].pressr_avg_t2 === 'null' ? 0 : parseFloat(list[i].pressr_avg_t2),
          pressr_rate: parseInt(list[i].pressr_count) / parseInt(list[i].count) * 100,
          pressr_one_pass_rate: list[i].pressr_one_pass_rate === 'NaN' ? 0 : parseFloat(list[i].pressr_one_pass_rate),
          cache_total: cTotal,
          cache_avg_t2: cT2,
          cache_rate: cRate,
          cache_one_pass_rate: cOne,
          static_total: sTotal,
          static_avg_t2: sT2,
          static_rate: sRate,
          static_one_pass_rate: sOne
        }
      } else {
        result.other.total = result.other.total + parseInt(list[i].count);
        result.other.avg_t2 = result.other.avg_t2 + parseFloat(list[i].avg_t2) * parseInt(list[i].count);
        result.other.one = result.other.one + parseInt(list[i].count) * parseFloat(list[i].one_pass_rate) / 100;
        result.other.cache_total = result.other.cache_total + cTotal;
        result.other.cache_avg_t2 = result.other.cache_avg_t2 + cT2 * cTotal;
        result.other.cache_one_pass_rate = result.other.cache_one_pass_rate + cTotal * cOne / 100;
        result.other.static_total = result.other.static_total + sTotal;
        result.other.static_avg_t2 = result.other.static_avg_t2 + sT2 * sTotal;
        result.other.static_one_pass_rate = result.other.static_one_pass_rate + sTotal * sOne / 100;
      }
      result.total.total = result.total.total + parseInt(list[i].count);
      result.total.avg_t2 = result.total.avg_t2 + parseFloat(list[i].avg_t2) * parseInt(list[i].count);
      result.total.one = result.total.one + parseInt(list[i].count) * parseFloat(list[i].one_pass_rate) / 100;
      result.total.cache_total = result.total.cache_total + cTotal;
      result.total.cache_avg_t2 = result.total.cache_avg_t2 + cT2 * cTotal;
      result.total.cache_one_pass_rate = result.total.cache_one_pass_rate + cTotal * cOne / 100;
      result.total.static_total = result.total.static_total + sTotal;
      result.total.static_avg_t2 = result.total.static_avg_t2 + sT2 * sTotal;
      result.total.static_one_pass_rate = result.total.static_one_pass_rate + sTotal * sOne / 100;
    }
    result.other = {
      one_pass_rate: result.other.total > 0 ? result.other.one / result.other.total * 100 : 0,
      count: result.other.total,
      avg_t2: result.other.total > 0 ? result.other.avg_t2 / result.other.total : 0,
      cache_one_pass_rate: result.other.cache_total > 0 ? result.other.cache_one_pass_rate / result.other.cache_total * 100 : 0,
      cache_rate: result.other.cache_total > 0 ? result.other.cache_total / result.other.total * 100 : 0,
      cache_avg_t2: result.other.cache_total > 0 ? result.other.cache_avg_t2 / result.other.cache_total : 0,
      static_one_pass_rate: result.other.static_total > 0 ? result.other.static_one_pass_rate / result.other.static_total * 100 : 0,
      static_rate: result.other.static_total > 0 ? result.other.static_total / result.other.total * 100 : 0,
      static_avg_t2: result.other.static_total > 0 ? result.other.static_avg_t2 / result.other.static_total : 0
    }
    result.total = {
      one_pass_rate: result.total.total > 0 ? result.total.one / result.total.total * 100 : 0,
      count: result.total.total,
      avg_t2: result.total.total > 0 ? result.total.avg_t2 / result.total.total : 0,
      cache_one_pass_rate: result.total.cache_total > 0 ? result.total.cache_one_pass_rate / result.total.cache_total * 100 : 0,
      cache_rate: result.total.cache_total > 0 ? result.total.cache_total / result.total.total * 100 : 0,
      cache_avg_t2: result.total.cache_total > 0 ? result.total.cache_avg_t2 / result.total.cache_total : 0,
      static_one_pass_rate: result.total.static_total > 0 ? result.total.static_one_pass_rate / result.total.static_total * 100 : 0,
      static_rate: result.total.static_total > 0 ? result.total.static_total / result.total.total * 100 : 0,
      static_avg_t2: result.total.static_total > 0 ? result.total.static_avg_t2 / result.total.static_total : 0
    }

    // 分端总数据
    for (const i in result) {
      result[i].total = parseInt(result[i].count || 0);
      if (result[i].total > 0) {
        result[i].one_pass_rate = parseFloat(result[i].one_pass_rate || 0)
        result[i].avg_t2 = parseFloat(result[i].avg_t2 || 0)
      } else {
        result[i].one_pass_rate = 0;
        result[i].avg_t2 = 0;
      }
    }
    console.log('result----', result)
    return result;
  }

  const handleSearch = () => {
    setChartConfig([])
    form
      .validateFields()
      .then((formRes: any) => {
        setLoading(true);
        fetchMonitorData(formRes);
      }).catch((err: any) => {
        setLoading(false);
        message.error('请输入必填项');
      });
  }

  const fetchMonitorData = (formRes: any) => {
    Promise.all([
      SSRDetailApi.monitorDetail({
        sunfirePluginId: 15064,
        projectGroup: formRes.projectGroup,
        projectName: formRes.projectName,
        pageName: formRes.pageName,
        projectBusiness: props.pageInfo.project_business,
        from: formRes.dateRange[0].valueOf(),
        to: formRes.dateRange[1].valueOf(),
        errorLogType: formRes.errorLogType,
        pid: props.pageInfo.pid,
        onlyArms: false
      }),
      // 获取P50
      ...[{id: 11041, name: 'ER-均值RT'},{id: 11066, name: 'ER-RT-P95'},{id: 11065, name: 'ER-RT-P90'},{id: 11032, name: 'P90-RT'},{id: 11031, name: 'P95-RT'}].map(r=>{
        return SSRDetailApi.monitorDetail({
          sunfirePluginId: r.id,
          projectGroup: formRes.projectGroup,
          projectName: formRes.projectName,
          pageName: formRes.pageName,
          projectBusiness: props.pageInfo.project_business,
          from: formRes.dateRange[0].valueOf(),
          to: formRes.dateRange[1].valueOf(),
          errorLogType: formRes.errorLogType,
          pid: props.pageInfo.pid,
          onlyArms: false
        }).then(res=>{
          return {name: r.name, ...res}
        })
      })
      
    ]).then(res=>{
      // 整体汇总
        const chartData = get(res, '0.data.sunfireRes', []).map((item: any, index: any) => {
          return {
            ...item,
            rate: Math.floor((1 - item.rate) * 10000) / 10000 * 100,
            time: timeFormatter(item.timestamp, formRes.dateRange[1].valueOf() - formRes.dateRange[0].valueOf())
          }
        })
        const newChartConfig1 = {
          data: chartData,
          xField: 'time',
          yField: 'count',
          meta: {
            count: {
              alias: 'QPS',
              formatter: fieldsFormatter['count']
            }
          },
          yAxis: {
            rate: {
              min: 0,
            }
          }
        }
        const newChartConfig2 = {
          data: chartData,
          xField: 'time',
          yField: 'duration',
          meta: {
            duration: {
              alias: 'RT',
              formatter: fieldsFormatter['duration']
            }
          },
          yAxis: {
            rate: {
              min: 0,
            }
          }
        }
        const newChartConfig3 = {
          data: chartData,
          xField: 'time',
          yField: 'rate',
          meta: {
            rate: {
              alias: '错误率',
              formatter: fieldsFormatter['rate']
            }
          },
          yAxis: {
            rate: {
              min: 0,
            }
          }
        }
        const armsRes = handleArmsData(get(res, '0.data.armsRes.body'));
        const errorRes = get(res, '0.data.errorList', []);

        const chartDataArrP: any = []
        res.forEach((element:any, index) => {
          if(index){
            chartDataArrP.push({
              data: get(res, `${index}.data.sunfireRes`, []).map((item:any)=> {
                return {...item, time: timeFormatter(item.timestamp, formRes.dateRange[1].valueOf() - formRes.dateRange[0].valueOf()),}
              }),
              xField: 'time',
              yField: 'duration',
              meta: {
                duration: {
                  alias: element.name,
                  formatter: fieldsFormatter['duration']
                }
              },
              yAxis: {
                rate: {
                  min: 0,
                }
              }
            })
          }
        });
        setChartConfig([newChartConfig1, newChartConfig3]);

        setRTLineChart({
          data: handleRtDetail([newChartConfig2, chartDataArrP[1], chartDataArrP[2], chartDataArrP[3], chartDataArrP[4], chartDataArrP[0]]),
          xField: 'time',
          yField: 'value',
          seriesField: 'type',
          meta: {},
          yAxis: {
            value: {
              min: 0
            }
          }
        });
        setArmsData(armsRes);
        setErrorList(errorRes);
        setLoading(false);
    }).catch(err => {
      setLoading(false);
    })
    // 其他P-RT

  }

  const handleArmsDetail = (list: any, index: number) => {
    if (!list) {
      return [];
    }
    let result: any = [];
    for (let i in list) {
      result.push({
        type: 'ER缓存命中率',
        time: list[i].time.split(' ')[0],
        value: !list[i].static_avg_t2 || list[i].static_avg_t2 === 'null' ? 0 : parseFloat(list[i].static_rate || 0)
      })
      result.push({
        type: 'ER缓存t2均值',
        time: list[i].time.split(' ')[0],
        value: !list[i].static_avg_t2 || list[i].static_avg_t2 === 'null' ? 0 : parseFloat(list[i].static_avg_t2 || 0)
      })
      result.push({
        type: 'ER缓存秒开率',
        time: list[i].time.split(' ')[0],
        value: !list[i].static_one_pass_rate || list[i].static_one_pass_rate === 'NaN' ? 0 : parseFloat(list[i].static_one_pass_rate || 0)
      })
      result.push({
        type: 'ER预加载命中率',
        time: list[i].time.split(' ')[0],
        value: !list[i].cache_avg_t2 || list[i].cache_avg_t2 === 'null' ? 0 : parseFloat(list[i].cache_rate || 0)
      })
      result.push({
        type: 'ER预加载t2均值',
        time: list[i].time.split(' ')[0],
        value: !list[i].cache_avg_t2 || list[i].cache_avg_t2 === 'null' ? 0 : parseFloat(list[i].cache_avg_t2 || 0)
      })
      result.push({
        type: 'ER预加载秒开率',
        time: list[i].time.split(' ')[0],
        value: !list[i].cache_one_pass_rate || list[i].cache_one_pass_rate === 'NaN' ? 0 : parseFloat(list[i].cache_one_pass_rate || 0)
      })
      if (index === 1) {
        result.push({
          type: '端预加载命中率',
          time: list[i].time.split(' ')[0],
          value: !list[i].pressr_avg_t2 || list[i].pressr_avg_t2 === 'null' ? 0 : parseFloat(list[i].pressr_rate || 0)
        })
        result.push({
          type: '端预加载t2均值',
          time: list[i].time.split(' ')[0],
          value: !list[i].pressr_avg_t2 || list[i].pressr_avg_t2 === 'null' ? 0 : parseFloat(list[i].pressr_avg_t2 || 0)
        }) 
        result.push({
          type: '端预加载秒开率',
          time: list[i].time.split(' ')[0],
          value: !list[i].pressr_one_pass_rate || list[i].pressr_one_pass_rate === 'NaN' ? 0 : parseFloat(list[i].pressr_one_pass_rate || 0)
        })
      }
      result.push({
        type: 'SSR t2均值',
        time: list[i].time.split(' ')[0],
        value: parseFloat(list[i].avg_t2 || 0)
      })
      result.push({  
        type: 'SSR秒开率',
        time: list[i].time.split(' ')[0],
        value: parseFloat(list[i].one_pass_rate || 0)
      }) 
    }
    result.sort((a: any, b: any) => {
      if (a.time > b.time) {
        return 1
      }
      return -1
    })
    return result;
  }

  const handleRtDetail = (list: any) => {
    if (!list) {
      return [];
    }
    let result: any = [];
    list[0].data.forEach((element: any, index: number) => {
      result.push({
        type: 'RT-均值',
        time: element.time,
        value: element.duration,
        second: element.timestamp
      })
      result.push({
        type: 'RT-P90',
        time: element.time,
        value: list[3].data.find((r: any)=> r.time === element.time)?.duration,
        second: element.timestamp
      })
      result.push({
        type: 'RT-P95',
        time: element.time,
        value: list[4].data.find((r: any)=> r.time === element.time)?.duration,
        second: element.timestamp
      })
      result.push({
        type: 'ER-均值',
        time: element.time,
        value: list[5].data.find((r: any)=> r.time === element.time)?.duration,
        second: element.timestamp
      })
      result.push({
        type: 'ER-RT-P95',
        time: element.time,
        value: list[1].data.find((r: any)=> r.time === element.time)?.duration,
        second: element.timestamp
      })
      result.push({
        type: 'ER-RT-P90',
        time: element.time,
        value: list[2].data.find((r: any)=> r.time === element.time)?.duration,
        second: element.timestamp
      })
    });
    result.sort((a: any, b: any) => {
      if (a.second > b.second) {
        return 1
      }
      return -1
    })
    return result;
  }

  const handleSelectArms = (index: number) => {
    if (index !== armsSelectedIndex) {
      setArmsLoading(true);
      setArmsSelectedIndex(index);
      const projectGroup = gerUrlParam('projectGroup') || props.pageInfo.project_group;
      const projectName = gerUrlParam('projectName') || props.pageInfo.path_project_name || props.pageInfo.project_name;
      const pageName = gerUrlParam('pageName') || props.pageInfo.path_page_name || props.pageInfo.page_name;
      const projectBusiness = gerUrlParam('projectBusiness') || props.pageInfo.project_business;
      SSRDetailApi.monitorDetail({
        projectGroup,
        projectName,
        pageName,
        projectBusiness,
        pid: props.pageInfo.pid,
        onlyArms: true,
        target: ARMS_MAP[index].key
      }).then(res => {
        const armsRes = handleArmsDetail(get(res, 'data.armsRes.body'), index);
        setDetailChart({
          data: armsRes,
          xField: 'time',
          yField: 'value',
          seriesField: 'type',
          meta: {},
          yAxis: {
            value: {
              min: 0,
            }
          }
        })
        setArmsLoading(false);
      })
    }
  }

  return (
    <div className="ssr-monitor-container">
      <div className="ssr-search">
        <Form form={form}>
          <Row>
            <Col span="6">
              <Form.Item labelCol={{ span: 7 }} wrapperCol={{ span: 16 }} label="项目名称" name="projectName" rules={[{ required: true }]}>
                <Input allowClear disabled={disabledChange} placeholder="项目名称" />
              </Form.Item>
            </Col>
            <Col span="6">
              <Form.Item labelCol={{ span: 7 }} wrapperCol={{ span: 16 }} label="页面名称" name="pageName" rules={[{ required: true }]}>
                <Input allowClear disabled={disabledChange} placeholder="页面名称" />
              </Form.Item>
            </Col>
            <Col span="10">
              <Form.Item labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} label="时间范围" name="dateRange" rules={[{ required: true }]}>
                <RangePicker
                  showTime
                  style={{ width: '100%' }}
                  ranges={{
                    近10分钟: [moment().subtract(11, 'minute'), moment().subtract(1, 'minute')],
                    近1小时: [moment().subtract(1, 'hour'), moment().subtract(1, 'minute')],
                    今天: [moment().startOf('day'), moment().subtract(1, 'minute')],
                    昨天: [moment().subtract(1, 'day').startOf('day'), moment().subtract(1, 'day').endOf('day')],
                    一天内: [moment().subtract(24, 'hour'), moment().subtract(1, 'minute')],

                  }}
                  disabledDate={(current) => {
                    // 不可小于2023-06-12，不可大于今天
                    if (current.isBefore('2023-06-12') || current.isAfter(moment().endOf('day'))) {
                      return true;
                    }
                    return false;
                  }}
                />
              </Form.Item>
            </Col>
            <Col span="6">
              <Form.Item labelCol={{ span: 7 }} wrapperCol={{ span: 16 }} label="错误日志类型" name="errorLogType" rules={[{ required: true }]}>
                  <Select
                    defaultValue="ALL"
                    options={[
                      { value: 'ALL', label: 'ALL' },
                      { value: 'TIMEOUT', label: 'TIMEOUT' },
                      { value: 'NO_CONFIG', label: 'NO_CONFIG' },
                      { value: 'SSR_ERR', label: 'SSR_ERR' },
                      { value: 'DOWNLOAD_ERR', label: 'DOWNLOAD_ERR' },
                      { value: 'RENDER_ERROR', label: 'RENDER_ERROR' },
                      { value: 'BUNDLE_NOT_FOUND', label: 'BUNDLE_NOT_FOUND' },
                      { value: 'PATH_ILLEGAL', label: 'PATH_ILLEGAL' }
                    ]}/>
              </Form.Item>
            </Col>
            <Col span="2" style={{ textAlign: 'right' }}>
              <Button type="primary" disabled={loading} onClick={handleSearch}>查询</Button>
            </Col>
          </Row>
        </Form>
      </div>

      <Spin spinning={loading}>
        <div className='ssr-monitor-content'>
          <h1 className='ssr-monitor-title'>性能大盘</h1>
          {armsData ?
            <ProCard
              gutter={[16, 16]}
              wrap
            >
              {ARMS_MAP.map((armsItem: any, armIndex: any) => {
                return <ProCard
                  ghost
                  colSpan={armIndex === 0 ? 24 : 12}
                >
                  <div
                    className={`ssr-monitor-arms-container ${armIndex === armsSelectedIndex ? 'arms-selected' : ''}`}
                    onClick={() => handleSelectArms(armIndex)}
                  >
                    <h3>{armsItem.label}<span className='arms-title'>{`样本量:${armsData[armsItem.key].total || 0}`}</span></h3>
                    <div className='ssr-monitor-arms'>
                      <div className='ssr-monitor-arms-item'>
                        <Statistic title="秒开率" value={armsData[armsItem.key].one_pass_rate.toFixed(2)} suffix="%" valueStyle={{ fontSize: 20 }} />
                      </div>
                      <div className='ssr-monitor-arms-item'>
                        <Statistic title="t2均值" value={armsData[armsItem.key].avg_t2.toFixed(2)} suffix="ms" valueStyle={{ fontSize: 20 }} />
                      </div>
                      {armIndex === 1 ?
                        <div className='ssr-monitor-arms-item'>
                          <Statistic title="端预加载秒开率" value={armsData[armsItem.key].pressr_one_pass_rate.toFixed(2)} suffix="%" valueStyle={{ fontSize: 20 }} />
                        </div> : null}
                      {armIndex === 1 ?
                        <div className='ssr-monitor-arms-item'>
                          <Statistic title="端预加载t2均值" value={armsData[armsItem.key].pressr_avg_t2.toFixed(2)} suffix="ms" valueStyle={{ fontSize: 20 }} />
                        </div> : null}
                      {armIndex === 1 ?
                        <div className='ssr-monitor-arms-item'>
                          <Statistic title="端预加载命中率" value={armsData[armsItem.key].pressr_rate.toFixed(2)} suffix="%" valueStyle={{ fontSize: 20 }} />
                        </div> : null}
                      <div className='ssr-monitor-arms-item'>
                        <Statistic title="ER预加载秒开率" value={armsData[armsItem.key].cache_one_pass_rate.toFixed(2)} suffix="%" valueStyle={{ fontSize: 20 }} />
                      </div>
                      <div className='ssr-monitor-arms-item'>
                        <Statistic title="ER预加载t2均值" value={armsData[armsItem.key].cache_avg_t2.toFixed(2)} suffix="ms" valueStyle={{ fontSize: 20 }} />
                      </div>
                      <div className='ssr-monitor-arms-item'>
                        <Statistic title="ER预加载命中率" value={armsData[armsItem.key].cache_rate.toFixed(2)} suffix="%" valueStyle={{ fontSize: 20 }} />
                      </div>
                      <div className='ssr-monitor-arms-item'>
                        <Statistic title="ER缓存秒开率" value={armsData[armsItem.key].static_one_pass_rate.toFixed(2)} suffix="%" valueStyle={{ fontSize: 20 }} />
                      </div>
                      <div className='ssr-monitor-arms-item'>
                        <Statistic title="ER缓存t2均值" value={armsData[armsItem.key].static_avg_t2.toFixed(2)} suffix="ms" valueStyle={{ fontSize: 20 }} />
                      </div>
                      <div className='ssr-monitor-arms-item'>
                        <Statistic title="ER缓存命中率" value={armsData[armsItem.key].static_rate.toFixed(2)} suffix="%" valueStyle={{ fontSize: 20 }} />
                      </div>
                    </div>
                  </div>
                </ProCard>
              })}
              {armsSelectedIndex >= 0 ? <ProCard
                ghost
                colSpan={24}
              >
                <ProCard
                  title="最近15天"
                  loading={armsLoading}
                  bordered
                  bodyStyle={{
                    height: '300px',
                    boxSizing: 'content-box'
                  }}
                >
                  <Line
                    key={1}
                    {...detailChart}
                  />
                </ProCard>
              </ProCard> : null}
            </ProCard> : <Empty />}
        </div>

        <div className='ssr-monitor-content'>
          <div className='ssr-monitor-title-wrap'>
            <h1 className='ssr-monitor-title'>稳定性大盘</h1>
            <Button
              className='ssr-monitor-title-button'
              onClick={() => {
                setShowAlarmPad({
                  ...showAlarmPad,
                  show: true
                })
              }}
            >成功率报警订阅</Button>
          </div>
          
          <ProCard
            gutter={[16, 16]}
            wrap
          >
            {Object.keys(rtLineChart).length ? <ProCard
              title="RT值"
              loading={armsLoading}
              bordered
              bodyStyle={{
                height: '300px',
                boxSizing: 'content-box'
              }}
            >
              <Line
                key={1}
                {...rtLineChart}
              />
            </ProCard> : null}
            {chartConfig.map((chartItem: any, index: number) => {
              return (
                <ProCard
                  ghost
                  colSpan={{
                    xs: 24,
                    sm: 24,
                    md: 24,
                    lg: index === 2 ? 24 : 12,
                    xl: index === 2 ? 24 : 12,
                    xxl: index === 2 ? 24 : 12,
                  }}
                >
                  <ProCard
                    title={TITLE_MAP[index]}
                    loading={loading}
                    bordered
                    bodyStyle={{
                      height: '300px',
                      boxSizing: 'content-box'
                    }}
                  >
                    <Area
                      key={index}
                      {...chartItem}
                    />
                  </ProCard>
                </ProCard>
              )
            })}

          </ProCard>

          <div>
            <h4 className='ssr-monitor-pd32'>错误明细（日志较多，只展示最100条）</h4>
            <div className='ssr-monitor-pd32'>
            <Table columns={ERROR_COLUMNS} dataSource={errorList} scroll={{ x: 500 }}/>
            </div>
          
          </div>
        </div>
      </Spin>

      {showAlarmPad.show ? <Alarm data={showAlarmPad.data} closePad={(type?: string, data?:any) => {
        setShowAlarmPad({
          show: false,
          data: type === 'refresh' ? data : showAlarmPad.data
        })}} /> : null}
    </div>
  )
}