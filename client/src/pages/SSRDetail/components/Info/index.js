import { useEffect, useState } from 'react';
import { Card, Button, Form, Input, Select, message, Popconfirm, Badge, Modal, Divider, Checkbox } from 'antd';
import { useModel } from 'umi';
import PreloadPop from '../PreloadPop';
import SSRDetailApi from '@/api/ssr-detail';
import './index.less';

export default (props) => {
  const {
    refreshFunc
  } = props;

  const { TextArea } = Input;

  const [confirmLoading, setConfirmLoading] = useState(false);
  const [form] = Form.useForm();
  const [pageInfo, setPageInfo] = useState(props.pageInfo);
  const [preloadShow, setPreloadShow] = useState(false);
  const [modalConfig, setModalConfig] = useState({
    visible: false,
    needInput: true,
    textValue: '',
    inputValue: '',
    hotBundle: true,
    secondStream: false,
    thirdStream: false
  })
  const currentUser = useModel('user');

  useEffect(() => {
    setPageInfo(props.pageInfo);
    form.setFieldsValue({
      page_name: props.pageInfo.page_name,
      project_group: props.pageInfo.project_group || 'trip',
      need_login: props.pageInfo.need_login,
      is_immersive: props.pageInfo.is_immersive || 2,
      csr_same_site: props.pageInfo.csr_same_site || 1,
      path_project_name: props.pageInfo.path_project_name || '',
      path_page_name: props.pageInfo.path_page_name || '',
      pre_stream_config: props.pageInfo.pre_stream_config || '',
      stream_config: props.pageInfo.stream_config || '',
      cdn_config: props.pageInfo.cdn_config || ''
    })
  }, [props.pageInfo])

  const PRELOAD_MAP = {
    1: '未开启',
    2: '审批中',
    3: '审批通过',
    4: '审批未通过',
    5: '已开启',
    6: 'F4灰度',
    7: 'F3+F4灰度',
    8: 'F2+F3+F4灰度',
    9: '已登录全量'
  }

  const PRELOAD_BTN_MAP = {
    1: '申请开启',
    2: '审批中',
    3: '灰度放量',
    4: '重新申请',
    5: '下线配置',
    6: '灰度放量',
    7: '灰度放量',
    8: '灰度放量',
    9: '灰度放量'
  }

  const PRELOAD_NEXT_MAP = {
    3: '灰度放量至 F4',
    6: '灰度放量至 F3+F4',
    7: '灰度放量至 F2+F3+F4',
    8: '灰度放量 已登录用户',
    9: '全量'
  }

  const getPreERUrl = () => {
    const imersiveParams = pageInfo.is_immersive != 1 ? '?disableNav=YES&titleBarHidden=2' : ''
    const host = `https://trip-air-fliggy-common-er.pre-air-er.taobao.com/app/${pageInfo.project_group || 'trip'}/`;
    return `${host}${pageInfo.path_project_name || pageInfo.project_name}/pages/${pageInfo.path_page_name || pageInfo.page_name}${imersiveParams}`
  }

  const getQRCodeUrl = (type) => {
    const imersiveParams = pageInfo.is_immersive != 1 ? '?disableNav=YES&titleBarHidden=2' : ''
    
    const preHost = pageInfo.project_business === 'btrip' ? 'er.wapa.alibtrip.com' : 'outfliggys.wapa.taobao.com';
    const prodHost = pageInfo.project_business === 'btrip' ? 'er.m.alibtrip.com' : 'outfliggys.m.taobao.com';
    const host = {
      'pre': `https://${preHost}/app/${pageInfo.project_group || 'trip'}/`,
      'prod': `https://${prodHost}/app/${pageInfo.project_group || 'trip'}/`,
    }
    return `${host[type]}${pageInfo.path_project_name || pageInfo.project_name}/pages/${pageInfo.path_page_name || pageInfo.page_name}${imersiveParams}`
  }

  const getBpmsUrl = () => {
    return `https://bpms.alibaba-inc.com/workdesk/instDetailLegao?__id=inist_detail&procInsId=${pageInfo.preload_apply_id}`
  }

  const updateFunc = () => {
    form
      .validateFields()
      .then((formRes) => {
        setConfirmLoading(true)

        const data = {
          id: pageInfo.id,
          isLogin: formRes.need_login,
          isImmersive: formRes.is_immersive,
          csrSameSite: formRes.csr_same_site,
          projectGroup: formRes.project_group || 'trip',
          pathProjectName: formRes.path_project_name || '',
          pathPageName: formRes.path_page_name || '',
          cdnConfig: formRes.cdn_config || ''
        };

        // 防止误操作，只有加了这个参数才能修改ER的配置
        if (location.href.indexOf('erConfig') > -1) {
          data.preStreamConfig = formRes.pre_stream_config || '';
          data.streamConfig = formRes.stream_config || '';
        }

        SSRDetailApi.updatePage(data).then(res => {
          if (res.success) {
            message.success('更新成功，重新发布预发/线上后生效');
            refreshFunc();
          } else {
            message.error(res.errorMsg)
          }
          setConfirmLoading(false);
        }).catch(e => {
          message.error(e.message || '更新失败');
          setConfirmLoading(false);
        })
      })
      .catch((err) => {
      });
  }

  const deleteFunc = () => {
    SSRDetailApi.delete({
      id: pageInfo.id,
      pageName: pageInfo.page_name
    }).then(res => {
      if (res.success) {
        message.success('删除成功');
        refreshFunc();
      } else {
        message.error(res.errorMsg)
      }
    }).catch(e => {
      message.error(e.message || '删除失败');
    })
  }

  const loadFunc = () => {
    if(!modalConfig.inputValue){return message.warn("输入有误，请重新输入")}
    let url = modalConfig.inputValue || getQRCodeUrl('prod');

    if(modalConfig.hotBundle){
      url = `${url}${url.indexOf('?')=== -1 ? '?hotBundle=true' : '&hotBundle=true'}`
    }

    if(modalConfig.secondStream){
      url = `${url}${url.indexOf('?')=== -1 ? '?_use_stream=1' : '&_use_stream=1'}`
    }

    if(modalConfig.thirdStream){
      url = `${url}${url.indexOf('?')=== -1 ? '?_use_stream=1&_use_three_part_stream=1' : '&_use_stream=1&_use_three_part_stream=1'}`
    }

    const iframeWrap = document.getElementById("load-iframe");
    if (pageInfo.prod_version) {
      message.loading('页面预热中，请勿关闭窗口，预计耗时四-八分钟', 240);
      let count = 0;
      const circleFun = ()=>{
        const iframeDom = document.createElement('iframe');
        iframeDom.style.display = 'none';
        iframeDom.src = url;
        iframeDom.id = `hot-iframe-${count}`;
        iframeWrap.appendChild(iframeDom);
        iframeDom.onload = () => {
          iframeWrap.removeChild(iframeDom);
          count ++;
          if(count === 60){
            message.loading('加热完毕', 2);
            return 
          }else{
            circleFun()
          }
        }
      }
      circleFun();
    } else {
      message.error('未发布线上版本，预热失败');
    }
  }


  const downgradeFunc = () => {
    SSRDetailApi.setDowngrade({
      id: pageInfo.id,
      enforceDowngrade: 2, // 1解除降级，2开启降级
    }).then(res => {
      if (res.success) {
        message.success('强制降级成功');
      } else {
        message.error(res.errorMsg)
      }
    }).catch(e => {
      message.error(e.message || '强制降级成功失败');
    })
  }

  const handlePreheat = (type) => {
    SSRDetailApi.setPreheatConfig({
      id: pageInfo.id,
      type, // 1更新2清空3禁止
    }).then(res => {
      if (res.success) {
        message.success('配置成功');
      } else {
        message.error(res.errorMsg)
      }
    }).catch(e => {
      message.error(e.message || '配置失败');
    })
  }


  const preloadFunc = (val) => {
    if (!pageInfo.ssr_preload ||
      pageInfo.ssr_preload === 1 ||
      pageInfo.ssr_preload === 4) {
      setPreloadShow(true);
    } else if (pageInfo.ssr_preload === 5 || val === 'cancel') {
      // 下线配置
      SSRDetailApi.preloadPublish({
        id: pageInfo.id,
        type: 'cancel'
      }).then(res => {
        if (res.success && res.data && res.data.res) {
          message.success('取消成功');
          refreshFunc();
        } else {
          message.error(res.errorMsg || (res.data && res.data.errorMsg));
        }
      }).catch(e => {
        message.error(e.message || '取消失败');
      })
    } else if (pageInfo.ssr_preload === 2 || pageInfo.forbiddenTag) {
    } else {
      Modal.confirm({
        title: PRELOAD_NEXT_MAP[pageInfo.ssr_preload],
        onOk: () => {
          SSRDetailApi.preloadPublish({
            id: pageInfo.id
          }).then(res => {
            if (res.success && res.data && res.data.res) {
              message.success('发布成功');
              refreshFunc();
            } else {
              message.error(res.errorMsg || (res.data && res.data.errorMsg));
            }
          }).catch(e => {
            message.error(e.message || '发布失败');
          })
        }
      })
    }
  }


  return (
    <>
      <Card title="基础信息" style={{ width: '100%' }}>
        <div className='ssr-info-line'><div className='line-lable'>项目仓库：</div>
          <a href={`https://code.alibaba-inc.com/${pageInfo.project_group || 'trip'}/${pageInfo.project_name}`} target='_blank'>
            {`https://code.alibaba-inc.com/${pageInfo.project_group || 'trip'}/${pageInfo.project_name}`}
          </a>
        </div>
        <div className='ssr-info-line'><div className='line-lable'>最近操作人：</div><div>{pageInfo.last_operator || '-'}</div></div>
        <div className='ssr-info-line'><div className='line-lable'>最近操作时间：</div><div>{pageInfo.gmt_modified || '-'}</div></div>
        <div className='ssr-info-line'><div className='line-lable'>预发地址：</div>
          <a href={getQRCodeUrl('pre')} target='_blank'>
            {getQRCodeUrl('pre')}
          </a>
        </div>
        <div className='ssr-info-line'><div className='line-lable'>线上地址：</div>
          <a href={getQRCodeUrl('prod')} target='_blank'>
            {getQRCodeUrl('prod')}
          </a>
        </div>
        <div className='ssr-info-line'><div className='line-lable'>版本：</div>
        <div>
          <Badge color="green" text={`预发：${pageInfo.pre_version || '暂无'}`} />
          <Divider type='vertical' />
          <Badge color="red" text={`线上：${pageInfo.prod_version || '暂无'}`} />
          <Divider type='vertical' />
          <Badge color="orange" text={`灰度：${pageInfo.gray_version || '暂无'}${pageInfo.gray_version ? '(' + pageInfo.gray_ratio + '%)' : ''}`} />
          </div>
        </div>
      </Card>
      <div style={{ height: '12px' }} />

      <Card title="页面配置" style={{ width: '100%' }}>
        <Form
          className="review-form"
          form={form}
          labelCol={{
            style: { width: '140px' }
          }}
          wrapperCol={{
            span: 12
          }}
        >
          <Form.Item
            name="page_name"
            label="页面名称"
            rules={[{ required: true, message: '请输入页面名称!' }]}
          >
            <Input placeholder='请输入页面名称' disabled={true} />
          </Form.Item>
          <Form.Item
            name="project_group"
            label="项目分组"
            rules={[{ required: true, message: '请输入项目分组!' }]}
          >
            <Input placeholder='请输入项目分组' disabled={!['205521', '359190','265692'].includes(currentUser.workid)} />
          </Form.Item>
          <Form.Item
            name="is_immersive"
            label="是否沉浸式"
            rules={[{ required: true, message: '请选择沉浸式状态!' }]}
          >
            <Select
              options={[
                { value: 1, label: '否' },
                { value: 2, label: '是' }
              ]}
              disabled={pageInfo.isDisabled}
            />
          </Form.Item>
          <Form.Item
            name="csr_same_site"
            label="同域名降级"
          >
            <Select
              options={[
                { value: 1, label: '关闭' },
                { value: 2, label: '开启' }
              ]}
              disabled={pageInfo.isDisabled}
            />
          </Form.Item>
          <Form.Item
            hidden={!pageInfo.pre_stream_config}
            name="pre_stream_config"
            label="预发流式ER配置"
          >
            <Input placeholder='使用前请咨询@紫期@吐司' disabled={location.href.indexOf('erConfig') === -1} />
          </Form.Item>
          <Form.Item
            hidden={!pageInfo.stream_config}
            name="stream_config"
            label="线上流式ER配置"
          >
            <Input placeholder='使用前请咨询@紫期@吐司' disabled={location.href.indexOf('erConfig') === -1} />
          </Form.Item>
          <Form.Item
            hidden={!['205521', '359190'].includes(currentUser.workid)}
            name="cdn_config"
            label="cdn静态配置"
          >
            <Input placeholder='使用前请咨询@紫期@行鸢' />
          </Form.Item>
        </Form>
        {!pageInfo.isDisabled ? <Button type="primary" onClick={updateFunc} loading={confirmLoading}>修改配置</Button> : null}
      </Card>
      <div style={{ height: '12px' }} />

      {/* <Card title="预加载配置" style={{ width: '100%' }}>
        <div className='ssr-info-line'><div className='line-lable'>预加载状态：</div><div>{PRELOAD_MAP[pageInfo.ssr_preload] || '未开启'}</div></div>
        {pageInfo.ssr_preload === 2 || pageInfo.ssr_preload === 4 ? <div className='ssr-info-line'><div className='line-lable'>审批单链接：</div><a href={getBpmsUrl()} target='_blank'>{getBpmsUrl()}</a></div> : null}
        {pageInfo.ssr_preload > 5 ? <div className='ssr-info-line'><div className='line-lable'>上次灰度时间：</div><div>{pageInfo.gmt_preload}</div></div> : null}
        <div className='ssr-btn-line'><Button type="primary" disabled={pageInfo.isDisabled || pageInfo.ssr_preload === 2 || pageInfo.forbiddenTag} onClick={preloadFunc}>
          {PRELOAD_BTN_MAP[pageInfo.ssr_preload] || '申请开启'}
        </Button>
        <Button style={{marginLeft: '12px'}} onClick={() => preloadFunc('cancel')}>重置</Button>
        {pageInfo.ssr_preload > 5 ? <Button style={{marginLeft: '12px'}} onClick={() => preloadFunc('cancel')}>取消灰度</Button> : null}
        {pageInfo.forbiddenTag ? <div style={{color: 'red', marginLeft: '12px'}}>灰度间隔需3小时以上，请稍后再试</div> : null}
        </div>
      </Card> */}
      <div style={{ height: '12px' }} />
      
      {!pageInfo.isDisabled ? <Card style={{ width: '100%' }}>
        <Button type="dashed" style={{marginRight: '12px'}} onClick={()=>{setModalConfig({...modalConfig, visible:true, inputValue: getQRCodeUrl('prod'), textValue: '如需更改地址，或增加参数，请在下方输入框操作'})}}>一键预热</Button>
        <Modal title="确定预热页面（4分钟预热）" open={modalConfig.visible} onOk={loadFunc} onCancel={()=>{setModalConfig({...modalConfig, visible: false})}}>
          <span>{modalConfig.textValue}</span>
          {modalConfig.needInput ? <TextArea value={modalConfig.inputValue} onChange={(e)=>{setModalConfig({...modalConfig, inputValue: e.target.value})}} style={{ height: 120, resize: 'none' }}/> : null}
          <Checkbox defaultChecked={modalConfig.hotBundle} value={modalConfig.hotBundle} onChange={()=>{setModalConfig({...modalConfig, hotBundle: !modalConfig.hotBundle})}}>是否预热新版本资源（会强行命中新版本）</Checkbox>
          <br/>
          <Checkbox value={modalConfig.secondStream} onChange={()=>{setModalConfig({...modalConfig, secondStream: !modalConfig.secondStream})}}>是否二段流式</Checkbox>
          <br/>
          <Checkbox value={modalConfig.thirdStream} onChange={()=>{setModalConfig({...modalConfig, thirdStream: !modalConfig.thirdStream})}}>是否三段流式</Checkbox>
        </Modal>
        {/* </Popconfirm> */}
        <Popconfirm
          title="确定强制降级页面？"
          onConfirm={() => downgradeFunc()}
          okText="确定"
          cancelText="取消"
        >
          <Button danger style={{marginRight: '12px'}}>强制降级</Button>
        </Popconfirm>
        <Popconfirm
          title="确定删除页面？"
          onConfirm={() => deleteFunc()}
          okText="确定"
          cancelText="取消"
        >
          <Button type="primary" danger>删除页面</Button>
        </Popconfirm>
        {['205521', '359190'].includes(currentUser.workid) ? <Button style={{marginLeft: '12px'}} onClick={() => handlePreheat(1)}>刷新预热</Button> : null}
        {['205521', '359190'].includes(currentUser.workid) ? <Button style={{marginLeft: '12px'}} onClick={() => handlePreheat(2)}>预热清空</Button> : null}
        {['205521', '359190'].includes(currentUser.workid) ? <Button style={{marginLeft: '12px'}} onClick={() => handlePreheat(3)}>禁止预热</Button> : null}
      </Card> : null}

      {preloadShow ?
        <PreloadPop
          pageInfo={pageInfo}
          path={getQRCodeUrl('prod')}
          closeFunc={(type) => {
            if (type === 'refresh') {
              refreshFunc();
            }
            setPreloadShow(false)
          }}
        /> : null}
        <div id="load-iframe" src="" style={{display: 'none'}} />
    </>
  )
}