import { useEffect, useState, useRef } from 'react';
import { ProTable } from '@ant-design/pro-components';
import { Button, Divider, Popconfirm, Modal, Form, Input, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import SSRDetailApi from '@/api/ssr-detail';
import { gerUrlParam } from '@/utils/base';

export default () => {
  const tableRef = useRef();
  const [form] = Form.useForm();
  const [list, setList] = useState([]);
  const [showAdd, setShowAdd] = useState({
    show: false,
    data: {},
    type: 'add'
  });

  const handleUpdate = (userId) => {
    let newConfig = JSON.parse(JSON.stringify(list));
    // 有id即删除
    if (userId) {
      newConfig = list.filter(item => {
        return item.id !== userId;
      });
      updateConfig(newConfig);
    } else {
      form
      .validateFields()
      .then((formRes) => {// 支持数组循环
        const ids = formRes.id.split(",");
        ids.forEach(r=>{
          const isExist = newConfig.filter(item => {
            return item.id === r;
          });
          if (isExist.length) {
            newConfig = newConfig.map(item => {
              if (item.id === r) {
                return {
                  id: r,
                  ip: formRes.ip
                }
              } else {
                return item
              }
            })
          } else {
            newConfig = newConfig.concat([{
              id: r,
              ip: formRes.ip
            }]);
          }
        })
        updateConfig(newConfig);
      })
    } 
  }

  const updateConfig = (val) => {
    SSRDetailApi.updatePage({
      id: gerUrlParam('id'),
      isSpeConfig: true,
      speConfig: JSON.stringify(val)
    }).then(res => {
      if (res.success) {
        message.success('更新成功');
        setList(val);
        tableRef.current.reload();
        setShowAdd({
          show: false,
          data: {},
          type: 'add'
        })
      } else {
        message.error(res.errorMsg)
      }
    }).catch(e => {
      message.error(e.message || '更新失败');
    })
  }

  const columns = [{
    title: '用户ID',
    dataIndex: 'id'
  }, {
    title: 'IP',
    dataIndex: 'ip',
    hideInSearch: true,
  }, {
    title: '操作',
    hideInSearch: true,
    render(text, record) {
      return (
        <>
          <Button type="link" onClick={() => {
            setShowAdd({
              show: true,
              data: {...record},
              type: 'edit'
            });
            form.setFieldsValue({
              ...record
            })
          }}>修改</Button>
          <Divider type='vertical' />
          <Popconfirm
            title="确定删除？"
            onConfirm={() => {
              handleUpdate(record.id);
            }}
            okText="确定"
            cancelText="取消"
          ><Button type="link" size="small" danger={true}>删除</Button></Popconfirm>
        </>
      )
    }
  }]

  const renderModal = () => {
    return (
      <Modal
        title={showAdd.type === 'add' ? "新增配置" : '修改配置'}
        open={true}
        maskClosable={false}
        onOk={() => {handleUpdate()}}
        onCancel={() => {
          setShowAdd({
            show: false,
            data: {},
            type: 'add'
          })
        }}
        width={400}
        destroyOnClose
      >
        <Form
          className="review-form"
          form={form}
          labelCol={{
            span: 5
          }}
          wrapperCol={{
            span: 19
          }}
        >
          <Form.Item
            name="id"
            label="用户ID"
            rules={[{ required: true, message: '请输入用户ID,可以一次输入多个，用逗号分隔' }]}
          >
            <Input disabled={showAdd.type === 'edit'} placeholder='请输入用户ID,可以一次输入多个，用逗号分隔'/>
          </Form.Item>
          <Form.Item
            name="ip"
            label="IP"
            rules={[{ required: true, message: '请输入IP' }]}
          >
            <Input placeholder='请输入IP,只能制定一个'/>
          </Form.Item>
        </Form>
      </Modal>
    )
  }

  return (
    <>
      <ProTable
        rowKey="id"
        search={true}
        pagination={{
          showQuickJumper: true,
          pageSize: 10
        }}
        columns={columns}
        actionRef={tableRef}
        request={async (params, sorter) => {
          let resData = await SSRDetailApi.queryPage({
            id: gerUrlParam('id'),
            isSpeConfig: true
          }).then(res => {
            if (res.success && res.data && res.data.speConfig) {
              try{
                const result = JSON.parse(res.data.speConfig);
                return {
                  data: result,
                  total: result.length
                }
              }catch(e){
                return {
                  data: [],
                  total: 0
                }
              }
            }
          }).catch(e => { })
          setList(resData.data);
          if (params.id) {
            const newList = resData.data.filter(item => {
              return item.id.includes(params.id)
            });
            resData = {
              data: newList,
              total: newList.length
            }
          }
          return resData;
        }}
        toolbar={{
          actions: [<Button type="primary" onClick={() => {
            setShowAdd({
              show: true,
              data: {},
              type: 'add'
            });
            form.setFieldsValue({
              id: '',
              ip: ''
            })
          }}><PlusOutlined />新增</Button>]
        }}
      />
      {showAdd.show ? renderModal() : null}
    </>
  )
}