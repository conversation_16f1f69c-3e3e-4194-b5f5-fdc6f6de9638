import { useEffect, useState } from 'react';
import { Modal, Input, Form, Select, Alert, message } from 'antd';
import SSRDetailApi from '@/api/ssr-detail';

const { TextArea } = Input;

export default (props) => {
  const {
    pageInfo,
    path,
    closeFunc
  } = props;
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      project: pageInfo.project_name,
      page: pageInfo.page_name,
      login: pageInfo.need_login || 1,
      path,
      all: 1,
      spmb: ''
    })
  }, [])

  const handleOk = () => {
    form
    .validateFields()
    .then((formRes) => {
      setConfirmLoading(true);
      SSRDetailApi.preloadApply({
        id: pageInfo.id,
        project: formRes.project,
        page: formRes.page,
        login: formRes.login,
        path: formRes.path,
        all: formRes.all,
        spmb: formRes.spmb
      }).then(res => {
        if (res.success && res.data && res.data.instance) {
          closeFunc('refresh')
          message.success('申请成功');
        } else {
          message.error(res.errorMsg || '申请失败');
        }
        setConfirmLoading(false);
      }).catch(e => {
        message.error(e.message || '申请失败');
        setConfirmLoading(false);
      })
    })
    .catch((err) => {
    });
    
  }

  const handleCancel = () => {
    closeFunc();
  }
  
  return (
    <Modal
      title={'预加载申请'}
      open={true}
      maskClosable={false}
      confirmLoading={confirmLoading}
      onOk={handleOk}
      onCancel={handleCancel}
      width={540}
      okText="申请"
      destroyOnClose
    >
      <Alert
        message="开启预加载会导致下游QPS虚高，请注意下游服务水位！请注意下游服务水位！有数据实时性要求的业务请自行执行数据刷新！"
        type="warning"
        showIcon
      />
      <div style={{height: '12px'}} />
      <Form
        form={form}
        labelCol={{
          span: 5
        }}
        wrapperCol={{
          span: 19
        }}
      >
        <Form.Item
          name="project"
          label="项目名称"
        >
          <Input placeholder='请输入项目名称' disabled={true} />
        </Form.Item>
        <Form.Item
          name="page"
          label="项目名称"
        >
          <Input placeholder='请输入页面名称' disabled={true} />
        </Form.Item>
        <Form.Item
          name="path"
          label="页面地址"
        >
          <TextArea placeholder='请输入页面地址' disabled={true} rows={4} />
        </Form.Item>
        <Form.Item
          name="login"
          label="是否登录"
        >
          <Select
            options={[
              { value: 1, label: '否' },
              { value: 2, label: '是' }
            ]}
            disabled={true}
          />
        </Form.Item>
        <Form.Item
          name="spmb"
          label="入口页面b点"
          rules={[{ required: true, message: '请填写入口页面b点' }]}
        >
          <Input placeholder='请填写入口页面b点' />
        </Form.Item>
        {/* <Form.Item
          name="all"
          label="全量开启"
          extra={<div style={{color: 'red'}}>开启后将不区分登录状态，请勿轻易开启</div>}
        >
          <Select
            options={[
              { value: 1, label: '否' },
              { value: 2, label: '是' }
            ]}
          />
        </Form.Item> */}
      </Form>
    </Modal>
  )
}
