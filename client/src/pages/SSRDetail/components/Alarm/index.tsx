
import { useEffect, useState } from 'react';
import { Modal, message, Select, Input, InputNumber } from 'antd';
import { get } from 'lodash';
import UserComp from '@/components/FR/user';
import SSRDetailApi from '@/api/ssr-detail';

export default (props: any) => {
  
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [value, setValue] = useState(JSON.parse(props.data.alarm_config || '{}').list || []);
  const [rank, setRank] = useState(JSON.parse(props.data.alarm_config || '{}').rank || 99);

  const footer = props.disabled ? {footer: null} : {}

  const handleOk = () => {
    setConfirmLoading(true);
    SSRDetailApi.updatePage({
      id: props.data.id,
      alarmConfig: JSON.stringify({
        list: value,
        rank
      })
    }).then(res => {
      if (res.success) {
        message.success('修改成功');
        props.closePad('refresh', {
          ...props.data,
          alarm_config: JSON.stringify({
            list: value,
            rank
          })
        });
      } else {
        message.error(res.errorMsg)
      }
      setConfirmLoading(false);
    }).catch(e => {
      message.error(e.message || '修改失败');
      setConfirmLoading(false);
    })
  }

  const handleCancel = () => {
    props.closePad();
  }

  const handleChange = (val:any) => {
    const newValue = val.map((item: any) => {
      if (item.key) {
        const keyValue = JSON.parse(item.key);
        return {
          value: keyValue.workid,
          label: keyValue.name
        }
      }
      return {
        value: item.value,
        label: item.label
      }
    })
    setValue(newValue);
  }

  const handleRankChange = (val: any) => {
    setRank(val);
  }
  
  return (
    <Modal
      title="成功率报警订阅"
      open={true}
      maskClosable={false}
      confirmLoading={confirmLoading}
      onOk={handleOk}
      onCancel={handleCancel}
      width={500}
      destroyOnClose
      {...footer}

    >
      <div className='version-pad'>
        <span className='version-pad-name'>订阅人：</span>
        <UserComp value={value} onChange={handleChange} disabled={props.data.isDisabled} />
      </div>
      <div className='version-pad'>
        <span className='version-pad-name'>成功率：</span>
        <InputNumber
          value={rank}
          onChange={handleRankChange}
          disabled={props.data.isDisabled}
          min={90}
          max={99}
          addonBefore="小于"
          addonAfter="%（范围90-99）" 
          precision={0}
          style={{width: '100%'}}
        />
      </div>
    </Modal>
  )
}