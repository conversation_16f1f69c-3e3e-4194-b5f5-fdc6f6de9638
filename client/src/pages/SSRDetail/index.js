import { useEffect, useState, useRef } from 'react';
import { InfoCircleOutlined, AreaChartOutlined, ProfileOutlined, ToolOutlined } from '@ant-design/icons';
import { Menu, Spin, Result, Button, Alert } from 'antd';
import { gerUrlParam } from '@/utils/base';
import { formateDate } from '@/utils/date';
import SSRDetailApi from '@/api/ssr-detail';

import Info from './components/Info';
import Monitor from './components/Monitor'
import Record from './components/Record';
import PreConfig from './components/PreConfig';
import SpeConfig from './components/SpeConfig';

import './index.less'

export default () => {
  const [current, setCurrent] = useState('info');
  const [loading, setLoading] = useState(true);
  const [pageInfo, setPageInfo] = useState({});

  const list = [{
    label: '页面信息',
    key: 'info',
    icon: <InfoCircleOutlined />,
  }, {
    label: '监控大盘',
    key: 'monitor',
    icon: <AreaChartOutlined />,
  }, {
    label: '发布记录',
    key: 'record',
    icon: <ProfileOutlined />,
  }, {
    label: '预发多套配置',
    key: 'pre',
    icon: <ToolOutlined />,
  }, {
    label: '安全生产配置',
    key: 'spe',
    icon: <ToolOutlined />,
  }]

  useEffect(() => {
    setLoading(true);
    queryFunc();
  }, [])

  const queryFunc = () => {
    const id = gerUrlParam('id');
    if (!id) {
      return;
    }
    SSRDetailApi.queryPage({ id }).then(res => {
      if (res.success && res.data && res.data.id) {
        setLoading(false);
        setPageInfo(res.data);
      } else {
        setLoading(false);
        setPageInfo({});
      }
    })
  }

  const onClick = (e) => {
    setCurrent(e.key)
  }

  return (
    <>
      {pageInfo.preloadConfig && pageInfo.preloadConfig.ratio ? 
      <Alert message={`该页面正在执行客户端预加载更新，当前灰度比例${pageInfo.preloadConfig.ratio}%，上次更新时间${formateDate('YYYY-MM-DD hh:mm:ss', new Date(pageInfo.preloadConfig.updateTime))}${pageInfo.preloadConfig.ratio < 100 ? '，将在' + formateDate('YYYY-MM-DD hh:mm:ss', new Date(pageInfo.preloadConfig.nextUpdate)) +'放量至' + pageInfo.preloadConfig.nextRatio + '%' : ''}（自动化任务时间${pageInfo.preloadConfig.taskTime}）`} type="warning" style={{marginBottom: 12}} /> : null}
      <Menu onClick={onClick} selectedKeys={[current]} mode="horizontal" items={list} />
      <div style={{ height: '12px' }} />
      {loading ? <div className='ssr-loading'><Spin size="large" /></div> : null}
      {!loading && !pageInfo.id ? <div className='ssr-loading'>
        <Result
          status="404"
          title="页面不存在"
          extra={<Button type="primary" onClick={queryFunc}>重试</Button>}
        />
      </div> : null}
      {!loading && current === 'info' && pageInfo.id ? <Info pageInfo={pageInfo} refreshFunc={queryFunc} /> : null}
      {!loading && current === 'monitor' && pageInfo.id ? <Monitor pageInfo={pageInfo} /> : null}
      {!loading && current === 'record'&& pageInfo.id ? <Record /> : null}
      {!loading && current === 'pre'&& pageInfo.id ? <PreConfig /> : null}
      {!loading && current === 'spe'&& pageInfo.id ? <SpeConfig /> : null}
    </>
  )
}