import { useEffect, useState } from 'react';
import { Typography } from 'antd';
import ProjectSelect from '@/components/ProjectSelect';
import QrCode from '@/components/QrCode';
import { IProject } from '@/interface/project';

import './index.less';

const { Title } = Typography;

export default () => {
  const [project, setProject] = useState<IProject>();
  
  function onProjectSelect(project: IProject) {
    setProject(project);
  }

  return (
    <div className="helper-link-container">
      <ProjectSelect onSelect={onProjectSelect} />

      <div className="qr-code-wrapper">
        <QrCode project={project} />
      </div>
    </div>
  );
}
