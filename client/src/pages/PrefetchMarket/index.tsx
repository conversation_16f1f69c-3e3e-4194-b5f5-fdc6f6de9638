import { useEffect, useState } from 'react';
import { Spin } from 'antd';

import './index.less';

export default () => {
  const [iframeLoading, setIframeLoading] = useState(true);

  useEffect(() => {
    // 超时自动关闭loading，原因是某些情况下 iframe 的 onLoad 不会触发
    setTimeout(() => {
      if (iframeLoading) setIframeLoading(false)
    }, 3000)
  }, [])

  const onIframeLoad = () => {
    setIframeLoading(false);
  }

  return <div className="page-container">
    <Spin spinning={iframeLoading} size="large">
      <iframe
        className="iframe"
        onLoad={() => onIframeLoad()}
        src="https://deepinsight.alipay.com/pc.htm?reportId=D2023071200161401000013026614" />
    </Spin>
  </div>
};
