import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, List } from 'antd';
import { useAccess, Access, useModel } from 'umi';
import EditProjectBtn, { Action } from './components/EditProjectBtn';
import Item from './components/Item';

import './index.less';

export default () => {
  const [projectList, refreshProjectList] = useModel('projectList');
  const access = useAccess();
  const loading = projectList.length === 0;

  return (
    <div style={{ background: '#fff' }}>
      <PageHeader
        ghost={false}
        title="项目管理"
        extra={
          <Access accessible={access.isAdmin}>
            <EditProjectBtn action={Action.Create} onEditEnd={refreshProjectList}><Button type="primary">添加项目</Button></EditProjectBtn>
          </Access>
        }>
      </PageHeader>

      <Spin size="large" spinning={loading}>
        <List
          style={{ padding: '0 24px 24px' }}
          itemLayout="horizontal"
          size="large"
          pagination={{
            pageSize: 10,
          }}
          dataSource={projectList}
          renderItem={item => (
            <List.Item key={item.id}>
              <Item data={item} onDeleteEnd={refreshProjectList} />
            </List.Item>
          )}
        />
      </Spin>
    </div>
  );
}
