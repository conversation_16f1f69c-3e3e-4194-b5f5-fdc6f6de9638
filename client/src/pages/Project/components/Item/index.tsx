import { useState } from 'react';
import { message, Col, List, Popconfirm, Divider, Spin, Avatar, Row } from 'antd';
import projectApi from '@/api/project';
import { IProject } from '@/interface/project';
import EditProjectBtn, { Action } from '../EditProjectBtn';

import './index.less';

interface Props {
  data: IProject;
  onDeleteEnd: Function;
}

export default ({ data, onDeleteEnd }: Props) => {
  const [latestData, setLatestData] = useState(data);
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(true);
  const canAccess = latestData.isAdmin;

  const Actions = () => {
    if (canAccess) {
      return <>
        <EditProjectBtn
          data={latestData}
          onEditEnd={onEditEnd}
          action={Action.Update}>
          <a>编辑</a>
        </EditProjectBtn>
        <Divider type="vertical" />
        <Popconfirm title="确认删除？" okText="是" cancelText="否" onConfirm={deleteProject}>
          <a>删除</a>
        </Popconfirm>
      </>
    } else {
      return <>
        <EditProjectBtn
          data={latestData}
          onEditEnd={onEditEnd}
          action={Action.Read}>
          <a>查看</a>
        </EditProjectBtn>
      </>
    }
  };

  function onEditEnd(_action: any, res: { data: IProject }) {
    setLatestData(res.data);
  }

  // 删除项目
  function deleteProject() {
    setLoading(true);

    projectApi.delete(latestData.id)
      .then((res) => {
        if (res?.success) {
          setShow(false);
          onDeleteEnd && onDeleteEnd();
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '删除失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  if (!show || !latestData) return null;

  return <Spin spinning={loading} wrapperClassName="project-list-item">
    <Row>
      <Col span={12}>
        <List.Item.Meta
          avatar={
            <Avatar src={latestData.icon} />
          }
          title={<a href={latestData.gitRepo} target="_blank">{latestData.cnName}</a>}
          description={latestData.name}
        />
      </Col>
      <Col span={12} className="col actions">
        <Actions />
      </Col>
    </Row>
  </Spin>
}