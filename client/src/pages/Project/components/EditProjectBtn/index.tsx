import { useState } from 'react';
import { Modal, message, Spin } from 'antd';
import { useForm } from 'form-render';
import _ from 'lodash';
import { useModel } from 'umi';
import { IProject } from '@/interface/project';
import FormRender from '@/components/FR';
import { bucToFormFields, amdpToFormFields } from '@/components/FR/user2';
import projectApi, { IProjectCreateRes, IProjectUpdateRes } from '@/api/project';
import schema from './schema';

import './index.less';

export enum Action {
  Create = 'create', // 新建
  Update = 'update', // 更新
  Read = 'read', // 查看
}

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  data?: IProject;
  action: Action;
  onEditEnd?: (action: Action, res: any) => void;
}

export default ({ data, children, action, onEditEnd }: Props) => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const currentUser = useModel('user');
  const form = useForm();
  const hasWriteAccess = action === Action.Create || action === Action.Update;

  // 行动名称
  let actionName = '';
  if (action === Action.Create) actionName = '新建';
  else if (action === Action.Update) actionName = '更新';
  else if (action === Action.Read) actionName = '查看';

  const onFormMount = () => {
    // 当有值时，回填表单
    if (data) {
      form.setValues({
        ...data,
        adminList: data.adminsBucInfo?.map(bucToFormFields) || [],
        deliverConfig: { content: JSON.stringify(data.deliverConfig) }
      });
    } else {
      // 创建时默认将自己加入管理员列表
      form.setValues({
        adminList: [currentUser].map(amdpToFormFields),
      });
    }
  }

  const onFormSubmit = () => {
    form.submit();
  };

  const onFormCancel = () => {
    form.resetFields();
    setShowModal(false);
  };

  const onFormFinish = (formData: any, errors: any) => {
    if (loading && errors.length > 0) return;

    let formatFormData: any = _.omit(formData, 'deliverConfig');
    formatFormData.deliverConfig = "";
    try {
      const { content } = formData.deliverConfig || {};
      // 组件投放配置去除空格和换行
      formatFormData.deliverConfig = content ? JSON.stringify(JSON.parse(content)) : '{}';
    } catch (error) {
      message.error('组件投放配置不是标准JSON格式，请检查！');
      return;
    }

    // 格式化adminList
    formatFormData.adminWorkidList = formData.adminList?.map((item: any) => item.value) || [];

    // 校验打码配置，只有配置项全部为空或完整时才通过
    const wsyConfig = formatFormData.wsyConfig;
    if (!wsyConfig.scene && !wsyConfig.accessKey && !wsyConfig.platformList) {      
      // 若配置项全部为空，则删除对应表单
      delete formatFormData.wsyConfig;
    } else if (!(wsyConfig.scene && wsyConfig.accessKey && wsyConfig.platformList)) {
      // 若配置项不完整，则提示错误
      message.error('若要开启打码配置，则“场景值”、“秘钥”、“平台”必须填写完整！');
      return;
    }
    
    setLoading(true);

    projectApi[action === Action.Update ? 'update' : 'create'](formatFormData).then((res: void | IProjectCreateRes | IProjectUpdateRes) => {
      if (res?.success) {
        message.info(`${actionName}成功`);
        setShowModal(false);
        setTimeout(() => form.resetFields(), 500); // 迟一点清空，省得闪一下
        onEditEnd && onEditEnd(action, res);
      } else {
        message.error(res?.errorMsg || `${actionName}失败`);
      }
    })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => setLoading(false));
  }

  function extendsSchema(originSchema: typeof schema) {
    const newSchema = _.cloneDeep(originSchema);
    newSchema.properties.dingtalkRobots.props.action = action;
    newSchema.properties.clientList.props.action = action;

    if (!hasWriteAccess) {
      newSchema.properties.dingtalkRobots.hidden = true;
    }

    if (action === Action.Update) {
      newSchema.properties.name.disabled = true;
    }

    return newSchema;
  }

  return <>
    <div onClick={() => setShowModal(true)}>
      {children}
    </div>

    <Modal
      title={`${actionName}项目`}
      open={showModal}
      onOk={onFormSubmit}
      onCancel={onFormCancel}
      maskClosable={false}
      footer={hasWriteAccess ? undefined : null}
      okButtonProps={{ disabled: form.errorFields?.length > 0 }}
      width={800}
      destroyOnClose
    >
      <Spin spinning={loading} wrapperClassName="form-wrapper">
        <FormRender
          form={form}
          schema={extendsSchema(schema)}
          disabled={!hasWriteAccess}
          removeHiddenData={false}
          onMount={onFormMount}
          onFinish={onFormFinish}
          displayType="row" />
      </Spin>
    </Modal>
  </>
}
