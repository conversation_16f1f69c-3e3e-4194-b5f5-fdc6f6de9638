import { EProjectType, PROJECT_TYPE_CN_NAME } from '@/const/project';
import { EClient, CLIENT_CN_NAME } from '@/const/iter-deliver';
import { BUILD_PLAtFORM_CN_NAME, EBuildPlatform } from '@/const/build';

export default {
  "type": "object",
  "properties": {
    "name": {
      "title": "项目名称",
      "type": "string",
      "placeholder": "限小写字母、数字、中划线、下划线，创建后不可修改",
      "required": true,
      "disabled": false,
      "rules": [
        {
          "pattern": "^[0-9a-z_\-]+$",
          "message": "限小写字母、数字、中划线、下划线"
        }
      ]
    },
    "cnName": {
      "title": "项目中文名",
      "type": "string",
      "required": true,
    },
    "type": {
      "title": "项目类型",
      "type": "string",
      "placeholder": "请选择",
      "enum": Object.values(EProjectType),
      "enumNames": Object.values(EProjectType).map(item => PROJECT_TYPE_CN_NAME[item]),
      "required": true,
      "validator": (value: string) => {
        let errMsg;

        if (!value) {
          errMsg = '请选择项目类型';
        }

        return {
          error: errMsg && new Error(errMsg)
        }
      }
    },
    "icon": {
      "title": "项目图标",
      "type": "string",
      "required": true
    },
    "gitRepo": {
      "title": "代码仓库",
      "type": "string",
      "required": true
    },
    "adminList": {
      "title": "管理员",
      "type": "array",
      "items": {
        "type": "string"
      },
      "props": {},
      "widget": "user2"
    },
    "clientList": {
      "title": "投放端",
      "type": "array",
      "widget": "wList",
      "hidden": false,
      "props": {
        "action": "create",
        "addBtnName": "添加端",
        "items": {
          "clientName": {
            "title": "客户端",
            "type": "radio",
            "enum": Object.values(EClient),
            "enumNames": Object.values(EClient).map(item => CLIENT_CN_NAME[item]),
            "validator": (value: string) => {
              let errMsg;

              if (!value) {
                errMsg = '请选择客户端';
              }

              return {
                error: errMsg && new Error(errMsg)
              }
            }
          },
          "miniAppId": {
            "title": "小程序ID",
            "placeholder": "小程序ID",
            "validator": (value: string) => {
              let errMsg;

              if (!value) {
                errMsg = '请填写小程序ID';
              }

              return {
                error: errMsg && new Error(errMsg)
              }
            }
          }
        }
      },
      "width": "100%",
    },
    "dingtalkRobots": {
      "title": "钉钉机器人",
      "type": "array",
      "widget": "wList",
      "hidden": false,
      "props": {
        "action": "create",
        "addBtnName": "添加机器人",
        "items": {
          "robotHook": {
            "title": "Webhook",
            "placeholder": "Webhook",
            "validator": (value: string) => {
              let errMsg;

              if (!value) {
                errMsg = '请填写Webhook';
              }

              return {
                error: errMsg && new Error(errMsg)
              }
            }
          },
          "signCode": {
            "title": "秘钥",
            "placeholder": "秘钥",
            "validator": (value: string) => {
              let errMsg;

              if (!value) {
                errMsg = '请填写秘钥';
              }

              return {
                error: errMsg && new Error(errMsg)
              }
            }
          }
        }
      },
      "width": "100%",
    },
    "deliverConfig": {
      "type": "object",
      "description": "JSON格式",
      "theme": "collapse:ghost",
      "hidden": "{{ formData.type !== 'component' }}",
      "properties": {
        "content": {
          "title": "组件投放配置",
          "type": "string",
          "format": "textarea",
          "placeholder": '当项目是组件类型是，请填写JSON格式的投放配置。（支持投放H5页面/支付宝小程序/微信小程序/抖音小程序）',
          "width": "90%"
        },
        "demo": {
          "type": "html",
          "default": "<a href='https://aliyuque.antfin.com/remtwr/cco1zs/nv0lhsyoe5n10gig?singleDoc' target='_blank'>查看示例</a>",
          "width": "10%",
          "labelWidth": 50,
        }
      }
    },
    "wsyConfig": {
      "type": "object",
      "hidden": "{{ formData.type === 'component' || formData.type === 'other' }}",
      "properties": {
        "scene": {
          "title": "打码配置",
          "type": "string",
          "placeholder": "场景值",
          "width": "40%"
        },
        "accessKey": {
          "type": "string",
          "placeholder": "秘钥",
          "width": "30%"
        },
        "platformList": {
          "widget": "multiSelect",
          "type": "array",
          "width": "30%",
          "props": {
            "options": Object.values(EBuildPlatform).map(item => {
              return {
                label: BUILD_PLAtFORM_CN_NAME[item],
                value: item
              }
            }),
            "placeholder": "请选择要开启的打码平台"
          }
        }
      },
    },
  },
  "labelWidth": 180,
  "displayType": "row"
};
