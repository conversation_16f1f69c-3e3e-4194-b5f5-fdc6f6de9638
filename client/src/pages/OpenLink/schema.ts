import { InsertType, PageType, CurrentTab, NavColor, PromotionType } from './types';

const promotionExtra = '<div class="open-link-tips">单个小程序每日生成短链上限为<span>100万</span>个 <a href="https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/shortlink.html" target="__blank">查看详情</a></div>'

export default {
  type: 'object',
  labelWidth: 180,
  displayType: 'row',
  required: ['type', 'pageType', 'h5Link', 'shid', 'id', 'poiId', 'path'],
  properties: {
    type: {
      title: '生成方式',
      type: 'string',
      widget: 'radio',
      props: {
        options: [
          { label: '页面类型', value: InsertType.PAGE_TYPE },
          { label: 'h5链接智能识别', value: InsertType.H5_LINK },
          { label: '小程序路径', value: InsertType.WEAPP_LINK }
        ],
      }
    },
    pageType: {
      title: '页面类型',
      type: 'string',
      widget: 'select',
      dependencies: ['type'],
      hidden: `{{formData.type !== '${InsertType.PAGE_TYPE}'}}`,
      props: {
        options: [
          { label: '首页', value: PageType.HOME },
          { label: '套壳', value: PageType.WEBVIEW },
          { label: '酒店详情', value: PageType.HOTEL_DETAIL },
          { label: 'poi详情', value: PageType.POI },
          { label: '商详', value: PageType.HOLIDAY_DETAIL }
        ]
      }
    },
    currentTab: {
      title: '锚定金刚',
      type: 'string',
      widget: 'select',
      hidden: `{{ formData.pageType !== '${PageType.HOME}' || formData.type !== '${InsertType.PAGE_TYPE}' }}`,
      props: {
        options: [
          { label: '默认', value: CurrentTab.DEFAULT },
          { label: '酒店', value: CurrentTab.HOTEL },
          { label: '机票', value: CurrentTab.FLIGHT },
          { label: '火汽', value: CurrentTab.TRAIN_BUS },
          { label: '租车', value: CurrentTab.RENT_CAR },
          { label: '门票', value: CurrentTab.TICKET }
        ]
      }
    },
    h5Link: {
      hidden: `{{!((formData.type === '${InsertType.PAGE_TYPE}' && formData.pageType === '${PageType.WEBVIEW}') || formData.type === '${InsertType.H5_LINK}')}}`,
      title: 'h5链接',
      type: 'string',
      widget: 'url'
    },
    shid: {
      hidden: `{{formData.pageType !== '${PageType.HOTEL_DETAIL}' || formData.type !== '${InsertType.PAGE_TYPE}'}}`,
      title: '酒店日历房id',
      type: 'string',
      widget: 'input'
    },
    id: {
      hidden: `{{formData.pageType !== '${PageType.HOLIDAY_DETAIL}' || formData.type !== '${InsertType.PAGE_TYPE}'}}`,
      title: '宝贝id',
      type: 'string',
      widget: 'input'
    },
    poiId: {
      hidden: `{{ formData.pageType !== '${PageType.POI}' || formData.type !== '${InsertType.PAGE_TYPE}' }}`,
      title: 'poiId',
      type: 'string',
      widget: 'input'
    },
    path: {
      hidden: `{{ formData.type !== '${InsertType.WEAPP_LINK}'}}`,
      title: '小程序路径',
      type: 'string',
      widget: 'select',
      props: {
        showSearch: true,
        options: [],  
      },
    },
    customQuery: {
      title: '自定义参数',
      type: 'string',
      widget: 'input',
      placeholder: 'key1=value1&key2=value2',
      description: '拼接在小程序路径上的参数',
    },
    h5CustomQuery: {
      title: 'h5链接自定义参数',
      type: 'string',
      widget: 'input',
      hidden: `{{!formData.isWebview}}`,
      placeholder: 'key1=value1&key2=value2',
      description: '拼接在套壳h5链接上的参数'
    },
    needLogin: {
      hidden: `{{!formData.isWebview}}`,
      title: '是否强登',
      type: 'boolean',
      widget: 'checkbox',
    },
    isAct: {
      hidden: `{{!formData.isWebview}}`,
      title: '是否裂变活动',
      type: 'boolean',
      widget: 'checkbox'
    },
    safeLink: {
      hidden: `{{!formData.isWebview}}`,
      title: '是否防封禁（封禁后快速切换路径）',
      type: 'boolean',
      widget: 'checkbox'
    },
    hideHomeButton: {
      hidden: `{{!formData.isWebview}}`,
      title: '是否隐藏导航栏home图标',
      type: 'boolean',
      widget: 'checkbox'
    },
    customNavStyle: {
      hidden: `{{!formData.isWebview}}`,
      title: '是否自定义导航栏样式',
      type: 'boolean',
      widget: 'checkbox'
    },
    navBg: {
      title: '导航栏背景颜色',
      type: 'string',
      widget: 'color',
      hidden: '{{!formData.customNavStyle || !formData.isWebview}}',
      extra: '有效值为十六进制颜色（不支持设置透明度）'
    },
    navColor: {
      title: '导航栏字体颜色',
      type: 'string',
      widget: 'radio',
      hidden: '{{ !formData.customNavStyle || !formData.isWebview}}',
      props: {
        options: [
          { label: '白色', value: NavColor.WHITE },
          { label: '黑色', value: NavColor.BLACK },
        ]
      }
    },
    promotionType: {
      title: '投放方式',
      type: 'array',
      widget: 'checkboxes',
      description: '微信内投放使用',
      props: {
        options: [
          { label: '小程序短链', value: PromotionType.SHORT_LINK },
          { label: '小程序码', value: PromotionType.WEAPP_CODE },
        ]
      },
      extra: promotionExtra,
    },
    shortLinkName: {
      required: true,
      title: '小程序短链导语',
      type: 'string',
      widget: 'input',
      max: 20,
      hidden: `{{!formData.promotionType || formData.promotionType.indexOf('${PromotionType.SHORT_LINK}') === -1}}`,
    },
    weappCodeWidth: {
      title: '小程序码宽度',
      type: 'number',
      widget: 'inputNumber',
      description: '单位px',
      min: 280,
      max: 1280,
      dependencies: ['promotionType'],
      hidden: `{{!formData.promotionType || formData.promotionType.indexOf('${PromotionType.WEAPP_CODE}') === -1}}`,
      props: {
        defaultValue: 430
      }
    },
    isHyaline: {
      title: '小程序码是否需要透明底色',
      type: 'boolean',
      widget: 'checkbox',
      hidden: `{{!formData.promotionType || formData.promotionType.indexOf('${PromotionType.WEAPP_CODE}') === -1}}`,
    },
    logo: {
      title: '外投二维码logo',
      type: 'string',
      widget: 'ImgUpload'
    },
    logoWidth: {
      title: '二维码logo宽度',
      type: 'number',
      widget: 'inputNumber',
      description: '单位px',
      min: 10,
      max: 50,
      hidden: '{{!formData.logo}}',
      props: {
        defaultValue: 30
      }
    },
    logoHeight: {
      title: '二维码logo高度',
      type: 'number',
      widget: 'inputNumber',
      description: '单位px',
      min: 10,
      max: 50,
      hidden: '{{!formData.logo}}',
      props: {
        defaultValue: 30
      }
    },
  }
}