// @ts-ignore
import { getWechatUrl } from '@ali/rxpi-wechat-url-map';
import { PAGE_TYPE_URL_MAP, WEBVIEW_PATH, WEBVIEW_PAGE_TYPE, WEBVIEW_PATH_LIST, ROUTER_REDIRECT, ACT_WEBVIEW_LIST, ACT_WEBVIEW } from './constants';
import { InsertType, PageType } from '../types';

function getWebviewUrlAndQuery(queryStr: string) {
  let url = '';
  const query = queryStr.split('&').filter(v => {
    const [queryKey, queryVal] = v.split('=');
    if (queryKey === 'url') {
      url = decodeURIComponent(decodeURIComponent(queryVal));
      return false;
    }
    return true;
  }).join('&');
  return {
    url,
    query,
  }
}

/** 判断是否套壳 */
export const judeIsWebview = (formData: Record<string, string>) => {
  // 页面类型
  if (formData.type === InsertType.PAGE_TYPE && WEBVIEW_PAGE_TYPE.indexOf(formData.pageType as PageType) > -1) {
    return true;
  }
  // 小程序路径
  if (formData.type === InsertType.WEAPP_LINK) {
    return WEBVIEW_PATH_LIST.some(v => v === formData.path.trim());
  }
  // url 统一化
  if (formData.type === InsertType.H5_LINK && formData.h5Link) {
    const pagePath = getWechatUrl(formData.h5Link.trim());
    return pagePath.startsWith('https://');
  }
  return false;
};

const getWebviewQuery = (formData: any) => {
  const { pageType, type, h5Link, needLogin, hideHomeButton, customNavStyle, navBg, navColor, h5CustomQuery, safeLink } = formData;
  let customQuery = formData.customQuery || '';
  let h5Url = '';
  const config = PAGE_TYPE_URL_MAP[pageType];
  if (type === InsertType.PAGE_TYPE && config) {
    const { pageKey, url } = config;
    h5Url = formData[pageKey] ? `${url}?${pageKey}=${formData[pageKey]}` : `${url}`;
  } else if (type === InsertType.WEAPP_LINK) {
    const { url, query } = getWebviewUrlAndQuery(customQuery);
    h5Url = url;
    customQuery = query;
  } else {
    h5Url = h5Link.trim();
  }
  // h5自定义参数
  if (h5CustomQuery) {
    h5Url += h5Url.indexOf('?') > -1 ? `&${h5CustomQuery}` : `?${h5CustomQuery}`;
  }
  let query = `url=${encodeURIComponent(encodeURIComponent(h5Url))}`;
  // 强登
  if (needLogin) {
    query += '&needLogin=true';
  }
  // 防封禁
  if (safeLink) {
    query += '&pageKey=actWebview';
  }
  // 隐藏home icon
  if (hideHomeButton) {
    query += '&hideHomeButton=true';
  }
  // 自定义导航栏颜色
  if (customNavStyle) {
    if (navBg) {
      query += `&navBg=${encodeURIComponent(navBg)}`;
    }
    if (navColor) {
      query += `&navColor=${encodeURIComponent(navColor)}`;
    }
  }

  // 自定义参数
  if (customQuery) {
    query += `&${customQuery}`;
  }
  return query;
};

function getWebviewPath(formData: any) {
  const { isAct, safeLink, path } = formData;
  // 防封禁
  if (safeLink) {
    return ROUTER_REDIRECT;
  }
  // 活动套壳
  if (isAct || ACT_WEBVIEW_LIST.some(v => v === path)) {
    return ACT_WEBVIEW;
  }
  // 普通套壳
  return WEBVIEW_PATH;
}

/** 获取小程序链接 */
export const getWeappUrlInfo = (formData: any) => {
  const { isWebview, type, customQuery, pageType } = formData;
  let path;
  let query = '';
  if (isWebview) {
    path = getWebviewPath(formData);
    query = getWebviewQuery(formData);
  } else if (type === InsertType.PAGE_TYPE) {
    const { path: configPath, pageKey } = PAGE_TYPE_URL_MAP[pageType];
    path = configPath;
    if (formData[pageKey]) {
      query = `${pageKey}=${formData[pageKey]}`;
    }
  } else if (type === InsertType.H5_LINK) {
    path = getWechatUrl(formData.h5Link.trim());
  } else {
    path = formData.path.trim();
  }

  if (!isWebview && customQuery) {
    query += !!query ? `&${customQuery}` : customQuery;
  }

  return {
    path,
    query,
  }
};