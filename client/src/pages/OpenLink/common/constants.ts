import { PageType, QrCodeType } from "../types";

export const HOME_PAGE = '/pages/main/home';
export const WEBVIEW_PATH = '/pages/main/webview';
export const ACT_WEBVIEW = '/pages/main/act-webview';

// 防封禁路径
export const ROUTER_REDIRECT = '/pages/utils/redirect/index'; 

// 普通套壳容器
export const NORMAL_WEBVIEW = [
  WEBVIEW_PATH,
  '/pages/utils/webview/index',
];

// 裂变活动套壳容器
export const ACT_WEBVIEW_LIST = [
  ACT_WEBVIEW,
  '/pages/utils/act-webview/index',
  '/pages/utils/act-webview2/index',
  '/pages/utils/act-webview3/index',
  '/pages/utils/act-webview4/index',
  '/pages/utils/act-webview5/index',
  '/pages/utils/act-webview6/index'
];

export const WEBVIEW_PATH_LIST = [
  ...NORMAL_WEBVIEW,
  ...ACT_WEBVIEW_LIST,
];

// 太阳码中间页
export const QRCODE_PATH = {
  NORMAL: 'pages/utils/weappcode/index',
  RISK: 'pages/utils/weappcode-act/index' // 防封禁使用
}

// 套壳页面
export const WEBVIEW_PAGE_TYPE = [
  PageType.HOTEL_DETAIL,
  PageType.POI,
  PageType.WEBVIEW,
];

export const PAGE_TYPE_URL_MAP: Record<string, { path: string; pageKey: string; url?: string; }> = {
  [PageType.HOME]: {
    path: HOME_PAGE,
    pageKey: 'currentTab',
  },
  [PageType.HOLIDAY_DETAIL]: {
    path: '/pages/holiday/detail/index',
    pageKey: 'id',
  },
  [PageType.HOTEL_DETAIL]: {
    path: WEBVIEW_PATH,
    pageKey: 'shid',
    url: 'https://market.m.taobao.com/app/trip/h5-hotel-detail/pages/detail/index.html',
  },
  [PageType.POI]: {
    path: WEBVIEW_PATH,
    pageKey: 'poiId',
    url: 'https://outfliggys.m.taobao.com/app/trip/rx-trip-ticket/pages/detail',
  }
};

export const RESULT_MAP = [
  {
    label: '小程序源路径',
    key: 'weappUrl',
  },
  {
    label: '小程序短链',
    key: 'weappShortLink',
  },
  {
    label: '外投源链接',
    key: 'url',
  },
  {
    label: '外投短链接',
    key: 'shortLink',
  },
  {
    label: '推广码',
    key: 'qrcode',
    list: [
      {
        id: QrCodeType.QR_CODE,
        name: '外投二维码',
        key: 'shortLink',
      },
      {
        id: QrCodeType.WEAPP_QR_CODE,
        name: '小程序码',
        key: 'weappCode',
      }
    ]
  }
];