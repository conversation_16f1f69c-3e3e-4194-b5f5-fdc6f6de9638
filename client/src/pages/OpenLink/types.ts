export enum InsertType {
  PAGE_TYPE = 'pageType',
  H5_LINK = 'h5Link',
  WEAPP_LINK = 'weappLink'
}

export enum PageType {
  HOME = 'home', // 首页
  HOTEL_DETAIL = 'hotel', // 酒店详情
  POI = 'poi', // poi详情
  HOLIDAY_DETAIL = 'holiday', // 度假宝贝
  WEBVIEW = 'webview', // 套壳
}

export enum PromotionType {
  SHORT_LINK = 'shortLink', // 短链
  WEAPP_CODE = 'weappCode', // 小程序码
}

export enum CurrentTab {
  DEFAULT = '',
  HOTEL = 'hotel',
  FLIGHT = 'flight',
  TRAIN_BUS = 'trainBus',
  RENT_CAR = 'rentCar',
  TICKET = 'ticket',
}

export enum NavColor {
  WHITE = '#ffffff',
  BLACK = '#000000',
}

export enum QrCodeType {
  QR_CODE = 'qrcode', // 外投二维码
  WEAPP_QR_CODE = 'weappCode', // 小程序码
}

export interface IUrlObj {
  url: string;
  shortLink: string;
  weappUrl: string;
  settings?: {
    src: string;
    height: number;
    width: number;
    excavate: boolean;
  };
  [key: string]: any;
}