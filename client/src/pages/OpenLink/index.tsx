import { useEffect, useState, useRef, Fragment } from 'react';
import { Typography, Button, Space, Divider, Row, Col, Card, message, Alert } from 'antd';
import { useForm } from 'form-render';
import FormRender from '@/components/FR';
import ImgUpload from './components/ImgUpload';
import QrCode from './components/QrCode';
import { InsertType, IUrlObj, PromotionType } from './types';
import urlApi from '@/api/url';
import packageApi from '@/api/package';
import shortLinkApi from '@/api/short-link';
import openLinkApi from '@/api/open-link';

import { RESULT_MAP, HOME_PAGE, QRCODE_PATH } from './common/constants';
import { judeIsWebview, getWeappUrlInfo } from './common/utils';
import schema from './schema';

const { Title, Paragraph, Link } = Typography;

const H5_URL = 'https://proxy-er.feizhu.com/app/trip/rx-fliggy-router/pages/wx?_fm_real_host_=outfliggys.m.taobao.com'

import './index.less';

export default () => {
  const [loading, setLoading] = useState(false);
  const [urlInfo, setUrlInfo] = useState<IUrlObj | null>(null);
  const wxUrlMap = useRef<Record<string, string>>({});
  const form = useForm();

  const watch = {
    '#': (val: any) => {
      const isWebview = judeIsWebview(val);
      form.setValueByPath('isWebview', isWebview);
    },
  };

  const onFormMount = () => {
    console.log(form.getValues())
    form.setValueByPath('type', InsertType.PAGE_TYPE);
  };

  const generateShortLink = (longLink: string) => {
    return shortLinkApi.creat({ longLink })
      .then(res => {
        const shortLink = res?.data?.shortKey;
        if (!shortLink) return Promise.reject();
        return shortLink;
      })
      .catch(() => {
        message.error('生成短链失败');
        return null;
      });
  };

  const getPromotionInfo = (formData: Record<string, any>, weappUrl: string) => {
    const { shortLinkName, weappCodeWidth = 430, isHyaline = false, promotionType = [], safeLink } = formData;
    const needQrcode = promotionType.indexOf(PromotionType.WEAPP_CODE) > -1;
    const needShortLink = promotionType.indexOf(PromotionType.SHORT_LINK) > -1;
    if (!needQrcode && !needShortLink) {
      return Promise.resolve({});
    }
    return openLinkApi.getWxPromotionInfo({
      url: weappUrl,
      needQrcode,
      needShortLink,
      shortLinkName,
      weappCodeWidth,
      isHyaline,
      qrcodePath: safeLink ? QRCODE_PATH.RISK : QRCODE_PATH.NORMAL,
    })
      .then(res => {
        const data = res?.data;
        if (!data) return Promise.reject();
        return data;
      })
      .catch(() => {
        message.error('生成微信推广链接失败');
        return {};
      });
  };

  const onFormFinish = () => {
    form.validateFields().then((formData: any) => {
      setLoading(true);
      const { path, query } = getWeappUrlInfo(formData);
      const linkTag = query ? '?' : '';
      const weappUrl = `${path}${linkTag}${query}`;
      let url = `${H5_URL}&path=${encodeURIComponent(path)}`;
      if (query) {
        // 经过proxy代理，会被decode一次
        url += `&query=${encodeURIComponent(query)}`;
      }
      const urlObj = { url, weappUrl } as IUrlObj;
      // 二维码logo
      if (formData.logo) {
        urlObj.settings = {
          src: formData.logo,
          height: formData.logoHeight || 30,
          width: formData.logoWidth || 30,
          excavate: true,
        }
      }
      // 生成短链
      Promise.all([
        generateShortLink(url),
        getPromotionInfo(formData, weappUrl)
      ])
        .then((([shortLink, promotionInfo]) => {
          setUrlInfo({
            ...urlObj,
            ...promotionInfo,
            shortLink,
            isHyaline: formData.isHyaline,
          });
          setLoading(false);
        }))
        .catch(() => {
          setLoading(false);
        });
    });
  };

  const onReset = () => {
    form.resetFields();
    onFormMount();
  };

  /** 获取url统一化配置 */
  const fetchUrlMap = () => {
    urlApi.list({
      projectName: 'fliggy-weixin'
    }).then(res => {
      wxUrlMap.current = (res.data || []).reduce((prev, val) => {
        return {
          ...prev,
          [val.h5Url]: val.pagePath
        }
      }, {});
    })
  };

  const fetchPages = () => {
    packageApi.getStructureByBranch({
      branchName: 'master',
      branchUrl: '**************************:trip/rx-fliggy-weixin.git'
    }).then(res => {
      if (!res.data?.packages || !res.data.packages.length) {
        return Promise.reject(res);
      }
      const pages = res.data.packages.flatMap((pkg: any) => pkg.pages?.flatMap((page: any) => [`/${page.source}`]) || []) || [];
      const options = pages.map(page => ({ label: page, value: page }));
      form.setSchemaByPath('path', {
        props: { options }
      });
    }).catch(err => {
      console.error('获取页面路径失败', err);
      // 兜底改成用户输入框
      form.setSchemaByPath('path', {
        widget: 'input',
        placeholder: HOME_PAGE
      });
    })
  };

  useEffect(() => {
    fetchUrlMap();
    fetchPages();
    window.open('https://fl-flian.fc.alibaba-inc.com/url-schema/record?id=7', '_blank');
  }, []);

  return (
    <Card>
      <div style={{ marginBottom: '16px' }}>
        <Alert type='info' showIcon message="前往飞链平台使用新版微信外投工具" action={<a href="https://fl-flian.fc.alibaba-inc.com/url-schema/record?id=7">立即前往</a>} />
      </div>
      <Title level={3}>微信小程序投放链接生成</Title>
      <FormRender
        className="open-link-forms"
        form={form}
        schema={schema}
        removeHiddenData={true}
        watch={watch}
        widgets={{ ImgUpload }}
        onMount={onFormMount}
        onFinish={onFormFinish}
      />
      <Space className="open-link-btns">
        <Button onClick={onReset}>重置表单</Button>
        <Button type="primary" loading={loading} onClick={form.submit}>生成链接</Button>
      </Space>
      <Divider />
      {urlInfo && (
        <div>
          {RESULT_MAP.map(item => (
            <Row className="open-link-result" key={item.key}>
              <Col flex="180px" className="open-link-label">{item.label}：</Col>
              {item.list ? (
                <Space size="large">
                  {item.list.map(v => (
                    urlInfo[v.key] ? <QrCode key={v.id} type={v.id} urlInfo={urlInfo} /> : null
                  ))}
                </Space>
              ) :
                (
                  <Col flex="auto">
                    {urlInfo[item.key] && (<Paragraph copyable={{ text: urlInfo[item.key] }}>
                      <Link href={urlInfo[item.key]} target="_blank">
                        {urlInfo[item.key]}
                      </Link>
                    </Paragraph>)}
                  </Col>
                )}
            </Row>
          ))}
        </div>
      )}
    </Card>
  );
};