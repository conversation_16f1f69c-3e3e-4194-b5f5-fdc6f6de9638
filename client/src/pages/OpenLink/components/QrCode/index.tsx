import React, { memo, useRef, useEffect, useState, useMemo } from 'react';
import { Button, Space } from 'antd';
import QRCode from 'qrcode.react';
import SvgSaver from 'svgsaver';
import { DownloadOutlined } from '@ant-design/icons';
import { IUrlObj, QrCodeType } from '../../types';

interface IProps {
  urlInfo: IUrlObj;
  type: string;
}

const QrCode = (props: IProps) => {
  const { urlInfo, type } = props;
  const svgRef = useRef<SVGSVGElement>(null);
  const [svgCode, setSvgCode] = useState('');

  const getQrcodeUrl = () => {
    return new Promise(resolve => {
      if (type === QrCodeType.WEAPP_QR_CODE) {
        const img = new Image();
        img.setAttribute('crossOrigin', 'anonymous');
        img.onload = () => {
          const canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext('2d');
          ctx?.drawImage(img, 0, 0, img.width, img.height);
          resolve(canvas.toDataURL('image/png'));
        }
        img.src = urlInfo.weappCode;
        return;
      }
      const qrcvs = document.querySelector('#qrcvs') as HTMLCanvasElement;
      resolve(qrcvs.toDataURL('image/png'));
    });
  }

  // 下载png
  const downloadImg = () => {
    getQrcodeUrl().then((imgUrl) => {
      if (!imgUrl) return;
      const downLink = document.createElement('a');
      // @ts-ignore
      downLink.href = imgUrl;
      downLink.download = '推广码';
      downLink.click();
    });
  };

  const downloadSvg = () => {
    const svgsaver = new SvgSaver();
    // 保存svg图片
    svgsaver.asSvg(svgRef.current, 'QRcode.svg');
  };

  // 绘制svg
  useEffect(() => {
    if (type === QrCodeType.WEAPP_QR_CODE) {
      setSvgCode(urlInfo.weappCode);
      return;
    }
    setTimeout(() => {
      const qrcvs = document.querySelector('#qrcvs') as HTMLCanvasElement;
      if (qrcvs) {
        const dataUrl = qrcvs.toDataURL('image/png');
        setSvgCode(dataUrl);
      }
    }, 10);
  }, [urlInfo.shortLink, type]);

  return (
    <Space direction="vertical" align="center">
      {type === QrCodeType.QR_CODE ? (
        <QRCode
          id='qrcvs'
          value={urlInfo.shortLink}
          level='H'
          size={200}
          fgColor='#000000'
          imageSettings={urlInfo.settings}
        />
       ) : (
        <img src={urlInfo.weappCode} className="open-link-weappcode" />
      )}
      <Space align="center">
        <Button
          type="primary"
          shape="round"
          icon={<DownloadOutlined />}
          onClick={downloadImg}
        >
          PNG 下载
        </Button>
        <Button
          type="primary"
          shape="round"
          icon={<DownloadOutlined />}
          onClick={downloadSvg}
        >
          SVG 下载
        </Button>
      </Space>
      <div style={{ display: 'none' }}>
        <svg width={400} height={400} ref={svgRef}>
          <image href={svgCode} style={{width: '100%', height: '100%'}} />
        </svg>
      </div>
    </Space>
  )
}

export default memo(QrCode);