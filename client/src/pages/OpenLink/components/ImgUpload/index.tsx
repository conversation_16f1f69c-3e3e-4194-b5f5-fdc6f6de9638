import React, { memo, useEffect, useState } from 'react';
import { Upload, Button, message } from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import { UploadOutlined } from '@ant-design/icons';

interface IProps {
  value?: UploadFile;
  onChange: (val: string) => void;
}

const ImgUpload = (props: IProps) => {
  const [fileList, setFileList] = useState([]);
  const { onChange, value } = props;

  const onImgChange = (e: any) => {
    if (e.file.size / 1024 > 50) {
      message.error('图片尺寸太大，请压缩至 50KB 以内再上传');
      return;
    }
    const reader = new FileReader();
    reader.onload = () => {
      setFileList(e.fileList);
      onChange(reader.result as string);
    };
    reader.readAsDataURL(e.fileList[0].originFileObj);
  };

  const beforeUpload = () => {
    return false;
  }

  const onRemove = () => {
    onChange('');
    setFileList([]);
  };

  useEffect(() => {
    if (!value) {
      setFileList([]);
    }
  }, [value]);

  return (
    <Upload
      accept="image/jpeg,image/png"
      listType="picture"
      maxCount={1}
      fileList={fileList}
      beforeUpload={beforeUpload}
      onChange={onImgChange}
      onRemove={onRemove}
    >
      <Button icon={<UploadOutlined />}>点击上传</Button>
    </Upload>
  )
}

export default memo(ImgUpload);