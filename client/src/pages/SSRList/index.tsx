import { useEffect, useState, useRef } from 'react';
import { Space, Radio, Popconfirm, Divider, message, Popover, Spin, Row, Steps, Button, List, Tag, Card, Select, Dropdown } from 'antd';
import {
  QrcodeOutlined,
  PlusOutlined,
  CopyOutlined,
  DownOutlined,
  CheckCircleOutlined,
  SettingOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { get } from 'lodash';
import { useModel } from 'umi';
import QRCode from 'qrcode.react';
import { ProTable, TableDropdown } from '@ant-design/pro-components';
import VersionPad from './components/VersionPad';
import GrayPad from './components/GrayPad';
import ProjectPad from './components/ProjectPad';
import PagePad from './components/PagePad';
import AdminPad from './components/AdminPad';
import TargetPad from './components/TargetPad';
import ssrDetail from '@/api/ssr-detail';
import def from '@/api/def';
import SSRDetailApi from '@/api/ssr-detail';
import './index.less';
import EnforceGrayPad from './components/EnforceGrayPad';
import { sleepFunc } from '@/utils/base';

export default () => {
  const [tabType, setTabType] = useState('my');
  const [showVersionPad, setShowVersionPad] = useState({
    show: false,
    type: 'pre',
    data: {}
  });
  const [showGrayPad, setGrayPad] = useState({
    show: false,
    data: {}
  });
  const [showTargetGray, setTargetGray] = useState({
    show: false,
    data: {}
  });
  const [showProjectAdd, setShowProjectAdd] = useState(false);
  const [showPageAdd, setShowPageAdd] = useState({
    show: false,
    data: {},
    type: 'add'
  })
  const [showAdminPad, setShowAdminPad] = useState({
    show: false,
    disabled: true,
    data: {}
  })
  const [showEnforceGrayPad, setShowEnforceGrayPad] = useState({
    show: false,
    data: {},
  })
  const [tableLoading, setTableLoading] = useState(false);
  const tableRef: any = useRef();
  const currentUser = useModel('user');

  const canRollback = (record:any) =>{
    return get(record, 'childrenList', []).find((r:any)=> r.prod_version) ? false : true
  }

  // 同步def权限到项目中
  const syncPermission = async (record:any)=>{
    setTableLoading(true);
    const memberData = await def.getDefMember({defAppIdorName: encodeURIComponent(encodeURIComponent(`${record.project_group || 'trip'}/${record.project_name}`))});
    const member = get(memberData, 'data.data',[]).map((r:any)=> {return {value: r.emp_id, label: r.user.nick}})
    if(!member || !member.length){
      message.error('def成员信息同步失败,请联系管理员手动添加@行鸢');
      setTableLoading(false);
      return;
    }
    const newAdminList: any = JSON.parse(record.admin_list) || [];
    member.forEach((element: any) => {
      if(!newAdminList.find((res:any)=> res.value === element.value)){newAdminList.push(element)}
    });
    SSRDetailApi.updateProject({
      id: record.id,
      adminList: JSON.stringify(newAdminList),
      projectGroup: record.project_group,
      projectBusiness: record.project_business,
      pid: record.pid,
      isAssociatedDef: record.is_associated_def || 2,
    }).then(res => {
      if (res.success) {
        message.success('同步成功');
      } else {
        message.error(res.errorMsg)
      }
    }).catch(e => {
      message.error(e.message || '同步失败' + ',请联系管理员手动添加@行鸢');
    }).finally(() => {
      tableRef.current.reload();
      setTableLoading(false);
    })
  }

  const BUSINIESS_MAP: any = {
    hotel: '酒店',
    traffic: '交通',
    vacation: '度假',
    btrip: '商旅',
    member: '会员'
  }

  const columns = [{
    width: '16%',
    title: '项目名称',
    dataIndex: 'projectName',
    render(text: any, record: any) {
      return (
        <div style={{ display: 'flex', flexDirection: 'row' }}>
          <a>{record.project_name}</a>
          {(record.childrenList || []).filter((item: any) => item.gray_version).length > 0 ? <Tag color="gold" style={{ height: '22px' }}>灰</Tag> : null}
        </div>
      )
    }
  }, {
    width: '7%',
    title: '行业',
    dataIndex: 'project_business',
    render(text: any, record: any) {
      return <div>{BUSINIESS_MAP[text] || '默认'}</div>
    },
    renderFormItem() {
      return <Select
      defaultValue="default"
      options={[
        { value: 'default', label: '全部' },
        { value: 'hotel', label: '酒店' },
        { value: 'traffic', label: '交通' },
        { value: 'vacation', label: '度假'},
        { value: 'btrip', label: '商旅'},
        { value: 'member', label: '会员'}
      ]}
    />
    }
  }, {
    width: '15%',
    title: '最近操作时间',
    dataIndex: 'gmt_modified',
    hideInSearch: true
  }, {
    width: '12%',
    title: '最近操作人',
    dataIndex: 'last_operator',
    hideInSearch: true
  }, 
  {
    width: '16%',
    title: '版本',
    dataIndex: 'version',
    hideInSearch: true,
    render(text: any, record: any) {
      return <div>-</div>
    }
  }, 
  {
    title: '操作',
    hideInSearch: true,
    width: '23%',
    render(text: any, record: any) {
      if (record.page_name) {
        return (
          <>
            {
              record.enforce_downgrade === 2 ? 
              <>
                <Button danger onClick={() => { setDowngrade(record) }}>解除强制降级</Button> 
              </>:
              <>
                <Button type="link" size="small" disabled={record.isDisabled} onClick={() => { handlePublish('pre', record) }}>预发</Button>
                <Divider type='vertical' />
                {record.gray_version ? <Button type="link" size="small" disabled={record.isDisabled} onClick={() => { handleGray(record) }}>灰度</Button> :
                  <Button type="link" size="small" disabled={record.isDisabled} onClick={() => { handlePublish('prod', record) }}>正式</Button>}
                <Divider type='vertical' /> 
                {record.gray_version ? <Button type="link" size="small" disabled={record.isDisabled} onClick={() => { handleGray(record, true) }}>定向灰度</Button> : null}
                {record.gray_version ? <Divider type='vertical' /> : null}
                <a href={`${window.location.origin}/#/ssr/detail?id=${record.id}`} target="_blank">更多</a>
              </>
            }
          </>
        )
      }
      return (
        <>
          <Button type="link" size="small" disabled={record.isDisabled} onClick={() => { handlePublish('pre', record) }}>预发发布</Button>
          <Divider type='vertical' />
          <Button type="link" size="small" disabled={record.isDisabled} onClick={() => { handlePublish('publish', record) }}>正式发布</Button>
          <Button type="link" size="small" disabled={record.isDisabled || canRollback(record)} onClick={() => { handleRollback('rollback', record) }}>一键回滚</Button>
          {/* {record.isDisabled ? <Button type="link" size="small" onClick={()=>{syncPermission(record)}}>同步权限</Button> : null} */}
          <Button type="link" size="small" onClick={()=>{syncPermission(record)}}>同步权限</Button>
          {/* <Divider type='vertical' /> */}
          <Divider type='vertical' />
          {['205521', '359190','265692'].includes(currentUser.workid) ? <><Button type="link" size="small" disabled={record.isDisabled} onClick={() => {
            setShowPageAdd({
              show: true,
              data: record,
              type: 'add'
            })
          }}>新增页面</Button><Divider type='vertical' /></> : null}

          <TableDropdown
            key="actionGroup"
            menus={[
              { 
                key: 'admin', 
                name: (
                  <>{record.isDisabled ? <Button type="link" size="small" onClick={() => {
                      setShowAdminPad({
                        show: true,
                        disabled: true,
                        data: record
                      })
                    }}>管理员列表</Button> : <Button type="link" size="small" onClick={() => {
                      setShowAdminPad({
                        show: true,
                        disabled: false,
                        data: record
                      })
                    }}>项目管理</Button>}
                  </>
                )
              },
              { 
                key: 'git', 
                name: (
                  <Button type="link" size="small" onClick={() => {
                    open(`https://code.alibaba-inc.com/${record.project_group || 'trip'}/${record.project_name}`)
                  }}>代码仓库</Button>
                ) 
              },
              {
                key: 'delete',
                name: (
                  <Popconfirm
                  title="确定删除项目？"
                  onConfirm={() => deleteSSR(record)}
                  okText="确定"
                  cancelText="取消"
                  disabled={record.isDisabled}
                  ><Button type="link" size="small" disabled={record.isDisabled} danger={true}>删除</Button></Popconfirm>
                )
              }
            ]}
          />
        </>
      )
    }
  }];

  useEffect(() => {
    tableRef.current.reloadAndRest();
  }, [tabType])

  const setDowngrade = (val: any) => {
    ssrDetail.setDowngrade({
      id: val.id,
      enforceDowngrade: 1, // 1解除降级，2开启降级
    }).then(res => {
      if (res.success) {
        message.success('解除强制降级成功');
        tableRef.current.reloadAndRest();
      } else {
        message.error(res.errorMsg)
      }
    }).catch(e => {
      message.error(e.message || '解除失败');
    })
  }

  const deleteSSR = (val: any) => {
    ssrDetail.delete({
      id: val.id,
      pageName: val.page_name
    }).then(res => {
      if (res.success) {
        message.success('删除成功');
        tableRef.current.reloadAndRest();
      } else {
        message.error(res.errorMsg)
      }
    }).catch(e => {
      message.error(e.message || '删除失败');
    })
  }

  const handlePublish = (type: string, val: any) => {
    setShowVersionPad({
      show: true,
      type,
      data: val
    })
  }

  const handleRollback = (type: string, val: any) => {
    setShowVersionPad({
      show: true,
      type,
      data: val
    })
  }


  const handleGray = (val: any, isTarget?: any) => {
    if (isTarget) {
      setTargetGray({
        show: true,
        data: val
      })
    } else {
      setGrayPad({
        show: true,
        data: val
      })
    }
  }

  const handleProjectGray = async (ratio: any, data: any, enforcePub = false) => {
    setTableLoading(true);

    const pageList = data.childrenList || [];

    if (ratio < 100) {
      Promise.all(pageList.map(async (item: any) => {
        if (item.gray_version) {
          return ssrDetail.ssrGray({
            id: item.id,
            projectName: item.project_name,
            pageName: item.page_name,
            version: item.gray_version,
            ratio: ratio,
            enforcePub,
          });
        }
      }).filter(Boolean)).then(res => {
        setTableLoading(false);
        
        let defIsBlock = false;
        let blockBody = '';
        let orderUrl = '';
        res.forEach(r_item => {
          const is_block = get(r_item, 'data.updateDef.data.data.is_block') || false;
          const stepin_msg = get(r_item, 'data.updateDef.data.data.body.stepin_msg') || '';
          const order_url = get(r_item, 'data.updateDef.data.data.body.orderUrl') || '';
          if (is_block) {
            defIsBlock = true;
            blockBody = stepin_msg;
            orderUrl = order_url;
          }
        });

        if (defIsBlock && !enforcePub) {
          setShowEnforceGrayPad({
            show: true,
            data: {
              ratio,
              projectData: data,
              blockBody,
            },
          });
        } else {
          message.success(`灰度成功`);
          if (orderUrl) {
            open(orderUrl);
          }
        }

        tableRef.current.reload();
      }).catch(e => {
        setTableLoading(false);
        message.error(e.message || '灰度失败');
        tableRef.current.reload();
      });
    } else {
      let defIsBlock = false;
      let blockBody = '';
      let orderUrl = '';

      async function batchPublish() {
        for (let i = 0; i < pageList.length; i++) {
          const item = pageList[i] || {};
          if (item.gray_version) {
            await ssrDetail.ssrPublish({
              id: item.id,
              projectName: item.project_name,
              pageName: item.page_name,
              needLogin: item.need_login,
              version: item.gray_version,
              enforcePub,
            }).then(res => {
              const is_block = get(res, 'data.updateDef.data.data.is_block') || false;
              const stepin_msg = get(res, 'data.updateDef.data.data.body.stepin_msg') || '';
              const order_url = get(res, 'data.updateDef.data.data.body.orderUrl') || '';
              if (is_block) {
                defIsBlock = true;
                blockBody = stepin_msg;
                orderUrl = order_url;
              } else if (res.success) {
                message.success('发布成功');
              } else {
                message.error(res.errorMsg);
              }
            }).catch(e => {
              message.error(e.message || '发布失败');
            });

            await sleepFunc();
          }
        }
      }
      
      await batchPublish();

      if (defIsBlock && !enforcePub) {
        setShowEnforceGrayPad({
          show: true,
          data: {
            ratio,
            projectData: data,
            blockBody,
          },
        });
      }

      setTableLoading(false);
      tableRef.current.reload();
      if (orderUrl) {
        open(orderUrl);
      }
    }
  }

  const cancelProjectGray = async (data: any, item?: any) => {
    setTableLoading(true);

    const pageList = item ? [item] : data.childrenList || [];

    async function batchCancelGray() {
      for (let i = 0; i < pageList.length; i++) {
        const item = pageList[i] || {};
        if (item.gray_version) {
          await ssrDetail.ssrGray({
            id: item.id,
            projectName: item.project_name,
            pageName: item.page_name,
            version: item.gray_version,
            ratio: -1,
          }).then(res => {
            message.success(`取消灰度成功`);
          }).catch(e => {
            message.error(e.message || '取消灰度失败');
          })
        }
      }
    }


    await batchCancelGray();

    setTableLoading(false);
    tableRef.current.reload();
  }

  const getQRCodeUrl = (type: any, val: any) => {
    const preHost = val.project_business === 'btrip' ? 'er.wapa.alibtrip.com' : 'outfliggys.wapa.taobao.com';
    const prodHost = val.project_business === 'btrip' ? 'er.m.alibtrip.com' : 'outfliggys.m.taobao.com';
    const host: any = {
      'pre': `https://${preHost}/app/${val.project_group || 'trip'}/`,
      'prod': `https://${prodHost}/app/${val.project_group || 'trip'}/`,
    }
    const queryStr = val.is_immersive === 2 ? 'disableNav=YES&titleBarHidden=2' : '';
    return `${host[type]}${val.project_name}/pages/${val.page_name}${queryStr ? `?${queryStr}` : ''}`
  }

  const copyLink = (type: any, val: any) => {
    const url = getQRCodeUrl(type, val);
    navigator.clipboard.writeText(url)
      .then(() => {
        message.success('复制成功')
      }).catch((err) => {
        message.error(err?.message || '复制失败！')
      });
  }

  const onRadioChange = (val: any) => {
    if (tabType !== val.target.value) {
      setTabType(val.target.value);
    }
  }

  const getGrayList = (data: any) => {
    let grayRatio = 0;
    (data && data.childrenList || []).forEach((item: any) => {
      if (item.gray_ratio && item.gray_ratio > grayRatio) {
        grayRatio = item.gray_ratio;
      }
    });

    const ratioList = [0, 5, 25, 50, 100];
    const grayRatioIndex = ratioList.indexOf(grayRatio) || 0;
    const grayList = ratioList.map((value, index) => {
      let ratioItem = {
        icon: <CheckCircleOutlined />,
        disabled: true,
        title: `${value}%`,
        value,
      };

      if (index === grayRatioIndex + 1) {
        ratioItem = {
          ...ratioItem,
          icon: (
            <Popconfirm
              title="确定灰度放量？"
              onConfirm={() => {
                handleProjectGray(value, data);
              }}
              okText="确定"
              cancelText="取消"
            >
              <SettingOutlined style={{ color: '#1890ff' }} />
            </Popconfirm>
          ),
          disabled: false,
        }
      }

      return ratioItem;
    });

    return {
      grayRatioIndex, 
      grayList,
    };
  }

  return (
    <>
      <ProTable
        rowKey="id"
        search={{
          defaultCollapsed: false
        }}
        pagination={{
          showQuickJumper: true,
          pageSize: 10
        }}
        loading={{
          spinning: tableLoading,
          indicator: (<Spin tip={
          <span
            style={{
              position: 'absolute',
              width: '500px',
              left: '50%',
              transform: 'translateX(-50%)',
              marginTop: '10px',
            }}
          >页面正在发布中，请不要关闭页面~</span>}></Spin>)
        }}
        columns={columns}
        actionRef={tableRef}
        request={async (params, sorter) => {
          const resData = await ssrDetail.ssrList({
            ...params,
            tabType,
            isSearch: true
          }).then(res => {
            if (res.success && res.data) {
              const list = (res.data && res.data.data || []).map((item: any) => {
                const childrenList = item.children || [];
                // 换个名字，children这个key会导致嵌套表格
                delete item['children'];
                return {
                  ...item,
                  childrenList,
                }
              });
              return {
                data: list,
                total: res.data && res.data.total | 0,
              }
            }
            return {};
          }).catch(e => { })
          return resData;
        }}
        toolbar={{
          title: (
            <Space>
              <Radio.Group onChange={onRadioChange} defaultValue="my">
                <Radio.Button value="my">我的项目</Radio.Button>
                <Radio.Button value="all">所有项目</Radio.Button>
              </Radio.Group>
            </Space>
          ),
          actions: ['205521', '359190','265692'].includes(currentUser.workid) ?
            [<Button type="primary" onClick={() => setShowProjectAdd(true)}><PlusOutlined />新建项目</Button>] : []
        }}
        expandable={{
          fixed: 'left',
          columnWidth: '4%',
          rowExpandable: (record) => {return record && record.childrenList && record.childrenList.length > 0},
          expandedRowRender: (record) => {
            const { grayRatioIndex, grayList } = getGrayList(record);
            return (
              <div>
                {(record.childrenList || []).filter((item: any) => item.gray_version).length > 0 ? 
                <Card 
                  title="灰度放量"
                  extra={ <Popconfirm
                    title="确定取消灰度？"
                    onConfirm={() => cancelProjectGray(record)}
                    okText="确定"
                    cancelText="取消"
                    disabled={record.isDisabled}
                    ><a>取消灰度</a></Popconfirm>}
                >
                  <Steps 
                    current={grayRatioIndex}
                    items={grayList} 
                    size="small" 
                    labelPlacement="vertical"
                  />
                </Card> : null
                }

                <List
                  dataSource={record.childrenList || []}
                  renderItem={(item: any) => (
                    <List.Item>
                      <Row style={{ width: '100%' }}>
                        <div className='table-expand-item' style={{ width: '4%' }}>{''}</div>
                        <div className='table-expand-item' style={{ width: '18%' }}>
                          <span>{item.page_name || '-'}</span>
                          {item.gray_version ? <Tag color="gold" style={{ height: '22px' }}>灰</Tag> : null}
                        </div>
                        <div className='table-expand-item' style={{ width: '6.5%' }}>-</div>
                        <div className='table-expand-item' style={{ width: '17%', padding: '0 8px' }}>{item.gmt_modified}</div>
                        <div className='table-expand-item' style={{ width: '13.5%' }}>{item.last_operator}</div>
                        <div className='table-expand-item' style={{ width: '18%', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start' }}>
                          <div>
                            <span style={{ marginRight: '6px' }}>{`预发：${item.pre_version || '暂无'}`}</span>
                            <Popover content={<QRCode value={getQRCodeUrl('pre', item)} />}><QrcodeOutlined /></Popover>
                            <Button type="text" style={{ marginLeft: '-4px' }} onClick={() => copyLink('pre', item)}>
                              <CopyOutlined />
                            </Button>
                          </div>
                          <div>
                            <span style={{ marginRight: '6px' }}>{`线上：${item.prod_version || '暂无'}`}</span>
                            <Popover content={<QRCode value={getQRCodeUrl('prod', item)} />}><QrcodeOutlined /></Popover>
                            <Button type="text" style={{ marginLeft: '-4px' }} onClick={() => copyLink('prod', item)}><CopyOutlined /></Button>
                          </div>
                          <div>
                            <span style={{ marginRight: '6px' }}>{`灰度：${item.gray_version || '暂无'}`}</span>
                            {item.gray_version ? <span>比例：{item.gray_ratio}%</span> : null}
                          </div>
                        </div>
                        <div className='table-expand-item' style={{ flex: 1 }}>
                          { item.enforce_downgrade === 2 
                            ? <Button danger onClick={() => { setDowngrade(item) }}>解除强制降级</Button> 
                            : <a href={`${window.location.origin}/#/ssr/detail?id=${item.id}`} target='_blank'>查看详情</a>
                          }

                          { item.gray_version 
                            ? <>
                            <Divider type='vertical' />
                            <a onClick={() => { handleGray(item, true) }}>定向灰度</a> 
                            <Popconfirm title="确定取消灰度？" onConfirm={() => cancelProjectGray(record, item)}
                                okText="确定" cancelText="取消" disabled={record.isDisabled}><a style={{marginLeft: '10px'}}>单页面灰度取消</a></Popconfirm>
                            </> 
                            : null 
                          }
                        </div>

                      </Row>
                    </List.Item>
                  )}
                />
              </div>
            )
          }
        }}
      />
      {showVersionPad.show ? <VersionPad data={showVersionPad} closePad={(type?: string) => {
        setShowVersionPad({
          show: false,
          type: 'pre',
          data: {}
        });
        if (type === 'refresh') {
          tableRef.current.reload();
        }
      }} /> : null}
      {showGrayPad.show ? <GrayPad data={showGrayPad.data} closePad={(type?: string) => {
        setGrayPad({
          show: false,
          data: {}
        });
        if (type === 'refresh') {
          tableRef.current.reload();
        }
      }} /> : null}
      {showProjectAdd ? <ProjectPad closePad={(type?: string) => {
        setShowProjectAdd(false);
        if (type === 'refresh') {
          tableRef.current.reload();
        }
      }} /> : null}
      {showPageAdd.show ? <PagePad data={showPageAdd.data} type={showPageAdd.type} closePad={(type?: string) => {
        setShowPageAdd({
          show: false,
          data: {},
          type: 'add'
        });
        if (type === 'refresh') {
          tableRef.current.reload();
        }
      }} /> : null}
      {showAdminPad.show ? <AdminPad data={showAdminPad.data} disabled={showAdminPad.disabled} closePad={(type?: string) => {
        setShowAdminPad({
          show: false,
          disabled: true,
          data: {}
        });
        if (type === 'refresh') {
          tableRef.current.reload();
        }
      }} /> : null}
      {showTargetGray.show ? <TargetPad data={showTargetGray.data} closePad={(type?: string) => {
        setTargetGray({
          show: false,
          data: {}
        });
        if (type === 'refresh') {
          tableRef.current.reload();
        }
      }} /> : null}
      {showEnforceGrayPad.show ? <EnforceGrayPad data={showEnforceGrayPad.data} handleProjectGray={handleProjectGray} closePad={(type?: string) => {
        setShowEnforceGrayPad({
          show: false,
          data: {},
        });
        if (type === 'refresh') {
          tableRef.current.reload();
        }
      }} /> : null}
    </>
  )
}