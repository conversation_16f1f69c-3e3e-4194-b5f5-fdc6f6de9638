import { useEffect, useState } from 'react';
import { Modal, message, Popconfirm, Input, Button, Select } from 'antd';
import SSRDetailApi from '@/api/ssr-detail';
import { fetchReq } from '@/utils/request';
import { get } from 'lodash';
import { sleepFunc } from '@/utils/base';
import ssrDetail from '@/api/ssr-detail';
import './index.less';

const TYPE_CONSTANTS: any = {
  'pre': '预发发布',
  'prod': '线上发布',
  'publish': '线上发布',
  'rollback': '回滚'
}

export default (props: any) => {

  const defaultPageList = get(props, 'data.data.childrenList') || [];
  const defaultPageListOpt = defaultPageList.map((item: any) => {
    return {
      label: item.page_name,
      value: item.page_name,
    }
  });
  const defaultPageNameList = defaultPageList.map((item: any) => item.page_name);

  const [confirmLoading, setConfirmLoading] = useState(false);
  const [pageList, setPageList] = useState(defaultPageNameList);
  const [inputValue, setInputValue] = useState('');
  const [iterationList, setIterationList] = useState([]);
  const [selectPage, setSelectPage] = useState([] as Array<string>);
  const [selectRollbackVer, setSelectRollbackVer] = useState([] as any)

  const handleOk = async () => {
    if (!inputValue) {
      message.error('请填写分支版本！');
      return;
    }
    const pattern = /^([1-9]\d|[1-9])(\.([1-9]\d\d|[1-9]\d|\d)){2}$/
    const result = pattern.test(inputValue);
    if (!result) {
      message.error('版本号校验失败，请填写正确的版本号');
      return;
    }

    setConfirmLoading(true);

    const pageDataList = defaultPageList.filter((item: any) => {
      return pageList.includes(item.page_name);
    });
  
    async function batchPub() {
      for (let i = 0; i < pageDataList.length; i++) {
        const item: any = pageDataList[i] || {};

        const checkResouce = await fetchReq(`https://${props.data.type === 'pre' ? 'dev.' : ''}g.alicdn.com/${item.project_group || 'trip'}/${item.project_name}/${inputValue}/node/${(item.page_name || '').replace('/index.html', '')}.js`);
        if (!checkResouce) {
          message.error('资源未发布，请先发布资源');
          continue;
        }

        if (props.data.type === 'pre') {
          await SSRDetailApi.ssrPrepub({
            id: item.id,
            projectName: item.project_name,
            pageName: item.page_name,
            needLogin: item.need_login,
            version: inputValue,
          }).then(res => {
            if (res.success) {
              message.success('发布成功'); 
            } else {
              message.error(res.errorMsg);
            }
          }).catch(e => {
            message.error(e.message || '发布失败');
          });

          await sleepFunc();
        } else if (item.prod_version) {
          await SSRDetailApi.ssrGray({
            id: item.id,
            projectName: item.project_name,
            pageName: item.page_name,
            version: inputValue,
            ratio: 0,
          }).then(res => {
            if (res.success) {
              message.success('发布成功');
            } else {
              message.error(res.errorMsg);
            }
          }).catch(e => {
            message.error(e.message || '发布失败');
          })
        } else {
          await SSRDetailApi.ssrPublish({
            id: item.id,
            projectName: item.project_name,
            pageName: item.page_name,
            needLogin: item.need_login,
            version: inputValue
          }).then(res => {
            if (res.success) {
              message.success('发布成功'); 
            } else {
              message.error(res.errorMsg);
            }
          }).catch(e => {
            message.error(e.message || '发布失败');
          });

          await sleepFunc();
        }
      }
    }

    await batchPub();

    setConfirmLoading(false);
    props.closePad('refresh');
  }

  const handleOkWithRollback = async () =>{

    if(!selectPage || !selectPage.length) {
      return message.warn("请选择页面后重试")
    }else if(!selectPage.every((r:any)=> selectRollbackVer.find((ro: any)=>ro.name === r))){
      return message.warn("请选择版本后重试")
    }
    setConfirmLoading(true);

    const pageDataList = defaultPageList.filter((item: any)=>{
      return selectPage.includes(item.page_name);
    })
  
    async function batchPub() {
      for (let i = 0; i < pageDataList.length; i++) {
        const item: any = pageDataList[i] || {};
        const version: any = selectRollbackVer.find((r:any)=>r.name === item.page_name)?.version?.value;
        if(!version){return}
          await SSRDetailApi.ssrPublish({
            id: item.id,
            projectName: item.project_name,
            pageName: item.page_name,
            needLogin: item.need_login,
            version
          }).then(res => {
            if (res.success) {
              message.success(`回滚${item.page_name}成功`); 
            } else {
              message.error(res.errorMsg);
            }
          }).catch(e => {
            message.error(e.message || '发布失败');
          });

          await sleepFunc();
      }
    }
    await batchPub();
    message.success(`全部页面成功，请在DEF同步回滚操作`, 5); 
    setConfirmLoading(false);
    props.closePad('refresh');
  }

  const handleCancel = () => {
    props.closePad();
  }

  const handleSelectVersion = (val:any, record: any) => {
    const iterationName = get(record, 'data.name', '');
    setSelectRollbackVer(iterationName)
  }

  const handleSelectVersionNew = (val:any, record: any, name: string) => {
    setSelectRollbackVer([...selectRollbackVer, {name: name, version: record }])
  }

  const handleSelectPage = (val: any, name: string) =>{
    if(val){
      setSelectPage([...selectPage, name])
    }else{
      setSelectPage(selectPage.filter((r:any)=> r !== name))
    }
  }

  const handleChange = (val:any) => {
    setInputValue(val.target.value);
  }

  const handleSelect = (val:any) => {
    setPageList(val);
  }

  useEffect(()=>{
    if(props.data.type === 'rollback'){
      const { project_group = '', project_name = '', childrenList = []} = props.data.data;
      const getVersionById = (id: number)=>{
        return ssrDetail.logList({
          publish_type: 4,
          current: 1,
          pageSize: 40,
          id,
        })
      }
      Promise.all(childrenList.map((r: any)=> getVersionById(r.id))).then((res: any) => {
        if(res && res.length){
          setIterationList(childrenList.map((r: any, index: number)=>{
            return {
              name: r.page_name,
              iterationArr: get(res, `${index}.data.data`, []).map((ro: any)=> ro.version).reduce((pre: any, next: any)=> {if(next && !pre.includes(next)){return [...pre, next]}else{return pre}} ,[]),
              prod_version: r.prod_version
            }
          }))
        }else{
          message.warn("查询历史迭代失败，请点击【正式发布】按钮，选择你需要回滚的版本，同时去DEF回滚应用")
          props.closePad();
        }
      }).catch(e => {})
    }
  },[])
  
  return (
    <Modal
      title={TYPE_CONSTANTS[props.data.type]}
      open={true}
      maskClosable={false}
      onCancel={handleCancel}
      width={500}
      destroyOnClose
      footer={
        (props.data.type === 'pre' || props.data.type === 'rollback') ?
        <Button onClick={()=>{props.data.type === 'pre' ? handleOk() : handleOkWithRollback() }} type="primary" loading={confirmLoading} disabled={!(props.data.type === 'pre' ? inputValue : selectRollbackVer) ? true : false}>确定</Button> : 
        <Popconfirm
          title={"请确保资源已发布上线，是否确认发布"}
          onConfirm={handleOk}
          placement="bottomRight"
          disabled={!inputValue ? true : false}
        >
          <Button type="primary" loading={confirmLoading} disabled={!inputValue ? true : false}>确定</Button>
        </Popconfirm>
      }
    >
      {props.data.type === 'rollback' ? (<div className='version-pad'>
        <span className='version-pad-name'>页面列表</span>
        {
          iterationList && iterationList.length ? iterationList.map(
            (r: any, index: number)=>
              (<div className='iteration-wrap' key={index}>
                <input type="checkbox" id={`page${index}`} onChange={(e: any)=>{handleSelectPage(e.target.checked, r.name)}}/>
                <label for={`page${index}`} style={{margin: '0 10px',width: '100px',userSelect: 'none'}}>{r.name}</label>
                {r.iterationArr && r.iterationArr.length ? 
                (<Select
                  // style={{ width: '100%' }}
                  placeholder="请选择版本号"
                  onChange={(val, record)=>handleSelectVersionNew(val, record, r.name)}
                  options={r.iterationArr.map((ro:any)=> {return {label: ro, value: ro}})}
                />) : 
                (<Input placeholder='请输入版本号' disabled={true}  />)}
                <span style={{marginLeft: '10px'}}>当前线上版本{r.prod_version}</span>
              </div>)
          ) : null
        }
      </div>) : (<><div className='version-pad'>
        <span className='version-pad-name'>页面列表：</span>
        <Select
          mode="multiple"
          style={{ width: '100%' }}
          placeholder="请选择页面列表"
          defaultValue={defaultPageListOpt}
          onChange={handleSelect}
          options={defaultPageListOpt}
        />
      </div>
      <div className='version-pad'>
        <span className='version-pad-name'>分支版本：</span>
        {iterationList && iterationList.length ? 
        (<Select
          style={{ width: '100%' }}
          placeholder="请选择版本号"
          onChange={handleSelectVersion}
          options={iterationList}
        />) : 
        (<Input placeholder='请输入版本号' onChange={handleChange} />)}
      </div></>)}
    </Modal>
  )
}