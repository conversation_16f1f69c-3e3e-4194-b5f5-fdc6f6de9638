
import { useEffect, useState } from 'react';
import { Modal, message, Select, Input, Button } from 'antd';
import { useModel } from 'umi';
import UserComp from '@/components/FR/user';
import SSRDetailApi from '@/api/ssr-detail';

export default (props: any) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [value, setValue] = useState(JSON.parse(props.data.admin_list));
  const [group, setGroup] = useState(props.data.project_group || 'trip');
  const [business, setBusiness] = useState(props.data.project_business || 'default');
  const [pid, setPid] = useState(props.data.pid|| '');
  const [isAssociatedDef, setIsAssociatedDef] = useState(props.data.is_associated_def || 2);
  const currentUser = useModel('user');

  const footer = props.disabled ? {footer: null} : {}

  const handleOk = () => {
    if (value.length < 1) {
      message.error('管理员不能为空');
      return;
    }
    setConfirmLoading(true);
    SSRDetailApi.updateProject({
      id: props.data.id,
      adminList: JSON.stringify(value),
      projectGroup: group,
      projectBusiness: business,
      pid,
      isAssociatedDef,
    }).then(res => {
      if (res.success) {
        message.success('修改成功');
        props.closePad('refresh');
      } else {
        message.error(res.errorMsg)
      }
      setConfirmLoading(false);
    }).catch(e => {
      message.error(e.message || '修改失败');
      setConfirmLoading(false);
    })
  }

  const handleCancel = () => {
    props.closePad();
  }

  const handleChange = (val:any) => {
    const newValue = val.map((item: any) => {
      if (item.key) {
        const keyValue = JSON.parse(item.key);
        return {
          value: keyValue.workid,
          label: keyValue.name
        }
      }
      return {
        value: item.value,
        label: item.label
      }
    })
    setValue(newValue);
  }

  const handlePidChange = (val: any) => {
    setPid(val.target.value);
  }

  const handleGroupChange = (val: any) => {
    setGroup(val.target.value);
  }

  const handleAssociatedDef = (val: any) => {
    setIsAssociatedDef(val)
  }
  
  return (
    <Modal
      title="项目管理"
      open={true}
      maskClosable={false}
      confirmLoading={confirmLoading}
      onOk={handleOk}
      onCancel={handleCancel}
      width={500}
      destroyOnClose
      {...footer}

    >
      <div className='version-pad'>
        <span className='version-pad-name'>管理员：</span>
        <UserComp value={value} onChange={handleChange} disabled={props.disabled} />
      </div>
      <div style={{height: '12px'}} />
      <div className='version-pad'>
        <span className='version-pad-name'>项目分组：</span>
        <Input value={group} onChange={handleGroupChange} disabled={currentUser.workid !== '205521' && currentUser.workid !== '265692'} />
      </div>
      <div style={{height: '12px'}} />
      <div className='version-pad'>
        <span className='version-pad-name'>项目PID：</span>
        <Input value={pid} onChange={handlePidChange} disabled={props.disabled} />
      </div>
      <div style={{height: '12px'}} />
      {['205521', '359190','265692'].includes(currentUser.workid) ?
        <div className='version-pad'>
          <span className='version-pad-name'>所属行业：</span>
          <Select
            style={{width: '100%'}}
            value={business}
            onChange={e => {
              setBusiness(e)
            }}
            options={[
              { value: 'default', label: '默认' },
              { value: 'hotel', label: '酒店' },
              { value: 'traffic', label: '交通' },
              { value: 'vacation', label: '度假' },
              { value: 'btrip', label: '商旅' },
              { value: 'member', label: '会员' },
            ]}
          />
        </div> : null}
      <div style={{height: '12px'}} />
      <div className='version-pad'>
        <span className='version-pad-name'>是否关联DEF发布：</span>
        <Select
          style={{width: '100%'}}
          value={isAssociatedDef}
          onChange={value => {
            handleAssociatedDef(value)
          }}
          options={[
            { value: 1, label: '否' },
            { value: 2, label: '是' },
          ]}
        />
      </div>
    </Modal>
  )
}