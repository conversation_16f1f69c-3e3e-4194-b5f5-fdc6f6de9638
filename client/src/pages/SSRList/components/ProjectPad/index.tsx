import { useEffect, useState } from 'react';
import { Modal, message, Select, Input, Form } from 'antd';
import SSRDetailApi from '@/api/ssr-detail';

export default (props: any) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    form.setFieldsValue({
      projectGroup: 'trip',
      projectName: '',
      projectBusiness: 'default',
      isAssociatedDef: 2,
    })
  }, [])

  const handleOk = () => {
    form
      .validateFields()
      .then((formRes: any) => {
        setConfirmLoading(true);
        SSRDetailApi.addProject({
          projectGroup: formRes.projectGroup,
          projectName: formRes.projectName,
          projectBusiness: formRes.projectBusiness,
          isAssociatedDef: formRes.isAssociatedDef,
        }).then(res => {
          if (res.success) {
            message.success('新建成功');
            props.closePad('refresh');
          } else {
            message.error(res.errorMsg)
          }
          setConfirmLoading(false);
        }).catch(e => {
          message.error(e.message || '新建失败');
          setConfirmLoading(false);
        })
      })
      .catch((err: any) => {
      });
    
  }

  const handleCancel = () => {
    props.closePad();
  }

  return (
    <Modal
      title="新建项目"
      open={true}
      maskClosable={false}
      confirmLoading={confirmLoading}
      onOk={handleOk}
      onCancel={handleCancel}
      width={600}
      destroyOnClose
    >
       <Form
        className="review-form"
        form={form}
        labelCol={{
          span: 7
        }}
        wrapperCol={{
          span: 19
        }}
      >
        <Form.Item
          name="projectName"
          label="项目名称"
          rules={[{ required: true, message: '请输入项目名称!' }]}
        >
          <Input placeholder='请输入项目名称' />
        </Form.Item>
        <Form.Item
          name="projectGroup"
          label="项目分组"
          rules={[{ required: true, message: '请输入项目分组!' }]}
        >
          <Input placeholder='请输入项目名称' />
        </Form.Item>
        <Form.Item
          name="projectBusiness"
          label="所属行业"
          rules={[{ required: true, message: '请选择所属行业!' }]}
        >
          <Select
            options={[
              { value: 'default', label: '默认' },
              { value: 'hotel', label: '酒店' },
              { value: 'traffic', label: '交通' },
              { value: 'vacation', label: '度假' },
              { value: 'btrip', label: '商旅' },
              { value: 'member', label: '会员' },
            ]}
          />
        </Form.Item>
        <Form.Item
          name="isAssociatedDef"
          label="是否关联DEF发布"
          rules={[{ required: true, message: '请选择是否关联!' }]}
        >
          <Select
            options={[
              { value: 1, label: '否' },
              { value: 2, label: '是' },
            ]}
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}