import { useEffect, useState } from 'react';
import { Modal, message, Popconfirm, Select, Button } from 'antd';
import { get } from 'lodash';
import SSRDetailApi from '@/api/ssr-detail';

export default (props: any) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [selectValue, setSelectValue] = useState(props.data.gray_ratio || 0);

  const handleOk = () => {
    setConfirmLoading(true);
    if (selectValue < 100) {
      SSRDetailApi.ssrGray({
        id: props.data.id,
        projectName: props.data.project_name,
        pageName: props.data.page_name,
        version: props.data.gray_version,
        ratio: selectValue
      }).then(res => {
        if (res.success) {
          message.success('灰度成功');
          props.closePad('refresh');
          const defStatus = get(res, 'data.updateDef.data.success');
          if (!defStatus) {
            message.info(`def后台灰度失败：${get(res, 'data.updateDef.data.errorMsg')}`);
          }
        } else {
          message.error(res.errorMsg)
        }
        setConfirmLoading(false);
      }).catch(e => {
        message.error(e.message || '灰度失败');
        setConfirmLoading(false);
      })
    } else {
      SSRDetailApi.ssrPublish({
        id: props.data.id,
        projectName: props.data.project_name,
        pageName: props.data.page_name,
        needLogin: props.data.need_login,
        version: props.data.gray_version
      }).then(res => {
        if (res.success) {
          message.success('发布成功');
          props.closePad('refresh');
          const defStatus = get(res, 'data.updateDef.data.success');
          if (!defStatus) {
            message.info(`def后台发布失败：${get(res, 'data.updateDef.data.errorMsg')}`);
          }
        } else {
          message.error(res.errorMsg);
        }
        setConfirmLoading(false);
      }).catch(e => {
        setConfirmLoading(e.message || '发布失败');
      })
    }

  }

  const handleCancel = () => {
    props.closePad();
  }

  const handleChange = (val:any) => {
    setSelectValue(val);
  }

  const handleCancelGray = () => {
    setConfirmLoading(true);
    SSRDetailApi.ssrGray({
      id: props.data.id,
      projectName: props.data.project_name,
      pageName: props.data.page_name,
      version: props.data.gray_version,
      ratio: -1
    }).then(res => {
      if (res.success) {
        message.success('取消成功');
        props.closePad('refresh');
      } else {
        message.error(res.errorMsg)
      }
      setConfirmLoading(false);
    }).catch(e => {
      message.error(e.message || '取消失败');
      setConfirmLoading(false);
    })
  }

  return (
    <Modal
      title={'灰度放量'}
      open={true}
      maskClosable={false}
      onCancel={handleCancel}
      width={500}
      destroyOnClose
      footer={<>
        <Button onClick={handleCancel}>取消</Button>
        <Popconfirm
          title="确认取消灰度？"
          onConfirm={handleCancelGray}
          placement="bottomRight"
        >
          <Button loading={confirmLoading}>取消灰度</Button>
        </Popconfirm>
        <Popconfirm
          title={`确认灰度放量至${selectValue}%`}
          onConfirm={handleOk}
          placement="bottomRight"
        >
          <Button type="primary" loading={confirmLoading}>发布</Button>
        </Popconfirm>
      </>
      }
    >
      <div className='version-pad'>
        <span className='version-pad-name'>灰度占比：</span>
        <Select
          value={selectValue}
          style={{width: '100%'}}
          onChange={handleChange}
          options={[
            { value: 0, label: '0%',  disabled: props.data.gray_ratio > 0},
            { value: 5, label: '5%',  disabled: props.data.gray_ratio >= 5},
            { value: 25, label: '25%',  disabled: props.data.gray_ratio >= 25},
            { value: 50, label: '50%', disabled: props.data.gray_ratio >= 50},
            { value: 100, label: '100%'},
          ]}
        />
      </div>
    </Modal>
  )
}