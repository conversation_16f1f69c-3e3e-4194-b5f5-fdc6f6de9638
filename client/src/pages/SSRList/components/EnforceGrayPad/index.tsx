
import { Modal } from 'antd';

export default (props: any) => {
  const {
    data = {},
    handleProjectGray = () => {},
    closePad = () => {},
  } = props || {};
  const {
    ratio = 0,
    projectData = {},
    blockBody,
  } = data;

  const handleOk = async () => {
    await handleProjectGray(ratio, projectData, true);

    closePad();
  }

  const handleCancel = () => {
    closePad();
  }

  return (
    <Modal
      title="操作提示"
      open={true}
      maskClosable={false}
      onOk={handleOk}
      onCancel={handleCancel}
      okText='继续操作'
      cancelText='取消操作'
      width={700}
      destroyOnClose
    >
      <div>检测到本次灰度放量操作有如下风险：</div>
      <div style={{ color: 'red', margin: '20px 0' }}>{blockBody || ''}</div>
      <a onClick={() => { open(orderUrl) }}>操作完成后，请前往O2后台手动设置灰度/发布流程</a>
    </Modal>
  )
}