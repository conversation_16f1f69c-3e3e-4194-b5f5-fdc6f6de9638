import { useEffect, useState } from 'react';
import { Modal, message, Input } from 'antd';
import SSRDetailApi from '@/api/ssr-detail';

const { TextArea } = Input;

export default (props: any) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [inputValue, setInputValue] = useState(props.data.target_user_id || '');
  const [zgValue, setZgValue] = useState(props.data.target_zg_id || '');

  const handleOk = async () => {
    setConfirmLoading(true);
    SSRDetailApi.ssrGray({
      id: props.data.id,
      projectName: props.data.project_name,
      pageName: props.data.page_name,
      isTarget: true,
      targetUser: inputValue,
      targetZg: zgValue
    }).then(res => {
      if (res.success) {
        message.success('灰度成功');
        props.closePad('refresh');
      } else {
        message.error(res.errorMsg)
      }
      setConfirmLoading(false);
    }).catch(e => {
      message.error(e.message || '灰度失败');
      setConfirmLoading(false);
    })
  }

  const handleCancel = () => {
    props.closePad();
  }

  const handleChange = (val:any) => {
    setInputValue(val.target.value);
  }

  const handleZgChange = (val:any) => {
    setZgValue(val.target.value);
  }
  
  return (
    <Modal
      title={'定向灰度'}
      open={true}
      maskClosable={false}
      confirmLoading={confirmLoading}
      onCancel={handleCancel}
      onOk={handleOk}
      width={500}
      destroyOnClose
      >
      <div className='version-pad'>
        <span className='version-pad-name'>用户ID：</span>
        <TextArea placeholder='请输入用户ID,多个以英文逗号分隔' autoSize value={inputValue} onChange={handleChange} />
      </div>
      <div style={{height: '12px'}} />
      <div className='version-pad'>
        <span className='version-pad-name'>圈人ID：</span>
        <Input placeholder='请输入圈人ID,仅支持单个' value={zgValue} onChange={handleZgChange} />
      </div>
    </Modal>
  )
}