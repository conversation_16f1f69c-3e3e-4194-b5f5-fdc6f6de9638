import { useEffect, useState } from 'react';
import { Modal, message, Select, Input, Form } from 'antd';
import SSRDetailApi from '@/api/ssr-detail';

export default (props: any) => {
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (props.type === 'edit') {
      form.setFieldsValue({
        page_name: props.data.page_name,
        need_login: props.data.need_login || 2,
        is_immersive: props.data.is_immersive || 2,
        path_project_name: props.data.path_project_name || '',
        path_page_name: props.data.path_page_name || '',
      })
    } else {
      form.setFieldsValue({
        need_login: 2,
        is_immersive: 2,
      })
    }
  }, [])

  const handleOk = () => {
    form
      .validateFields()
      .then((formRes: any) => {
        setConfirmLoading(true);
        if (props.type === 'add') {
          SSRDetailApi.addPage({
            projectId: props.data.id,
            projectName: props.data.project_name,
            pageName: formRes.page_name,
            isLogin: formRes.need_login,
            isImmersive: formRes.is_immersive,
            pathProjectName: formRes.path_project_name || '',
            pathPageName: formRes.path_page_name || ''
          }).then(res => {
            if (res.success) {
              message.success('新建成功');
              props.closePad('refresh');
            } else {
              message.error(res.errorMsg)
            }
            setConfirmLoading(false);
          }).catch(e => {
            message.error(e.message || '新建失败');
            setConfirmLoading(false);
          })
        } else {
          SSRDetailApi.updatePage({
            id: props.data.id,
            isLogin: formRes.need_login,
            isImmersive: formRes.is_immersive,
            pathProjectName: formRes.path_project_name || '',
            pathPageName: formRes.path_page_name || ''
          }).then(res => {
            if (res.success) {
              message.success('更新成功');
              props.closePad('refresh');
            } else {
              message.error(res.errorMsg)
            }
            setConfirmLoading(false);
          }).catch(e => {
            message.error(e.message || '更新失败');
            setConfirmLoading(false);
          })
        }
      })
      .catch((err: any) => {
      });
  }

  const handleCancel = () => {
    props.closePad();
  }
  
  return (
    <Modal
      title={props.type === 'add' ? "新建页面" : '编辑页面'}
      open={true}
      maskClosable={false}
      confirmLoading={confirmLoading}
      onOk={handleOk}
      onCancel={handleCancel}
      width={600}
      destroyOnClose
    >
      <Form
        className="review-form"
        form={form}
        labelCol={{
          span: 7
        }}
        wrapperCol={{
          span: 19
        }}
      >
        <Form.Item
          name="page_name"
          label="页面名称"
          rules={[{ required: true, message: '请输入页面名称!' }]}
        >
          <Input placeholder='请输入页面名称' disabled={props.type === 'edit'} />
        </Form.Item>
        <Form.Item
          name="need_login"
          label="是否登录"
          rules={[{ required: true, message: '请选择登录状态!' }]}
        >
          <Select
            options={[
              { value: 1, label: '否' },
              { value: 2, label: '是' }
            ]}
          />
        </Form.Item>
        <Form.Item
          name="is_immersive"
          label="是否沉浸式"
          rules={[{ required: true, message: '请选择沉浸式状态!' }]}
        >
          <Select
            options={[
              { value: 1, label: '否' },
              { value: 2, label: '是' }
            ]}
          />
        </Form.Item>
        {/* <Form.Item
          name="path_project_name"
          label="路径项目名称"
        >
          <Input placeholder='谨慎使用，使用前请咨询@紫期@吐司' />
        </Form.Item>
        <Form.Item
          name="path_page_name"
          label="路径页面名称"
        >
          <Input placeholder='谨慎使用，使用前请咨询@紫期@吐司' />
        </Form.Item> */}
      </Form>
    </Modal>
  )
}