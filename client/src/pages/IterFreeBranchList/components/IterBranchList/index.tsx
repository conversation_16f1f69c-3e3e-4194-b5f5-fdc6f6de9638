import { useEffect, useState } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { Table, TablePaginationConfig, message } from 'antd';
import { RowSelectionType } from 'antd/es/table/interface';
import { Link } from 'umi';
import iterBranchApi from '@/api/iter-branch';
import { IIterBranch } from '@/interface/iter-branch';
import { ITER_STATUS_TEXT, IterStatus } from '@/const/iter-branch';

import './index.less';

const PAGE_SIZE = 10;

interface IDataSource extends IIterBranch {
  key: number;
}

interface Props {
  /** 项目名称 */
  projectName: string;
  /** 默认筛选的迭代状态 */
  defaultFilteredStatus?: number[];
  /** 禁用状态筛选 */
  disableStatusFilter?: boolean;
  /** 选中挂载到的迭代 */
  onCheck?: (iterBranch: IIterBranch) => void
}

const Comp: React.FC<Props> = ({ projectName, defaultFilteredStatus = [], disableStatusFilter = false, onCheck }) => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<IDataSource[]>([])
  const [pageNum, setPageNum] = useState(1);
  const [pageTotal, setPageTotal] = useState(0);
  const [checkedIterId, setCheckedIterId] = useState<number>();
  const [filteredStatus, setFilteredStatus] = useState<number[]>(defaultFilteredStatus);

  const rowSelection = {
    type: 'checkbox' as RowSelectionType,
    hideSelectAll: true,
    onChange: (_selectedRowKeys: React.Key[], selectedRows: IDataSource[]) => {
      const iterId = selectedRows[0]?.iterId;
      setCheckedIterId(iterId)
      if (onCheck) onCheck(selectedRows[0])
    },
    getCheckboxProps: (record: IDataSource) => {
      return {
        disabled: checkedIterId ? (checkedIterId !== record.iterId) : false, // 若有选中的迭代，则其他迭代不可再被选中
        name: String(record.status),
      }
    },
  };

  const columns = [
    {
      title: '迭代Id',
      dataIndex: 'iterId',
      render: (text: string) => <Link to={`/iter/detail?iterId=${text}`} target="_blank">{text}</Link>,
    },
    {
      title: '版本号',
      dataIndex: 'version',
    },
    {
      title: '版本描述',
      dataIndex: 'description',
    },
    {
      title: '迭代状态',
      dataIndex: 'status',
      render: (_text: string, record: IDataSource) => ITER_STATUS_TEXT[record.status],
      ...(disableStatusFilter ? {} : {
        filters: Object.values(IterStatus).map(status => typeof status === 'number' ? { text: ITER_STATUS_TEXT[status], value: status } : undefined).filter((item): item is { text: string; value: IterStatus } => !!item),
        defaultFilteredValue: defaultFilteredStatus.map(item => String(item)),
        onFilter: (value: string | number | boolean, record: IDataSource) => Number(record.status) === Number(value),
      })
    },
  ];

  useEffect(() => {
    fetchIterBranchList()
  }, [projectName, filteredStatus, pageNum])

  function fetchIterBranchList() {
    setLoading(true);

    iterBranchApi.list({
      pageNum,
      pageSize: PAGE_SIZE,
      conditions: {
        projectName,
        statusList: filteredStatus.length === 0 ? undefined : filteredStatus
      }
    }).then(res => {
      if (!res.data) throw Error(res.errorMsg || '获取迭代分支列表失败');

      unstable_batchedUpdates(() => {
        setLoading(false);
        setDataSource(res.data.list.map(item => ({ key: item.iterId, ...item })));
        setPageTotal(res.data.total);
      })
    }).catch((err) => {
      message.error(err.message || '加载失败');

      unstable_batchedUpdates(() => {
        setDataSource([]);
        setLoading(false);
      })
    })
  }

  function onChange(_pagination: TablePaginationConfig, filters: Record<string, any>) {
    unstable_batchedUpdates(() => {
      const newFilteredStatus = filters.status || [];

      // 若状态筛选条件改变则更新状态
      if (!disableStatusFilter && (newFilteredStatus.sort().join(',') !== filteredStatus.sort().join(','))) {
        setFilteredStatus(newFilteredStatus);
      }
    })
  }

  return <Table
    loading={loading}
    rowSelection={rowSelection}
    columns={columns}
    dataSource={dataSource}
    pagination={{
      current: pageNum,
      pageSize: PAGE_SIZE,
      total: pageTotal,
      onChange: (pageNum) => setPageNum(pageNum)
    }}
    onChange={onChange} />
}

export default Comp;