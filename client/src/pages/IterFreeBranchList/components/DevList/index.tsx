import { useEffect, useState, useRef } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { List, message } from 'antd';
import freeDevBranchApi from '@/api/free-dev-branch';
import { IFreeDevBranch } from '@/interface/free-dev-branch';
import Item from '@/components/DevItem';
import { IProject } from '@/interface/project';

import './index.less';

const PAGE_SIZE = 10;

interface Props {
  /** 项目 */
  project?: IProject;
  /** 刷新标识 */
  refreshTag?: number;
  /** 是否过滤自己 */
  filterMine?: boolean;
  /** 搜索的分支名 */
  searchBranchName?: string;
}

const Comp: React.FC<Props> = ({ project, refreshTag, filterMine = false, searchBranchName }) => {
  const [loading, setLoading] = useState(true);
  const [pageNum, setPageNum] = useState(1);
  const [pageTotal, setPageTotal] = useState(0);
  const [freeDevBranchList, setFreeDevBranchList] = useState<IFreeDevBranch[]>();
  const { current: preState } = useRef({} as { [key: string]: any; });

  useEffect(() => {
    preState.projectName = project?.name;

    setFreeDevBranchList([]);

    // 若搜索词变化则重置页码
    if (searchBranchName === preState.searchBranchName) {
      getList(pageNum)
    } else {
      preState.searchBranchName = searchBranchName;
      getList(1)
    }
  }, [project, searchBranchName, refreshTag])

  function getList(pageNum: number) {
    if (!project) return;

    setLoading(true);

    freeDevBranchApi.list({
      pageNum,
      pageSize: PAGE_SIZE,
      conditions: {
        projectName: project.name,
        filterMine,
        searchBranchName
      },
      returnIterBranchDetail: true
    }).then(res => {
      // 防止请求回来后项目变了，导致数据错乱
      if (preState.projectName !== project.name) return;
      if (!res.data) throw Error(res.errorMsg || '获取游离分支列表失败');

      unstable_batchedUpdates(() => {
        setPageNum(pageNum);
        setLoading(false);
        setFreeDevBranchList(res.data.list);
        setPageTotal(res.data.total);
      })
    }).catch((err) => {
      message.error(err.message || '加载失败');

      unstable_batchedUpdates(() => {
        setPageNum(pageNum);
        setLoading(false);
        setFreeDevBranchList([]);
      })
    })
  }

  return <List
    itemLayout="horizontal"
    size="large"
    pagination={{
      current: pageNum,
      pageSize: PAGE_SIZE,
      total: pageTotal,
      onChange: (pageNum) => getList(pageNum)
    }}
    dataSource={freeDevBranchList}
    loading={loading}
    className="free-dev-list"
    renderItem={item => project && (
      <List.Item
        key={item.devId}
        style={{ padding: 0 }}
      >
        <Item
          devBranch={item}
          iterBranch={item.iterBranch}
          project={project}
          from='branch'
          isMountMode />
      </List.Item>
    )}
  />
}

export default Comp;