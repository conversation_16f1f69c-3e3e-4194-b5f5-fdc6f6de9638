import { useState } from 'react';
import { Button, PageHeader, Space, Tabs, Input } from 'antd';
import { UserOutlined, UsergroupAddOutlined } from '@ant-design/icons';
import EditDevBtn, { Action } from '@/components/EditDevBtn';
import ProjectSelect from '@/components/ProjectSelect';
import DevList from './components/DevList';
import { IProject } from '@/interface/project';

import './index.less';

export default () => {
  const [project, setProject] = useState<IProject>();
  const [listRefreshTag, setListRefreshTag] = useState(0);
  const [searchBranchName, setSearchBranchName] = useState('');

  function onProjectSelect(project: IProject) {
    setProject(project);
  }

  return <div className="iter-free-branch-list-container">
    <PageHeader
      className="page-header"
      title={
        <ProjectSelect onSelect={onProjectSelect} style={{ fontWeight: 'normal' }} />
      }
      extra={
        <Space className="actions">
          <EditDevBtn
            isFreeDevBranch
            action={Action.Create}
            project={project}
            onCreateEnd={() => setListRefreshTag(Date.now())}
          >
            <Button type="primary" disabled={!project}>新建游离开发分支</Button>
          </EditDevBtn>
        </Space>
      }
    />

    <Tabs
      tabBarExtraContent={{
        right: <Input.Search
          placeholder="搜索分支名"
          allowClear
          onSearch={val => setSearchBranchName(val)}
        />
      }}
      tabBarStyle={{ marginBottom: '24px', padding: '0 24px' }}>
      <Tabs.TabPane
        tab={<span><UserOutlined />我创建的</span>}
        key="mine"
      >
        <DevList searchBranchName={searchBranchName} project={project} refreshTag={listRefreshTag} filterMine />
      </Tabs.TabPane>
      <Tabs.TabPane
        tab={<span><UsergroupAddOutlined />全部</span>}
        key="all"
      >
        <DevList searchBranchName={searchBranchName} project={project} refreshTag={listRefreshTag} />
      </Tabs.TabPane>
    </Tabs>
  </div >
};
