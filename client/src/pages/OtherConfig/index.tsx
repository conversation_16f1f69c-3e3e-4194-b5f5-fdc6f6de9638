import { useEffect, useState } from 'react';
import { List, message, Form, Card, Button, InputNumber, Select, Input } from 'antd';
import SSRApi from '@/api/ssr-detail';
import { get } from 'lodash';

const { TextArea } = Input;
let requestLock = false;
export default () => {
  const [form1] = Form.useForm();
  const [form2] = Form.useForm();
  const [form3] = Form.useForm();
  const [form4] = Form.useForm();
  const [formHot] = Form.useForm();

  const isFormatRight = (paramsStr: string) => {
    if (typeof paramsStr === 'string') {
      try {
        const paramsArr = paramsStr.split(',');
        return paramsArr.every((res) => {
          return typeof res === 'string';
        });
      } catch (e) {}
    }
    return false;
  };

  const onConfirm = (env: string, type: string) => {
    if(requestLock){return message.error('请等待其他请求完成');}
    requestLock = true;

    if(type === 'uid'){
      let target = form1;
      if (env === 'center') { target = form2; }
  
      target.validateFields().then((formRes) => {
        const { whiteListPre = '', whiteListOnline = '' } = formRes;
        const isPreFormatRight = isFormatRight(whiteListPre);
        const isOnlineFormatRight = isFormatRight(whiteListOnline);
        if ((env === 'pre' && !isPreFormatRight) || (env === 'center' && !isOnlineFormatRight)) {
          requestLock = false;
          return message.error('数据格式不正确，请重新输入');
        }
  
        SSRApi.setDiamondWithDownGrade({ env, type, downgradeUidStr: env === 'center' ? whiteListOnline : whiteListPre }).then((res) => {
          message.success(`修改${env === 'center' ? '线上' : '预发'}成功`);
          init();
          requestLock = false
        }).catch((err) => {
          message.error(`修改${env === 'center' ? '线上' : '预发'}失败，请稍后重试`);
          init();
          requestLock = false
        });
      }).catch((err)=>{
        requestLock = false;
      });
    }
    
    if(type === 'page'){

      let pageTarget = form3;
      if (env === 'center') { pageTarget = form4; }
  
      pageTarget.validateFields().then((formRes) => {
        const { whiteListPagePre = '', whiteListPageOnline = '' } = formRes;
        const isPreFormatRight = isFormatRight(whiteListPagePre);
        const isOnlineFormatRight = isFormatRight(whiteListPageOnline);
        if ((env === 'pre' && !isPreFormatRight) || (env === 'center' && !isOnlineFormatRight)) {
          requestLock = false;
          return message.error('数据格式不正确，请重新输入');
        }
  
        SSRApi.setDiamondWithDownGrade({ env, type, downgradePageStr: env === 'center' ? whiteListPageOnline : whiteListPagePre }).then((res) => {
          message.success(`修改${env === 'center' ? '线上' : '预发'}成功`);
          init();
          requestLock = false
        }).catch((err) => {
          message.error(`修改${env === 'center' ? '线上' : '预发'}失败，请稍后重试`);
          init();
          requestLock = false
        });
      }).catch((err)=>{
        requestLock = false;
      });
    }

  };

  const onHot = () => {
    formHot.validateFields().then((formRes) => {
      if (formRes.hotList) {
        const list = formRes.hotList.split('\n');
        let checkTag = true;
        list.forEach((item:any) => {
          if (item.indexOf('cdnoss.fliggy.com') < 0) {
            message.error('预热地址错误');
            checkTag = false;
            return;
          }
        })
        if (checkTag) {
          SSRApi.addCdnHot(list);
          message.success('预热任务创建成功')
        }
        return ;
      }
    })
  }

  const init = () => {
    // 获取预发diamond名单/页面黑名单
    SSRApi.getDiamondWithDownGrade({ env: 'pre' }).then((res) => {
      form1.setFieldsValue({
        whiteListPre: get(res, 'data.data.downgradeUid', []).join(','),
      });
      form3.setFieldsValue({
        whiteListPagePre: get(res, 'data.data.downgradePage', []).join(','),
      });
    });
    // 获取线上diamond名单/页面黑名单
    SSRApi.getDiamondWithDownGrade({ env: 'center' }).then((res) => {
      form2.setFieldsValue({
        whiteListOnline:  get(res, 'data.data.downgradeUid', []).join(','),
      });
      form4.setFieldsValue({
        whiteListPageOnline: get(res, 'data.data.downgradePage', []).join(','),
      });
    });
  };

  useEffect(() => {
    init()
  }, []);

  return (
    <>
      <Card title="全链路降级名单" style={{ marginBottom: '12px' }}>

        <Form
          name="basic"
          form={form1}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 12 }}
          initialValues={{ handleType: 'get' }}
          style={{ width: '50%', display: 'inline-block'}}
        >
          <Form.Item label="预发名单" name="whiteListPre">
            <TextArea
              rows={4}
              style={{ width: '100%' }}
              placeholder='暂无，填写规范为:2212884880168,2212906028193'
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => {
                onConfirm('pre', 'uid');
              }}
            >
              预发修改提交
            </Button>
          </Form.Item>
        </Form>
        <Form
          name="basic"
          form={form3}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 12 }}
          initialValues={{ handleType: 'get' }}
          style={{ width: '50%', display: 'inline-block' }}
        >
          <Form.Item label="预发不降级页面" name="whiteListPagePre">
            <TextArea
              rows={4}
              style={{ width: '100%' }}
              placeholder='暂无，填写规范为:/app/trip/rx-trip-visa-channel/pages/home,/app/trip/rx-trip-visa-channel/pages/detail'
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => {
                onConfirm('pre', 'page');
              }}
            >
              预发不降级名单修改提交
            </Button>
          </Form.Item>
        </Form>
        <Form
          name="basic"
          form={form2}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 12 }}
          initialValues={{ handleType: 'get' }}
          style={{ width: '50%', display: 'inline-block' }}
        >
          <Form.Item label="线上名单" name="whiteListOnline">
            <TextArea
              rows={4}
              style={{ width: '100%' }}
              placeholder='暂无，填写规范为:2212884880168,2212906028193'
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => {
                onConfirm('center', 'uid');
              }}
            >
              线上修改提交
            </Button>
          </Form.Item>
        </Form>
        <Form
          name="basic"
          form={form4}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 12 }}
          initialValues={{ handleType: 'get' }}
          style={{ width: '50%', display: 'inline-block' }}
        >
          <Form.Item label="线上不降级页面：" name="whiteListPageOnline">
            <TextArea
              rows={4}
              style={{ width: '100%' }}
              placeholder='暂无，填写规范为:/app/trip/rx-trip-visa-channel/pages/home,/app/trip/rx-trip-visa-channel/pages/detail'
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => {
                onConfirm('center', 'page');
              }}
            >
              线上不降级名单修改提交
            </Button>
          </Form.Item>
        </Form>
      </Card>
      <Card title="cdn预热工具">
        <Form
          name="hot"
          form={formHot}
        >
          <Form.Item name="hotList">
            <TextArea
              rows={10}
              style={{ width: '100%' }}
              placeholder='多个地址换行分隔，谨慎使用，详情联系@紫期'
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              onClick={onHot}
            >
              预热
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </>
  );
};
