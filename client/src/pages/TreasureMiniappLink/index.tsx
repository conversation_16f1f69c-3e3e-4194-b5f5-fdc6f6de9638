import { useEffect, useState, useMemo, Key, ReactChild, ReactFragment, ReactPortal } from 'react';
import { Link } from 'umi';
import { Input, Form, Select, Radio, Button, Switch, message, Row, Col, Image, Typography, Empty, Alert } from 'antd';
import { ProCard } from '@ant-design/pro-components';
import { CopyOutlined } from '@ant-design/icons';
import QRCode from 'qrcode.react';
import qs from 'query-string';

import ProjectSelect from '@/components/ProjectSelect';
import { IProject } from '@/interface/project';
import {
  alipayMiniappRouteOptions,
  taobaoMiniappRouteOptions,
  homeTabOptions,
  homeSubTabConfig,
  EPageType,
  taobaoMiniappEndOptions,
} from './formOptions';
import './index.less';

const { TextArea } = Input;
const { Option } = Select;
const { Title } = Typography;

// 淘端流量海关选品池泰坦配置地址
const tbItemPoolConfigUrl = 'https://fliggy.alibaba-inc.com/#/titan-channel/channel/detail?id=628&title=%E6%89%8B%E6%B7%98%E6%B5%81%E9%87%8F%E6%B5%B7%E5%85%B3';

const AlipayConfigEdit = ({ setResult }: { setResult: (url: string) => void }) => {
  // 海关页选择的Index
  const [customIndex, setCustomIndex] = useState(0);
  const [customPageSpm, setCustomPageSpm] = useState('');
  const [anchoredHomeTab, setAnchoredHomeTab] = useState('');
  const [anchoredHomeSubTab, setAnchoredHomeSubTab] = useState<any>('');
  const [customTTID, setCustomTTID] = useState('');
  const [customFpt, setCustomFpt] = useState('');
  const [customWebviewUrl, setCustomWebviewUrl] = useState('');
  const [endUseWebview, setEndUseWebview] = useState<boolean>(false);
  const [nowCollectHome, setNowCollectHome] = useState<boolean>(true);
  const [staticJumpType, setStaticJumpType] = useState<boolean>(false);
  const [noDynamic, setNoDynamic] = useState<boolean>(false);
  const [endUrl, setEndUrl] = useState('');

  const [form] = Form.useForm();
  // 海关页配置
  const customPage = alipayMiniappRouteOptions[customIndex].value;

  const onCustomIndexChange = (value: number) => {
    setCustomIndex(value);
  };

  const onAnchoredHomeTabChange = (value: string) => {
    setAnchoredHomeTab(value);
  };
  const onAnchoredHomeSubTabChange = (value: string) => {
    setAnchoredHomeSubTab(value || '');
  };

  const onCustomPageSpmChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomPageSpm(e.target.value);
  };
  const onCustomTtidChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setCustomTTID(e.target.value);
  };

  const onCustomFptChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setCustomFpt(e.target.value);
  };

  const onCustomWebviewUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomWebviewUrl(e.target.value);
  };

  const onEndUseWebviewChange = (checked: boolean) => {
    setEndUseWebview(checked);
  };

  const onCollectHomeChange = (checked: boolean) => {
    setNowCollectHome(checked);
  };

  const onStaticJumpChange = (checked: boolean) => {
    setStaticJumpType(checked);
  };

  const onNoDynamicChange = (checked: boolean) => {
    setNoDynamic(checked);
  };

  const onEndUrlChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setEndUrl(e.target.value);
  };

  useEffect(() => {
    setCustomPageSpm(customPage.spm || '');
  }, [customPage]);

  const generatedCustomPage = useMemo<string>(() => {
    const customUrl = qs.parseUrl(customPage.path);
    customUrl.query['flowCustomChannel'] = 'true';
    if (customTTID) customUrl.query['ttid'] = customTTID;
    if (customPage.type === EPageType.WEBVIEW) {
      customUrl.query['url'] = customWebviewUrl;
    } else {
      if (customPage.type === EPageType.HOME && anchoredHomeTab) customUrl.query['tab'] = anchoredHomeTab;
      if (customPage.type === EPageType.HOME && anchoredHomeTab && anchoredHomeSubTab && homeSubTabConfig[anchoredHomeTab]?.findIndex?.((it: any) => it && it.value === anchoredHomeSubTab) > -1) customUrl.query['subTab'] = anchoredHomeSubTab;
      if (customFpt) customUrl.query['fpt'] = `ftid(${customFpt})`;
    }
    return qs.stringifyUrl(customUrl, { strict: false });
  }, [customPage, customTTID, customWebviewUrl, anchoredHomeTab, customFpt, anchoredHomeSubTab]);

  const generatedQuery = useMemo<string>(() => {
    const queryData: Record<string, string> = {};
    if (!endUrl) {
      queryData['noPlaceHoldUrl'] = 'true';
    } else {
      queryData['jumpUrl'] = endUrl;
      if (noDynamic) queryData['noDynamicJumpUrl'] = 'true';
    }
    if (staticJumpType) queryData['staticJumpType'] = 'true';
    if (nowCollectHome) queryData['nowCollectHome'] = 'true';
    if (endUseWebview && endUrl) queryData['flowCustomWebview'] = 'true';
    return qs.stringify(queryData);
  }, [endUrl, noDynamic, staticJumpType, nowCollectHome, endUseWebview]);

  useEffect(() => {
    form
      .validateFields()
      .then((res) => {
        const finalUrl = qs.parseUrl('alipays://platformapi/startapp?appId=2018081461095002');
        finalUrl.query['page'] = generatedCustomPage;
        finalUrl.query['query'] = generatedQuery;
        setResult(qs.stringifyUrl(finalUrl, { strict: false }));
      })
      .catch((err) => {
        setResult('');
      });
  }, [generatedCustomPage, generatedQuery]);

  return (
    <Form className="form-content" form={form} labelCol={{ span: 5, offset: 1 }} labelAlign="left">
      <ProCard title="海关页配置" bordered headerBordered>
        <Form.Item label="海关页类型" required wrapperCol={{ span: 8 }}>
          <Select placeholder="选择小程序页面" onChange={onCustomIndexChange} value={customIndex}>
            {alipayMiniappRouteOptions.map((item, index) => (
              <Option value={index} key={item.label}>
                {item.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        {customPage.type === EPageType.HOME ? (
          <Form.Item label="海关首页锚定Tab" name="anchored-home-tab" wrapperCol={{ span: 8 }}>
            <Select placeholder="选择首页tab" onChange={onAnchoredHomeTabChange} value={anchoredHomeTab}>
              {homeTabOptions.map((item) => (
                <Option value={item.value} key={item.value}>
                  {item.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        {customPage.type === EPageType.HOME && anchoredHomeTab && homeSubTabConfig[anchoredHomeTab] ? (
          <Form.Item label="海关首页锚定二级Tab (暂未开放,投放无效)" name="anchored-home-sub-tab" wrapperCol={{ span: 8 }} >
            <Select placeholder="选择首页二级Tab" onChange={onAnchoredHomeSubTabChange} value={anchoredHomeSubTab} allowClear>
              {homeSubTabConfig[anchoredHomeTab].map((it: any) => (
                <Option value={it.value} key={it.value} x-if={it}>
                  {it.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        <Form.Item label="海关页spm：" wrapperCol={{ span: 12 }}>
          <Input
            placeholder="在此提供海关页的SPM"
            onChange={onCustomPageSpmChange}
            value={customPageSpm}
            disabled={customPage.type !== EPageType.WEBVIEW}
          />
        </Form.Item>
        <Form.Item label="小程序海关页ttid：" wrapperCol={{ span: 12 }}>
          <Input placeholder="在此粘贴ttid" onChange={onCustomTtidChange} value={customTTID} />
        </Form.Item>
        {customPage.type !== EPageType.WEBVIEW ? (
          <Form.Item label="小程序海关页fpt：" wrapperCol={{ span: 12 }}>
            <Input placeholder="在此粘贴fpt" onChange={onCustomFptChange} value={customFpt} />
          </Form.Item>
        ) : null}
        {customPage.type === EPageType.WEBVIEW ? (
          <Form.Item name="custom-webview-url" label="海关页套壳链接：" rules={[{ type: 'url' }, { required: true }]}>
            <Input
              placeholder="在此粘贴H5链接,请带上ttid or fpt"
              onChange={onCustomWebviewUrlChange}
              value={customWebviewUrl}
            />
          </Form.Item>
        ) : null}
      </ProCard>

      <ProCard title="承接页配置" bordered headerBordered>
        <Form.Item label="承接页是否套壳：">
          <Switch
            checkedChildren="套壳"
            unCheckedChildren="不套壳"
            onChange={onEndUseWebviewChange}
            checked={endUseWebview}
          />
        </Form.Item>
        <Form.Item name="end-url" label="承接页链接">
          <Input placeholder="在此粘贴链接,请带上ttid or fpt" onChange={onEndUrlChange} value={endUrl} />
        </Form.Item>
      </ProCard>

      <ProCard title="高级配置" collapsible bordered headerBordered defaultCollapsed>
        <Form.Item label="是否强拉设首：" labelCol={{ span: 6, offset: 1 }}>
          <Switch
            checkedChildren="强拉"
            unCheckedChildren="不强拉"
            onChange={onCollectHomeChange}
            checked={nowCollectHome}
          />
        </Form.Item>
        <Form.Item
          label="是否不获取动态数据（适用需要快速二跳的场景，例机票listing）"
          labelCol={{ span: 6, offset: 1 }}
        >
          <Switch
            checkedChildren="不获取"
            unCheckedChildren="获取"
            onChange={onStaticJumpChange}
            checked={staticJumpType}
          />
        </Form.Item>
        <Form.Item label="承接页是否固定，不定投（适用触达场景）" labelCol={{ span: 6, offset: 1 }}>
          <Switch checkedChildren="固定" unCheckedChildren="不固定" onChange={onNoDynamicChange} checked={noDynamic} />
        </Form.Item>
      </ProCard>
    </Form>
  );
};

const TaobaoConfigEdit = ({ setResult }: { setResult: (url: string) => void }) => {
  // 海关页选择的Index
  const [customIndex, setCustomIndex] = useState(0);
  const [customItemType, setCustomItemType] = useState(1);
  const [customItemId, setCustomItemId] = useState('');
  const [customPageSpm, setCustomPageSpm] = useState('');
  const [customTTID, setCustomTTID] = useState('');
  const [customFpt, setCustomFpt] = useState('');
  const [customWebviewUrl, setCustomWebviewUrl] = useState('');
  const [staticJumpType, setStaticJumpType] = useState<boolean>(false);
  const [endIndex, setEndIndex] = useState<number>(taobaoMiniappEndOptions.length - 1);
  const [endPageQuery, setEndPageQuery] = useState('');
  const [endWebviewUrl, setEndWebviewUrl] = useState('');

  const [form] = Form.useForm();

  // 海关页配置
  const customPage = taobaoMiniappRouteOptions[customIndex].value;
  // 承接页配置
  const endPage = taobaoMiniappEndOptions[endIndex].value;

  const onCustomIndexChange = (value: number) => {
    setCustomIndex(value);
  };

  const onCustomItemType = (e: any) => {
    setCustomItemType(e.target.value);
  };

  const onCustomPageSpmChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomPageSpm(e.target.value);
  };
  const onCustomTtidChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setCustomTTID(e.target.value);
  };

  const onCustomFptChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setCustomFpt(e.target.value);
  };

  const onCustomWebviewUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomWebviewUrl(e.target.value);
  };

  const onCustomItemIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomItemId(e.target.value);
  };

  const onStaticJumpChange = (checked: boolean) => {
    setStaticJumpType(checked);
  };

  const onEndIndexChange = (value: number) => {
    setEndIndex(value);
  };

  const onEndWebviewUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEndWebviewUrl(e.target.value);
  };

  const onEndPageQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEndPageQuery(e.target.value);
  };

  useEffect(() => {
    setCustomPageSpm(customPage.spm || '');
  }, [customPage]);

  const generatedCustomPage = useMemo<string>(() => {
    let customUrl = '';
    if (customTTID) customUrl += `&ttid=${customTTID}`;
    if (customFpt) customUrl += `&fpt=ftid(${customFpt})`;
    if (customPage.type === EPageType.WEBVIEW) {
      customUrl += `&pha_jump_url=${encodeURIComponent(encodeURIComponent(customWebviewUrl))}`;
    } else if (customPage.type === EPageType.WEAL_CENTER || customPage.type === EPageType.ITEM_DETAIL) {
      customUrl += `&pha_active_page_key=${customPage?.path}`;
      if (customPage.type === EPageType.ITEM_DETAIL) {
        customUrl += customItemType === 2 ? `&defaultItemId=${customItemId}` : `&itemId=${customItemId}`;
      }
    }
    return customUrl;
  }, [customPage, customTTID, customWebviewUrl, customFpt, customItemId, customItemType]);

  const generatedQuery = useMemo<string>(() => {
    const queryData: Record<string, string> = {};
    if (endPage.type === EPageType.WEBVIEW || endPage.type === EPageType.H5) {
      // 承接页为套壳H5或者H5，需要挂上spm
      if (customPageSpm) {
        const endUrlObj = qs.parseUrl(endWebviewUrl);
        endUrlObj.query['spm'] = `${customPageSpm}.flow-custom.static-jump`;
        queryData['jumpUrl'] = qs.stringifyUrl(endUrlObj, { strict: false });
      } else {
        queryData['jumpUrl'] = endWebviewUrl;
      }
      if (endPage.type === EPageType.WEBVIEW) queryData['flowCustomWebview'] = 'true';
    } else {
      // 承接页为小程序，需要挂上spm，挂上自定义参数
      const endUrlObj = qs.parseUrl(endPage.path);
      // 往承接页带上海关页spm
      if (customPageSpm) {
        endUrlObj.query['spm'] = `${customPageSpm}.flow-custom.static-jump`;
      }
      // 往承接页带上自定义参数
      const customQueries = qs.parse(endPageQuery);
      Object.entries(customQueries).forEach(([key, value]) => {
        endUrlObj.query[key] = value;
      });
      queryData['jumpUrl'] = qs.stringifyUrl(endUrlObj, { strict: false });
    }
    // 不获取数据
    if (staticJumpType) queryData['staticJumpType'] = 'true';
    return qs.stringify(queryData);
  }, [endPage, customPageSpm, endWebviewUrl, endPageQuery, staticJumpType, customItemType]);

  const finalUrl = useMemo(() => {
    // 商品池
    if (customItemType === 2) {
      const finalUrlObj = qs.parseUrl('https://outfliggys.m.taobao.com/tb_miniapp_link');
      finalUrlObj.query['query'] = generatedQuery;
      return qs.stringifyUrl(finalUrlObj, { strict: false }) + generatedCustomPage;
    }
    const finalUrlObj = qs.parseUrl('https://m.duanqu.com?_ariver_appid=6579080');
    finalUrlObj.query['flowCustomChannel'] = 'true';
    finalUrlObj.query['query'] = generatedQuery;
    return qs.stringifyUrl(finalUrlObj, { strict: false }) + generatedCustomPage;
  }, [generatedCustomPage, generatedQuery]);

  useEffect(() => {
    form
      .validateFields()
      .then(() => {
        setResult(finalUrl);
      })
      .catch((e) => {
        if (e && Array.isArray(e.errorFields) && e.errorFields.length === 0) {
          setResult(finalUrl);
        } else {
          setResult('');
        }
      });
  }, [finalUrl]);

  return (
    <Form className="form-content" form={form} labelCol={{ span: 5, offset: 1 }} labelAlign="left">
      <ProCard title="海关页配置" bordered headerBordered>
        <Form.Item label="海关页类型" required wrapperCol={{ span: 8 }}>
          <Select placeholder="选择小程序页面" onChange={onCustomIndexChange} value={customIndex}>
            {taobaoMiniappRouteOptions.map((item, index) => (
              <Option value={index} key={item.label}>
                {item.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        {customPage.type === EPageType.ITEM_DETAIL ? (
          <Form.Item
            label="选品类型"
            wrapperCol={{ span: 8 }}
          >
            <Radio.Group
              onChange={onCustomItemType}
              value={customItemType}
            >
              <Radio value={1}>单个商品</Radio>
              <Radio value={2}>选品池</Radio>
            </Radio.Group>
          </Form.Item>
        ) : null}
        {customPage.type === EPageType.ITEM_DETAIL ? (
          <Form.Item
            name="custom-webview-url"
            label={customItemType === 2 ? '默认商品ID：' : '商品ID：'}
            rules={[{ type: 'string', pattern: /^[1-9][0-9]+$/g, message: '商品ID必须为数字' }, { required: true }]}
            wrapperCol={{ span: 8 }}
          >
            <Input onChange={onCustomItemIdChange} value={customItemId} />
          </Form.Item>
        ) : null}
        {customPage.type === EPageType.ITEM_DETAIL && customItemType === 2 ? (
          <Form.Item
            label="商品池配置："
            wrapperCol={{ span: 8 }}
          >
            <Button onClick={() => { open(tbItemPoolConfigUrl) }}>泰坦配置</Button>
          </Form.Item>
        ) : null}
        <Form.Item label="海关页spm：" wrapperCol={{ span: 12 }}>
          <Input
            placeholder="在此提供海关页的SPM"
            onChange={onCustomPageSpmChange}
            value={customPageSpm}
            disabled={customPage.type !== EPageType.WEBVIEW}
          />
        </Form.Item>
        <Form.Item label="海关页ttid：" wrapperCol={{ span: 12 }}>
          <Input placeholder="在此粘贴ttid" onChange={onCustomTtidChange} value={customTTID} />
        </Form.Item>
        {customPage.type !== EPageType.WEBVIEW ? (
          <Form.Item label="海关页fpt：" wrapperCol={{ span: 12 }}>
            <Input placeholder="在此粘贴fpt" onChange={onCustomFptChange} value={customFpt} />
          </Form.Item>
        ) : null}
        {customPage.type === EPageType.WEBVIEW ? (
          <Form.Item
            name="custom-webview-url"
            label="海关页套壳链接："
            rules={[{ type: 'url', message: '请输入有效的URL' }, { required: true }]}
          >
            <Input
              placeholder="在此粘贴H5链接,请带上ttid or fpt"
              onChange={onCustomWebviewUrlChange}
              value={customWebviewUrl}
            />
          </Form.Item>
        ) : null}
      </ProCard>

      <ProCard title="承接页配置" bordered headerBordered>
        <Form.Item label="轻应用承接页" required wrapperCol={{ span: 8 }}>
          <Select onChange={onEndIndexChange} value={endIndex}>
            {taobaoMiniappEndOptions.map((opt, index) => (
              <Option value={index} key={index}>
                {opt.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        {endPage.type === EPageType.WEBVIEW || endPage.type === EPageType.H5 ? (
          <Form.Item name="end-webview-url" label="承接页链接：" rules={[{ type: 'url' }, { required: true }]}>
            <Input placeholder="在此粘贴H5链接" onChange={onEndWebviewUrlChange} value={endWebviewUrl} />
          </Form.Item>
        ) : (
          <Form.Item
            name="custom-query"
            label="承接页参数"
            rules={[{ type: 'string', pattern: /^(?:&?[^=&]*=[^=&]*)*$/g, message: '请输入正确格式的参数' }]}
          >
            <Input placeholder="相关参数将会带到承接页" onChange={onEndPageQueryChange} value={endPageQuery} />
          </Form.Item>
        )}
      </ProCard>

      <ProCard title="高级配置" collapsible bordered headerBordered defaultCollapsed>
        <Form.Item label="是否关闭动态化能力（默认不关闭，即开启动态化能力）" labelCol={{ span: 6, offset: 1 }}>
          <Switch
            checkedChildren="关闭"
            unCheckedChildren="开启"
            onChange={onStaticJumpChange}
            checked={staticJumpType}
          />
        </Form.Item>
      </ProCard>
    </Form>
  );
};

export default () => {
  const [end, setEnd] = useState<string>('');
  const [realUrl, setRealUrl] = useState('');

  useEffect(() => {
    window.open('https://fl-flian.fc.alibaba-inc.com/url-schema/record?id=5', "_blank");
  }, [])

  // 投放端change
  const onEndChange = (end: IProject) => {
    setEnd(end.name);
  };

  const noSupport = end !== 'fliggy-allinone' && end !== 'tb-fliggy-miniapp';

  const copyLink = () => {
    navigator.clipboard
      .writeText(realUrl)
      .then(() => {
        message.success('复制成功');
      })
      .catch((err) => {
        message.error(err?.message || '复制失败！');
      });
  };

  return (
    <>
      <Alert
        message={<div>不需要流量海关的套壳链接生成请前往<Link to="/route/onelink">【多端投放】</Link>｜前往飞链平台使用新版<a href="https://fl-flian.fc.alibaba-inc.com/url-schema/record?id=5">【流量海关工具】</a></div>}
        type="info"
        showIcon
      />

      <div className="miniapp-link-container">
        <ProjectSelect onSelect={onEndChange} />
        {noSupport ? (
          <Empty description="暂不支持" />
        ) : (
          <Row justify="space-between" align="top">
            <Col span={8} style={{ marginTop: 100 }}>
              <>
                <Row align="middle" justify="center">
                  {realUrl ? (
                    <QRCode value={realUrl} size={200} />
                  ) : (
                    <Image
                      preview={false}
                      width={200}
                      height={200}
                      style={{ opacity: 0.4, background: '#BBBBBB' }}
                      src="https://img.alicdn.com/imgextra/i3/O1CN01Qa0qBB1Y9NFZWM5UY_!!6000000003016-2-tps-200-200.png"
                    />
                  )}
                </Row>
                <Row style={{ marginTop: 50 }}>
                  <TextArea rows={6} disabled={true} value={realUrl} style={{ wordBreak: 'break-all' }} />
                  <Button type="primary" onClick={copyLink} disabled={!realUrl} block icon={<CopyOutlined />}>
                    复制链接
                  </Button>
                </Row>
              </>
            </Col>
            <Col span={15}>
              {end === 'fliggy-allinone' ? <AlipayConfigEdit setResult={setRealUrl} /> : null}
              {end === 'tb-fliggy-miniapp' ? <TaobaoConfigEdit setResult={setRealUrl} /> : null}
            </Col>
          </Row>
        )}
      </div>
    </>
  );
};
