export const endOptions = [
  { label: '支付宝', value: 'alipay' },
  { label: '淘宝', value: 'taobao' },
];

export enum EPageType {
  HOME = 'home',
  WEAL_CENTER = 'weal-center',
  WEBVIEW = 'webview',
  H5 = 'h5',
  HOTEL_LIST = 'hotel-list',
  ITEM_DETAIL = 'item-detail',
}

export const alipayWebviewPath = 'pages/webview/index';
export const taobaoWebviewPath = 'pages/webview/index';

export const alipayMiniappRouteOptions = [
  { label: '首页', value: { path: 'pages/index/index', spm: '181.22633512', type: EPageType.HOME } },
  { label: '福利中心', value: { path: 'pages/weal-center/index', spm: '181.24050330', type: EPageType.WEAL_CENTER } },
  { label: '套壳页', value: { path: alipayWebviewPath, type: EPageType.WEBVIEW } },
  { label: '酒店列表', value: { path: 'hotel/searchlist/index', spm: '181.7437890', type: EPageType.HOTEL_LIST } },
];

export const taobaoMiniappRouteOptions = [
  { label: '首页', value: { path: null, spm: '181.7474825', type: EPageType.HOME } },
  { label: '福利中心', value: { path: 'lottery', spm: '181.26989430', type: EPageType.WEAL_CENTER } },
  { label: '商品详情', value: { path: 'item_detail', spm: '181.7850105', type: EPageType.ITEM_DETAIL } },
  { label: '套壳页', value: { path: null, type: EPageType.WEBVIEW } },
];

export const taobaoMiniappEndOptions = [
  { label: '首页', value: { path: '/pages/index/index', type: EPageType.HOME } },
  { label: '福利中心', value: { path: '/pages/lottery/index', type: EPageType.WEAL_CENTER } },
  { label: '商品详情', value: { path: '/holiday/detail/index', type: EPageType.ITEM_DETAIL } },
  { label: '套壳H5', value: { path: taobaoWebviewPath, type: EPageType.WEBVIEW } },
  { label: 'H5', value: { path: '', type: EPageType.H5 } },
];

export const homeTabOptions = [
  { label: '酒店', value: 'hotel' },
  { label: '机票', value: 'flight' },
  { label: '火车票', value: 'train' },
  { label: '门票', value: 'ticket' },
  { label: '汽车票', value: 'bus' },
  { label: '租车', value: 'vehicle' },
  { label: '本地化', value: 'local' },
];
export const homeSubTabConfig: any = {
  flight: [
    { label: '单程', value: 'ow' },
    { label: '往返', value: 'rt' },
  ] 
};
