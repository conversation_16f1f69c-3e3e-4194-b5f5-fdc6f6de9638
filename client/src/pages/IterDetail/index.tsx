import { useEffect, useRef, useState } from 'react';
import { Spin, Skeleton, Result, Tabs, Alert } from 'antd';
import { ResultStatusType } from 'antd/es/result';
import { useLocation } from 'umi';
import queryString from 'query-string';
import iterBranch<PERSON><PERSON> from '@/api/iter-branch';
import iterCalendarApi from '@/api/iter-calendar';
import iterQaReviewApi from '@/api/iter-qa-review';
import { IIterBranch } from '@/interface/iter-branch';
import { IDevBranch } from '@/interface/dev-branch';
import { IIterQaReview } from '@/interface/iter-qa-review';
import { IFreeDevBranch } from '@/interface/free-dev-branch';
import { IProject } from '@/interface/project';
import DevList from './components/DevList';
import QaReviewList from './components/QaReviewList';
import IterSteps from './components/IterSteps';
import IterHeader from './components/IterHeader';
import { ITER_STATUS_TEXT } from '@/const/iter-branch';
import './index.less';

export default () => {
  const [iterBranchDetail, setIterBranchDetail] = useState<IIterBranch>();
  const [project, setProject] = useState<IProject>();
  const [devBranchList, setDevBranchList] = useState<(IDevBranch | IFreeDevBranch)[]>();
  const [loading, setLoading] = useState(false);
  const [errorCode, setErrorCode] = useState<ResultStatusType>();
  const [iterQaReviewList, setIterQaReviewList] = useState<IIterQaReview[]>([]);
  const location = useLocation();
  const [preIter, setPreIter] = useState<any>(null);
  const { iterId } = queryString.parse(location.search, {
    types: {iterId: 'number' }
  }) as { iterId?: number };
  
  if (!iterId) throw Error('url参数缺少iterId'); 

  useEffect(refresh, [])

  function refresh() {
    if (!iterId) return;

    // 获取迭代详情
    let publishDay_ = '';
    let projectName = "fliggy-allinone";
    iterBranchApi.get(iterId).then(res => {
      if (!res?.data) throw Error();
      const { project, detail, devBranchList, freeDevBranchList } = res.data;
      publishDay_ = detail?.publishDay || '';
      projectName = detail?.projectName;
      setIterBranchDetail(detail);
      setDevBranchList(([] as (IDevBranch | IFreeDevBranch)[]).concat(devBranchList, freeDevBranchList))
      setProject(project);

    if (projectName && publishDay_) {
      let dateSplite = publishDay_.split('-');
      dateSplite[0] && dateSplite[1] && iterCalendarApi.list({ year: Number(dateSplite[0]), month: Number(dateSplite[1]), projectName: projectName }).then(res => {
        if(res?.success) {
          const { iterList=[] } = res?.data || {};
          let temp = iterList.sort((a, b) => a && b && a.publishDay < b.publishDay ? 1 : -1);
          let idx = temp.findIndex(it => it && it.publishDay === publishDay_) + 1;
          let preIter = temp[idx];
          if (idx > 0 && preIter && preIter.status !== 3) {
            setPreIter({
              status: preIter?.status,
              desc: ITER_STATUS_TEXT[preIter?.status],
              publishDay: preIter.publishDay,
            })
          } else {
            setPreIter(null)
          }
        }
      })
    }
    }).catch(() => {
      setErrorCode(500);
    })

    // 获取全量回归列表，可能无
    iterQaReviewApi.list({ iterId }).then(res => {
      if (!res.data.list) return;
      setIterQaReviewList(res.data.list)
    })
    
  }

  if (errorCode) {
    return <div className="page-container">
      <Result
        status={errorCode}
        title={errorCode}
        subTitle="系统出错了~"
      ></Result>
    </div>
  }

  function renderContent() {
    if (!iterBranchDetail || !project) return;

    const devListJsx = <DevList
      iterBranchDetail={iterBranchDetail}
      list={devBranchList}
      project={project} />

    // 如果存在全量回归，则以tab形式展示
    if (iterQaReviewList.length > 0) {
      const qaReviewListJsx = <QaReviewList
        iterBranchDetail={iterBranchDetail}
        list={iterQaReviewList} />

      return <Tabs
        defaultActiveKey="1"
        items={[
          {
            label: '集成区',
            key: '1',
            children: devListJsx
          },
          {
            label: '全量回归',
            key: '2',
            children: qaReviewListJsx
          },
        ]}
      />
    } else {
      return devListJsx;
    }
  }

  if (iterBranchDetail && project) {
    return <>
      <Alert showIcon message="注意：请不要提交迭代分支（前缀为stable/）和集成分支（前缀为rc/）的代码！" type="warning" />
      <Alert 
        showIcon 
        message={`警告 前序迭代（${preIter && preIter.publishDay}）未完成，状态为：${preIter && preIter.desc}，请检查后再继续操作本次迭代！！！`} 
        type="error" 
        style={preIter && preIter.desc ? {} : { display: 'none'}} 
      />

      <Spin wrapperClassName="page-container" size="large" spinning={loading}>
        <IterHeader
          iterBranchDetail={iterBranchDetail}
          iterQaReviewList={iterQaReviewList}
          devBranchList={devBranchList as (IDevBranch | IFreeDevBranch)[]}
          project={project}
          onIterChange={refresh}
          setLoading={setLoading}
        />
        <IterSteps
          iterBranchDetail={iterBranchDetail}
          project={project} />
        {renderContent()}
      </Spin>
    </>
  } else {
    return <div className="page-container">
      <Skeleton avatar paragraph={{ rows: 3 }} active />
      <div style={{ margin: '200px 0', textAlign: 'center' }}>
        <Spin size="large" />
      </div>
    </div>
  }
};
