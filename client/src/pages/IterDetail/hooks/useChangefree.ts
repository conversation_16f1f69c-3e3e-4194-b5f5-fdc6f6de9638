import { useEffect, useState } from 'react';
import get from 'lodash/get';
import changefreeApi from '@/api/changefree';

const STATUS_MAP: { [key: string]: string } = {
  'CHECK_PASS': '审批通过',
  'CHECK_HOLD': '未通过',
  'CHECK_WAIT': '风险检测中',
  'CHECK_REFUSE': '阻断变更',
  'CHECK_CANCEL': '变更取消',
}

export default function useChangefree({ iterId }: { iterId: number }) {
  const [changefreeInfo, setChangefreeInfo] = useState<{ show: boolean; orderUrl?: string; statusText?: string;}>({
    show: false,
  });

  useEffect(() => {
    if (!iterId) return;
    changefreeApi.query({ iterId }).then((res) => {
      const code = get(res, 'data.code');
      const checkStatus = get(res, 'data.body.check_status_enum');
      const orderStatus = get(res, 'data.body.order_status_enum');
      const orderUrl = get(res, 'data.body.orderUrl');
      // 仅命中changefree规则才展示
      if (code === '0' && orderStatus) {
        setChangefreeInfo({
          show: true,
          orderUrl,
          statusText: STATUS_MAP[checkStatus],
        })
      }
    })
  }, [iterId]);

  return {
    changefreeInfo,
  }
}