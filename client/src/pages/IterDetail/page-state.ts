import { createGlobalState } from 'react-hooks-global-state';
import { IDeliverClientWithTask } from '@/interface/iter-branch';

const initialState = {
  /** 是否是多端统一投放 */
  isUnifiedDeliver: undefined as boolean | undefined,
  /** 投放端列表，含任务详情 */
  deliverClientWithTaskList: [] as IDeliverClientWithTask[],
};

const { useGlobalState } = createGlobalState(initialState);

export const usePageState = useGlobalState;