import { useState } from 'react';
import { Popover, Form, Input, Button, message, Checkbox, notification } from 'antd';
import helperApi from '@/api/helper';

import './index.less';

interface Props {
  /** 迭代分支id */
  iterId?: number;
  /** 开发分支id */
  devId?: number;
}

const Comp: React.FC<Props> = ({ iterId, devId, children }) => {
  const [visible, setVisible] = useState(false);

  function triggerAutomatedTest({ qrCodeLink, update }: { qrCodeLink?: string; update?: string[] }) {
    setVisible(false);

    helperApi.triggerAutomatedTest({
      iterId,
      devId,
      qrCodeLink,
      update: !!(update && update[0])
    })
      .then((res) => {
        if (res?.success) {
          notification.success({
            message: '触发自动化测试成功',
            description: `塔台id: ${res.data.towerId}`,
            duration: 0,
          });
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        notification.error({
          message: '触发自动化测试失败',
          description: err.message,
          duration: 0,
        });
      })
  }

  const Content = <Form className="notice-form" onFinish={triggerAutomatedTest}>
    <Form.Item name="qrCodeLink" label="二维码链接">
      <Input
        size="middle"
        placeholder="不填则自动构建"
      />
    </Form.Item>

    <Form.Item name="update" label="是否更新库">
      <Checkbox.Group><Checkbox value="1" /></Checkbox.Group>
    </Form.Item>

    <Button className="form-submit" type="primary" size="middle" htmlType="submit">确定触发</Button>
  </Form>

  return <Popover
    visible={visible}
    onVisibleChange={visible => setVisible(visible)}
    content={Content}
    placement="left"
    trigger="click">
    {children}
  </Popover>
}

export default Comp;