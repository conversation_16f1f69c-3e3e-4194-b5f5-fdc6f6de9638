import { useEffect, useState } from 'react';
import { List } from 'antd';
import { IDevBranch } from '@/interface/dev-branch';
import { IFreeDevBranch } from '@/interface/free-dev-branch';
import { IIterBranch } from '@/interface/iter-branch';
import { IProject } from '@/interface/project';
import Item from '@/components/DevItem';
import packageApi from '@/api/package';

import './index.less';

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  iterBranchDetail: IIterBranch;
  list: (IDevBranch | IFreeDevBranch)[] | undefined;
  project: IProject;
}

export default ({ iterBranchDetail, list, project }: Props) => {
  const [branchSizeMap, setBranchSizeMap] = useState<Record<string, Record<string, number>>>({});

  list = list?.filter(item => !!item);

  useEffect(() => {
    if (list?.length) {
      const branchList = list
        .map(item => ({ name: item.branchName, reportAnalyzed: item.reportAnalyzed || '' }))
        .filter(item => item.reportAnalyzed);

      if (!branchList.length) return;
      packageApi.analyzeBranchSize({
        projectName: iterBranchDetail.projectName,
        gmtModified: iterBranchDetail.gmtModified,
        branchList: list
          .map(item => ({ name: item.branchName, reportAnalyzed: item.reportAnalyzed || '' }))
          .filter(item => item.reportAnalyzed),
      }).then(res => {
        setBranchSizeMap((res.data?.branchSizeMapList || []).reduce((pre, item) => {
          pre[item.branchName] = item.sizeDiffInfo;
          return pre;
        }, {} as any));
      })
    }
  }, [JSON.stringify(list || [])]);

  return <List
    itemLayout="horizontal"
    size="large"
    pagination={{
      pageSize: 10,
    }}
    dataSource={list}
    renderItem={item => (
      <List.Item
        key={item.branchName}
        style={{ padding: 0 }}
      >
        <Item iterBranch={iterBranchDetail} devBranch={item} project={project} sizeDiffInfo={branchSizeMap[item.branchName]} />
      </List.Item>
    )}
  />
}