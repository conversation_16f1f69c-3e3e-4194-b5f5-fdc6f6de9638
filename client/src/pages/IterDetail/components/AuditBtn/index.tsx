import { useState } from 'react';
import { Mo<PERSON>, But<PERSON>, message, Popconfirm, Space, Select, Avatar } from 'antd';
import { IIterDeliverTask, IMiniAppVersionInfo, IAlipayVersionInfo } from '@/interface/iter-deliver';
import iterDeliverApi from '@/api/iter-deliver';
import { IDeliverClientWithTask } from '@/interface/iter-branch';
import { EClient } from '@/const/iter-deliver';
import { usePageState } from '../../page-state';

import './index.less';

interface Props {
  // 类型，提审、撤销审核、通过审核
  type: 'audit' | 'cancelAudit' | 'passAudit';
  // 投放任务
  deliverTask: IIterDeliverTask;
  // 使用内嵌iframe进行投放
  useIframe: boolean;
  // 状态变更回调
  onChange: () => void;
}

export default (({ deliverTask, type, useIframe, onChange, children }) => {
  const miniAppVersionInfo = deliverTask.miniAppVersionInfo as IAlipayVersionInfo | IMiniAppVersionInfo;
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedClientList, setSelectedClientList] = useState<string[]>([]);
  const [deliverClientWithTaskList] = usePageState('deliverClientWithTaskList');
  const [isUnifiedDeliver] = usePageState('isUnifiedDeliver');

  let actionTitle = '';
  let clickFn = () => {};
  if (type === 'audit') {
    actionTitle = '提审';
    clickFn = audit;
  } else if (type === 'cancelAudit') {
    actionTitle = '撤销审核';
    clickFn = cancelAudit;
  } else if (type === 'passAudit') {
    actionTitle = '通过审核';
    clickFn = passAudit;
  }

  // 提审
  function audit() {
    setLoading(true);
    handleRes(iterDeliverApi.audit({
      id: deliverTask.id,
      clientList: isUnifiedDeliver ? selectedClientList as EClient[] : undefined
    }), '提审')
  }

  // 通过审核
  function passAudit() {
    setLoading(true);

    handleRes(iterDeliverApi.passAudit({
      id: deliverTask.id
    }), '通过审核')
  }

  // 撤销审核
  function cancelAudit() {
    setLoading(true);

    handleRes(iterDeliverApi.cancelAudit({
      id: deliverTask.id
    }), '撤销审核')
  }

  function handleRes(requestPromise: ReturnType<typeof iterDeliverApi.audit>, actionTitle: string) {
    requestPromise.then((res) => {
      if (res?.success) {
        message.success(`${actionTitle}成功`);
        onChange();
        closeModal();
      } else {
        throw Error(res?.errorMsg);
      }
    })
      .catch((err: any) => {
        message.error(`${actionTitle}失败${err?.message ? ': ' + err.message : ''}`);
      }).finally(() => {
        setLoading(false)
      });
  }

  function closeModal() {
    setShowModal(false);
  }

  function renderFooter() {
    if (isUnifiedDeliver && type === 'audit') {
      return <Space>
        <Select
          mode="multiple"
          style={{ width: '300px', textAlign: 'left' }}
          placeholder="请选择至少一个投放端"
          onChange={setSelectedClientList}
          optionLabelProp="label"
        >
          {
            deliverClientWithTaskList.map((client, index) =>
              <Select.Option key={index} value={client.clientName} label={client.clientName}>
                <Space>
                  <Avatar size="small" src={client.clientExtInfo?.icon} aria-label={client.clientName} />
                  {client.clientExtInfo?.cnName}
                </Space>
              </Select.Option>)
          }
        </Select>
        <Button
          disabled={selectedClientList.length === 0}
          type="primary"
          onClick={clickFn}
          loading={loading}>确认已{actionTitle}</Button>
      </Space>
    } else {
      return <Button
        type="primary"
        onClick={clickFn}
        loading={loading}>确认已{actionTitle}</Button>
    }
  }

  if (!useIframe) {
    return <Popconfirm
      title={`确定${actionTitle}？`}
      onConfirm={clickFn}
      okText="确定"
      cancelText="取消"
    >{children}</Popconfirm>
  } else {
    return <>
      <span onClick={() => setShowModal(true)}>{children}</span>

      <Modal
        open={showModal}
        title={<span>{actionTitle}<a href={miniAppVersionInfo.platformUrl} target="_blank" style={{ fontSize: '12px' }}> 直接打开原链接</a></span>}
        width={1500}
        destroyOnClose
        maskClosable={false}
        onCancel={closeModal}
        footer={renderFooter()}
      >
        <iframe className="modal-iframe" src={miniAppVersionInfo.platformUrl}></iframe>
      </Modal>
    </>
  }
}) as React.FC<Props>