import { useEffect, useState } from 'react';
import { useAccess } from 'umi';
import { Spin, Space, message, Tag, Popconfirm, Tooltip, Avatar } from 'antd';
import { CloseCircleOutlined } from '@ant-design/icons';
import { IIterBranch, IDeliverClientWithTask } from '@/interface/iter-branch';
import { IProject } from '@/interface/project';
import { IterStatus } from '@/const/iter-branch';
import iterBranchApi from '@/api/iter-branch';
import TaskTable from './task-table';
import Upload from './upload';

interface Props {
  deliverClientWithTaskList: IDeliverClientWithTask[];
  iterBranchDetail: IIterBranch;
  project: IProject;
  // 状态变更回调
  onChange: () => void;
}

export default (({ deliverClientWithTaskList, iterBranchDetail, project, onChange }) => {
  const [loading, setLoading] = useState(false);
  const [taskRefreshTag, setTaskRefreshTag] = useState(1);
  const access = useAccess();
  // 当迭代处于发布状态的时候，锁住投放
  const lockDeliver = iterBranchDetail.status === IterStatus.PUBLISHED;
  // 多端统一投放的 miniAppId、originalPlatform 是一致的，因此取第一个即可
  const { miniAppId, originalPlatform } = deliverClientWithTaskList[0] || {};
  // 本次投放的端
  const clientListForThisDelivery = deliverClientWithTaskList.filter(item => item.deliverTaskId);
  const { deliverTask, deliverTaskId } = clientListForThisDelivery[0] || {};

  function refresh() {
    setTaskRefreshTag(Date.now());
  }

  function onChildrenChange() {
    onChange();
  }

  /** 解绑投放任务 */
  function unbindDeliverTask() {
    setLoading(true);

    iterBranchApi.unbindDeliverTask({
      iterId: iterBranchDetail.iterId,
    })
      .then((res) => {
        if (res?.success) {
          setLoading(false);
          message.success('解绑版本号成功');
          onChange();
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(`解绑版本号失败${err?.message ? ': ' + err.message : ''}`);
        setLoading(false)
      })
  }

  /** 绑定投放任务 */
  function bindDeliverTask(deliverTaskId: number) {
    setLoading(true);

    iterBranchApi.bindDeliverTask({
      iterId: iterBranchDetail.iterId,
      clientList: deliverTask?.clientList,
      miniAppId,
      deliverTaskId,
    })
      .then((res) => {
        if (res?.success) {
          setLoading(false);
          message.success('绑定版本号成功');
          onChange();
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(`绑定版本号失败${err?.message ? ': ' + err.message : ''}`);
        setLoading(false)
      })
  }

  function renderActions() {
    const actions = [];

    actions.push(<Upload
      key="upload"
      lockDeliver={lockDeliver}
      iterBranchDetail={iterBranchDetail}
      project={project}
      originalPlatform={originalPlatform}
      miniAppId={miniAppId}
      onChange={refresh} />)

    return actions;
  }

  function renderClientList() {
    if (clientListForThisDelivery.length === 0) return null;

    return <Space direction="vertical" size={2}>
      <Space>
        <Avatar.Group size="small">
          {
            clientListForThisDelivery.map((client, index) => (
              <Avatar key={index} src={client.clientExtInfo?.icon} />
            ))
          }
        </Avatar.Group>
        <span>{Array.from(new Set(clientListForThisDelivery.map(client => client.clientExtInfo?.cnName))).join(',')}</span>
      </Space>
      <span className="light-text">{Array.from(new Set(clientListForThisDelivery.map(client => client.miniAppId))).join(',')}</span>
    </Space>
  }

  return <Spin spinning={loading}>
    <div className="deliver-panel-header">
      <Space className="client-list" size={20}>
        {renderClientList()}

        {deliverTask?.miniAppVersion &&
          <div>
            <Tag color="orange">v{deliverTask.miniAppVersion}</Tag>
            {
              access.isAdmin && !lockDeliver
                ? <Popconfirm
                  title="确定解绑版本号？"
                  onConfirm={unbindDeliverTask}
                  okText="确定"
                  cancelText="取消"
                ><Tooltip title="解绑版本号"><CloseCircleOutlined className="danger-text" /></Tooltip></Popconfirm>
                : null
            }
          </div>
        }

        {deliverTask?.miniAppVersionInfo
          ? deliverTask.miniAppVersionInfo.statusText
          : (deliverTask?.uploadStatusText || '')}
      </Space>
      <div className="actions">
        <Space>{access.isAdmin ? renderActions() : null}</Space>
      </div>
    </div>

    <TaskTable
      iterBranchDetail={iterBranchDetail}
      bindDeliverTaskId={deliverTaskId}
      project={project}
      refreshTag={taskRefreshTag}
      lockDeliver={lockDeliver}
      onChange={onChildrenChange}
      onBindDeliverTask={bindDeliverTask}
    />
  </Spin>
}) as React.FC<Props>