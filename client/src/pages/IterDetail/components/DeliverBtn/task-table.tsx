import { useEffect, useState } from 'react';
import { Space, message, Table, Popconfirm, Tooltip } from 'antd';
import { useAccess } from 'umi';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { IIterDeliverTask, IAlipayVersionInfo } from '@/interface/iter-deliver';
import { IIterBranch } from '@/interface/iter-branch';
import { EClient, EMiniAppUploadStatus, EMiniAppVersionStatus } from '@/const/iter-deliver';
import iterDeliverApi from '@/api/iter-deliver';
import { IProject } from '@/interface/project';
import { EProjectType } from '@/const/project';
import AuditBtn from '../AuditBtn';
import GrayBtn from '../GrayBtn';
import BackDevBtn from '../BackDevBtn';
import OnlineBtn from '../OnlineBtn';
import OfflineBtn from '../OfflineBtn';
import RollbackBtn from '../RollbackBtn';
import { usePageState } from '../../page-state';

interface IDataSource extends IIterDeliverTask {
  key: number;
}

interface Props {
  /** 投放端绑定的任务id */
  bindDeliverTaskId: number | null;
  iterBranchDetail: IIterBranch;
  project: IProject;
  // 投放端，适用于多端单独投放
  clientName?: EClient;
  refreshTag: number;
  lockDeliver: boolean;
  onChange: () => void;
  onBindDeliverTask: (deliverTaskId: number) => void;
}

export default (({ clientName, bindDeliverTaskId, iterBranchDetail, project, refreshTag, lockDeliver, onChange, onBindDeliverTask }) => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<IDataSource[]>([]);
  const [isUnifiedDeliver] = usePageState('isUnifiedDeliver');
  const access = useAccess();
  // 判断是否存在审核中、审核通过、审核拒绝、灰度中或上架的版本
  const hasVersionInProcess = dataSource.some(deliverTask => {
    const status = deliverTask.miniAppVersionInfo?.status;
    return status
      ? [EMiniAppVersionStatus.AUDITING, EMiniAppVersionStatus.AUDIT_REJECT, EMiniAppVersionStatus.WAIT_RELEASE, EMiniAppVersionStatus.GRAY, EMiniAppVersionStatus.RELEASE].indexOf(status) > 0
      : false
  });
  // 是否打通投放链路,目前只有支系小程序项目打通
  const fullProcess = project.type === EProjectType.ALIPAY;

  useEffect(() => {
    getDeliverTaskList()
  }, [refreshTag]);

  function onChildrenChange() {
    getDeliverTaskList();
    onChange();
  }

  // 获取投放任务列表
  function getDeliverTaskList() {
    setLoading(true);

    iterDeliverApi.list({
      iterId: iterBranchDetail.iterId,
      clientName
    })
      .then((res) => {
        if (res?.success && res.data) {
          setDataSource(res.data.list.map(item => ({ key: item.id, ...item })));
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(err?.message || '获取投放任务列表失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  // 删除任务
  function deleteTask(deliverTask: IIterDeliverTask) {
    setLoading(true);

    iterDeliverApi.delete({
      id: deliverTask.id
    })
      .then((res) => {
        if (res?.success && res.data) {
          getDeliverTaskList();
          message.success('删除成功');
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(`删除投放失败${err?.message ? ': ' + err.message : ''}`);
        setLoading(false)
      })
  }

  const columns = [
    {
      title: '版本号', dataIndex: 'miniAppVersion', key: 'miniAppVersion', render: (text: string) => {
        return text || '-'
      }
    },
    {
      title: '状态', dataIndex: 'statusText', key: 'statusText', render: (_text: string, record: IDataSource) => {
        const miniAppVersionInfo = record.miniAppVersionInfo;

        // 有版本信息的情况下，取版本状态
        if (miniAppVersionInfo) {
          // 灰度情况
          if (miniAppVersionInfo.status === EMiniAppVersionStatus.GRAY) {
            const curGrayStep = miniAppVersionInfo.graySteps.find(item => item.strategy === miniAppVersionInfo.curGrayStrategy);

            if (curGrayStep) {
              return `${miniAppVersionInfo.statusText}, ${curGrayStep.title}`
            }
          }

          // 拒审情况
          if (miniAppVersionInfo.status === EMiniAppVersionStatus.AUDIT_REJECT) {
            let rejectReason = '';

            if (record.clientName === EClient.ALIPAY) {
              rejectReason = (miniAppVersionInfo as IAlipayVersionInfo).rejectReason;
            }

            if (rejectReason) {
              return <Space>{miniAppVersionInfo.statusText}<Tooltip title={rejectReason}><QuestionCircleOutlined /></Tooltip></Space>
            }
          }

          return miniAppVersionInfo.statusText;
        }
        // 否则取上传状态
        else {
          // 上传失败且有日志情况
          if (record.uploadStatus === EMiniAppUploadStatus.FAILED && record.uploadLog) {
            return <>{record.uploadStatusText}, <a target="_blank" href={record.uploadLog}>日志</a></>
          }

          return record.uploadStatusText;
        }
      }
    },
    {
      title: '关键时间点', dataIndex: 'criticalTime', key: 'criticalTime', render: (_text: string, record: IDataSource) => {
        const miniAppVersionInfo = record.miniAppVersionInfo;
        let criticalTimeList = [] as string[];

        if (miniAppVersionInfo && miniAppVersionInfo.status) {
          switch (miniAppVersionInfo.status) {
            case EMiniAppVersionStatus.INIT:
            case EMiniAppVersionStatus.AUDITING:
            case EMiniAppVersionStatus.WAIT_RELEASE:
            case EMiniAppVersionStatus.AUDIT_REJECT:
              criticalTimeList = [`版本生成：${miniAppVersionInfo.createTime}`];
              break;
            case EMiniAppVersionStatus.GRAY:
              criticalTimeList = [`灰度开始：${miniAppVersionInfo.grayStartTime}`];
              break;
            case EMiniAppVersionStatus.RELEASE:
              criticalTimeList = [`上架：${miniAppVersionInfo.shelfTime}`];
              break;
            case EMiniAppVersionStatus.OFFLINE:
              criticalTimeList = [
                `下架：${miniAppVersionInfo.offlineTime}`,
                `上架：${miniAppVersionInfo.shelfTime}`,
              ];
              break;
          }
        } else {
          criticalTimeList = [`构建上传：${record.gmtCreate}`]
        }

        return <Space direction="vertical">{criticalTimeList}</Space>;
      }
    },
    {
      title: '操作', key: 'operation', render: (_text: string, record: IDataSource) => {
        const miniAppVersionInfo = record.miniAppVersionInfo;
        let actions = [] as (React.ReactElement | null)[];
        const DeleteBtn = <Popconfirm
          key="delete"
          placement="bottom"
          title="确认删除？"
          okText="是"
          cancelText="否"
          onConfirm={() => deleteTask(record)}><a className="danger-text">删除</a></Popconfirm>

        if (miniAppVersionInfo && miniAppVersionInfo.status) {
          switch (miniAppVersionInfo.status) {
            case EMiniAppVersionStatus.INIT:
              actions = [
                !hasVersionInProcess ? <AuditBtn key="audit" deliverTask={record} useIframe onChange={onChildrenChange} type="audit"><a>提审</a></AuditBtn> : null,
                DeleteBtn
              ]
              break;
            case EMiniAppVersionStatus.AUDITING:
              actions = [
                !fullProcess ? <AuditBtn key="passAudit" useIframe={!fullProcess} deliverTask={record} onChange={onChildrenChange} type="passAudit"><a>通过审核</a></AuditBtn> : null,
                <AuditBtn key="cancelAudit" useIframe={!fullProcess} deliverTask={record} onChange={onChildrenChange} type="cancelAudit"><a className="danger-text">撤销审核</a></AuditBtn>
              ]
              break;
            case EMiniAppVersionStatus.WAIT_RELEASE:
              actions = [
                isUnifiedDeliver
                  ? <OnlineBtn key="online" deliverTask={record} useIframe={!fullProcess} onChange={onChildrenChange}><a>上架</a></OnlineBtn>
                  : <GrayBtn key="gray" deliverTask={record} useIframe={!fullProcess} onChange={onChildrenChange}><a>开始灰度</a></GrayBtn>,
                <BackDevBtn key="backDev" deliverTask={record} useIframe={!fullProcess} onChange={onChildrenChange}><a className="danger-text">退回开发</a></BackDevBtn>
              ]
              break;
            case EMiniAppVersionStatus.AUDIT_REJECT:
              actions = [
                <BackDevBtn key="backDev" deliverTask={record} useIframe={!fullProcess} onChange={onChildrenChange}><a className="danger-text">退回开发</a></BackDevBtn>
              ]
              break;
            case EMiniAppVersionStatus.GRAY:
              actions = [
                <GrayBtn key="gray" deliverTask={record} useIframe={!fullProcess} onChange={onChildrenChange}><a>修改灰度范围</a></GrayBtn>,
                <OnlineBtn key="online" deliverTask={record} useIframe={!fullProcess} onChange={onChildrenChange}><a>上架</a></OnlineBtn>,
                <GrayBtn key="finishGray" deliverTask={record} useIframe={!fullProcess} isFinishGray onChange={onChildrenChange}><a className="danger-text">结束灰度</a></GrayBtn>,
              ]
              break;
            case EMiniAppVersionStatus.RELEASE:
              actions = [
                <RollbackBtn key="rollback" deliverTask={record} useIframe={!fullProcess} onChange={onChildrenChange}><a>回滚</a></RollbackBtn>,
                <OfflineBtn key="offline" deliverTask={record} useIframe={!fullProcess} onChange={onChildrenChange}><a className="danger-text">下架</a></OfflineBtn>
              ]
              break;
          }
        } else {
          actions = [DeleteBtn]
        }

        // 如果投放端没有绑定投放任务，且当前投放任务是上架状态，则支持绑定
        if (!bindDeliverTaskId && miniAppVersionInfo?.status === EMiniAppVersionStatus.RELEASE) {
          actions.push(<Popconfirm
            key="bind"
            placement="bottom"
            title="是否绑定当前投放任务？"
            okText="是"
            cancelText="否"
            onConfirm={() => onBindDeliverTask(record.id)}><a>绑定</a></Popconfirm>)
        }

        return access.isAdmin && actions.length > 0 && !lockDeliver && (bindDeliverTaskId ? bindDeliverTaskId === record.id : true) ? <Space>{actions}</Space> : '无';
      }
    },
  ];

  return <Table
    loading={loading}
    columns={columns}
    dataSource={dataSource}
    pagination={false}
  />
}) as React.FC<Props>