import { useEffect, useState } from 'react';
import { Mo<PERSON>, But<PERSON>, message, Popconfirm, Tooltip } from 'antd';
import { useModel } from 'umi';
import get from 'lodash/get';
import { IIterBranch } from '@/interface/iter-branch';
import { IProject } from '@/interface/project';
import iterBranchApi from '@/api/iter-branch';
import { EMiniAppVersionStatus } from '@/const/iter-deliver';
import { IterStatus } from '@/const/iter-branch';
import { EProjectType } from '@/const/project';
import UnifiedPanel from './unified-panel';
import SeparatePanel from './separate-panel';
import { usePageState } from '../../page-state';
import { IDevBranch } from '@/interface/dev-branch';
import { EMergeCode } from '@/const/dev-branch';
import { IFreeDevBranch } from '@/interface/free-dev-branch';
import { BIZ_LINE, BIZ_TITLE } from '@/const/biz';
import { checkSizeBeforeIterDeliver } from '@/utils/perf';
import changefreeApi from '@/api/changefree';

import './index.less';

interface Props {
  iterBranchDetail: IIterBranch;
  project: IProject;
  devBranchList: (IDevBranch | IFreeDevBranch)[];
  // 发布完成回调
  onPublishEnd: () => void;
}

export default (({ iterBranchDetail, devBranchList, project, onPublishEnd }) => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isUnifiedDeliver, setIsUnifiedDeliver] = usePageState('isUnifiedDeliver');
  const [deliverClientWithTaskList, setDeliverClientWithTaskList] = usePageState('deliverClientWithTaskList');
  const currentUser = useModel('user');

  // 是否可发布，必须所有投放端都上架或跳过投放
  const canPublish = (() => {
    const clientListForThisDelivery = deliverClientWithTaskList.filter(item => item.deliverTaskId);
    const failedDeliverClientList = clientListForThisDelivery.filter(deliverClientWithTask => {
      // 若是跳过投放，通过
      if (deliverClientWithTask.skip) {
        return false
      } else {
        // 状态不是已上架，不通过
        return deliverClientWithTask.deliverTask?.miniAppVersionInfo?.status !== EMiniAppVersionStatus.RELEASE;
      }
    })

    // 有投放的端，且投放的端都已上架
    return clientListForThisDelivery.length > 0 && failedDeliverClientList.length === 0;
  })()

  // 首次或 iterId 变更的时候请求一次
  useEffect(() => {
    setIsUnifiedDeliver(project.type === EProjectType.BYTEDANCE);
    listDeliverClient();
  }, [iterBranchDetail.iterId]);

  // 显示弹窗时请求一次进行刷新
  useEffect(() => {
    if (showModal) listDeliverClient()
  }, [showModal])

  function listDeliverClient() {
    if (loading) return;
    setLoading(true);

    iterBranchApi.listDeliverClient({
      iterId: iterBranchDetail.iterId
    })
      .then((res) => {
        if (res?.success && res.data) {
          setDeliverClientWithTaskList(res.data)
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(err?.message || '获取列表失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  /** 发布代码，既合进master */
  async function publish() {
    return iterBranchApi.publish({
      iterId: iterBranchDetail.iterId,
      isGray: false,
    })
      .then((res) => {
        if (res?.success) {
          message.success('发布代码成功');
          onPublishEnd();
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(`发布代码失败${err?.message ? ': ' + err.message : ''}`);
      })
  }

  function closeModal() {
    setShowModal(false);
  }

  function onChildrenChange() {
    listDeliverClient();
    onPublishEnd();
  }

  /** changefree拦截 */
  function changefreeCheck() {
    return new Promise((resolve, reject) => {
      const { iterId, version, publishDay } = iterBranchDetail;
      const { project: projectName, group, cnName  } = project;
      const branchList = devBranchList.map(item => {
        const { branchName, bizLine = '', description, creator, qaList, aoneList, mergeCode, npmList, npmResolutionList } = item;
        const npmStr = (npmList || []).map(v => `${v.name}@${v.value}`).join(',');
        const resolutionStr = (npmResolutionList || []).map(v => `${v.name}@${v.value}`).join(',');
        const depsStr = `${npmStr ? '依赖包更新：' + npmStr : ''}；${resolutionStr ? '\n resolution更新：' + resolutionStr : ''}`
        const extra = mergeCode === EMergeCode.NO ?  depsStr : '合并代码';
        const qaInfo = (qaList || []).map(v => v.name).join(',');
        const { subject, stamp, id } = aoneList[0] || {};
        const aoneType = stamp === 'Bug' ? 'issue' : stamp.toLowerCase();
        return {
          branchName,
          description,
          creator,
          qaInfo,
          bizLine: BIZ_TITLE[bizLine as BIZ_LINE],
          aoneInfo: {
            name: subject,
            url:  `https://aone.alibaba-inc.com/${aoneType}/${id}`
          },
          extra,
        }
      });
      changefreeApi.checkPublish({
        projectName,
        group,
        cnName,
        version,
        publishDay,
        iterId,
        branchList,
      }).then(res => {
        const code = get(res, 'data.code');
        const checkStatus = get(res, 'data.body.check_status_enum');
        const orderUrl = get(res, 'data.body.orderUrl');
        const UNPASS_CODE = ['CHECK_HOLD', 'START_HOLD', 'STEPIN_HOLD'];
        // 拦截情况
        if (code === '0' && UNPASS_CODE.includes(checkStatus)) {
          Modal.warning({
            width: 360,
            title: 'Changefree 发布单检查拦截',
            content: '非发布窗口，请点击按钮申请发布~',
            okText: '前往申请',
            closable: true,
            onOk() {
              window.open(orderUrl);
            },
          });
          return reject(false);
        }
        return resolve(true);
      }).catch(err => {
        message.error(err?.message || 'changefree检查失败');
        reject(false);
      })
    });
  }

  /** 点击投放按钮：先检查体积，再打开弹窗 */
  function onBtnClick() {
    if (iterBranchDetail.status === IterStatus.MERGE) {
      changefreeCheck().then(() => {
        const isProjectAdmin = project?.adminWorkidList?.includes(currentUser.workid);
        checkSizeBeforeIterDeliver(iterBranchDetail, isProjectAdmin, () => setShowModal(true));
      });
      return;
    }
    setShowModal(true);
  }

  return <>
    <Button type="primary" onClick={onBtnClick}>投放</Button>
    {
      deliverClientWithTaskList.length > 0 ?
        <Modal
          open={showModal}
          title="投放列表"
          className="iter-deliver-modal"
          width={850}
          destroyOnClose
          maskClosable={false}
          onCancel={closeModal}
          footer={
            iterBranchDetail.status === IterStatus.DELIVERING ?
              <Popconfirm
                title="确定发布代码，既合进master？"
                onConfirm={publish}
                okText="确定"
                cancelText="取消"
                disabled={!canPublish}
              >
                <Tooltip title="必须所有投放端都上架或跳过投放，才能发布代码">
                  <Button
                    type="primary"
                    disabled={!canPublish}
                  >投放完成,发布代码</Button>
                </Tooltip>
              </Popconfirm> : null
          }
        >
          {
            isUnifiedDeliver
              ? <UnifiedPanel
                deliverClientWithTaskList={deliverClientWithTaskList}
                iterBranchDetail={iterBranchDetail}
                project={project}
                onChange={onChildrenChange} />
              : <SeparatePanel
                deliverClientWithTaskList={deliverClientWithTaskList}
                iterBranchDetail={iterBranchDetail}
                project={project}
                onChange={onChildrenChange}
              />
          }
        </Modal> : null
    }
  </>
}) as React.FC<Props>