import { useEffect, useState } from 'react';
import { Mo<PERSON>, Button, message, Popconfirm, Tooltip } from 'antd';
import { IIterBranch } from '@/interface/iter-branch';
import { IProject } from '@/interface/project';
import iterBranchApi from '@/api/iter-branch';
import { EMiniAppVersionStatus } from '@/const/iter-deliver';
import { IterStatus } from '@/const/iter-branch';
import { EProjectType } from '@/const/project';
import UnifiedPanel from './unified-panel';
import SeparatePanel from './separate-panel';
import { usePageState } from '../../page-state';
import { checkSizeBeforeIterDeliver } from '@/utils/perf';

import './index.less';

interface Props {
  iterBranchDetail: IIterBranch;
  project: IProject;
  // 发布完成回调
  onPublishEnd: () => void;
}

export default (({ iterBranchDetail, project, onPublishEnd }) => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isUnifiedDeliver, setIsUnifiedDeliver] = usePageState('isUnifiedDeliver');
  const [deliverClientWithTaskList, setDeliverClientWithTaskList] = usePageState('deliverClientWithTaskList');
  // 是否可发布，必须所有投放端都上架或跳过投放
  const canPublish = (() => {
    const clientListForThisDelivery = deliverClientWithTaskList.filter(item => item.deliverTaskId);
    const failedDeliverClientList = clientListForThisDelivery.filter(deliverClientWithTask => {
      // 若是跳过投放，通过
      if (deliverClientWithTask.skip) {
        return false
      } else {
        // 状态不是已上架，不通过
        return deliverClientWithTask.deliverTask?.miniAppVersionInfo?.status !== EMiniAppVersionStatus.RELEASE;
      }
    })

    // 有投放的端，且投放的端都已上架
    return clientListForThisDelivery.length > 0 && failedDeliverClientList.length === 0;
  })()

  // 首次或 iterId 变更的时候请求一次
  useEffect(() => {
    setIsUnifiedDeliver(project.type === EProjectType.BYTEDANCE);
    listDeliverClient();
  }, [iterBranchDetail.iterId]);

  // 显示弹窗时请求一次进行刷新
  useEffect(() => {
    if (showModal) listDeliverClient()
  }, [showModal])

  function listDeliverClient() {
    if (loading) return;
    setLoading(true);

    iterBranchApi.listDeliverClient({
      iterId: iterBranchDetail.iterId
    })
      .then((res) => {
        if (res?.success && res.data) {
          setDeliverClientWithTaskList(res.data)
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(err?.message || '获取列表失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  /** 发布代码，既合进master */
  async function publish() {
    return iterBranchApi.publish({
      iterId: iterBranchDetail.iterId,
      isGray: false,
    })
      .then((res) => {
        if (res?.success) {
          message.success('发布代码成功');
          onPublishEnd();
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(`发布代码失败${err?.message ? ': ' + err.message : ''}`);
      })
  }

  function closeModal() {
    setShowModal(false);
  }

  function onChildrenChange() {
    listDeliverClient();
    onPublishEnd();
  }

  /** 点击投放按钮：先检查体积，再打开弹窗 */
  function onBtnClick() {
    checkSizeBeforeIterDeliver(iterBranchDetail, () => setShowModal(true));
  }

  return <>
    <Button type="primary" onClick={onBtnClick}>投放</Button>

    {
      deliverClientWithTaskList.length > 0 ?
        <Modal
          open={showModal}
          title="投放列表"
          className="iter-deliver-modal"
          width={850}
          destroyOnClose
          maskClosable={false}
          onCancel={closeModal}
          footer={
            iterBranchDetail.status === IterStatus.DELIVERING ?
              <Popconfirm
                title="确定发布代码，既合进master？"
                onConfirm={publish}
                okText="确定"
                cancelText="取消"
                disabled={!canPublish}
              >
                <Tooltip title="必须所有投放端都上架或跳过投放，才能发布代码">
                  <Button
                    type="primary"
                    disabled={!canPublish}
                  >投放完成,发布代码</Button>
                </Tooltip>
              </Popconfirm> : null
          }
        >
          {
            isUnifiedDeliver
              ? <UnifiedPanel
                deliverClientWithTaskList={deliverClientWithTaskList}
                iterBranchDetail={iterBranchDetail}
                project={project}
                onChange={onChildrenChange} />
              : <SeparatePanel
                deliverClientWithTaskList={deliverClientWithTaskList}
                iterBranchDetail={iterBranchDetail}
                project={project}
                onChange={onChildrenChange}
              />
          }
        </Modal> : null
    }
  </>
}) as React.FC<Props>