import { useState } from 'react';
import { message, But<PERSON>, Popconfirm, Input, Modal, Space } from 'antd';
import { CloudUploadOutlined, UploadOutlined } from '@ant-design/icons';
import { IIterBranch } from '@/interface/iter-branch';
import { IProject } from '@/interface/project';
import { EProjectType } from '@/const/project';
import iterDeliverApi from '@/api/iter-deliver';
import { EClient } from '@/const/iter-deliver';

interface Props {
  iterBranchDetail: IIterBranch;
  project: IProject;
  lockDeliver: boolean;
  miniAppId: string;
  // 小程序源投放平台，适用于多端统一投放
  originalPlatform: string;
  // 投放端，适用于多端单独投放
  clientName?: EClient;
  onChange: () => void;
}

export default (({ miniAppId, clientName, originalPlatform, iterBranchDetail, project, lockDeliver, onChange }) => {
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  // 是否支持自动构建上传
  // const supportAutoUpload = project.type === EProjectType.ALIPAY;
  // TODO: 暂时关闭支付宝的自动上传
  const supportAutoUpload = false;
  // 手动上传版本号场景下，填写的版本号
  const [miniAppVersion, setMiniAppVersion] = useState<string>();

  function upload() {
    setLoading(true);

    iterDeliverApi.upload({
      iterId: iterBranchDetail.iterId,
      clientName,
      miniAppId,
      miniAppVersion
    })
      .then((res) => {
        if (res?.success && res.data) {
          message.success(supportAutoUpload ? '触发云上传成功' : '上传版本号成功');
          onChange()
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(`${supportAutoUpload ? '触发云上传失败' : '上传版本号失败'}${err?.message ? ': ' + err.message : ''}`);
      }).finally(() => {
        setLoading(false)
      });
  }

  function onConfirm() {
    // 如果没有 miniAppVersion，则提示
    if (!miniAppVersion) {
      message.error('请填写版本号')
    } else {
      setModalVisible(false)
      upload();
    }
  }

  if (supportAutoUpload) {
    return <Popconfirm
      title="确定触发？"
      onConfirm={upload}
      okText="确定"
      cancelText="取消"
    ><Button
      key="upload"
      shape="round"
      size="small"
      icon={<CloudUploadOutlined />}
      type="primary"
      disabled={lockDeliver}
      loading={loading}>触发云上传</Button></Popconfirm>
  } else {
    return <>
      <Button
        key="upload"
        shape="round"
        size="small"
        icon={<UploadOutlined />}
        onClick={() => setModalVisible(true)}
        type="primary"
        disabled={lockDeliver}
        loading={loading}>上传版本号</Button>

      <Modal
        open={modalVisible}
        title={<span>上传版本号<a href={originalPlatform} target="_blank" style={{ fontSize: '12px' }}> 跳转原投放平台</a></span>}
        width={1500}
        destroyOnClose
        maskClosable={false}
        onCancel={() => setModalVisible(false)}
        footer={<Space>
          <Input
            type="text"
            size='middle'
            style={{
              width: '250px',
              marginLeft: '-25px'
            }}
            value={miniAppVersion}
            onChange={e => setMiniAppVersion(e.target.value)}
            allowClear
            placeholder="请填写小程序版本号" />
          <Button
            type="primary"
            onClick={onConfirm}>上传</Button>
        </Space>}
      >
        <iframe className="modal-iframe" src={originalPlatform}></iframe>
      </Modal>
    </>
  }
}) as React.FC<Props>