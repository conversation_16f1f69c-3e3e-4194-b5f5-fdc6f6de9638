import { useEffect, useState } from 'react';
import { useAccess } from 'umi';
import { List, Spin, Space, message, Tag, Popconfirm, Tooltip, Avatar } from 'antd';
import { CloseCircleOutlined } from '@ant-design/icons';
import { IIterBranch, IDeliverClientWithTask } from '@/interface/iter-branch';
import { IProject } from '@/interface/project';
import { IterStatus } from '@/const/iter-branch';
import iterBranchApi from '@/api/iter-branch';
import TaskTable from './task-table';
import Upload from './upload';

interface ListProps {
  deliverClientWithTaskList: IDeliverClientWithTask[];
  iterBranchDetail: IIterBranch;
  project: IProject;
  // 状态变更回调
  onChange: () => void;
}

interface ItemProps {
  deliverClientWithTask: IDeliverClientWithTask;
  iterBranchDetail: IIterBranch;
  project: IProject;
  // 状态变更回调
  onChange: () => void;
}

const Item = (({ deliverClientWithTask, iterBranchDetail, project, onChange }) => {
  const [loading, setLoading] = useState(false);
  const [taskRefreshTag, setTaskRefreshTag] = useState(1);
  const access = useAccess();
  // 当迭代处于发布状态的时候，锁住投放
  const lockDeliver = iterBranchDetail.status === IterStatus.PUBLISHED;

  function refresh() {
    setTaskRefreshTag(Date.now());
  }

  function onChildrenChange() {
    onChange();
  }

  /** 解绑投放任务 */
  function unbindDeliverTask() {
    setLoading(true);

    iterBranchApi.unbindDeliverTask({
      iterId: iterBranchDetail.iterId,
      clientName: deliverClientWithTask.clientName,
      miniAppId: deliverClientWithTask.miniAppId
    })
      .then((res) => {
        if (res?.success) {
          setLoading(false);
          message.success('解绑版本号成功');
          onChange();
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(`解绑版本号失败${err?.message ? ': ' + err.message : ''}`);
        setLoading(false)
      })
  }

  /** 绑定投放任务 */
  function bindDeliverTask(deliverTaskId: number) {
    setLoading(true);

    iterBranchApi.bindDeliverTask({
      iterId: iterBranchDetail.iterId,
      clientName: deliverClientWithTask.clientName,
      miniAppId: deliverClientWithTask.miniAppId,
      deliverTaskId,
    })
      .then((res) => {
        if (res?.success) {
          setLoading(false);
          message.success('绑定版本号成功');
          onChange();
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(`绑定版本号失败${err?.message ? ': ' + err.message : ''}`);
        setLoading(false)
      })
  }

  function renderActions() {
    const actions = [];

    actions.push(<Upload
      key="upload"
      lockDeliver={lockDeliver}
      iterBranchDetail={iterBranchDetail}
      project={project}
      originalPlatform={deliverClientWithTask.originalPlatform}
      clientName={deliverClientWithTask.clientName}
      miniAppId={deliverClientWithTask.miniAppId}
      onChange={refresh} />)

    return actions;
  }

  function renderClientList() {
    return <Space>
      <Avatar size="large" src={deliverClientWithTask.clientExtInfo?.icon} />

      <Space direction="vertical" size={0}>
        <span>{deliverClientWithTask.clientExtInfo?.cnName}</span>
        <span className="light-text">{deliverClientWithTask.miniAppId}</span>
      </Space>
    </Space>
  }

  return <Spin spinning={loading} wrapperClassName="deliver-panel-item">
    <div className="deliver-panel-header">
      <Space className="client-list" size={20}>
        {renderClientList()}

        {deliverClientWithTask.deliverTask?.miniAppVersion &&
          <div>
            <Tag color="orange">v{deliverClientWithTask.deliverTask.miniAppVersion}</Tag>
            {
              access.isAdmin && !lockDeliver
                ? <Popconfirm
                  title="确定解绑版本号？"
                  onConfirm={unbindDeliverTask}
                  okText="确定"
                  cancelText="取消"
                ><Tooltip title="解绑版本号"><CloseCircleOutlined className="danger-text" /></Tooltip></Popconfirm>
                : null
            }
          </div>
        }

        {deliverClientWithTask.deliverTask?.miniAppVersionInfo
          ? deliverClientWithTask.deliverTask.miniAppVersionInfo.statusText
          : (deliverClientWithTask.deliverTask?.uploadStatusText || '')}
      </Space>
      <div className="actions">
        <Space>{access.isAdmin ? renderActions() : null}</Space>
      </div>
    </div>

    <TaskTable
      iterBranchDetail={iterBranchDetail}
      bindDeliverTaskId={deliverClientWithTask.deliverTaskId}
      project={project}
      clientName={deliverClientWithTask.clientName}
      refreshTag={taskRefreshTag}
      lockDeliver={lockDeliver}
      onChange={onChildrenChange}
      onBindDeliverTask={bindDeliverTask}
    />
  </Spin>
}) as React.FC<ItemProps>

export default (({ deliverClientWithTaskList, iterBranchDetail, project, onChange }) => {

  return <List
    size="large"
    dataSource={deliverClientWithTaskList}
    renderItem={item => <Item
      project={project}
      deliverClientWithTask={item}
      iterBranchDetail={iterBranchDetail}
      onChange={onChange}
    />}
  />
}) as React.FC<ListProps>