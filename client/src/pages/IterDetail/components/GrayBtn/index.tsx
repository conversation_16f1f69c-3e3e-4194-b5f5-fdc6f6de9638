import { useState } from 'react';
import { Modal, message, Spin, Steps, Popconfirm } from 'antd';
import { IIterDeliverTask, IMiniAppVersionInfo, IAlipayVersionInfo } from '@/interface/iter-deliver';
import iterDeliverApi from '@/api/iter-deliver';

import './index.less';

interface Props {
  // 投放任务
  deliverTask: IIterDeliverTask;
  // 使用内嵌iframe进行投放
  useIframe: boolean;
  // 是否是结束灰度按钮
  isFinishGray?: boolean;
  // 状态变更回调
  onChange: () => void;
}

export default (({ deliverTask, useIframe, isFinishGray, onChange, children }) => {
  const miniAppVersionInfo = deliverTask.miniAppVersionInfo as IAlipayVersionInfo | IMiniAppVersionInfo;
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const stepIndex = miniAppVersionInfo.graySteps?.findIndex(item => item.strategy === miniAppVersionInfo.curGrayStrategy);

  // 灰度
  function gray(grayStrategy: string) {
    setLoading(true);

    handleRes(iterDeliverApi.gray({
      id: deliverTask.id,
      grayStrategy
    }), '灰度')
  }

  // 结束灰度
  function finishGray() {
    return handleRes(iterDeliverApi.finishGray({
      id: deliverTask.id,
    }), '结束灰度')
  }

  function handleRes(requestPromise: ReturnType<typeof iterDeliverApi.gray>, actionTitle: string) {
    return requestPromise.then((res) => {
      if (res?.success) {
        message.success(`${actionTitle}成功`);
        onChange();
        closeModal();
      } else {
        throw Error(res?.errorMsg);
      }
    })
      .catch((err: any) => {
        message.error(`${actionTitle}失败${err?.message ? ': ' + err.message : ''}`);
      }).finally(() => {
        setLoading(false)
      });
  }

  function closeModal() {
    setShowModal(false);
  }

  function onStepChange(targetStepIndex: number) {
    const step = miniAppVersionInfo.graySteps[targetStepIndex];

    Modal.confirm({
      title: `确认将灰度范围修改为 ${step.title} ？`,
      onOk: () => gray(step.strategy)
    })
  }

  if (isFinishGray) {
    return <Popconfirm
      title="确定结束灰度？"
      onConfirm={finishGray}
      okText="确定"
      cancelText="取消"
    >{children}</Popconfirm>
  } else {
    const GraySteps = () => {
      return <Spin wrapperClassName="steps" spinning={loading}>
        <Steps current={stepIndex} size="small" onChange={onStepChange}>
          {miniAppVersionInfo.graySteps.map((step, index) => {
            return <Steps.Step key={index} title={step.title} disabled={index < stepIndex} />
          })}
        </Steps>
      </Spin>
    }

    return <>
      <span onClick={() => setShowModal(true)}>{children}</span>

      <Modal
        open={showModal}
        title="灰度"
        width={1500}
        destroyOnClose
        maskClosable={false}
        onCancel={closeModal}
        footer={useIframe ? <GraySteps /> : null}
      >
        {
          useIframe
            ? <iframe className="modal-iframe" src={miniAppVersionInfo.platformUrl}></iframe>
            : <GraySteps />
        }
      </Modal>
    </>
  }
}) as React.FC<Props>