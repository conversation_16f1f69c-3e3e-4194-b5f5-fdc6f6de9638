import { useState } from 'react';
import { Mo<PERSON>, Button, message, Popconfirm } from 'antd';
import { IIterDeliverTask, IMiniAppVersionInfo, IAlipayVersionInfo } from '@/interface/iter-deliver';
import iterDeliverApi from '@/api/iter-deliver';

import './index.less';

interface Props {
  // 投放任务
  deliverTask: IIterDeliverTask;
  // 使用内嵌iframe进行投放
  useIframe: boolean;
  // 状态变更回调
  onChange: () => void;
}

export default (({ deliverTask, useIframe, onChange, children }) => {
  const miniAppVersionInfo = deliverTask.miniAppVersionInfo as IAlipayVersionInfo | IMiniAppVersionInfo;
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const actionTitle = '下架';

  // 下架
  function offline() {
    setLoading(true);

    return iterDeliverApi.offline({
      id: deliverTask.id,
    })
      .then((res) => {
        if (res?.success) {
          message.success(`${actionTitle}成功`);
          onChange();
          closeModal();
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(`${actionTitle}失败${err?.message ? ': ' + err.message : ''}`);
      }).finally(() => {
        setLoading(false)
      });
  }

  function closeModal() {
    setShowModal(false);
  }

  if (!useIframe) {
    return <Popconfirm
      title={`确定${actionTitle}？`}
      onConfirm={offline}
      okText="确定"
      cancelText="取消"
    >{children}</Popconfirm>
  } else {
    return <>
      <span onClick={() => setShowModal(true)}>{children}</span>

      <Modal
        open={showModal}
        title={actionTitle}
        width={1500}
        destroyOnClose
        maskClosable={false}
        onCancel={closeModal}
        footer={<Button
          type="primary"
          onClick={offline}
          loading={loading}>确认已{actionTitle}</Button>}
      >
        <iframe className="modal-iframe" src={miniAppVersionInfo.platformUrl}></iframe>
      </Modal>
    </>
  }
}) as React.FC<Props>