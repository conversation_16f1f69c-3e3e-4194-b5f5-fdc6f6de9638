import { useState } from 'react';
import { Mo<PERSON>, <PERSON>confirm, Steps, Button, Progress, message, Space, Spin } from 'antd';
import { IProject } from '@/interface/project';
import { IIterBranch } from '@/interface/iter-branch';
import iterBranchApi from '@/api/iter-branch';

import './index.less';

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  project: IProject;
  iterBranchDetail: IIterBranch;
  onPublishEnd?: Function;
}

export default ({ iterBranchDetail, project, onPublishEnd }: Props) => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [stepIndex, setStepIndex] = useState(project.publishInfo.steps.findIndex(item => item.title === iterBranchDetail.grayStatus));

  /** 修改灰度状态 */
  function changeGrayStatus(targetStepIndex: number, grayStatus: string, isLastStep: boolean) {
    setLoading(true);

    iterBranchApi.publish({
      iterId: iterBranchDetail.iterId,
      isGray: !isLastStep,
      grayStatus
    })
      .then((res) => {
        if (res?.success) {
          setStepIndex(targetStepIndex);
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        Modal.error({
          title: err?.message || '修改灰度状态失败',
          content: targetStepIndex === 0 ? <div>如果是代码在合并进 master 时冲突，请使用 <a href={iterBranchDetail.rcGitBranch?.url} target="_blank">{iterBranchDetail.rcGitBranch?.name}</a> 分支手动合并 master，解冲突并提交后再进行发布</div> : '',
        });
      }).finally(() => {
        setLoading(false)
      });
  }

  function closeModal() {
    setShowModal(false);
    onPublishEnd && onPublishEnd();
  }

  function onStepChange(targetStepIndex: number) {
    const step = project.publishInfo.steps[targetStepIndex];
    const grayStatus = step.title;
    const isLastStep = targetStepIndex === project.publishInfo.steps.length - 1;

    Modal.confirm({
      title: isLastStep ? '确认上架 ？' : `确认将灰度比列调节为 ${step.title} ？`,
      content: isLastStep ? `同时会将集成分支 ${iterBranchDetail.rcGitBranch?.name} 合并到 master ！` : '',
      onOk() {
        changeGrayStatus(targetStepIndex, grayStatus, isLastStep);
      }
    })
  }

  function renderFooter() {
    return <Spin wrapperClassName="steps" spinning={loading}>
      <Steps current={stepIndex} size="small" onChange={onStepChange}>
        {project.publishInfo.steps.map((step, index) => <Steps.Step key={index} title={step.title} disabled={index < stepIndex} />)}
      </Steps>
    </Spin>
  }

  return <>
    <Button type="primary" onClick={() => setShowModal(true)}>{stepIndex > -1 ? '修改灰度比例' : '去发布'}</Button>

    <Modal
      open={showModal}
      title="发布小程序"
      className="publish-modal"
      width={1500}
      destroyOnClose
      maskClosable={false}
      onCancel={closeModal}
      footer={renderFooter()}
    >
      <iframe className="iframe" src={project.publishInfo.platform}></iframe>
    </Modal>
  </>
}