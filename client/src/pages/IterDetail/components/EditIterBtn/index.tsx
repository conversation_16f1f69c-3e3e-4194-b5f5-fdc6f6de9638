import { useState } from 'react';
import { Modal, message, Spin } from 'antd';
import { useForm } from 'form-render';
import { IIterBranch } from '@/interface/iter-branch';
import FormRender from '@/components/FR';
import iterBranchApi from '@/api/iter-branch';
import schema from './schema';

import './index.less';

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  iterBranchDetail: IIterBranch;
  onEditEnd?: () => void;
}

export default ({ iterBranchDetail, children, onEditEnd }: Props) => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const form = useForm();

  const onFormMount = () => {
    // 当有值时，回填表单
    if (iterBranchDetail) {
      form.setValues({
        iterId: iterBranchDetail.iterId,
        creator: iterBranchDetail.creator,
        publishDay: iterBranchDetail.publishDay,
        version: iterBranchDetail.version,
        description: iterBranchDetail.description,
      })
    }

    form.setSchemaByPath('version', { disabled: true }); // 禁止修改版本号
  }

  const onFormSubmit = () => {
    form.submit();
  };

  const onFormCancel = () => {
    form.resetFields();
    setShowModal(false);
  };

  const onFormFinish = (formData: any, errors: any) => {
    if (errors.length > 0) return;

    setLoading(true);

    // 删除不上报字段
    delete formData.creator;

    iterBranchApi.update(formData).then((res) => {
      if (res?.success) {
        message.info('更新成功');
        setShowModal(false);
        setTimeout(() => form.resetFields(), 500); // 迟一点清空，省得闪一下
        onEditEnd && onEditEnd();
      } else {
        message.error(res?.errorMsg || `更新失败`);
      }
    })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => setLoading(false));
  }

  return <>
    <div onClick={() => setShowModal(true)}>
      {children}
    </div>

    <Modal
      title="更新迭代分支"
      open={showModal}
      onOk={onFormSubmit}
      onCancel={onFormCancel}
      maskClosable={false}
      okButtonProps={{ disabled: form.errorFields?.length > 0 }}
      width={700}
      destroyOnClose
    >
      <Spin spinning={loading} wrapperClassName="form-wrapper">
        <FormRender
          form={form}
          schema={schema}
          removeHiddenData={false}
          onMount={onFormMount}
          onFinish={onFormFinish}
          displayType="row" />
      </Spin>
    </Modal>
  </>
}
