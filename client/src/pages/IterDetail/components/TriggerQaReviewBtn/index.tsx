import { useState } from 'react';
import { Form, Button, Checkbox, notification, Modal } from 'antd';
import iterQaReviewApi from '@/api/iter-qa-review';

import './index.less';
import { BIZ_LINE, BIZ_TITLE } from '@/const/biz';

interface Props {
  iterId: number;
  onIterChange: () => void;
}

const Comp: React.FC<Props> = ({ iterId, children, onIterChange }) => {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm<{ bizNames: BIZ_LINE[] }>();

  /**
   * 触发全量回归
   */
  function triggerQaReview() {
    const { bizNames } = form.getFieldsValue();
    
    setVisible(false);

    iterQaReviewApi.create({
      iterId,
      bizNames
    })
      .then((res) => {
        if (res?.success) {
          onIterChange();
          notification.success({
            message: '触发全量回归成功',
            duration: 0,
          });
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        notification.error({
          message: '触发全量回归失败',
          description: err.message,
          duration: 0,
        });
      })
  }

  const defaultBizCheck = Object.values(BIZ_LINE);
  const bizOptions = Object.values(BIZ_LINE).map(bizName => {
    return {
      label: BIZ_TITLE[bizName],
      value: bizName,
      checked: true
    }
  });

  return <>
    <span onClick={() => setVisible(true)}>{children}</span>

    <Modal
      open={visible}
      onCancel={() => setVisible(false)}
      onOk={() => triggerQaReview()}
      okText="确定触发"
      title="触发全量回归"
    >
      <Form className="review-form" form={form}>
        <Form.Item name="bizNames" label="需回归的行业" initialValue={defaultBizCheck}>
          <Checkbox.Group options={bizOptions} />
        </Form.Item>
      </Form>

    </Modal>
  </>
}

export default Comp;