import React from 'react';
import { Popover, Typography } from 'antd';
import <PERSON><PERSON>Differ from '@/components/SizeDiffer';

const { Link } = Typography;

export default ({ data, label = '体积分析' }: { data?: Record<string, number>; label?: string }) => {
  if (!data) {
    return null;
  }

  return (
    <Popover trigger="click" content={<SizeDiffer data={data} />}>
      <Link>{label}</Link>
    </Popover>
  );
}