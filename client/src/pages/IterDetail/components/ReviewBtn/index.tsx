import { useState } from 'react';
import { <PERSON><PERSON>, Button, message } from 'antd';
import { IProject } from '@/interface/project';
import { IIterBranch } from '@/interface/iter-branch';
import iterBranchApi from '@/api/iter-branch';

import './index.less';

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  project: IProject;
  iterBranchDetail: IIterBranch;
  onReviewEnd?: Function;
}

export default ({ iterBranchDetail, project, onReviewEnd }: Props) => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);

  /** 修改灰度状态 */
  function review() {
    setLoading(true);

    iterBranchApi.review({
      iterId: iterBranchDetail.iterId
    })
      .then((res) => {
        if (res?.success) {
          message.success('修改提审状态成功');
          closeModal();
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(err?.message || '修改提审状态失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  function closeModal() {
    setShowModal(false);
    onReviewEnd && onReviewEnd();
  }


  function renderFooter() {
    return <Button type="primary" onClick={review} loading={loading}>确认已提审</Button>
  }

  return <>
    <Button type="primary" onClick={() => setShowModal(true)}>去提审</Button>

    <Modal
      open={showModal}
      title="提审小程序"
      className="review-modal"
      width={1500}
      destroyOnClose
      maskClosable={false}
      onCancel={closeModal}
      footer={renderFooter()}
    >
      <iframe className="iframe" src={project.publishInfo.platform}></iframe>
    </Modal>
  </>
}