import { useState } from 'react';
import { unstable_batchedUpdates } from "react-dom";
import { <PERSON><PERSON>, <PERSON>con<PERSON>rm, Button, Progress, message, Space, Spin, Alert, Result } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { IIterBranch } from '@/interface/iter-branch';
import iterBranchApi, { IIterMergeResData } from '@/api/iter-branch';
import changefreeApi from '@/api/changefree';

import './index.less';

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  iterBranchDetail: IIterBranch;
  onMergeEnd?: Function;
}

export default ({ children, iterBranchDetail, onMergeEnd }: Props) => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [mergeList, setMergeList] = useState<IIterMergeResData[]>([]);
  const errList = mergeList?.filter(item => !item.mergeRes?.success);

  /** 集成分支 */
  function mergeBranch(ignoreEmpty?: boolean) {
    setLoading(true);
    
    iterBranchApi.merge({
      iterId: iterBranchDetail.iterId, // 这个id是研发平台的迭代id
      ignoreEmpty,
    })
      .then((res) => {
        const devBranchList = res?.data?.data || [];

        unstable_batchedUpdates(() => {
          if (res?.success) {
            if (!ignoreEmpty && devBranchList.length === 0) { // 就绪的开发分支如果为空，切非“忽略空”场景，则弹窗提示
              closeModal();
              Modal.confirm({
                title: '没有准备就绪的开发分支',
                icon: <ExclamationCircleOutlined />,
                content: '是否继续集成？',
                onOk: close => {
                  close();
                  setShowModal(true);
                  mergeBranch(true)
                },
                okText: '继续集成',
                cancelText: '取消',
              });
            } else {
              setSuccess(true);
              setMergeList(devBranchList);
              if (!res?.data?.defBuildRes) {
                message.error('def发布失败，请到def自动操作');
              }
            }
          } else {
            setError(res?.errorMsg || '集成失败');
            setMergeList(devBranchList);
          }
        })
      })
      .catch((err: any) => {
        setError(err?.message || '集成失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  function cancelMerge() {
    setLoading(true);

    iterBranchApi.cancelMerge(iterBranchDetail.iterId)
      .then((res) => {
        if (res?.success) {
          closeModal();
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '取消集成失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  function closeModal() {
    setShowModal(false);
    setError('');
    setSuccess(false);
    setMergeList([]);

    success && onMergeEnd && onMergeEnd();
  }

  function onConfirm() {
    // setShowModal(true);
    // mergeBranch();
    changefreeApi.checkPublish({}).then(res => {
      console.log(44444, res);
    })
  }

  let modalTitle = '集成中';
  if (success) modalTitle = '集成完成';
  if (error) modalTitle = '集成失败';

  return <>
    <Popconfirm placement="bottom" title="确认冻结集成？" okText="是" cancelText="否" onConfirm={onConfirm}>
      {children}
    </Popconfirm>

    <Modal
      open={showModal}
      title={modalTitle}
      className="merge-modal"
      width={500}
      destroyOnClose
      maskClosable={false}
      onCancel={closeModal}
      footer={
        (success || error) ? <Button onClick={closeModal}>确定</Button>
          : <Button onClick={cancelMerge} danger>取消集成</Button>
      }
    >
      <Spin spinning={loading} size="large">
        <Space style={{ width: '100%' }} direction="vertical">
          {
            success && mergeList.length !== 0 &&
            <Space className="progress-wrapper" direction="vertical">
              {
                mergeList.map(devBranch => <div className="branch-progress" key={devBranch.devId}>
                  <div className="branch-name">{devBranch.branchName}</div>
                  <Progress percent={devBranch.mergeRes?.success ? 100 : 0} status={devBranch.mergeRes?.success ? 'success' : 'exception'} />
                </div>)
              }
            </Space>
          }

          {
            success && mergeList.length === 0 &&
            <Result
              status="success"
              className="success-result"
            />
          }

          {
            error &&
            <Alert
              message="集成错误"
              description={error}
              type="error"
              showIcon
            />
          }

          {
            errList.length > 0 &&
            <Alert
              message="如果是代码冲突可以尝试以下两种解法"
              description={<>
                <div>1. 修改冲突分支，再尝试集成
                  {mergeList.filter(item => !item.mergeRes?.success).map(item => <a key={item.devId} href={item.gitBranch.url} target="_blank"> {item.gitBranch.name} </a>)}
                </div>
                <div>2. 使用 <a href={iterBranchDetail.gitBranch.url} target="_blank">{iterBranchDetail.gitBranch.name}</a> 分支手动合并冲突分支，解冲突并提交后再进行集成</div>
              </>}
              type="info"
            />
          }
        </Space>
      </Spin>
    </Modal>
  </>
}
