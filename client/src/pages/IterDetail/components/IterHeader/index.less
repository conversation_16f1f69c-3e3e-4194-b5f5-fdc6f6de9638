.iter-header {
  padding: 0 0 20px;

  .project {
    display: flex;
    flex-direction: row;
  }

  .project-icon {
    width: 32px;
    height: 32px;
    margin-right: 16px;
    margin-top: 4px;
  }

  .project-cn {
    font-size: 14px;
    font-weight: normal;
    line-height: 1.8;
    color: rgba(0, 0, 0, 0.45);
  }

  .actions {
    display: flex;
  }

  .content {
    margin-left: 48px;
    display: flex;
  }

  .main {
    flex: 5;
  }

  .extra {
    flex: 3;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
  }

  .ant-breadcrumb {
    margin-bottom: 20px;
    ol {
      padding: 0;
    }
  }

  .deliver-status-title {
    color: rgba(0, 0, 0, 0.45);
  }

  .deliver-status-content {
    text-align: right;
    color: rgba(0, 0, 0, 0.85);

    .ant-tag {
      margin-right: 0;
    }
  }
}