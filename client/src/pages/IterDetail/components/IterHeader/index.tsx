import { useState, useRef, useEffect, useMemo } from 'react';
import { <PERSON>ert, Button, Space, PageHeader, Descriptions, Statistic, Popconfirm, Menu, Dropdown, message, Tooltip, Modal, MenuProps, Tag, notification } from 'antd';
import { MoreOutlined, StopOutlined, DownloadOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useAccess, Link, useModel } from 'umi';
import iterBranchApi from '@/api/iter-branch';
import gitApi from '@/api/git';
import helperApi from '@/api/helper';
import defApi from '@/api/def';
import pkgApi from '@/api/package';
import iterQaReviewApi from '@/api/iter-qa-review';
import devBranchApi, { IReportAnalyzedReq } from '@/api/dev-branch';
import freeDevBranchApi from '@/api/free-dev-branch';
import { EChecked, IIterBranch } from '@/interface/iter-branch';
import { IProject } from '@/interface/project';
import { IIterQaReview } from '@/interface/iter-qa-review';
import { IUser } from '@/interface/user';
import { IDevBranch } from '@/interface/dev-branch';
import { IFreeDevBranch } from '@/interface/free-dev-branch';
import { EBranchType, IterStatus } from '@/const/iter-branch';
import { EMiniAppVersionStatus } from '@/const/iter-deliver';
import { STORAGE_MERGE_TIP } from '@/const/storage';
import { isSupportBuild } from '@/const/build';
import { EDefBuildTaskType, EPubTypeEnv } from '@/const/def';
import { EDeliverBranchType } from '@/const/component-deliver';
import { getDefIterationUrl, getAutoUITestUrl, getCRUserList } from '@/utils/def';
import BuildBtn from '@/components/BuildBtn';
import EditDevBtn, { Action } from '@/components/EditDevBtn';
import DefBuildBtn from '@/components/DefBuildBtn';
import EditIterBtn from '../EditIterBtn';
import NoticeTestBtn from '../NoticeTestBtn';
import TriggerAutomatedTestBtn from '../TriggerAutomatedTestBtn';
import TriggerComponentAutoTestBtn from '../TriggerComponentAutoTestBtn';
import buyPhoenixAutoTestCfg from '../TriggerComponentAutoTestBtn/config';
import MergeBtn from '../MergeBtn';
import PublishBtn from '../PublishBtn';
import ReviewBtn from '../ReviewBtn';
import DeliverBtn from '../DeliverBtn';
import IterSize from '../IterSize';
import CRDetail from '../../../../components/CRDetail';
import TriggerQaReviewBtn from '../TriggerQaReviewBtn';
import ComponentDeliverBtn from '@/components/ComponentDeliverBtn';
import { usePageState } from '../../page-state';

import './index.less';
import { pick } from 'lodash';
import { EProjectType } from '@/const/project';


enum ACTION_BTN {
  /** 打集成码 */
  RC_BUILD = 'rcBuild',
  /** 打基线码 */
  STABLE_BUILD = 'stableBuild',
  /** 同步主干 */
  SYNC_MASTER = 'syncMaster',
  /** 冻结集成 */
  MERGE = 'merge',
  /** 取消集成 */
  CANCEL_MERGE = 'cancelMerge',
  /** 创建开发分支 */
  CREATE_DEV_BRANCH = 'createDevBranch',
  /** cr */
  CR = 'cr',
  /** def发布预发 */
  DEF_PREPUB = 'defPrepub',
  /** def发布线上 */
  DEF_PUBLISH = 'defPublish',
  /** 组件从已发布变成投放 */
  INIT_DELIVER = 'initDeliver',
  /** 投放 */
  DELIVER = 'deliver',
  /** 模块投放 */
  MODULE_DELIVER = 'moduleDeliver',
  /** 提审 */
  REVIEW = 'review',
  /** 发布 */
  PUBLISH = 'publish',
  /** 回滚 */
  ROLLBACK = 'rollback',
  /** 确认回归 */
  QA_CHECK = 'qaCheck'
}

const routes = [
  {
    path: '/iter/calendar',
    breadcrumbName: '迭代日历',
  },
  {
    path: '',
    breadcrumbName: '迭代详情',
  },
];

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  iterBranchDetail: IIterBranch;
  iterQaReviewList: IIterQaReview[];
  devBranchList: (IDevBranch | IFreeDevBranch)[];
  project: IProject;
  setLoading: (loading: boolean) => void;
  onIterChange: () => void;
}

export default ({ iterBranchDetail, iterQaReviewList, devBranchList, project, onIterChange, setLoading }: Props) => {
  const [syncLoading, setSyncLoading] = useState(false);
  const [deliverClientWithTaskList] = usePageState('deliverClientWithTaskList');
  const [CRLoading, setCRLoading] = useState(false);
  const [isChecked, setIsChecked] = useState(iterBranchDetail.checked === EChecked.YES);
  const [checkLoading, setCheckLoading] = useState(false);
  const [pkgSizeInfo, setPkgSizeInfo] = useState<Record<string, any> | undefined>();
  const CRDetailRef: any = useRef(null);
  const access = useAccess();
  // 项目类型是模块类型
  const isComponentProject = iterBranchDetail.branchType === EBranchType.COMPONENT;
  const DEF_URL = getDefIterationUrl(iterBranchDetail.defIterId);
  const AUTO_UI_TEST_URL = getAutoUITestUrl(buyPhoenixAutoTestCfg.UITest.testId);
  // TODO 这里UI自动化测试开启需要改成读配置
  const COMP_HAS_AUTO_UI_TEST = project.name === 'buy-phoenix';
  const currentUser = useModel('user');
  const qaList = iterBranchDetail.qaList || [];
  const isAlipayProj = project.type === EProjectType.ALIPAY;

  /**
   * 删除分支，会删库！
   */
  function deleteBranch() {
    setLoading(true);

    // todo 删除分支的时候，需要删除对应的def 迭代

    iterBranchApi.delete(iterBranchDetail.iterId)
      .then((res) => {
        if (res?.success) {
          onIterChange();
          message.success('删除成功');
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '删除失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  /**
   * 废弃分支，只会状态修改为废弃状态
   */
  function discardBranch() {
    setLoading(true);

    iterBranchApi.discard(iterBranchDetail.iterId)
      .then((res) => {
        if (res?.success) {
          onIterChange();
          message.success('废弃成功');
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '废弃失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  /**
   * 取消集成
   */
  function cancelMerge() {
    setLoading(true);

    iterBranchApi.cancelMerge(iterBranchDetail.iterId)
      .then((res) => {
        if (res?.success) {
          onIterChange();
          message.success('取消集成成功');
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '取消集成失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  /**
   * 展示集成提示，只有首次提示
   */
  function showMergeTip() {
    if (localStorage.getItem(STORAGE_MERGE_TIP)) return;
    localStorage.setItem(STORAGE_MERGE_TIP, '1');

    Modal.warning({
      title: '只有“准备就绪”的开发分支才会被集成！',
      width: 1000,
      content: <>
        <img style={{ marginTop: '12px' }} width="100%" src="https://gw.alicdn.com/imgextra/i3/O1CN01dQ5SUK1i5qMQQWSI8_!!6000000004362-2-tps-2602-346.png" />
      </>,
    });
  }

  /**
   * 同步master
   */
  function syncBranch() {
    setSyncLoading(true);

    gitApi.syncBranch({
      branchName: iterBranchDetail.gitBranch.name,
      branchUrl: iterBranchDetail.gitBranch.url,
      targetBranchName: 'master'
    })
      .then((res) => {
        if (res?.success) {
          message.success('同步成功');
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '同步失败');
      }).finally(() => {
        setSyncLoading(false)
      });
  }

  /**
   * 通知集成
   */
  function noticeMerge() {
    helperApi.noticeMerge({
      iterId: iterBranchDetail.iterId
    })
      .then((res) => {
        if (res?.success) {
          message.success('通知集成成功');
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '通知集成失败');
      })
  }

  /**
   * 生成shrinkwrap
   */
  function makeAndSaveShrinkwrap() {
    helperApi.makeAndSaveShrinkwrap({
      iterId: iterBranchDetail.iterId
    })
      .then((res) => {
        if (res?.success) {
          message.success('正在生成shrinkwrap');
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '生成shrinkwrap失败');
      })
  }

  /**
   * 生成report_analyzed.json
   */
  function makeAndSaveReportAnalyzed() {
    helperApi.makeAndSaveReportAnalyzed({
      iterId: iterBranchDetail.iterId
    })
      .then((res) => {
        if (res?.success) {
          message.success('正在生成report_analyzed.json');
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '生成report_analyzed.json失败');
      })
  }

  /** 生成集成分支的体积分析文件 */
  function makeBranchReportAnalyzed() {
    const devReqBranchList: IReportAnalyzedReq[] = [];
    const freeDevReqBranchList: IReportAnalyzedReq[] = [];
    devBranchList.forEach(br => {
      // 就绪状态，并且存在dist结果，并且不含体积分析文件
      const needMake = br.status === 1 && br.dist && !br.reportAnalyzed;
      if (!needMake) {
        return;
      }
      const [prefix] = br.branchName.split('/');
      const branchInfo = pick(br, ['devId', 'branchName', 'dist']);
      if (prefix === 'dev') {
        devReqBranchList.push(branchInfo)
      } else {
        freeDevReqBranchList.push(branchInfo);
      }
    });

    if (devReqBranchList.length) {
      devBranchApi.makeReportAnalyzed(devReqBranchList);
    }

    if (freeDevReqBranchList.length) {
      freeDevBranchApi.makeReportAnalyzed(freeDevReqBranchList);
    }
    if (devReqBranchList.length || freeDevReqBranchList.length) {
      message.success('正在生成集成分支的report_analyzed.json');
    }
  }

  /** 依赖变更通知 */
  function noticePublishReport() {
    const { iterId, shrinkwrap, projectName } = iterBranchDetail;
    const { dingtalkRobots } = project;

    if (!shrinkwrap || !dingtalkRobots) return;

    helperApi.noticePublishReport({
      iterId,
      projectName,
    })
      .then((res) => {
        if (res?.success && res?.data) {
          message.success('发送发版报告成功');
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '发送发版报告失败');
      })
  }

  /**
   * 回滚发布
   */
  function rollback() {
    iterBranchApi.rollback({ iterId: iterBranchDetail.iterId })
      .then((res) => {
        if (res?.success) {
          onIterChange();
          message.success('回滚成功');
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '回滚失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  /** 发布前检查一下构建卡口是否通过 */
  async function onBeforeBuild() {
    const res = await defApi.publishCheck({
      defIterId: iterBranchDetail.defIterId
    });
    if (res?.success) {
      const failItems = res.data?.filter((item: any) => ['FAILED', 'NOT_TRIGGER'].indexOf(item.status) !== -1);
      if (failItems?.length) {
        const failTips = failItems.reduce((tips: string, item: any) => {
          tips += `【${item.displayName}】`;
          return tips;
        }, '')
        message.error({ content: <a href={DEF_URL} target="_blank">{`${failTips}：检查不通过，请去def查看详情`}</a>, duration: 10 });
        return false;
      }
      return true;
    }
    message.error({ content: <a href={DEF_URL} target="_blank">构建检查查询失败，请去def查看详情</a>, duration: 10 });
    return false;
  }
  /**
   * 取消全量回归
   */
  function cancelQaReview() {
    iterQaReviewApi.cancel({
      iterId: iterBranchDetail.iterId
    })
      .then((res) => {
        if (res?.success) {
          onIterChange();
          notification.success({
            message: '取消全量回归成功',
            duration: 0,
          });
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        notification.error({
          message: '取消全量回归失败',
          description: err.message,
          duration: 0,
        });
      })
  }

  function addDist() {
    iterBranchApi.addDist({ iterId: iterBranchDetail.iterId })
      .then(() => {
        message.success('更新成功');
      })
      .catch((err) => {
        message.error(err.message || '更新失败');
      })
  }

  // 菜单
  const menu = (() => {
    const list: MenuProps['items'] = [];

    if (access.isAdmin && iterBranchDetail.status === IterStatus.PLAN) {
      list.push({
        key: 'edit',
        label:
          <EditIterBtn iterBranchDetail={iterBranchDetail} onEditEnd={() => onIterChange()}>
            编辑信息
          </EditIterBtn>
      })
    }

    if (access.isAdmin && iterBranchDetail.status === IterStatus.PLAN) {
      list.push({
        key: 'noticeMerge',
        label:
          <Popconfirm title="确认发送通知？" okText="是" cancelText="否" onConfirm={noticeMerge}>
            <div style={{ width: '100%' }}>通知集成</div>
          </Popconfirm>
      })
    }

    if (access.isAdmin && iterBranchDetail.status === IterStatus.MERGE && !isComponentProject) {
      // TODO 不需要这一步骤
      list.push({
        key: 'noticeTest',
        label:
          <NoticeTestBtn projectType={project?.type} iterBranch={iterBranchDetail}><div style={{ width: '100%' }} >通知回归</div></NoticeTestBtn>
      })
    }

    if (access.isAdmin) {
      if (!isComponentProject) {
        list.push({
          key: 'triggerAutomatedTest',
          label:
            <TriggerAutomatedTestBtn iterId={iterBranchDetail.iterId}>触发自动化测试</TriggerAutomatedTestBtn>
        })
      } else if (COMP_HAS_AUTO_UI_TEST){
        list.push({
          key: 'triggerComponentAutoTest',
          label: (
            <TriggerComponentAutoTestBtn
              taskEnv={EPubTypeEnv.Beta}
              pkgPublishVersion={iterBranchDetail.pkgPublishVersion!}
            >触发自动化UI测试</TriggerComponentAutoTestBtn>
          ),
        });
      }
    }

    if (access.isAdmin && ![IterStatus.ABANDON, IterStatus.PLAN].includes(iterBranchDetail.status) && !isComponentProject) {
      if (iterQaReviewList.length > 0) {
        list.push({
          key: 'cancelQaReview',
          label:
            <Popconfirm title="确认取消全量回归（回归记录会删除）？" okText="是" cancelText="否" onConfirm={cancelQaReview}>
              <div style={{ width: '100%' }}>取消全量回归</div>
            </Popconfirm>
        })
      } else {
        list.push({
          key: 'triggerQaReview',
          label:
            <TriggerQaReviewBtn iterId={iterBranchDetail.iterId} onIterChange={onIterChange}>触发全量回归</TriggerQaReviewBtn>
        })
      }
    }

    if (access.isAdmin && !isComponentProject) {
      list.push({
        key: 'makeAndSaveReportAnalyzed',
        label:
          <Popconfirm title="确认生成包体积分析文件（report_analyzed.json）？" okText="是" cancelText="否" onConfirm={makeAndSaveReportAnalyzed}>
            <div style={{ width: '100%' }}>生成包体积分析文件</div>
          </Popconfirm>
      })
    }

    if (access.isAdmin && !isComponentProject) {
      list.push({
        key: 'makeBranchReportAnalyzed',
        label:
          <Popconfirm title="确认生成集成分支体积分析文件（report_analyzed.json）？" okText="是" cancelText="否" onConfirm={makeBranchReportAnalyzed}>
            <div style={{ width: '100%' }}>生成集成分支体积分析文件</div>
          </Popconfirm>
      })
    }


    if (access.isAdmin && ![IterStatus.ABANDON, IterStatus.PLAN].includes(iterBranchDetail.status) && !isComponentProject) {
      list.push({
        key: 'makeAndSaveShrinkwrap',
        label:
          <Popconfirm title="确认生成Shrinkwrap？" okText="是" cancelText="否" onConfirm={makeAndSaveShrinkwrap}>
            <div style={{ width: '100%' }}>生成Shrinkwrap</div>
          </Popconfirm>
      })
    }

     if (access.isAdmin && !isComponentProject && !iterBranchDetail.dist && ![IterStatus.ABANDON, IterStatus.PLAN].includes(iterBranchDetail.status)) {
      list.push({
        key: 'addDist',
        label: 
          <Popconfirm title="确认更新dist？" okText="是" cancelText="否" onConfirm={addDist}>
            <div style={{ width: '100%' }}>上传王守义打码产物</div>
          </Popconfirm>
      });
    }

    if (access.isAdmin && iterBranchDetail.defDist && project.dingtalkRobots && !isComponentProject) {
      list.push({
        key: 'noticePublishReport',
        label: 
          <Popconfirm title="确认发送发版报告？" okText="是" cancelText="否" onConfirm={noticePublishReport}>
            <div style={{ width: '100%' }}>发送发版报告</div>
          </Popconfirm>
      });
    }

    if (access.isAdmin && iterBranchDetail.status === IterStatus.PLAN) {
      list.push({
        key: 'discard',
        danger: true,
        label:
          <Popconfirm title="确认废弃？" okText="是" cancelText="否" onConfirm={discardBranch}>
            <div style={{ width: '100%' }}>废弃迭代</div>
          </Popconfirm>
      })
    }

    if (access.isAdmin && (iterBranchDetail.status === IterStatus.ABANDON || iterBranchDetail.status === IterStatus.PLAN)) {
      list.push({
        key: 'delete',
        danger: true,
        label:
          <Popconfirm title="确认删除（会删库）？" okText="是" cancelText="否" onConfirm={deleteBranch}>
            <div style={{ width: '100%' }}>删除迭代</div>
          </Popconfirm>
      })
    }

    return list;
  })()

  const actionBtnList = (() => {
    let list = [];

    // 全量按钮
    switch (iterBranchDetail.status) {
      // 计划发布
      case IterStatus.PLAN: list = [ACTION_BTN.STABLE_BUILD, ACTION_BTN.SYNC_MASTER, ACTION_BTN.MERGE, ACTION_BTN.CREATE_DEV_BRANCH]; break;
      // 集成回归
      case IterStatus.MERGE: {
        const deliverAndReviewBtn = iterBranchDetail.deliverClientList ? ACTION_BTN.DELIVER : ACTION_BTN.REVIEW
        const btns = isComponentProject ? [ACTION_BTN.CR, ACTION_BTN.DEF_PREPUB, ACTION_BTN.DEF_PUBLISH] : [deliverAndReviewBtn];
        // 配置了迭代测试且是管理员或测试人员才展示
        const showCheckBtn = qaList.length && (access.isAdmin || qaList.some(v => v.workid === currentUser.workid));
        const qaBtn = showCheckBtn ? [ACTION_BTN.QA_CHECK] : [];
        list = [ACTION_BTN.RC_BUILD, ACTION_BTN.STABLE_BUILD, ...qaBtn, ACTION_BTN.CANCEL_MERGE, ...btns];
        break;
      }
      // 提审（新链路中，该状态废弃）
      case IterStatus.AUDITING: list = [ACTION_BTN.RC_BUILD, ACTION_BTN.STABLE_BUILD, ACTION_BTN.ROLLBACK, ACTION_BTN.PUBLISH]; break;
      // 灰度（新链路中，该状态废弃）
      case IterStatus.GRAY: list = [ACTION_BTN.RC_BUILD, ACTION_BTN.STABLE_BUILD, ACTION_BTN.ROLLBACK, ACTION_BTN.PUBLISH]; break;
      // 投放中
      case IterStatus.DELIVERING: list = isComponentProject ? [ACTION_BTN.MODULE_DELIVER] : [ACTION_BTN.RC_BUILD, ACTION_BTN.STABLE_BUILD, ACTION_BTN.DELIVER]; break;
      // 已发布
      case IterStatus.PUBLISHED: list = isComponentProject ? [ACTION_BTN.INIT_DELIVER] : [ACTION_BTN.RC_BUILD, ACTION_BTN.STABLE_BUILD, iterBranchDetail.deliverClientList ? ACTION_BTN.DELIVER : null]; break;
      // 废弃
      case IterStatus.ABANDON: list = [ACTION_BTN.STABLE_BUILD]; break;
    }

    // 不支持打码的项目，过滤掉打码按钮
    if (!isSupportBuild(project)) {
      list = list.filter(btn => btn && ![ACTION_BTN.RC_BUILD, ACTION_BTN.STABLE_BUILD].includes(btn))
    }
    return list;
  })()

  // 投放状态
  const DeliverStatus = () => {
    // 当迭代是“集成回归”或“投放中”或“已发布”状态，且有投放任务时，才显示投放状态
    if (![IterStatus.MERGE, IterStatus.DELIVERING, IterStatus.PUBLISHED].includes(iterBranchDetail.status) || deliverClientWithTaskList.length === 0) return null;

    // 本次投放的端
    const deliverClientWithTaskListThisTime = deliverClientWithTaskList.filter(deliverClientWithTask => deliverClientWithTask.deliverTaskId);
    if (deliverClientWithTaskListThisTime.length === 0) return null;

    return <Space direction="vertical" align="start">
      <span className="deliver-status-title" key="title">投放状态</span>
      {deliverClientWithTaskListThisTime.map((deliverClientWithTask, index) => {
        const miniAppVersionInfo = deliverClientWithTask.deliverTask?.miniAppVersionInfo;

        let statusText: string | undefined;
        if (miniAppVersionInfo) {
          statusText = miniAppVersionInfo.statusText
          // 灰度情况特殊处理
          if (miniAppVersionInfo.status === EMiniAppVersionStatus.GRAY) {
            const curGrayStep = miniAppVersionInfo.graySteps.find(item => item.strategy === miniAppVersionInfo.curGrayStrategy);
            if (curGrayStep) {
              statusText = `${miniAppVersionInfo.statusText}(${curGrayStep.title})`
            }
          }
        } else {
          statusText = deliverClientWithTask.deliverTask?.uploadStatusText
        }

        return <Space className="deliver-status-content" size={14} key={index}>
          <span>{deliverClientWithTask.clientExtInfo?.cnName}</span>
          {
            deliverClientWithTask.skip
              ? <Tag icon={<StopOutlined />}>已跳过投放</Tag>
              : <>
                {deliverClientWithTask.deliverTask?.miniAppVersion
                  ? <Tag color="orange">v{deliverClientWithTask.deliverTask.miniAppVersion}</Tag>
                  : <Tag>版本号暂未生成</Tag>}
                {statusText ? <span>{statusText}</span> : null}
              </>
          }
        </Space>
      })}
    </Space>
  }

  const initDeliver = (iterId: number) => {
    setLoading(true);

    iterBranchApi.startComponentDeliver({
      iterId,
      status: IterStatus.DELIVERING,
    }).then((res) => {
      if (res?.success) {
        onIterChange();
        message.info('初始化投放成功');
      } else {
        message.error(res?.errorMsg || `初始化投放失败`);
      }
    })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => setLoading(false));
  }

  // 自动发起组件发布的CR，并通知到发布群
  const createCR = async () => {
    setCRLoading(true);
    let desc = '';
    devBranchList.forEach((branch, index) => {
      desc += `\n${index+1}. ${branch.description}（${branch.creator}）`
    })
    const crWorkIdList = getCRUserList(iterBranchDetail.projectName, devBranchList, currentUser.workid, true);
    const res = await defApi.createCR({
      projectName: iterBranchDetail.projectName,
      iterationId: iterBranchDetail.defIterId,
      devbranchId: iterBranchDetail.defBranchId!,
      title: `${iterBranchDetail.projectName} 变更 rc/${iterBranchDetail.version} 的代码评审`,
      description: `${iterBranchDetail.publishDay}发布迭代的冻结版本（分支号 rc/${iterBranchDetail.version}）${desc}\n`,
      ids: crWorkIdList.join(','),
    })
    if (res?.success) {
      message.success('创建CR成功！');
      // CR状态重新获取和更新
      CRDetailRef?.current?.getCR();
    } else if (res?.rawData?.indexOf('alread exist')){
      message.error({ content: 'CR已存在不可重复创建', duration: 5 });
    } else {
      message.error({ content: <a href={DEF_URL} target="_blank">自动创建CR失败，请去def手动创建</a>, duration: 10 });
    }
    setCRLoading(false);
  }

  const handleCheck = () => {
    const { iterId } = iterBranchDetail;
    const checked =  isChecked ? EChecked.NO : EChecked.YES;

    setCheckLoading(true);
    iterBranchApi.switchChecked({ iterId, checked })
      .then(() => {
        setIsChecked(!!checked);
        setCheckLoading(false);
      })
      .catch((err) => {
        message.error(err.message || '设置回归状态失败');
        setCheckLoading(false);
      })
  };

  useEffect(() => {
    const { defUploadPkgRes, gmtModified, projectName } = iterBranchDetail;
    if (defUploadPkgRes) {
      pkgApi.analyzeIterSize({
        gmtModified,
        projectName,
        defUploadPkgRes,
      }).then((res: any) => {
        setPkgSizeInfo(res.data);
      });
    }
  }, []);

  return <PageHeader
    className="iter-header"
    breadcrumb={{ routes }}
    title={
      <div className="project">
        <img className="project-icon" src={project.icon} />
        <div>
          <div>迭代版本：v{iterBranchDetail.version}</div>
          <div className="project-cn">{project.cnName}</div>
        </div>
      </div>
    }
    extra={
      <Space className="actions">
        {
          actionBtnList.map(actionBtn => {
            switch (actionBtn) {
              case ACTION_BTN.RC_BUILD:
                return iterBranchDetail.rcGitBranch && <BuildBtn
                  key={actionBtn}
                  project={project}
                  gitRepo={iterBranchDetail.gitRepo}
                  branchName={iterBranchDetail.rcGitBranch.name}
                >
                  <Tooltip title="基于 rc 分支构建"><Button>打集成码</Button></Tooltip>
                </BuildBtn>
              case ACTION_BTN.STABLE_BUILD:
                return <BuildBtn
                  key={actionBtn}
                  project={project}
                  gitRepo={iterBranchDetail.gitRepo}
                  branchName={iterBranchDetail.gitBranch.name}
                >
                  <Tooltip title="基于 stable 分支构建"><Button>打基线码</Button></Tooltip>
                </BuildBtn>
              case ACTION_BTN.SYNC_MASTER:
                return access.isAdmin && <Popconfirm
                  key={actionBtn}
                  placement="bottom"
                  title="确认同步？"
                  okText="是"
                  cancelText="否"
                  onConfirm={syncBranch}>
                  <Tooltip title="支持自动解 version 冲突"><Button loading={syncLoading}>同步master</Button></Tooltip>
                </Popconfirm>
              case ACTION_BTN.QA_CHECK:
                return <Button key={actionBtn} loading={checkLoading} onClick={handleCheck}>{isChecked ? '取消回归确认' : '确认已回归'}</Button>;
              case ACTION_BTN.MERGE:
                return access.isAdmin && <MergeBtn
                  key={actionBtn}
                  iterBranchDetail={iterBranchDetail}
                  onMergeEnd={onIterChange}>
                  <Button danger>冻结集成</Button>
                </MergeBtn>
              case ACTION_BTN.CR:
                return <Button key={actionBtn} loading={CRLoading} onClick={createCR}>一键CR</Button>;
              case ACTION_BTN.DEF_PREPUB:
                return access.isAdmin && <DefBuildBtn
                  key={actionBtn}
                  btnType="primary"
                  // project={project}
                  // gitRepo={latestDevBranch.gitRepo}
                  // branchName={latestDevBranch.gitBranch.name}
                  iterId={iterBranchDetail.iterId}
                  defIterId={iterBranchDetail.defIterId}
                  defTaskId={iterBranchDetail.defTaskId}
                  defBuildStatus={iterBranchDetail.defBuildStatus!}
                  defEnvType={iterBranchDetail.defEnvType}
                  taskType={EDefBuildTaskType.ITERATION}
                  taskEnv={EPubTypeEnv.Beta}
                  refreshBuildStatus={onIterChange}
                >
                  部署预发
                </DefBuildBtn>
              case ACTION_BTN.DEF_PUBLISH:
                return access.isAdmin && <DefBuildBtn
                  key={actionBtn}
                  btnType="primary"
                  // project={project}
                  // gitRepo={latestDevBranch.gitRepo}
                  // branchName={latestDevBranch.gitBranch.name}
                  iterId={iterBranchDetail.iterId}
                  defIterId={iterBranchDetail.defIterId}
                  defTaskId={iterBranchDetail.defTaskId}
                  defBuildStatus={iterBranchDetail.defBuildStatus!}
                  defEnvType={iterBranchDetail.defEnvType}
                  taskType={EDefBuildTaskType.ITERATION}
                  taskEnv={EPubTypeEnv.Prod}
                  refreshBuildStatus={onIterChange}
                  onBeforeBuild={onBeforeBuild}
                >
                  正式发布
                </DefBuildBtn>
              case ACTION_BTN.CANCEL_MERGE:
                return access.isAdmin && <Popconfirm
                  key={actionBtn}
                  placement="bottom"
                  title="确认取消集成？"
                  okText="是"
                  cancelText="否"
                  onConfirm={cancelMerge}>
                  <Button danger>取消集成</Button>
                </Popconfirm>
              case ACTION_BTN.INIT_DELIVER:
                return access.isAdmin && <Button type="primary" onClick={() => initDeliver(iterBranchDetail.iterId)}>去投放</Button>
              case ACTION_BTN.DELIVER:
                return <DeliverBtn
                  key={actionBtn}
                  project={project}
                  devBranchList={devBranchList}
                  iterBranchDetail={iterBranchDetail}
                  onPublishEnd={onIterChange}
                />
              case ACTION_BTN.MODULE_DELIVER:
                return <ComponentDeliverBtn
                  key={actionBtn}
                  btnType="primary"
                  project={project}
                  // TODO aonelist没有
                  latestDevBranch={{
                    ...iterBranchDetail,
                    iterId: iterBranchDetail.iterId,
                    branchName: iterBranchDetail.version,
                    aoneList: [{ id: 46857764 }]
                  }}
                  branchType={EDeliverBranchType.ITERATION}
                  taskEnv={EPubTypeEnv.Prod}
                  description={`${iterBranchDetail.description}的页面投放(组件v${iterBranchDetail.version})`}
                >
                  投放线上
                </ComponentDeliverBtn>
              case ACTION_BTN.REVIEW:
                return access.isAdmin && <ReviewBtn
                  key={actionBtn}
                  iterBranchDetail={iterBranchDetail}
                  project={project}
                  onReviewEnd={onIterChange} />
              case ACTION_BTN.PUBLISH:
                return access.isAdmin && <PublishBtn
                  key={actionBtn}
                  iterBranchDetail={iterBranchDetail}
                  project={project}
                  onPublishEnd={onIterChange} />
              case ACTION_BTN.ROLLBACK:
                return access.isAdmin && <Popconfirm
                  key={actionBtn}
                  placement="bottom"
                  title="确认回滚发布？"
                  okText="是"
                  cancelText="否"
                  onConfirm={rollback}>
                  <Button danger>回滚</Button>
                </Popconfirm>
              case ACTION_BTN.CREATE_DEV_BRANCH:
                return <EditDevBtn
                  key={actionBtn}
                  iterBranch={iterBranchDetail}
                  action={Action.Create}
                  project={project}
                  onCreateEnd={() => { onIterChange(); showMergeTip(); }}>
                  <Button type="primary">新建开发分支</Button>
                </EditDevBtn>
            }
          })
        }
        {
          menu.length > 0 &&
          <Dropdown menu={{ items: menu }} trigger={['click']} placement="bottomRight">
            <Button type="primary" shape="circle" icon={<MoreOutlined />} />
          </Dropdown>
        }
      </Space>
    }
  >
    {/* <Button key="test-auto" onClick={() => {
      iterBranchApi.defHookText().then((res) => {
        console.log('lingyue' + JSON.stringify(res))
      })
        .catch((err) => {
        })
        .finally(() => {
        });
    }}>测试自动通知</Button> */}
    <div className="content">
      <Descriptions className="main" size="small" column={iterBranchDetail.rcGitBranch ? 3 : 2}>
        <Descriptions.Item label="代码分支">
          {iterBranchDetail.gitBranch ? <a href={iterBranchDetail.gitBranch.url} target="_blank">{iterBranchDetail.gitBranch.name}</a> : '无'}
        </Descriptions.Item>
        <Descriptions.Item label="计划发布日期">{iterBranchDetail.publishDay}</Descriptions.Item>

        {iterBranchDetail.rcGitBranch && <Descriptions.Item label="集成分支">
          <a href={iterBranchDetail.rcGitBranch.url} target="_blank">{iterBranchDetail.rcGitBranch.name}</a>
        </Descriptions.Item>}

        <Descriptions.Item label="创建人">{iterBranchDetail.creator}</Descriptions.Item>
        <Descriptions.Item label="创建时间">{iterBranchDetail.gmtCreate}</Descriptions.Item>

        {iterBranchDetail.rcGitBranch && <Descriptions.Item label="与主干对比">
          <a href={`${iterBranchDetail.gitRepo.url}/compare/master...${encodeURIComponent(iterBranchDetail.rcGitBranch.name)}`} target="_blank">Diff</a>
        </Descriptions.Item>}

        {iterBranchDetail.modifier && <Descriptions.Item label="修改人">{iterBranchDetail.modifier}</Descriptions.Item>}
        {iterBranchDetail.gmtModified && <Descriptions.Item label="修改时间">{iterBranchDetail.gmtModified}</Descriptions.Item>}

        {iterBranchDetail.rcGitBranch && !isComponentProject && <Descriptions.Item label="包分析" style={{ whiteSpace: 'nowrap' }}>
          <Space>
          {iterBranchDetail.dist ? <Link to={`/package/structure?iterId=${iterBranchDetail.iterId}`} target="_blank">结构</Link> : null}
          {iterBranchDetail.shrinkwrap && <Link to={`/package/dependencies?iterId=${iterBranchDetail.iterId}`} target="_blank">依赖</Link>}
          {pkgSizeInfo && <IterSize data={pkgSizeInfo} label="体积" />}
          {iterBranchDetail.defDist && <Link to={`/package/home-size?iterId=${iterBranchDetail.iterId}`} target="_blank">首页体积</Link>}
          </Space>
        </Descriptions.Item>}

        {qaList.length && (
          <Descriptions.Item label="测试" contentStyle={{ display: 'flex', alignItems: 'center'}}>
            <span>{iterBranchDetail.qaList.map(item => item.name).join('、')}</span>
            {isChecked && <CheckCircleOutlined style={{ marginLeft: '6px', color: '#389e0d', verticalAlign: 'middle' }} />}
          </Descriptions.Item>
        )}

        <Descriptions.Item label="迭代描述">{iterBranchDetail.description}</Descriptions.Item>

        {iterBranchDetail.defIterId && <Descriptions.Item label="DEF迭代">
          <a href={DEF_URL} target="_blank">点击跳转</a>
        </Descriptions.Item>}

        {iterBranchDetail.defIterId && <Descriptions.Item label="构建版本">
          {iterBranchDetail.pkgPublishVersion || '暂未构建'}
        </Descriptions.Item>}

        {iterBranchDetail.defDist && <Descriptions.Item label="DEF构建产物">
        <a href={iterBranchDetail.defDist}><DownloadOutlined /></a>
        </Descriptions.Item>}

        {isComponentProject &&<Descriptions.Item label="代码CR">
          <CRDetail ref={CRDetailRef} defIterId={iterBranchDetail.defIterId} />
        </Descriptions.Item>}

        {isComponentProject && COMP_HAS_AUTO_UI_TEST && <Descriptions.Item label="自动化UI对比测试">
          <a href={AUTO_UI_TEST_URL} target="_blank">点击跳转</a>
        </Descriptions.Item>}
      </Descriptions>
      <Space className="extra" direction="horizontal" size={50}>
        {iterBranchDetail.status === IterStatus.GRAY && <Statistic title="灰度状态" value={iterBranchDetail.grayStatus} />}
        <DeliverStatus />
        <Statistic title="迭代状态" value={iterBranchDetail.statusText} valueStyle={{ color: iterBranchDetail.status === IterStatus.ABANDON ? '#f5222d' : undefined }} />
      </Space>
    </div>
    { isAlipayProj ? <Alert message="📢 测试回归务必切换至 外网环境，覆盖安卓、iOS、鸿蒙三端！" type="error" /> : "" }
  </PageHeader>
}
