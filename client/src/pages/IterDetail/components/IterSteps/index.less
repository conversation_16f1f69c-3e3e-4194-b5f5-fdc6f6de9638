.iter-steps {
  background-color: #fafafa;
  margin-bottom: 30px;
  margin-left: -24px;
  margin-right: -24px;
  padding: 10px 48px;

  .deliver-steps-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .line {
    margin: 0 16px;
    height: 1px;
    width: 11%;
    background: #f0f0f0;
    &.active {
      background-color: #1890ff;
    }

    &-offset {
      position: relative;
      top: -10px;
    }
  }

  .deliver-steps {
    flex: 1;
  }

  .deliver-steps-content {
    display: flex;
    align-items: center;
  }
  
  .client-name {
    font-size: 12px;
    width: 48px;
    color: #91d5ff;
  }

  .ant-steps-item {
    padding: 20px 0;
  }
}