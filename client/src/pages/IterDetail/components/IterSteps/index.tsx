import { useEffect, useState } from 'react';
import { Steps } from 'antd';
import { IIterBranch, IIterStep } from '@/interface/iter-branch';
import { IterStatus, OLD_ITER_STEPS, ITER_STEPS, MODULE_ITER_STEPS } from '@/const/iter-branch';
import { getDeliverSteps, EMiniAppVersionStatus } from '@/const/iter-deliver';
import { Link } from 'umi';
import { IProject } from '@/interface/project';
import { EBranchType } from '@/const/dev-branch';
import { usePageState } from '../../page-state';

import './index.less';

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  iterBranchDetail: IIterBranch;
  project: IProject;
}

export default ({ iterBranchDetail, project }: Props) => {
  const [deliverClientWithTaskList] = usePageState('deliverClientWithTaskList');
  // 如果是组件形式，步骤需要定制化
  const isComponentProject = iterBranchDetail.branchType === EBranchType.COMPONENT;
  let showSteps;

  if (iterBranchDetail.deliverClientList) { // 新链路
    const deliverSteps = getDeliverSteps(project.type);
    // 本次投放的端
    const clientListForThisDelivery = deliverClientWithTaskList.filter(item => item.deliverTask?.miniAppVersionInfo?.status);
    // 按投放进度升序排列
    const deliverClientListOrderByAcs = clientListForThisDelivery.sort((clientA, clientB) => {
      function findIndex(client: typeof clientA) {
        const clientMiniAppVersionStatus = client.deliverTask?.miniAppVersionInfo?.status as EMiniAppVersionStatus;
        return deliverSteps.findIndex(step => {
          return step.matchProcessStatus?.includes(clientMiniAppVersionStatus)
            || step.matchErrorStatus?.includes(clientMiniAppVersionStatus)
            || step.matchFinishStatus?.includes(clientMiniAppVersionStatus)
        });
      }

      return findIndex(clientA) - findIndex(clientB);
    })

    let iterSteps = ITER_STEPS.slice();
    iterSteps.splice(-1, 0, ...deliverSteps);
    showSteps = parseSteps({
      iterSteps,
      iterBranchDetail,
      miniAppVersionStatus: deliverClientListOrderByAcs[0]?.deliverTask?.miniAppVersionInfo?.status,
    });
  } else if (isComponentProject) { // 项目类型是模块类型时的链路
    showSteps = parseSteps({ iterSteps: MODULE_ITER_STEPS.slice(), iterBranchDetail, isComponentProject });
  } else { // 老链路
    showSteps = parseSteps({ iterSteps:OLD_ITER_STEPS.slice(), iterBranchDetail });
  }

  return (
    <div className="iter-steps">
      <Steps status={showSteps.currentStatus} current={showSteps.currentIndex}>
        {
          showSteps.steps.map((step, index) => (
            <Steps.Step title={step.title} description={step.description} key={index} />
          ))
        }
      </Steps>
    </div>
  )
}

function parseSteps({
  iterSteps,
  iterBranchDetail,
  miniAppVersionStatus,
  isComponentProject
}: {
  iterSteps: IIterStep[];
  iterBranchDetail: IIterBranch;
  miniAppVersionStatus?: EMiniAppVersionStatus;
  isComponentProject?: boolean;
}) {
  let currentIndex = -1;
  let currentStatus = 'wait' as 'wait' | 'process' | 'error' | 'finish';
  const { status: iterStatus, iterId, towerId } = iterBranchDetail;

  const steps = iterSteps.map((step, index) => {
    let description = step.description && step.description[iterStatus] as string | React.ReactNode;

    // 如果是已发布状态，则特殊处理
    if (iterStatus === IterStatus.PUBLISHED && !isComponentProject) {
      currentIndex = iterSteps.length - 1;
      currentStatus = 'finish';
    } else {
      // 优先匹配小程序版本状态，没有这匹配迭代状态
      if (step.matchProcessStatus?.includes(miniAppVersionStatus || iterStatus)) {
        currentIndex = index;
        currentStatus = 'process';
      } else if (step.matchErrorStatus?.includes(miniAppVersionStatus || iterStatus)) {
        currentIndex = index;
        currentStatus = 'error';
      } else if (step.matchFinishStatus?.includes(miniAppVersionStatus || iterStatus)) {
        currentIndex = index;
        currentStatus = 'finish';
      }
    }

    // 集成回归步骤注入描述
    if (towerId && step.matchProcessStatus?.includes(IterStatus.MERGE)) {
      // TODO def检测任务
      description = <Link
        to={`/helper/report?iterId=${iterId}&towerId=${towerId}`}
        target="_blank">测试报告</Link>;
    } else {
      description = step.description && step.description[miniAppVersionStatus || iterStatus];
    }

    return {
      title: step.title,
      description,
      step
    }
  });

  return {
    steps,
    currentStatus,
    currentIndex
  }
}
