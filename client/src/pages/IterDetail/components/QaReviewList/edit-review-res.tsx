import { useState } from 'react';
import { Form, message, Radio, Modal, Input } from 'antd';
import iterQaReviewApi from '@/api/iter-qa-review';
import { IIterQaReviewRes, IIterQaReview } from '@/interface/iter-qa-review';

interface Props {
  /** 回归记录 */
  iterQaReview: IIterQaReview;
  /** 回归结论 */
  iterQaReviewRes?: IIterQaReviewRes;
  /** 回归记录变更回调 */
  onIterQaReviewChange: (iterQaReview: IIterQaReview) => void;
  /** 回归记录变更前回调 */
  onIterQaReviewBeforeChange: () => void;
  /** 回归记录变更结束回调 */
  onIterQaReviewAfterChange: () => void;
}

interface ReviewResForm {
  pass: number;
  title: string;
  comment: string
}

const Comp: React.FC<Props> = ({ iterQaReview, iterQaReviewRes, children, onIterQaReviewChange, onIterQaReviewBeforeChange, onIterQaReviewAfterChange }) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm<ReviewResForm>();
  const initialValues = iterQaReviewRes ? {
    pass: iterQaReviewRes.pass ? 1 : 0,
    title: iterQaReviewRes.title,
    comment: iterQaReviewRes.comment,
  } : undefined;

  // 新增回归结论
  function addReviewRes({ pass, title, comment }: ReviewResForm) {
    onIterQaReviewBeforeChange();
    setLoading(true);

    iterQaReviewApi.addReviewRes({
      id: iterQaReview.id,
      pass: pass === 1 ? true : false,
      title: title.trim(),
      comment: comment.trim()
    })
      .then((res) => {
        if (res.success) {
          setVisible(false);
          onIterQaReviewChange(res.data);
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '新增回归结论失败');
      }).finally(() => {
        onIterQaReviewAfterChange();
        setLoading(false);
      });
  }

  // 修改回归结论
  function modifyReviewRes({ pass, title, comment }: ReviewResForm) {
    if (!iterQaReviewRes) return;

    onIterQaReviewBeforeChange();
    setLoading(true);

    iterQaReviewApi.modifyReviewRes({
      id: iterQaReview.id,
      resId: iterQaReviewRes.resId,
      pass: pass === 1 ? true : false,
      title: title.trim(),
      comment: comment.trim()
    })
      .then((res) => {
        if (res.success) {
          setVisible(false);
          onIterQaReviewChange(res.data);
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '修改回归结论失败');
      }).finally(() => {
        onIterQaReviewAfterChange();
        setLoading(false);
      });
  }

  return <>
    <span onClick={() => setVisible(true)}>{children}</span>

    <Modal
      open={visible}
      onCancel={() => setVisible(false)}
      onOk={() => form.submit()}
      confirmLoading={loading}
      title={`${iterQaReviewRes ? '修改' : '新增'}回归结论`}
    >
      <Form
        className="review-form"
        form={form}
        initialValues={initialValues}
        labelCol={{
          span: 6
        }}
        wrapperCol={{
          span: 18
        }}
        onFinish={iterQaReviewRes ? modifyReviewRes : addReviewRes}
      >
        <Form.Item
          name="pass"
          label="是否通过"
          required
          initialValue={1}
        >
          <Radio.Group options={[{ label: '通过', value: 1 }, { label: '不通过', value: 0 }]}></Radio.Group>
        </Form.Item>

        <Form.Item
          name="title"
          label="主要回归内容"
          rules={[
            {
              required: true,
              message: '请填写主要回归内容',
              whitespace: true
            },
          ]}
        >
          <Input placeholder="请简要填写，如详情页" maxLength={20} showCount />
        </Form.Item>

        <Form.Item
          name="comment"
          label="回归纪要"
          rules={[
            {
              required: true,
              message: '请填写回归纪要',
              whitespace: true
            },
          ]}
        >
          <Input.TextArea placeholder="如Bug的aone地址" />
        </Form.Item>
      </Form>

    </Modal>
  </>
}

export default Comp;