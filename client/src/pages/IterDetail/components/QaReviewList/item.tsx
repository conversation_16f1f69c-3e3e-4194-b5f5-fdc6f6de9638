import { useEffect, useMemo, useState } from 'react';
import { message, Row, Col, Space, MenuProps, Dropdown, Popconfirm, Divider, Spin, Tag, Popover, Timeline } from 'antd';
import { useAccess } from 'umi';
import { DownOutlined, PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons';
import iterQaReviewApi from '@/api/iter-qa-review';
import { IIterBranch } from '@/interface/iter-branch';
import { BIZ_TITLE, BIZ_TITLE_COLOR } from '@/const/biz';
import { EIterQaReviewAction } from '@/const/iter-qa-review';
import { formateDate } from '@/utils/date';
import { IIterQaReview } from '@/interface/iter-qa-review';
import ReviewRes from './review-res';
import EditReviewRes from './edit-review-res';

interface Props {
  /** 迭代全量回归记录 */
  iterQaReview: IIterQaReview;
  /** 迭代分支 */
  iterBranch: IIterBranch;
}

const Comp: React.FC<Props> = ({ iterBranch, iterQaReview }) => {
  const [latestIterQaReview, setLatestIterQaReview] = useState<IIterQaReview | null>(iterQaReview);
  const [loading, setLoading] = useState(false);
  const [fold, setFold] = useState(true);
  const access = useAccess();

  if (!latestIterQaReview) return null;

  // 计算回归通过、不通过个数
  const [passCount, noPassCount] = useMemo(() => {
    if (!latestIterQaReview) return [0, 0]

    return latestIterQaReview.reviewResList.reduce((pre, cur) => {
      pre[cur.pass ? 0 : 1] += 1;
      return pre;
    }, [0, 0])
  }, [latestIterQaReview])

  // 删除回归记录
  function deleteQaReview() {
    if (!latestIterQaReview) return;

    setLoading(true);

    iterQaReviewApi.delete({ id: latestIterQaReview.id })
      .then((res) => {
        if (res.success) {
          setLatestIterQaReview(null);
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '删除回归记录失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  // 回归记录变更前回调
  function onIterQaReviewBeforeChange() {
    setLoading(true);
  }

  // 回归记录变更结束回调
  function onIterQaReviewAfterChange() {
    setLoading(false);
  }

  // 回归记录变更回调
  function onIterQaReviewChange(iterQaReview: IIterQaReview) {
    setLatestIterQaReview(iterQaReview);
  }

  const renderActions = () => {
    // 动作列表
    const actionList = [];

    // 更多动作列表
    const moreActionList: MenuProps['items'] = [];

    // 新增回归记录
    actionList.push(<EditReviewRes
      key="add"
      iterQaReview={latestIterQaReview}
      onIterQaReviewChange={onIterQaReviewChange}
      onIterQaReviewBeforeChange={onIterQaReviewBeforeChange}
      onIterQaReviewAfterChange={onIterQaReviewAfterChange}><a>新增回归结论</a></EditReviewRes>)

    // 历史记录
    moreActionList.push({
      key: 'history',
      label: <Popover
        content={<Timeline className='review-history' reverse>
          {
            latestIterQaReview.actionHistory.map((item, index) => {
              let color;
              let titleClassName = '';
              switch (item.action) {
                case EIterQaReviewAction.ReviewPass: color = 'green'; titleClassName = 'success-text'; break;
                case EIterQaReviewAction.ReviewNoPass: color = 'red'; titleClassName = 'danger-text'; break;
                case EIterQaReviewAction.DeleteReviewRes: color = 'gray'; break;
                default: color = 'blue';
              }
              return <Timeline.Item key={index} color={color} className="review-history-item">
                <Space>
                  <span className='empty-text'>{formateDate('YYYY-MM-DD hh:mm:ss', new Date(item.timestamp))}</span>
                  {item.userName}
                  <span className={titleClassName}>{item.title}</span>
                </Space>
              </Timeline.Item>
            })
          }
        </Timeline>}
        placement="left"
        trigger="click">
        <span><a>历史记录</a></span>
      </Popover>
    })

    if (access.isAdmin) {
      moreActionList.push({
        key: 'delete',
        danger: true,
        label:
          <Popconfirm title="确认删除本条回归记录？" okText="是" cancelText="否" onConfirm={deleteQaReview}>
            <a>删除</a>
          </Popconfirm>
      })
    }

    if (moreActionList.length > 0) {
      actionList.push(<Dropdown key="more" menu={{ items: moreActionList }} trigger={['click']}>
        <a onClick={e => e.preventDefault()}>
          更多 <DownOutlined />
        </a>
      </Dropdown>)
    }

    return actionList.map((Item, index) => index === 0 ? Item : [<Divider key={`divider-${index}`} type="vertical" />, Item]);
  };

  return <Spin spinning={loading} wrapperClassName="qa-review-item">
    <Row className="content" gutter={8} align="middle">
      <Col span={1} className="col">
        {/* 展开/收起回归结论 */}
        {fold ? <PlusCircleOutlined onClick={() => setFold(false)} /> : <MinusCircleOutlined onClick={() => setFold(true)} />}
      </Col>

      <Col span={4} className="col">
        {/* 行业线 */}
        <Tag color={BIZ_TITLE_COLOR[latestIterQaReview.bizName]} className="tag">{BIZ_TITLE[latestIterQaReview.bizName]}</Tag>
      </Col>

      <Col span={9} className="col">
        回归结论：
        {passCount !== 0 && <span className='success-text'>{passCount} 通过</span>}
        {passCount !== 0 && noPassCount !== 0 && <Divider type="vertical" />}
        {noPassCount !== 0 && <span className='danger-text'>{noPassCount} 不通过</span>}
        {passCount === 0 && noPassCount === 0 && <span className='empty-text'>无</span>}
      </Col>

      <Col span={10} className="col actions">
        {renderActions()}
      </Col>
    </Row>

    {/* 回归结论 */}
    <Row style={{ display: fold ? 'none' : 'flex' }} gutter={8}>
      <Col span={1}></Col>
      <Col span={23}>
        <ReviewRes iterQaReview={latestIterQaReview} onIterQaReviewChange={onIterQaReviewChange} onIterQaReviewBeforeChange={onIterQaReviewBeforeChange} onIterQaReviewAfterChange={onIterQaReviewAfterChange} />
      </Col>
    </Row>
  </Spin>
}

export default Comp;