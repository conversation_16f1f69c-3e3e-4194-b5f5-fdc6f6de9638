import { useEffect, useState } from 'react';
import { List } from 'antd';
import { IIterBranch } from '@/interface/iter-branch';
import Item from './item';

import './index.less';
import { IIterQaReview } from '@/interface/iter-qa-review';

interface Props {
  iterBranchDetail: IIterBranch;
  list: IIterQaReview[];
}

const Comp: React.FC<Props> = ({ iterBranchDetail, list }) => {
  list = list?.filter(item => !!item);

  return <List
    itemLayout="horizontal"
    size="large"
    pagination={{
      pageSize: 15,
    }}
    dataSource={list}
    renderItem={(item: IIterQaReview) => (
      <List.Item
        key={item.id}
        style={{ padding: 0 }}
      >
        <Item iterBranch={iterBranchDetail} iterQaReview={item} />
      </List.Item>
    )}
  />
}

export default Comp;