import { useMemo, useState } from 'react';
import { message, Table, Popconfirm, Divider, } from 'antd';
import { useAccess, useModel } from 'umi';
import iterQaReviewApi from '@/api/iter-qa-review';
import { IIterQaReview, IIterQaReviewRes } from '@/interface/iter-qa-review';
import type { ColumnsType } from 'antd/es/table';
import { IUser } from '@/interface/user';
import EditReviewRes from './edit-review-res';

interface Props {
  /** 回归记录 */
  iterQaReview: IIterQaReview;
  /** 回归记录变更回调 */
  onIterQaReviewChange: (iterQaReview: IIterQaReview) => void;
  /** 回归记录变更前回调 */
  onIterQaReviewBeforeChange: () => void;
  /** 回归记录变更结束回调 */
  onIterQaReviewAfterChange: () => void;
}

interface DataType extends IIterQaReviewRes {
  key: number;
}

const Comp: React.FC<Props> = ({ iterQaReview, onIterQaReviewChange, onIterQaReviewBeforeChange, onIterQaReviewAfterChange }) => {
  const access = useAccess();
  const currentUser = useModel('user');
  const columns: ColumnsType<DataType> = [
    {
      title: '回归人',
      dataIndex: ['reviewer', 'name'],
      key: 'name',
      width: '10%'
    },
    {
      title: '回归结论',
      dataIndex: 'pass',
      key: 'pass',
      width: '10%',
      render: (pass: IIterQaReviewRes['pass']) => {
        if (pass) {
          return <span className='success-text'>通过</span>
        } else {
          return <span className='danger-text'>不通过</span>
        }
      }
    },
    {
      title: '主要回归内容',
      dataIndex: 'title',
      key: 'title',
      width: '25%',
    },
    {
      title: '回归纪要',
      dataIndex: 'comment',
      key: 'comment',
      width: '40%',
    },
    {
      title: '操作',
      key: 'action',
      width: '15%',
      align: 'center',
      render: (_, record) => {
        // 动作列表
        const actionList = [];

        // 仅管理员或回归者本人有权限
        if (access.isAdmin || record.reviewer.workid === currentUser.workid) {
          // 修改
          actionList.push(<EditReviewRes
            key="editReviewRes"
            iterQaReview={iterQaReview}
            iterQaReviewRes={record}
            onIterQaReviewChange={onIterQaReviewChange}
            onIterQaReviewBeforeChange={onIterQaReviewBeforeChange}
            onIterQaReviewAfterChange={onIterQaReviewAfterChange}><a>修改回归结论</a></EditReviewRes>)

          // 删除
          actionList.push(<Popconfirm
            key="delete"
            title="确认删除本条回归结论？"
            okText="是"
            cancelText="否"
            onConfirm={() => deleteReviewRes(record.resId)}>
            <a className="danger-text">删除</a>
          </Popconfirm>)
        }

        if (actionList.length > 0) {
          return actionList.map((Item, index) => index === 0 ? Item : [<Divider key={`divider-${index}`} type="vertical" />, Item]);
        } else {
          return <span className="empty-text">无</span>
        }
      },
    },
  ];
  const dataSource = useMemo(() => {
    return iterQaReview.reviewResList.map(reviewRes => {
      return {
        key: reviewRes.resId,
        ...reviewRes
      } as DataType
    })
  }, [iterQaReview])

  // 删除回归结论
  function deleteReviewRes(resId: number) {
    onIterQaReviewBeforeChange();

    iterQaReviewApi.deleteReviewRes({ id: iterQaReview.id, resId })
      .then((res) => {
        if (res.success) {
          onIterQaReviewChange(res.data);
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '删除回归结论失败');
      }).finally(() => {
        onIterQaReviewAfterChange()
      });
  }

  return <Table
    size="middle"
    dataSource={dataSource}
    columns={columns}
    pagination={dataSource.length > 5 ? {} : false}
  />;
}

export default Comp;