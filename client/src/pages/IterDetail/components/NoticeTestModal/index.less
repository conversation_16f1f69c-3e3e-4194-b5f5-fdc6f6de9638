.notice-modal-qrcode-content {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-direction: row;
}
.notice-modal-qrcode-detail {
  display: flex;
  flex: 1;
  // overflow: hidden;
  flex-direction: column;
  align-items: center;
  min-height: 150px;
  margin-left: 24px;
  max-height: 200px;
  overflow-y: auto;
}
.notice-modal-qrcode-detail-item {
  width: 100%;
  font-weight: bold;
}
.notice-modal-qrcode-detail-item-qr-info {
  width: 100%;
  font-weight: bold;
  margin-top: 6px;
  overflow: hidden;
}
.notice-modal-qrcode-detail-item-qr-info-val {
  width: 100%;
  font-size: 12px;
  width: 100%;
  overflow: hidden;
}

.notice-modal-qrcode-detail-item:nth-child(3) {
  .notice-modal-qrcode-detail-item-val {
    font-size: 12px;
    text-decoration: underline;
    color: dodgerblue;
  }
}
.notice-modal-qrcode-detail-item:nth-child(2) {
  .notice-modal-qrcode-detail-item-val {
    font-weight: bold;
    text-decoration: underline;
    color: green;
  }
}
.notice-modal-qrcode-detail-item-val {
  width: 100%;
  margin-left: 5px;
  font-weight: normal;
}
.notice-modal-qrcode-qr {
  width: 150px;
  min-height: 150px;
  position: relative;
}
.notice-modal-qrcode-qr-process {
  position: absolute;
  z-index: 1;
  top: 0px;
  left: 0px;
  width: 150px;
  height: 150px;
  padding-top: 65px;
  transition: visibility 0.3s;
}