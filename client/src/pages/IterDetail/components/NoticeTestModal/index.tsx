/**
 * 支端 - 生成体验码回归
 */
import helper<PERSON>pi from "@/api/helper";
import iterExperience<PERSON><PERSON> from "@/api/iter-experience";
import { IIterBranch } from "@/interface/iter-branch";
import { <PERSON><PERSON>, Button, Image, message, Modal, Spin } from "antd";
import { memo, useEffect, useMemo, useRef, useState } from "react";
import "./index.less";

const RES_STATUS = {
  INIT: "init", //初始化
  LOADING: "loading", //构建中
  GET_INFO_ERROR: "get_info_error", // 构建上传失败
  GET_INFO_SUCCESS: "get_info_success", //构建上传成功
  GET_QR_ERROR: "get_qr_error", // 构建上传成功，生成二维码失效
  GET_QR_SUCCESS: "get_qr_success" // 构建成功,获取体验码成功
};

const STATUS_DESC_MAP = {
  [RES_STATUS["INIT"]]: "",
  [RES_STATUS["LOADING"]]: "构建上传中...",
  [RES_STATUS["GET_INFO_ERROR"]]: "构建上传失败!",
  [RES_STATUS["GET_INFO_SUCCESS"]]: "构建上传成功，生成体验码中...",
  [RES_STATUS["GET_QR_ERROR"]]: "构建上传成功，生成体验码失败!",
  [RES_STATUS["GET_QR_SUCCESS"]]: "构建上传成功，生成体验码成功!"
};
const GET_EXPERIENCE_QR_TIMEOUT = 10 * 1000;
const GET_EXPERIENCE_INFO_TIMEOUT = 15 * 1000;

const QR_PALCEHOLDER =
  "https://img.alicdn.com/imgextra/i3/O1CN01Qa0qBB1Y9NFZWM5UY_!!6000000003016-2-tps-200-200.png";
const QR_ERROR_PLACEHOLDER =
  "https://img.alicdn.com/imgextra/i4/O1CN01SUGGva1xB5BCYrBHI_!!6000000006404-2-tps-180-180.png";

interface IProps {
  iterBranch: IIterBranch;
  visible: boolean;
  setVisible: (visible: boolean) => void;
}

const NoticeModal: React.FC<IProps> = memo((props) => {
  const { iterBranch, visible, setVisible = () => null } = props;
  // const [visible, setVisible] = useState(false);
  const [hashK, setHashK] = useState("");
  const [qrCode, setQRCode] = useState("");
  const [qrSchema, setQRSchema] = useState("");
  const [miniAppVer, setMiniAppVer] = useState("");
  const [expirenceInfo, setExpirenceInfo] = useState<any>(null);
  const [status, updateStatus] = useState(RES_STATUS["INIT"]);
  const errStatuses = [
    RES_STATUS["GET_INFO_ERROR"],
    RES_STATUS["GET_QR_ERROR"]
  ];

  const hashKRef = useRef("");

  useEffect(() => {
    console.info("curIterInfo", iterBranch);
    const { deliverClientList, iterId, rcGitBranch } = iterBranch || {};
    const { name = "", url = "" } = rcGitBranch || {};
    if (!iterId) {
      message.error("获取迭代id出错，请检查迭代信息");
      return;
    }
    updateStatus(RES_STATUS["LOADING"]);
    getExperienceInfoAndQrCode({
      miniAppId: deliverClientList?.[0]?.miniAppId || "",
      clientName: deliverClientList?.[0]?.clientName,
      iterId: iterId,
      rcGitName: name,
      rcGitUrl: url
    });
  }, []);

  useEffect(() => {
    hashKRef.current = hashK;
    if (status === RES_STATUS["GET_INFO_SUCCESS"] && hashK) {
      const timer = setTimeout(() => {
        getQRCode(hashK);
        clearTimeout(timer);
      }, 500);
    }
  }, [hashK, status]);

  const displayOpt: any = useMemo(() => {
    switch (true) {
      case status === RES_STATUS["GET_QR_SUCCESS"]:
        return {
          alertType: "success",
          alertDesc: STATUS_DESC_MAP[status],
          percent: 100,
          processStatus: "success",
          processStyle: {
            visibility: "hidden"
            // backgroundColor: "rgba(82, 196, 26, .4)"
          }
        };
      case status === RES_STATUS["GET_QR_ERROR"]:
        return {
          alertType: "error",
          alertDesc: STATUS_DESC_MAP[status],
          percent: 100,
          processStatus: "exception",
          processStyle: {
            visibility: "hidden"
            // backgroundColor: "rgba(255, 77, 79, .4)"
          }
        };
      case status === RES_STATUS["GET_INFO_ERROR"]:
        return {
          alertType: "error",
          alertDesc: STATUS_DESC_MAP[status],
          percent: 50,
          processStatus: "exception",
          processStyle: {
            visibility: "hidden"
            // backgroundColor: "rgba(255, 77, 79, .4)"
          }
        };

      default: {
        return {
          alertType: "warning",
          alertDesc: STATUS_DESC_MAP[status],
          percent:
            status === RES_STATUS.GET_INFO_SUCCESS
              ? 50
              : status === RES_STATUS.LOADING
              ? 10
              : 5,
          processStatus: "normal",
          processStyle: {}
        };
      }
    }
  }, [status]);

  const QRUrlOpt = useMemo(() => {
    switch (true) {
      case status === RES_STATUS["GET_QR_SUCCESS"]:
        return {
          qrCode,
          opacity: 1
        };
      case errStatuses.includes(status):
        return {
          qrCode: QR_ERROR_PLACEHOLDER,
          opacity: 1
        };
      default: {
        return {
          qrCode: QR_PALCEHOLDER,
          opacity: 0.4
        };
      }
    }
  }, [status, qrCode]);

  function getExperienceInfoAndQrCode({
    miniAppId,
    clientName,
    iterId,
    commitId,
    rcGitName,
    rcGitUrl,
    recursion
  }: any) {
    iterExperienceApi
      .getExperienceInfo({
        miniAppId,
        clientName,
        iterId,
        commitId,
        rcGitName,
        rcGitUrl
      })
      .then((res) => {
        if (res?.success && res?.data?.status) {
          // 构建中，轮询
          if (res?.data?.status == 1) {
            const tempIns = setTimeout(() => {
              getExperienceInfoAndQrCode({
                miniAppId,
                clientName,
                iterId,
                commitId,
                rcGitName,
                rcGitUrl,
                recursion: true // 后续轮询不再触发构建，有问题就直接构建报错了
              });
              clearTimeout(tempIns);
            }, GET_EXPERIENCE_INFO_TIMEOUT);
            return;
          } else if (res?.data?.status == 2) {
            // 构建完成
            updateStatus(RES_STATUS["GET_INFO_SUCCESS"]);
            setExpirenceInfo(res?.data);
            const hashK = res?.data?.hashK;
            setHashK(hashK);
            const miniAppVersion = res?.data?.miniAppVersion;
            setMiniAppVer(miniAppVersion);
            return;
          } else if (res?.data?.status == -1) {
            // 构建失败
            updateStatus(RES_STATUS["GET_INFO_ERROR"]);
            const miniAppVersion = res?.data?.miniAppVersion;
            setMiniAppVer(miniAppVersion);
            setExpirenceInfo(res?.data);
            return;
          }
        } else if (!recursion && res?.success && !res?.data?.status) {
          // 触发构建
          forceReBuild({
            miniAppId,
            clientName,
            iterId,
            rcGitName,
            rcGitUrl,
            reBuild: true
          });
          return;
        }
        updateStatus(RES_STATUS["GET_INFO_ERROR"]);
      })
      .catch((err) => {
        updateStatus(RES_STATUS["GET_INFO_ERROR"]);
      });
  }

  function resetAllState() {
    updateStatus(RES_STATUS["INIT"]);
    setHashK("");
    setMiniAppVer("");
    setExpirenceInfo(null);
    setQRCode("");
    setQRSchema("");
  }

  function forceReBuild({
    miniAppId,
    clientName,
    iterId,
    reBuild,
    rcGitName,
    rcGitUrl
  }: any) {
    resetAllState();
    updateStatus(RES_STATUS["LOADING"]);
    return iterExperienceApi
      .createExperience({
        miniAppId,
        clientName,
        iterId,
        reBuild
      })
      .then((res) => {
        if (res?.success) {
          const tempIns = setTimeout(() => {
            getExperienceInfoAndQrCode({
              miniAppId,
              clientName,
              iterId,
              rcGitName,
              rcGitUrl,
              commitId: res?.data?.commitId,
              recursion: true
            });
            clearTimeout(tempIns);
          }, 500);
        } else {
          updateStatus(RES_STATUS["GET_INFO_ERROR"]);
        }
      })
      .catch((err) => {
        updateStatus(RES_STATUS["GET_INFO_ERROR"]);
      });
  }

  function getQRCode(k: any) {
    if (!k || k !== hashKRef.current) {
      return;
    }
    iterExperienceApi
      .getExperienceQRCode({
        hashK: k
      })
      .then((res) => {
        if (res?.success) {
          const { status: _s, qrCode, schemaUrl } = res?.data || {};
          if (_s == 2) {
            updateStatus(RES_STATUS["GET_QR_SUCCESS"]);
            setQRCode(qrCode);
            setQRSchema(schemaUrl);
            return;
          } else if (_s == 1) {
            const timer = setTimeout(() => {
              getQRCode(k);
              clearTimeout(timer);
            }, GET_EXPERIENCE_QR_TIMEOUT);
            return;
          } else if (_s == -1) {
            updateStatus(RES_STATUS["GET_QR_ERROR"]);
            return;
          }
        }
        updateStatus(RES_STATUS["GET_QR_ERROR"]);
      })
      .catch((err) => {
        updateStatus(RES_STATUS["GET_QR_ERROR"]);
      });
  }

  /** 通知回归 */
  function noticeRegressionTesting({ qrCodeLink }: { qrCodeLink?: string }) {
    setVisible(false);
    helperApi
      .noticeRegressionTesting({
        iterId: iterBranch.iterId,
        qrCodeLink
      })
      .then((res) => {
        if (res?.success) {
          message.success("通知回归成功");
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || "通知回归失败");
      });
  }

  function copyLink(link: string) {
    navigator.clipboard
      .writeText(link)
      .then(() => {
        message.success("复制成功");
      })
      .catch((err) => {
        message.error(err?.message || "复制失败！");
      });
  }

  return (
    <Modal
      open={visible}
      title={"通知回归"}
      width={600}
      destroyOnClose
      maskClosable={false}
      onCancel={() => {
        setVisible(false);
      }}
      footer={
        <>
          {errStatuses.includes(status) ||
          status === RES_STATUS.GET_QR_SUCCESS ? (
            <Button
              onClick={() => {
                const { deliverClientList, iterId, rcGitBranch } =
                  iterBranch || {};
                const { name, url } = rcGitBranch || {};
                forceReBuild({
                  miniAppId: deliverClientList?.[0]?.miniAppId || "",
                  clientName: deliverClientList?.[0]?.clientName,
                  iterId: iterId,
                  rcGitName: name,
                  rcGitUrl: url,
                  reBuild: true
                });
              }}
            >
              重新构建
            </Button>
          ) : (
            ""
          )}
          {status === RES_STATUS["GET_QR_SUCCESS"] ? (
            <Button
              onClick={() => {
                noticeRegressionTesting({ qrCodeLink: qrCode });
              }}
              type="primary"
            >
              确定通知
            </Button>
          ) : (
            ""
          )}
        </>
      }
    >
      {displayOpt.alertDesc ? (
        <Alert
          showIcon
          message={displayOpt.alertDesc}
          type={displayOpt.alertType}
        />
      ) : (
        ""
      )}
      <div className="notice-modal-qrcode-content">
        <div
          className="notice-modal-qrcode-qr"
          onClick={() => {
            status === RES_STATUS.GET_QR_SUCCESS && copyLink(QRUrlOpt.qrCode);
          }}
        >
          <Image
            preview={false}
            width={150}
            height={150}
            src={QRUrlOpt.qrCode}
            style={{ opacity: QRUrlOpt.opacity }}
          />
          <Spin
            className="notice-modal-qrcode-qr-process"
            style={displayOpt.processStyle}
          />
          {expirenceInfo && expirenceInfo.gmtCreate ? (
            <>
              <div className="notice-modal-qrcode-detail-item-qr-info">
                创建任务时间:
              </div>
              <div className="notice-modal-qrcode-detail-item-qr-info-val">
                {expirenceInfo.gmtCreate}
              </div>
            </>
          ) : (
            ""
          )}
        </div>
        <div className="notice-modal-qrcode-detail">
          <div className="notice-modal-qrcode-detail-item">
            appid:
            <span className="notice-modal-qrcode-detail-item-val">
              {expirenceInfo?.miniAppId}
            </span>
          </div>
          <div className="notice-modal-qrcode-detail-item">
            小程序版本:
            <span
              className="notice-modal-qrcode-detail-item-val"
              onClick={() => {
                copyLink(miniAppVer);
              }}
            >
              {miniAppVer}
            </span>
          </div>
          <div className="notice-modal-qrcode-detail-item">
            alipaySchema:
            <span
              className="notice-modal-qrcode-detail-item-val"
              onClick={() => {
                copyLink(qrSchema);
              }}
            >
              {qrSchema}
            </span>
          </div>
        </div>
      </div>
    </Modal>
  );
});

export default NoticeModal;
