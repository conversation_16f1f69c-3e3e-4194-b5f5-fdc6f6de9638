import { useState } from 'react';
import { Modal, Button, message, Popconfirm, Space, Input } from 'antd';
import { IIterDeliverTask, IMiniAppVersionInfo, IAlipayVersionInfo } from '@/interface/iter-deliver';
import iterDeliver<PERSON><PERSON> from '@/api/iter-deliver';

import './index.less';

interface Props {
  // 投放任务
  deliverTask: IIterDeliverTask;
  // 使用内嵌iframe进行投放
  useIframe: boolean;
  // 状态变更回调
  onChange: () => void;
}

export default (({ deliverTask, useIframe, onChange, children }) => {
  const miniAppVersionInfo = deliverTask.miniAppVersionInfo as IAlipayVersionInfo | IMiniAppVersionInfo;
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [rollbackTargetVersion, setRollbackTargetVersion] = useState('');
  const actionTitle = '回滚';

  // 回滚
  function rollback() {
    setLoading(true);

    return iterDeliverApi.rollback({
      id: deliverTask.id,
    })
      .then((res) => {
        if (res?.success) {
          message.success(`${actionTitle}成功`);
          onChange();
          closeModal();
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err: any) => {
        message.error(`${actionTitle}失败${err?.message ? ': ' + err.message : ''}`);
      }).finally(() => {
        setLoading(false)
      });
  }

  function closeModal() {
    setShowModal(false);
  }

  if (!useIframe) {
    return <Popconfirm
      title={`确定${actionTitle}？`}
      onConfirm={rollback}
      okText="确定"
      cancelText="取消"
    >{children}</Popconfirm>
  } else {
    return <>
      <span onClick={() => setShowModal(true)}>{children}</span>

      <Modal
        open={showModal}
        title={actionTitle}
        width={1500}
        destroyOnClose
        maskClosable={false}
        onCancel={closeModal}
        footer={<Space>
          <Input
            value={rollbackTargetVersion}
            onChange={(e) => setRollbackTargetVersion(e.target.value)}
            placeholder='请填写回退的目标版本号'
          />
          <Button
            type="primary"
            disabled={!rollbackTargetVersion}
            onClick={rollback}
            loading={loading}>确认已{actionTitle}</Button>
        </Space>}
      >
        <iframe className="modal-iframe" src={miniAppVersionInfo.platformUrl}></iframe>
      </Modal>
    </>
  }
}) as React.FC<Props>