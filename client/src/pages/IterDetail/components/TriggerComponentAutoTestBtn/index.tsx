import { useState } from 'react';
import { Popover, Button, message } from 'antd';
import helper<PERSON><PERSON> from '@/api/helper';
import { EPubTypeEnv } from '@/const/def';
import buyPhoenixAutoTestCfg from './config';

interface Props {
  /** 构建任务环境 */
  taskEnv: EPubTypeEnv;
  /** 需要更新的依赖版本 */
  pkgPublishVersion: string;
}

const Comp: React.FC<Props> = ({ pkgPublishVersion, taskEnv, children }) => {

  function triggerTest() {
    const { gitRepo, gitBranchName, defIterId } = buyPhoenixAutoTestCfg.UITest;
    helperApi.triggerComponentAutoTest({
      pub_env: taskEnv,
      gitRepo,
      npmList: [{ name: buyPhoenixAutoTestCfg.moduleName, value: pkgPublishVersion }],
      defIterId,
      branchName: gitBranchName
    }).then((res) => {
      if (res?.success) {
        message.success('触发自动化测试成功')
      } else {
        message.error({ content: res?.errorMsg || '触发自动化测试失败', duration: 10 });
      }
    })
  }

  return <div onClick={triggerTest}>{children}</div>
}

export default Comp;
