import './index.less';
import { useState } from 'react';
import helper<PERSON><PERSON> from '@/api/helper';
import NoticeModal from '../NoticeTestModal';
import { EProjectType } from '@/const/project';
import { IIterBranch } from '@/interface/iter-branch';
import { Popover, Form, Input, Button, message } from 'antd';

interface Props {
  /** 迭代分支 */
  iterBranch: IIterBranch;
  projectType: EProjectType
}

const Comp: React.FC<Props> = ({ iterBranch, projectType, children }) => {
  const [visible, setVisible] = useState(false);
  const isAPMiniApp = projectType === EProjectType.ALIPAY;
  /**
   * 通知回归
   */
  function noticeRegressionTesting({ qrCodeLink }: { qrCodeLink?: string; }) {
    setVisible(false);

    helperApi.noticeRegressionTesting({
      iterId: iterBranch.iterId,
      qrCodeLink
    })
      .then((res) => {
        if (res?.success) {
          message.success('通知回归成功');
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '通知回归失败');
      })
  }

  const Content = <Form className="notice-form" onFinish={noticeRegressionTesting}>
    <Form.Item name="qrCodeLink" label="二维码链接">
      <Input
        size="middle"
        placeholder="微信小程序可不填，默认取体验版链接"
      />
    </Form.Item>

    <Button className="form-submit" type="primary" size="middle" htmlType="submit">确定通知</Button>
  </Form>

  return (
    isAPMiniApp ? 
      <>
        <div 
          onClick={e => {
            // todo lingyue 如果是微信判断下是否构建完成
            setVisible(true)
          }}>
            {children}
          </div>
        {visible ? <NoticeModal iterBranch={iterBranch} visible={visible} setVisible={setVisible} /> : ""}
      </> 
    : <Popover
        visible={visible}
        onVisibleChange={visible => {
          // todo lingyue 如果是微信判断下是否构建完成
          setVisible(visible)
        }}
        content={Content}
        placement="left"
        trigger="click">
        {children}
      </Popover>
  )
}

export default Comp;