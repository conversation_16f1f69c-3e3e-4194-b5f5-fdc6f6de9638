import { useState } from 'react';
import { Input, message, Alert, Form, Radio, Button, Popconfirm } from 'antd';
const { TextArea } = Input;
import SSRApi from '@/api/ssr-detail';
import './index.less';

export default () => {  
  const [form] = Form.useForm();
  const [handleType, setHandelType] = useState('get');
  const [loading, setLoading] = useState(false);

  const handleOptions = [
    { label: '读取', value: 'get' },
    { label: '写入', value: 'set' },
  ];

  const onChangeHandleType = (event: any) => {
    const value = event?.target?.value || 'get';
    setHandelType(value);
    form.setFieldsValue({
      value: '',
      result: '',
    });
  }

  const onConfirm = () => {
    form.validateFields().then(formRes => {
      console.log('onConfirm>>>>', formRes);

      setLoading(true);

      const {
        handleType,
        nameSpace,
        key,
        value,
      } = formRes;

      if (handleType === 'get') {
        SSRApi.getDcdnKV({
          nameSpace,
          key,
        }).then(res => {
          setLoading(false);
          console.log('res>>>>', res);
          const success = res?.data?.success || false;
          if (success) {
            const erValue = res?.data?.data || '';
            message.success('查询成功');
            form.setFieldsValue({
              result: erValue,
            });
          } else {
            message.error('查询失败' + res.errorMsg);
          }
        }).catch(err => {
          setLoading(false);
          message.error('查询失败' + JSON.stringify(err));
        });
      } else {
        SSRApi.setDcdnKV({
          nameSpace,
          key,
          value,
        }).then(res => {
          setLoading(false);
          console.log('res>>>>', res);
          const success = res?.success || false;
          if (success) {
            message.success('写入成功');
            form.setFieldsValue({
              value: '',
              result: '写入成功',
            });
          } else {
            message.error('写入失败' + JSON.stringify(res));
          }
        }).catch(err => {
          setLoading(false);
          message.error('写入失败' + JSON.stringify(err));
        });
      }
    });
  };

  return (
    <div className="er-config-container">
      <Alert message="修改ER配置实时生效，且不支持回滚，务必谨慎操作！" type="info" />

      <div className="content">
        <h3>ER配置</h3>

        <Form
          name="basic"
          form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 12 }}
          initialValues={{ handleType: 'get' }}
          style={{ width: '60%', margin: '0 auto' }}
        >
          <Form.Item 
            label="操作"
            name="handleType" 
          >
            <Radio.Group 
              options={handleOptions} 
              optionType="button" 
              buttonStyle="solid"
              onChange={onChangeHandleType}
            />
          </Form.Item>

          <Form.Item
            label="空间名称nameSpace（string类型）"
            name="nameSpace"
            rules={[{ required: true, message: 'Please input 空间名称(nameSpace)!' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            label="键Key（string类型）"
            name="key"
            rules={[{ required: true, message: 'Please input 键(Key)!' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            label="值Value（string类型）"
            name="value"
          >
            <TextArea 
              disabled={handleType === 'get'} 
              rows={3} 
            />
          </Form.Item>

          <Form.Item
            label="执行结果"
            name="result"
          >
            <TextArea rows={8} disabled={true} />
          </Form.Item>

          <Form.Item 
            wrapperCol={{ offset: 8, span: 16 }}
          >
              <Popconfirm
                title="确定提交吗?"
                onConfirm={onConfirm}
                okText="确定"
                cancelText="取消"
              >
                <Button type="primary" htmlType="submit" loading={loading}>提交</Button>
              </Popconfirm>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
}
