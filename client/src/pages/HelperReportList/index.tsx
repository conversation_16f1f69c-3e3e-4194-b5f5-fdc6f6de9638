import { useLocation } from 'umi';
import moment from 'moment';
import { useState, useEffect, SetStateAction } from 'react';
import { Table, Statistic, Row, Col, Button, Card, Space, DatePicker, Tabs } from 'antd';
import { MainTaskDetail } from '@/interface/task-detail';
import taskApi, { SubTaskCountDetail } from '@/api/task';

import './index.less';

const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

export default () => {
  const location = useLocation();
  // @ts-ignore

  const [taskList, setTaskList] = useState<MainTaskDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState<string[]>([
    moment().subtract(1, 'month').format('YYYY-MM-DD'),
    moment().format('YYYY-MM-DD'),
  ]);
  const [updateTag, setUpdateTag] = useState(false);
  const [totalNumList, setTotalNumList] = useState<SubTaskCountDetail[]>([]);


  function showTaskStatus(value: number) {
    switch (value) {
      case 1: {
        return (
          <span
            style={{
              backgroundColor: 'grey',
              color: 'white',
              padding: '6px',
              borderRadius: '5px',
              fontSize: '12px',
            }}
          >
            初始化
          </span>
        )
        break
      }
      case 2: {
        return (
          <span
            style={{
              backgroundColor: 'rgb(45, 183, 245)',
              color: 'white',
              padding: '6px',
              borderRadius: '5px',
              fontSize: '12px',
            }}
          >
            执行中
          </span>
        )
        break
      }
      case 3: {
        return (
          <span
            style={{
              backgroundColor: 'rgb(135, 208, 104)',
              color: 'white',
              padding: '6px',
              borderRadius: '5px',
              fontSize: '12px',
            }}
          >
            执行成功
          </span>
        )
        break
      }
      case 4: {
        return (
          <span
            style={{
              backgroundColor: 'red',
              color: 'white',
              padding: '6px',
              borderRadius: '5px',
              fontSize: '12px',
            }}
          >
            执行失败
          </span>
        )
        break
      }
      default: {
        return <span>状态异常</span>
      }
    }
  }

  const columns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      width: '10%',
    },
    {
      title: '端',
      dataIndex: 'appType',
      key: 'appType',
      width: '10%',
      render: (value: string, record: any) => (
        record.appType == "WX" ? (
          <Space size="middle">
            <span>微信</span>
          </Space>
        ) : (
          <Space size="middle">
            <span>支付宝</span>
          </Space>
        )
      ),
    },
    {
      title: '迭代分支',
      dataIndex: 'branchName',
      key: 'branchName',
      width: '20%',
    },
    {
      title: '执行时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      width: '25%',
      render: (value: string, record: any) => (
        <Space size="middle">
          <span>{new Date(record.gmtCreate).toLocaleString()}</span>
        </Space>

      ),
    },
    {
      title: '执行结果',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      width: '20%',
      render: (value: string, record: any) => (
        <Space size="middle">
          {showTaskStatus(record.taskStatus)}
        </Space>
      ),
    },
    {
      title: '任务明细',
      dataIndex: 'id',
      width: '25%',
      key: 'id',
      render: (value: string, record: any) => (
        <a target="_blank"
          href={"https://fl-miniwork.fc.alibaba-inc.com/#/helper/report?iterId=62&towerId=" + record.id}>任务明细</a>
      ),
    },
  ];

  useEffect(fetchData, [updateTag])

  function fetchData() {
    setLoading(true);

    //查询主任务list
    taskApi.getMainTaskDetail(
      dateRange[0],
      dateRange[1]
    ).then(res => {
      if (!res || !res.data || !res.data.success) throw Error('empty');
      setTaskList(res.data.data.dataList);
    })
      .catch(err => {
        console.log("error:" + err);
        setTaskList([])
      })

    //查询任务统计结果
    taskApi.getTotalTaskCount(
      dateRange[0],
      dateRange[1]
    ).then(res => {
      if (!res || !res.data || !res.data.success) throw Error('empty');
      // setMainTotalNum(res.data.data.mainTaskCount);
      // setSuccessNum(res.data.data.successTaskCount);
      // setSubTaskTotalNum(res.data.data.totalSubTaskCount);
      console.log(res.data.data);
      setTotalNumList(res.data.data);
      console.log(totalNumList);
    })
      .catch(err => {
        console.log("error:" + err);
        setTotalNumList([])
      })
      .finally(() => setLoading(false))

  }

  function showTable (value: number){

    const taskAP: SetStateAction<MainTaskDetail[]> = [];
    const taskWX: SetStateAction<MainTaskDetail[]> = [];
    
    taskList.map((item, i) => { 
      switch (item.appType) {
        case "AP": {
          taskAP.push(item);
          break
        }
        case "WX": {
          taskWX.push(item);
          break
        }
        default: {
        }
      } 
    })

    let taskListResult: SetStateAction<MainTaskDetail[]> = [];
    let tabName = "回归主任务明细";
    switch (value) {
      case 1: {
        taskListResult = taskAP;
        tabName = "支付宝小程序";
        break
      }
      case 2: {
        taskListResult = taskWX;
        tabName = "微信小程序";
        break
      }
      default: {
      }
    } 
    if(taskListResult.length == 0) {
      return null;
    }
    return (
        <TabPane tab={tabName} key={value}>
          <Table
            loading={loading}
            columns={columns}
            dataSource={taskListResult}
            rowClassName="table-component"
            rowKey={record => record.id}
          />
        </TabPane>
    )
  }

  return (
    <div className="helper-report-list-container">
      <RangePicker
        value={[
          !dateRange[0] ? null : moment(dateRange[0]),
          !dateRange[1] ? null : moment(dateRange[1]),
        ]}
        onChange={(date: any) => {
          setDateRange(date.map((e: any) => moment(e).format('YYYY-MM-DD')));
        }}
      />
      <Button
        onClick={() => {
          setUpdateTag((x) => !x);
        }}
        type="primary"
      >
        搜索
      </Button>
      {totalNumList.length > 0 ? totalNumList.map(e => {
        if (e.taskType == 0) {
          return <div className="platform">
            <div className="header">
              <span className="platform-name">{e.taskTypeDesc}</span>
            </div>
            <Row gutter={16} style={{ marginBottom: '10px' }}>
              <Col span={6}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>迭代总次数</h3>} value={e.totalMainCount} valueStyle={{
                    color: '#ffc53d',
                  }} />
                </Card>
              </Col>
              <Col span={6}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>子任务总量</h3>} value={e.totalCount}
                    valueStyle={{
                      color: '#cf1311',
                    }} />
                </Card>
              </Col>
              <Col span={6}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>子任务执行成功量</h3>} value={e.taskSuccCount} valueStyle={{
                    color: '#3f8600',
                  }} />
                </Card>
              </Col>
              <Col span={6}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>子任务校验成功量</h3>} value={e.checkSuccCount} valueStyle={{
                    color: '#cf1322',
                  }} />
                </Card>
              </Col>
            </Row>
          </div>
        } else if (e.taskType == 1) {
          return <div className="platform">
            <div className="header">
              <span className="platform-name">{e.taskTypeDesc}</span>
            </div>
            <Row gutter={16} style={{ marginBottom: '10px' }}>
              <Col span={8}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>子任务总量</h3>} value={e.totalCount} valueStyle={{
                    color: '#ffc53d',
                  }} />
                </Card>
              </Col>
              <Col span={8}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>子任务执行成功量</h3>} value={e.taskSuccCount} valueStyle={{
                    color: '#3f8600',
                  }} />
                </Card>
              </Col>
              <Col span={8}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>执行成功率</h3>} value={`${(e.taskSuccCount * 100 / e.totalCount).toFixed(2)}%`}
                    valueStyle={{
                      color: '#cf1322',
                    }} />
                </Card>
              </Col>
            </Row>
            <Row gutter={16} >
              <Col span={8}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>case总数</h3>} value={e.totalCaseCount} valueStyle={{
                    color: '#ffc53d',
                  }} />
                </Card>
              </Col>
              <Col span={8}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>case成功量</h3>} value={e.succCaseCount}
                    valueStyle={{
                      color: '#3f8600',
                    }} />
                </Card>
              </Col>
              <Col span={8}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>case成功率</h3>} value={`${(e.succCaseCount * 100 / e.totalCaseCount).toFixed(2)}%`}
                    valueStyle={{
                      color: '#cf1311',
                    }} />
                </Card>
              </Col>
            </Row>
          </div>
        } else {
          return <div className="platform">
            <div className="header">
              <span className="platform-name">{e.taskTypeDesc}</span>
            </div>
            <Row gutter={16} style={{ marginBottom: '10px' }}>
              <Col span={6}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>执行总次数</h3>} value={e.totalCount} valueStyle={{
                    color: '#ffc53d',
                  }} />
                </Card>
              </Col>
              <Col span={6}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>执行成功量</h3>} value={e.taskSuccCount} valueStyle={{
                    color: '#3f8600',
                  }} />
                </Card>
              </Col>
              <Col span={6}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>校验成功量</h3>} value={e.checkSuccCount} valueStyle={{
                    color: '#cf1322',
                  }} />
                </Card>
              </Col>
              <Col span={6}>
                <Card style={{ textAlign: 'center' }}>
                  <Statistic title={<h3>执行成功率</h3>} value={`${(e.taskSuccCount * 100 / e.totalCount).toFixed(2)}%`}
                    valueStyle={{
                      color: '#cf1322',
                    }} />
                </Card>
              </Col>
            </Row>
          </div>
        }
      }) : null}

      <Card title="回归主任务明细" bordered={false} style={{ width: '100%' }}>
        <Tabs defaultActiveKey="1" type="card" size='large'>
          {showTable(1)}
          {showTable(2)}
        </Tabs>
      </Card>
    </div>
  );
}
