import { useEffect, useState } from 'react';
import { List, message, Form, Card, Button, InputNumber, Select, Input, Steps, Modal } from 'antd';
import ReactD<PERSON><PERSON>iewer from 'react-diff-viewer-continued';

import SSRApi from '@/api/ssr-detail';
import { get } from 'lodash';

import './index.less';

const { TextArea } = Input;

let requestLock = false;

const appNameOption = [
  {label: '浏览器',value: 'others', ua: 'ozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1'},
  {label: '淘宝',value: 'TB', ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 AliApp(TB/10.28.0) WindVane/8.7.2 UT4Aplus/0.0.4 1170x2532 Winding(WV_4) WK'},
  {label: '飞猪',value: 'LX', ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 AliApp(LX/9.9.67) AliTrip/9.9.67 A2U/x WK'},
  {label: '支端',value: 'AP', ua: 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AlipayDefined() AliApp(AP/10.2.23) AlipayClient/10.2.23 Language/en'},
  {label: '微信',value: 'WX', ua: 'Mozilla/5.0 (Linux; Android 9; Redmi Note 8 Pro Build/PPR1.180610.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.72 MQQBrowser/6.2 TBS/045811 Mobile Safari/537.36 MMWEBID/5523 MicroMessenger/8.0.14.2000(0x28000E37) Process/tools WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64'}
]

const modalInitData = {
  show: false
}

export default () => {

  const [form1] = Form.useForm();
  const [form2] = Form.useForm();

  const [modalConfig, setModalConfig] = useState(modalInitData);

  const [loading, setLoading] = useState(false);

  // 编辑器内容
  const [editorContent, setEditorContent] = useState({
    left: "1",
    right: "2"
  })

  // 字符串处理；PreloadK_TB_noUid_/app/trip/rx-trip-ticket/pages/home?citycode=1&disableNav=YES&titleBarHidden=2&envTag=local
  const handleStr = (str: string, type: string)=>{
    const [strBefore, strAfter] = str.split("?");
    const tarArr = [strBefore]
    strAfter.split("&")?.forEach(res=>{
      tarArr.push(res)
    })
    return tarArr.join("\n")
  }

  const onConfirm = (env: string, type: string) => {
    if(requestLock){return message.error('请等待其他请求完成');}
    requestLock = true;
    form2.resetFields();
    setEditorContent({left: '',right: ''})

    setLoading(true)

    form1.validateFields().then((formRes) => {
      const { prefetchUrl = '', preloadUrl = '', userId = '', appName = '' } = formRes;
      // 获取ER侧对应的key值
      const result = Promise.all([
        SSRApi.getPreloadCacheKey({tarUrl: prefetchUrl, ua: appNameOption.find(r=> r.value === appName)?.ua || '',unb: `${userId};`}),
        SSRApi.getPreloadCacheKey({tarUrl: preloadUrl, ua: appNameOption.find(r=> r.value === appName)?.ua || '',unb: `${userId};`})
      ]).then(res=>{

        if(get(res, '[0].success', false) && get(res, '[1].success', false)){
          setModalConfig({show: true})
          form2.setFieldsValue({
            erStatus: (get(res, '[0].data', '') && get(res, '[0].data', '') === get(res, '[1].data', '')) ? '一致' : '不一致',
          })
          setEditorContent({
            left: handleStr(get(res, '[0].data', ''), 'prefetch'),
            right: handleStr(get(res, '[1].data', ''), 'preload')
          })
        }else{
          message.error("获取失败，请检查参数")
        }
      })
    }).finally(()=>{
      requestLock = false;
      setLoading(false)
    })

  };


  useEffect(() => {
    // init()
  }, []);

  return (
    <div className="ssr-tool-container">
      <Card title="预加载自查工具" style={{ marginBottom: '12px' }}>
      <Steps current={2} items={[
          {
            title: '第一步',
            description: '去「日志查询/本地」获取你的预请求URL和页面请求URL',
          },
          {
            title: '第二步',
            description: '将两个URL、端信息、userId填入',
          },
          {
            title: '第三步',
            description: '点击查询',
          },
        ]}
      />
      <br/>
        <Form
          name="basic"
          form={form1}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 16 }}
          initialValues={{ handleType: 'get' }}
          style={{ width: '100%', display: 'inline-block'}}
        >
          <Form.Item label="预请求URL" name="prefetchUrl">
            <TextArea autoSize style={{ width: '100%' }} placeholder=''/>
          </Form.Item>

          <Form.Item label="正式请求URL" name="preloadUrl">
            <TextArea autoSize style={{ width: '100%' }} placeholder=''/>
          </Form.Item>

          <Form.Item label="userId" name="userId">
            <TextArea autoSize rows={4} style={{ width: '100%' }} placeholder=''/>
          </Form.Item>

          <Form.Item label="端类型" name="appName">
             <Select options={appNameOption} />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => {
                onConfirm('pre', 'uid');
              }}
              loading={loading}
            >
              提交查询
            </Button>
          </Form.Item>
        </Form>
      </Card>
      {modalConfig.show ? <Modal
        title="预加载KEY"
        open={true}
        maskClosable={false}
        onOk={()=>{setModalConfig(modalInitData)}}
        onCancel={()=>{setModalConfig(modalInitData)}}
        width={800}
        destroyOnClose
      >
         <Form
          className="review-form"
          form={form2}
          labelCol={{
            span: 7
          }}
          wrapperCol={{
            span: 19
          }}
      >
        <Form.Item
          name="erStatus"
          label="预加载key值是否一致"
        >
          <Input />
        </Form.Item>
        </Form>
         <div style={{width: '100%', height: '400px'}}>
          <ReactDiffViewer oldValue={editorContent.left} newValue={editorContent.right} leftTitle="预请求参数" rightTitle="预加载参数" splitView={true} showDiffOnly={false}/>
        </div>
      </Modal> : null}
    </div>
  );
};
