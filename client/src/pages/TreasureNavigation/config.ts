export default [
  {
    name: '支付宝小程序',
    logo: 'https://gw.alicdn.com/imgextra/i1/O1CN01WXnDlN1UYmkVbgh5A_!!6000000002530-2-tps-201-200.png',
    list: [
      {
        name: '雨燕监控',
        intro: '包含白屏、闪退、JS异常等监控',
        href: 'https://yuyan.antfin-inc.com/app/as/2018081461095002/monitor/dashboard/monitor',
        icon: 'https://gw.alipayobjects.com/zos/bmw-prod/afad7e29-9535-4367-a989-3c9f0c32186e.svg'
      },
      {
        name: '发布管理',
        intro: '推SYNC，加快提升小程序覆盖率',
        href: 'https://cicada.alipay.com/miniapphome/activity/publisherManage.htm',
        icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01yfDBMn1QMdnaKfVhO_!!6000000001962-2-tps-32-32.png'
      },
      {
        name: '构建服务台',
        intro: '查看小程序/插件的上传及发布记录',
        href: 'https://hybrid.alipay.com/w/info?bundleId=com.alipay.alipaywallet&instCode=alipay',
        icon: 'https://gw.alicdn.com/imgextra/i4/O1CN01RKSTzp1jFI8q0AtVQ_!!6000000004518-2-tps-505-505.png'
      },
      {
        name: '设备日志查询',
        intro: '查询客户端的手动埋点、网管RPC、H5、业务不可用、闪退等日志',
        href: 'https://replays.alipay.com/index.htm#/clientQuery',
        icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01xHJVxo22puVcCEEn1_!!6000000007170-2-tps-32-32.png'
      },
      {
        name: '设备日志可视化查询',
        intro: '查询用户日志，如无可先进行设备日志采集',
        href: 'https://replays.alipay.com/index.htm#/faultDiagnosisReport/view/931',
        icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01xHJVxo22puVcCEEn1_!!6000000007170-2-tps-32-32.png'
      },
      {
        name: '设备日志采集',
        intro: '采集用户日志，需用户设备在线',
        href: 'https://r.alipay.com/index.htm#/diagnosisTaskList?app=wallet',
        icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01xHJVxo22puVcCEEn1_!!6000000007170-2-tps-32-32.png'
      },
      {
        name: '代码扫描评估',
        intro: '分析小程序调用到的API是否合规，常用于新投放到其他端时的评估',
        href: 'https://aliminiabilityprod.alipay.com/miniappcenter/createanalysistask',
        icon: 'https://gw.alicdn.com/imgextra/i1/O1CN016LVcDB1DULSFI8XIo_!!6000000000219-2-tps-32-32.png'
      },
      {
        name: '内部开发者手册',
        intro: '包括小程序开发指南、小程序框架和工具、及小程序开发常见问题等官方内容',
        href: 'https://yuque.antfin-inc.com/miniprogram-docs',
        icon: 'https://gw.alipayobjects.com/zos/bmw-prod/3cdb9e39-8db0-4bee-bcf8-2f2842bc5eb5.svg'
      }
    ]
  },
  {
    name: '微信小程序',
    logo: 'https://gw.alicdn.com/imgextra/i4/O1CN01umVV2c22o4uPZ7P6X_!!6000000007166-2-tps-200-200.png',
    list: [
      {
        name: '海纳外投',
        intro: '生成携带商品ID、TTID等参数的微信小程序外投链接',
        href: 'https://hayner.alibaba-inc.com/#/trip-haina/commoditylink',
        icon: 'https://img.alicdn.com/tfs/TB1SSh1pSslXu8jSZFuXXXg7FXa-713-693.png'
      },
      {
        name: 'ARMS监控',
        intro: '包含JS错误、MTop错误、自定义错误等监控',
        href: 'https://arms.console.aliyun.com/retcode?pid=haa3j9plpi@ff139ef9b51871b&login_arms_t3h_token=fGNxFUPQrfNrIzyapZWVjhBl7oIg5CSbL6rj8BvfFm6+8TyWKfA0hud07qYQpPvtNYQ0KbjWvDLIFFnv3CXQRi7sTH8dEogmp2KPDbhR6OpK8e814h2O4ju2M+Y+BM4cq3L8oHBGMjyXll9ZFM1xKa6g/LR4RphijsDqq5bQ7sYCERcJ9QBGAnzmTX6IU+K8MKd+ZaUvCJE0YxAiopPBnkA5rqX2GlOFyI1XJTh+YpT/mToLTkncqg+oWP4mKLjFOij4zA/LXPJ8Cx8YET7cKTVFqzuFAh69qKjXKzwfQv1Zjxt3Wx90MpENguegnH86LMNBeRBpguGaxSHlQGKPZkMWgHEBmms+dHu/ZyW472Lp0c+II3piIqa5avWIpWzKzOaSIrbTyqv5tU1DSz/uu3WAhSqKHPc2j1qRAHoGd6VQeBmeJK9jCWnUwAeqmQ6P6JXqT4fvrH96xbD2vQYr0QIRFwn1AEYCfOZNfohT4rwSUJpA9j3IwXUay9lFj1njKg8pBSMy1nVoRzpfgSe8aKio1ys8H0L9WY8bd1sfdDIDDN35SGMKRRgXAJspDd/sFpTgs44k7TXRugr1Q7OlGuEEsfgorvwOWc1xjbAtpqck3T9MIiQLnlZyjcQVjHY+r1i1F3W/qEoSdBmFPyKFlctHPftGDZpwLMvJ8spvtzCzC8G7D5W8Vu1716jAkLh6XELcYy+tcqnhOmv8zPsA6g==#/index',
        icon: 'https://gw.alicdn.com/imgextra/i1/O1CN017OsqXX1O1udsP0yPM_!!6000000001646-55-tps-280-200.svg'
      }
    ]
  }
]