import { useState, useEffect } from 'react';
import { Card, Typography } from 'antd';
import Config from './config';

import './index.less';

export default () => {

  return (
    <div className="page-container">
      {Config.map((platform, index) => (
        <div className="platform" key={index}>
          <div className="header">
            <img src={platform.logo} className="platform-logo" />
            <span className="platform-name">{platform.name}</span>
          </div>

          <Card>
            {platform.list.map((site, siteIndex) => (
              <Card.Grid className="site-card" hoverable key={siteIndex} >
                <a className="link" href={site.href} target="_blank">
                  <Card.Meta
                    avatar={<img src={site.icon} className="site-logo" />}
                    title={site.name}
                    description={<Typography.Paragraph ellipsis={{ rows: 2 }}>{site.intro}</Typography.Paragraph>}
                  />
                </a>
              </Card.Grid>
            ))}
          </Card>
        </div>
      ))}
    </div>
  );
}
