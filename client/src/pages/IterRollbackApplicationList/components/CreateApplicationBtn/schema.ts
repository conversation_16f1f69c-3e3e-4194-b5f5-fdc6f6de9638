export default {
  "type": "object",
  "properties": {
    "projectName": {
      "title": "回滚项目",
      "type": "string",
      "required": true,
      "enum": ['fliggy-allinone', 'fliggy-weixin', 'fliggy-bytedance'],
      "enumNames": ['支付宝一体化小程序', '飞猪微信小程序', '飞猪字节小程序'],
      "widget": "select",
    },
    "version": {
      "title": "回滚版本",
      "type": "string",
      "widget": "select",
      "readOnly": true,
      "required": true,
      "default": "查询中...",
      "hidden": "{{ !formData.projectName }}"
    },
    "reason": {
      "title": "回滚原因",
      "type": "string",
      "widget": "textarea",
      "required": true
    }
  },
  "labelWidth": 120,
  "displayType": "row"
};
