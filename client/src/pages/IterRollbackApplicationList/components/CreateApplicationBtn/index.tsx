import { useEffect, useState } from 'react';
import { Modal, message, Spin } from 'antd';
import { useForm } from 'form-render';
import { IIterApplication } from '@/interface/iter-application';
import { IIterBranch } from '@/interface/iter-branch';
import FormRender from '@/components/FR';
import IterApplication<PERSON>pi from '@/api/iter-application';
import HelperA<PERSON> from '@/api/helper';
import schema from './schema';

import './index.less';

interface Props {
  onCreateEnd?: (data: IIterApplication) => void;
}

const Comp: React.FC<Props> = ({ children, onCreateEnd }) => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [projectName, setProjectName] = useState(''); // 回滚项目
  const [iterBranch, setIterBranch] = useState<IIterBranch>(); // 回滚迭代
  const form = useForm();

  useEffect(() => {
    if (!showModal || !projectName) return;

    HelperApi.getLatestPublishedByProject({ projectName })
      .then((res) => {
        if (res.success && res.data) {
          form.setValueByPath('version', res.data.version)
          setIterBranch(res.data);
        } else {
          throw Error(res?.errorMsg || `查询${projectName}最近一次迭代失败`);
        }
      }).catch((err) => {
        message.error(err.message, 10);
      })
  }, [projectName, showModal]);

  const onFormSubmit = () => {
    form.submit();
  };

  const onFormCancel = () => {
    form.resetFields();
    setShowModal(false);
  };

  const onFormFinish = (formData: any, errors: any) => {
    if (errors.length > 0 || !iterBranch) return;

    setLoading(true);

    IterApplicationApi.applyRollback({
      iterId: iterBranch.iterId,
      reason: formData.reason
    })
      .then(res => {
        if (res?.success) {
          message.info('已提交回滚申请');
          setShowModal(false);
          setTimeout(() => form.resetFields(), 500); // 迟一点清空，省得闪一下
          onCreateEnd && onCreateEnd(res.data);
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '提交申请失败');
      })
      .finally(() => setLoading(false));
  }

  return <>
    <div onClick={() => setShowModal(true)}>
      {children}
    </div>

    <Modal
      title="提交回滚申请"
      open={showModal}
      onOk={onFormSubmit}
      onCancel={onFormCancel}
      maskClosable={false}
      okButtonProps={{ disabled: !iterBranch || form.errorFields?.length > 0 }}
      width={800}
      destroyOnClose
    >
      <Spin spinning={loading} wrapperClassName="form-wrapper">
        <FormRender
          form={form}
          schema={schema}
          removeHiddenData={false}
          onFinish={onFormFinish}
          watch={{
            projectName: (value: string) => setProjectName(value),
          }}
          displayType="row" />
      </Spin>
    </Modal>
  </>
}

export default Comp;
