.page-container {
  .ant-picker-calendar-header {
    margin-bottom: 40px;
  }

  .date-cell-custom {
    position: relative;
    margin: 0 4px;
    padding: 4px 8px 0;
    height: 110px;
    border-top: 2px solid #f0f0f0;
    transition: background-color 0.3s;
    overflow: hidden;
    color: rgba(0, 0, 0, 0.85);

    &.today {
      border-color: #1890ff;
      background: #e6f7ff;

      .date-value {
        color: #1890ff;
      }
    }

    &.iter-day {
      border-color: #f0f0f0;

      .date-value {
        color: #fff;
      }
    }

    &.disable {
      cursor: default;
      color: #999;

      .iter-disable {
        position: absolute;
        bottom: 8px;
        right: 8px;
      }

      .disable-icon {
        font-size: 16px;
        margin-right: 6px;
        color: rgba(255, 0, 0, 0.3)
      }
    }


    &.mouseenter {
      .iter-create {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .date-value {
      line-height: 24px;
    }

    .iter-info {
      color: #fff;
      text-align: right;
    }

    .iter-status {
      margin: 10px 0 10px;
      font-size: 18px;
      font-weight: bold;
    }

    .iter-version {
      font-size: 16px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .iter-create {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      top: 0;
      opacity: 0;
      transform: translateY(100%);
      transition: transform 0.3s ease-in-out;
      background-color: #F59A23;
      text-align: center;
      line-height: 110px;
      font-size: 18px;
      font-weight: bold;
      color: #fff;
    }

    .description {
      margin-left: 5px;
      font-size: 14px;
    }
  }
}