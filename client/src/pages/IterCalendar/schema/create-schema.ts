export default {
  "type": "object",
  "properties": {
    "creator": {
      "title": "创建人",
      "type": "string",
      "props": {},
      "readOnly": true,
      "width": "100%"
    },
    "publishDay": {
      "title": "计划发布日期",
      "type": "string",
      "props": {},
      "disabled": false,
      "readOnly": true,
      "hidden": false,
      "width": "100%"
    },
    "version": {
      "title": "迭代版本",
      "type": "string",
      "props": {},
      "required": true,
      "width": "90%",
      "rules": [
        {
          "pattern": "^[0-9]+\.[0-9]+\.[0-9]+$",
          "message": "请按照 X.Y.Z 格式填写"
        }
      ]
    },
    "versionSuffix": {
      "title": "版本后缀",
      "type": "string",
      "props": {},
      "width": "90%",
      "description": "一般用作创建 hotfix 版本",
      "rules": [
        {
          "pattern": "^[0-9a-z_\-]+$",
          "message": "限小写字母、数字、中划线、下划线"
        }
      ]
    },
    "qaList": {
      "title": "测试负责人",
      "type": "array",
      "items": {
        "type": "string"
      },
      "props": {},
      "width": "90%",
      "widget": "user",
      "hidden": false
    },
    "description": {
      "title": "迭代描述",
      "type": "string",
      "format": "textarea",
      "props": {},
      "required": true,
      "width": "90%",
      "rules": [
        {
          "max": 100
        }
      ]
    },
    "projectName": {
      "title": "所属项目名称",
      "type": "string",
      "required": true,
      "readOnly": true,
      "hidden": true,
      "props": {}
    }
  },
  "labelWidth": 180,
  "displayType": "row"
};
