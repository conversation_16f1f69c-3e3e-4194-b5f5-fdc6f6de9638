import { useEffect, useState } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { Calendar, Modal, message, Tooltip, Spin } from 'antd';
import { Link, useAccess, history, useModel } from 'umi';
import { QuestionCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import { useForm } from 'form-render';
import FormRender from '@/components/FR';
import IconFont, { iconType } from '@/components/IconFont';
import ProjectSelect from '@/components/ProjectSelect';
import iterCalendarApi from '@/api/iter-calendar';
import iterBranchApi from '@/api/iter-branch';
import { IIterBranch } from '@/interface/iter-branch';
import { IterStatus } from '@/const/iter-branch';
import { EProjectType } from '@/const/project';
import { IProject } from '@/interface/project';
import { getNewAndBiggestVersion } from '@/utils/git';
import createSchema from './schema/create-schema';

import './index.less';

// 组件迭代发布是每周1、3
const COMPONENT_PUBLISH_DAY = [1, 3];

export default () => {
  const today = moment();
  const todayFormatted = today.format('YYYY-MM-DD');
  const [year, setYear] = useState<number>(today.year());
  const [month, setMonth] = useState<number>(today.month() + 1);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [project, setProject] = useState<IProject>();
  const [dateMap, setDateMap] = useState<{ [publishDay: string]: IIterBranch }>({});
  const [loading, setLoading] = useState(true);
  const [modalLoading, setModalLoading] = useState(false);
  const form = useForm();
  const access = useAccess();
  const currentUser = useModel('user');
  const canAccess = access.isAdmin || project?.isAdmin;
  const isComponentProject = project?.type === EProjectType.COMPONENT;
  const { name: projectName, deliverConfig: { gitProjectId = 0 } = {} } = project || {};

  useEffect(refresh, [year, month, projectName])

  function refresh() {
    if (!projectName) return;

    setLoading(true);

    iterCalendarApi.list({ year, month, projectName }).then(res => {
      if (!res || !res.success) throw new Error('查询失败');

      const { iterList, project } = res.data;

      // 以 publishDay 为 key，生成迭代分支的 dateMap
      const newDateMap = iterList.reduce((per: { [publishDay: string]: IIterBranch }, cur) => {
        per[cur.publishDay] = cur;
        return per;
      }, {})
      unstable_batchedUpdates(() => {
        setDateMap(newDateMap);
        setProject(project);
      })
    })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => {
        setLoading(false);
      })
  }

  function onProjectSelect(project: IProject) {
    setProject(project);
  }

  function onDateChange(moment: moment.Moment) {
    setYear(moment.year())
    setMonth(moment.month() + 1)
  }

  const onFormMount = async () => {
    const masterVersion = project?.pkgJSON?.json?.version;
    let moduleTips = '';
    if (isComponentProject && gitProjectId) {
      setModalLoading(true);
      const maxVer = await getNewAndBiggestVersion(gitProjectId);
      moduleTips = `, 现存最大版本号${maxVer}`
      setModalLoading(false);
    }

    if (masterVersion) {
      form.setSchemaByPath("version", { placeholder: `当前 master 版本号 ${masterVersion} ${moduleTips}` })
    }
  }

  const onFormSubmit = () => {
    form.submit();
  };

  const onFormCancel = () => {
    form.resetFields();
    setShowModal(false);
  };

  const onFormFinish = (formData: any, errors: any) => {
    if (errors.length > 0) return;

    setModalLoading(true);

    // 如果有后缀，则拼上
    if (formData.versionSuffix) {
      formData.version += `-${formData.versionSuffix}`
      delete formData.versionSuffix
    }

    // 处理 qaList
    formData.qaList = formData.qaList?.map((item: any) => JSON.parse(item.value)) || [];

    iterBranchApi.create(formData).then(res => {
      if (res?.success && res?.data) {
        message.info('创建成功');
        // 跳转
        history.push(`/iter/detail?iterId=${res.data.iterId}`);
      } else {
        message.error(res?.errorMsg || '创建失败');
        setModalLoading(false)
      }
    })
      .catch((err) => {
        message.error(err.message);
        setModalLoading(false)
      });
  }

  // 日期坑位
  const DateCell = (moment: moment.Moment) => {
    const momentFormatted = moment.format('YYYY-MM-DD');
    const isToday = momentFormatted === todayFormatted;
    const disable = isComponentProject ? COMPONENT_PUBLISH_DAY.indexOf(moment.day()) === -1 : moment.day() !== 4;
    const iterInfo = dateMap[momentFormatted];
    const wrapperStyle: any = { color: '#fff' };

    // 根据迭代分支状态判断显示颜色
    switch (iterInfo?.status) {
      case IterStatus.ABANDON:
        wrapperStyle.backgroundColor = '#7F7F7F'; break;
      case IterStatus.PLAN:
        wrapperStyle.backgroundColor = '#00BFBF'; break;
      case IterStatus.MERGE:
      case IterStatus.DELIVERING:
      case IterStatus.AUDITING:
      case IterStatus.GRAY:
        wrapperStyle.backgroundColor = '#8bc34a'; break;
      case IterStatus.PUBLISHED:
        wrapperStyle.backgroundColor = '#4caf50'; break;
      default: delete wrapperStyle.color;
    }

    function onCreateIter(event: any) {
      event.stopPropagation();

      if (!canAccess) return message.warn('只有管理员能新建迭代！');

      form.setValues({
        publishDay: momentFormatted,
        creator: currentUser.name,
        projectName,
      });
      setShowModal(true)
    }

    return <div
      className={`date-cell-custom ${isToday ? 'today' : ''} ${disable ? 'disable' : ''} ${iterInfo ? 'iter-day' : ''}`}
      style={wrapperStyle}
      onMouseEnter={(e) => e.currentTarget.classList.add('mouseenter')}
      onMouseLeave={(e) => e.currentTarget.classList.remove('mouseenter')}
    >
      <div className="date-value">{isToday ? '今天' : moment.date()}</div>
      {
        // 迭代日
        iterInfo &&
        <Link to={`/iter/detail?iterId=${iterInfo.iterId}`}>
          <div className="iter-info">
            {/* 迭代状态 */}
            <div className="iter-status">{iterInfo.statusText}</div>
            {/* 迭代版本 */}
            <div className="iter-version">
              v{iterInfo.version}
              <Tooltip placement="top" title={iterInfo.description}>
                <QuestionCircleOutlined className="description" />
              </Tooltip>
            </div>
          </div>
        </Link>
      }
      {
        // 非迭代窗口
        !iterInfo && disable && <><div className="iter-disable"><IconFont className="disable-icon" type={iconType.jinzhi} />非发布窗口</div></>
      }
      {
        // 新建迭代
        !iterInfo && (!disable || access.isAdmin) && <div className="iter-create" onClick={onCreateIter}>新建迭代?</div>
      }
    </div>
  }

  return <div className="page-container">
    {/* 项目选择 */}
    <ProjectSelect onSelect={onProjectSelect} style={{ position: 'absolute', top: '24px', left: '24px', zIndex: 9 }} />

    {/* 日历 */}
    <Spin size="large" spinning={loading}>
      <Calendar
        defaultValue={today}
        onChange={onDateChange}
        dateFullCellRender={DateCell} />
    </Spin>

    {/* 新建迭代 */}
    <Modal
      title="新建迭代"
      open={showModal}
      onOk={onFormSubmit}
      onCancel={onFormCancel}
      maskClosable={false}
      okButtonProps={{ disabled: form.errorFields?.length > 0 }}
      width={700}
      destroyOnClose
    >
      <Spin spinning={modalLoading}>
        <FormRender
          form={form}
          removeHiddenData={false}
          onMount={onFormMount}
          onFinish={onFormFinish}
          schema={createSchema}
          displayType="row" />
      </Spin>
    </Modal>
  </div>
};
