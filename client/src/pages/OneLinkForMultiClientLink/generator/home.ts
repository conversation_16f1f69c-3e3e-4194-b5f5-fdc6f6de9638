import qs from 'query-string';
import { BASIC_LINK, CLIENT, MINIAPP_HOME_PATH } from '../constants';
import { IGeneratedMultiLink } from '../types';

export function generateHomeLink(extendQueryStr: string): IGeneratedMultiLink {
  return {
    taobao: generateTaobaoHomeLink(extendQueryStr),
    alipay: generateAlipayHomeLink(extendQueryStr),
    multiClient: generateMultiClientHomeLink(extendQueryStr),
  };
}

function generateAlipayHomeLink(extendQueryStr: string): string {
  if (!extendQueryStr) return BASIC_LINK[CLIENT.ALIPAY];
  const miniappPath = qs.parseUrl(MINIAPP_HOME_PATH[CLIENT.ALIPAY]);
  const extendQueries = qs.parse(extendQueryStr);
  for (const key in extendQueries) {
    miniappPath.query[key] = extendQueries[key];
  }
  return `${BASIC_LINK[CLIENT.ALIPAY]}&page=${encodeURIComponent(qs.stringifyUrl(miniappPath, { strict: false }))}`;
}

function generateTaobaoHomeLink(extendQueryStr: string): string {
  if (!extendQueryStr) return BASIC_LINK[CLIENT.TAOBAO];
  return `${BASIC_LINK[CLIENT.TAOBAO]}&${extendQueryStr}`;
}

function generateMultiClientHomeLink(extendQueryStr: string): string {
  const resultUrl = qs.parseUrl(`${BASIC_LINK[CLIENT.MULTI_CLIENT]}/home`);
  const extendQueries = qs.parse(extendQueryStr);
  for (const key in extendQueries) {
    resultUrl.query[key] = extendQueries[key];
  }
  return qs.stringifyUrl(resultUrl, { strict: false });
}
