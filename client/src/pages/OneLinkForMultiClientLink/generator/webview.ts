import qs from 'query-string';
import { BASIC_LINK, CLIENT, MINIAPP_WEBVIEW_PATH, WEBVIEW_TO_LINK_QUERIES, SHOP_MINIAPP_WEBVIEW_PATH } from '../constants';
import { IGeneratedMultiLink } from '../types';

export function generateShopWebviewLink(url: string, shopAppId: string): IGeneratedMultiLink {
  const miniappPath = `${SHOP_MINIAPP_WEBVIEW_PATH}?url=${encodeURIComponent(url)}`;
  return {
    taobao: '',
    alipay: `alipays://platformapi/startapp?appId=${shopAppId}&page=${encodeURIComponent(miniappPath)}`,
    multiClient: '',
  };
}

export function generateWebviewLink(url: string): IGeneratedMultiLink {
  return {
    taobao: generateTaobaoWebviewLink(url),
    alipay: generateAlipayWebviewLink(url),
    multiClient: generateMultiClientWebviewLink(url),
  };
}

function generateAlipayWebviewLink(url: string): string {
  const miniappPath = `${MINIAPP_WEBVIEW_PATH[CLIENT.ALIPAY]}?url=${encodeURIComponent(url)}`;
  return `${BASIC_LINK[CLIENT.ALIPAY]}&page=${encodeURIComponent(miniappPath)}`;
}

function generateTaobaoWebviewLink(url: string): string {
  const miniappPath = `jump_url=${encodeURIComponent(encodeURIComponent(url))}`;
  return `${BASIC_LINK[CLIENT.TAOBAO]}&${miniappPath}`;
}

function generateMultiClientWebviewLink(url: string): string {
  const resultUrl = qs.parseUrl(`${BASIC_LINK[CLIENT.MULTI_CLIENT]}/webview`);
  resultUrl.query['url'] = encodeURIComponent(url);

  // 部分配置在套壳页里的参数，需要提到多投链接上
  const parsedWebviewUrl = qs.parseUrl(url);
  Object.entries(parsedWebviewUrl.query).forEach(([key, value]) => {
    if (WEBVIEW_TO_LINK_QUERIES.indexOf(key) >= 0) resultUrl.query[key] = value;
  });

  return qs.stringifyUrl(resultUrl, { strict: false });
}
