export enum ROUTES {
  HOME = 'home',
  WEBVIEW = 'webview',
}

export enum CLIENT {
  ALIPAY = 'alipay',
  TAOBAO = 'taobao',
  MULTI_CLIENT = 'multiClient',
}

// 部分待打开套壳页的参数，需要提到多投链接上，否则不生效
export const WEBVIEW_TO_LINK_QUERIES = ['titleBarHidden', 'disableNav'];

export const BASIC_LINK: Record<CLIENT, string> = {
  [CLIENT.ALIPAY]: 'alipays://platformapi/startapp?appId=2018081461095002',
  [CLIENT.TAOBAO]: 'https://outfliggys.m.taobao.com/tb/jump?uniapp_id=1011089&uniapp_page=jump',
  [CLIENT.MULTI_CLIENT]: 'https://router.feizhu.com/multi',
};

export const MINIAPP_HOME_PATH: Record<CLIENT, string> = {
  [CLIENT.ALIPAY]: 'pages/index/index',
  [CLIENT.TAOBAO]: 'pages/index/index',
  [CLIENT.MULTI_CLIENT]: '',
}
export const MINIAPP_WEBVIEW_PATH: Record<CLIENT, string> = {
  [CLIENT.ALIPAY]: 'pages/webview/index',
  [CLIENT.TAOBAO]: 'pages/webview/index',
  [CLIENT.MULTI_CLIENT]: '',
}

export const SHOP_MINIAPP_WEBVIEW_PATH: string = 'pages/h5/index'