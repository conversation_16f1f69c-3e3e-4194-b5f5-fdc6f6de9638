import { useEffect, useState } from 'react';
import { Form, Select, Typography, Input, Button, message, Popover, Space, Divider, Row, Col, Modal, Switch, Alert } from 'antd';
import { CopyOutlined, QrcodeOutlined } from '@ant-design/icons';
import QRCode from 'qrcode.react';
const { Title, Paragraph, Text } = Typography;

import { ROUTES, CLIENT } from './constants';

import './index.less';
import { IGeneratedMultiLink } from './types';
import { generateHomeLink, generateWebviewLink, generateShopWebviewLink } from './generator';

export default () => {
  const [pageType, setPageType] = useState<ROUTES>(ROUTES.WEBVIEW);
  const [pageQuery, setPageQuery] = useState<string>('');
  const [webviewUrl, setWebviewUrl] = useState<string>('');
  const [generatedUrl, setGeneratedUrl] = useState<IGeneratedMultiLink | null>();
  const [confirmCopyModalVisible, setConfirmCopyModalVisible] = useState<boolean>(false);
  const [isShop, setIsShop] = useState<boolean>(false);
  const [shopAppId, setShopAppId] = useState<string>('');
  const [form] = Form.useForm();

  const onChangePageType = (val: ROUTES) => {
    setPageType(val);
    setGeneratedUrl(null);
  };

  const onChangePageQuery = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPageQuery(e.target.value);
  };

  const onChangeWebviewUrl = (e: React.ChangeEvent<HTMLInputElement>) => {
    setWebviewUrl(e.target.value);
  };

  const onChangeIsShop = (e: boolean) => {
    setIsShop(e);
  };

  const onChangeShopAppId = (e: React.ChangeEvent<HTMLInputElement>) => {
    setShopAppId(e.target.value);
  };

  const generateUrl = () => {
    form
      .validateFields()
      .then((res) => {
        // 表单输入正确，生成链接
        let url: IGeneratedMultiLink;
        if (pageType === ROUTES.WEBVIEW) {
          if (isShop) {
            url = generateShopWebviewLink(webviewUrl, shopAppId);
          } else {
            url = generateWebviewLink(webviewUrl);
          }
        } else {
          url = generateHomeLink(pageQuery);
        }
        setGeneratedUrl(url);
      })
      .catch(() => {
        // 表单输入正确，隐藏链接
        setGeneratedUrl(null);
      });
  };

  const handleCopy = (client: CLIENT) => {
    hideModal();
    if (!generatedUrl || !generatedUrl[client]) {
      message.error('链接不存在');
      return;
    }
    navigator.clipboard
      .writeText(generatedUrl[client])
      .then(() => {
        message.success('复制成功');
      })
      .catch((err) => {
        message.error(err?.message || '复制失败！');
      });
  };

  const hideModal = () => {
    setConfirmCopyModalVisible(false);
  };

  const handleMultiEndCopy = () => {
    setConfirmCopyModalVisible(true);
  };

  useEffect(() => {
    window.open('https://fl-flian.fc.alibaba-inc.com/url-schema/record?id=4', '_blank');
  }, []);

  useEffect(() => {
    generateUrl();
  }, [pageType, pageQuery, webviewUrl, isShop, shopAppId]);

  return (
    <div className="one-link-for-multi-client-container">
      <div style={{marginBottom: '16px'}}>
        <Alert type='info' showIcon message="前往飞链平台使用新版多端投放工具" action={<a href="https://fl-flian.fc.alibaba-inc.com/url-schema/record?id=4">立即前往</a>} />
      </div>
      <Title level={3}>多端投放</Title>
      <div className="container">
        <Form className="form-content" labelCol={{ span: 4 }} wrapperCol={{ span: 17 }} form={form}>
          <Form.Item name="page" label="通投页面" required={true}>
            <Select
              options={[
                { value: ROUTES.HOME, label: '首页' },
                { value: ROUTES.WEBVIEW, label: '套壳页' },
              ]}
              value={pageType}
              defaultValue={ROUTES.WEBVIEW}
              onChange={onChangePageType}
            />
          </Form.Item>
          {pageType !== ROUTES.WEBVIEW ? (
            <Form.Item
              name="home-query"
              label="页面参数"
              tooltip="相关参数将会带到页面"
              rules={[{ type: 'string', pattern: /^(?:&?[^=&]*=[^=&]*)*$/g, message: '请输入正确格式的参数' }]}
            >
              <Input
                value={pageQuery}
                onChange={onChangePageQuery}
                placeholder="ttid=12mtb000000111&fpt=ftid(1024)&currentTab=train"
              />
            </Form.Item>
          ) : null}
          {pageType === ROUTES.WEBVIEW ? (
            <Form.Item name="shop-switch" label="是否店铺">
              <Switch onChange={onChangeIsShop} />
            </Form.Item>
          ) : null}
          {isShop ? (
            <Form.Item name="shop-appid" label="店铺appId" tooltip="不清楚可咨询九屻">
              <Input
                value={shopAppId}
                onChange={onChangeShopAppId}
              />
            </Form.Item>
          ) : null}
          {pageType === ROUTES.WEBVIEW ? (
            <Form.Item name="webview-url" label="套壳H5链接" rules={[{ type: 'url' }, { required: true }]}>
              <Input value={webviewUrl} onChange={onChangeWebviewUrl} />
            </Form.Item>
          ) : null}
        </Form>
        {generatedUrl ? (
          <div className="generated-area">
            <Divider plain>链接生成结果</Divider>
            <Row>
              <Col offset={3} span={18}>
                <Paragraph style={{ background: '#F6F6F6', opacity: 0.9 }}>
                  <blockquote style={{ marginTop: 0, marginBottom: '1.5em', padding: '0.5em 1em' }}>
                    <ul>
                      <li>如果只需要单独投放某端，直接使用对应客户端的链接即可。</li>
                      <li style={{ marginTop: 4 }}>
                        一码多投只在投放多端的时候使用，在不同的客户端会拉起各自的小程序。
                        <br />
                        如在淘宝打开会拉起淘宝轻应用，在支付宝打开会拉起支付宝小程序。
                        <br />
                        由于需要判断客户端类型并拉起小程序，打开可能略慢。
                      </li>
                    </ul>
                    <Text type="danger" strong>
                      大流量场景（每小时访问UV超过3w）使用一码通投，请联系 @南麓 、@太吾 进行报备。
                    </Text>
                  </blockquote>
                </Paragraph>
              </Col>
            </Row>
            <Form.Item label="支付宝链接" labelCol={{ span: 4 }} wrapperCol={{ span: 17 }}>
              <Space.Compact block size="large">
                <Input disabled={true} value={generatedUrl[CLIENT.ALIPAY]} className="generated-link-input" />
                <Popover content={<QRCode value={generatedUrl[CLIENT.ALIPAY]} size={150} />} trigger="hover">
                  <Button icon={<QrcodeOutlined />} />
                </Popover>
                <Button
                  type="primary"
                  className="generated-link-button"
                  onClick={() => handleCopy(CLIENT.ALIPAY)}
                  icon={<CopyOutlined />}
                />
              </Space.Compact>
            </Form.Item>
            <Form.Item label="淘宝链接" labelCol={{ span: 4 }} wrapperCol={{ span: 17 }}>
              <Space.Compact block size="large">
                <Input disabled={true} value={generatedUrl[CLIENT.TAOBAO]} className="generated-link-input" />
                <Popover content={<QRCode value={generatedUrl[CLIENT.TAOBAO]} size={150} />} trigger="hover">
                  <Button icon={<QrcodeOutlined />} />
                </Popover>
                <Button
                  type="primary"
                  className="generated-link-button"
                  onClick={() => handleCopy(CLIENT.TAOBAO)}
                  icon={<CopyOutlined />}
                />
              </Space.Compact>
            </Form.Item>
            <Form.Item label="一码通投链接" labelCol={{ span: 4 }} wrapperCol={{ span: 17 }}>
              <Space.Compact block size="large">
                <Input disabled={true} value={generatedUrl?.[CLIENT.MULTI_CLIENT]} className="generated-link-input" />
                <Popover content={<QRCode value={generatedUrl[CLIENT.MULTI_CLIENT]} size={150} />} trigger="hover">
                  <Button icon={<QrcodeOutlined />} />
                </Popover>
                <Button
                  type="primary"
                  className="generated-link-button"
                  onClick={handleMultiEndCopy}
                  icon={<CopyOutlined />}
                />
              </Space.Compact>
            </Form.Item>
          </div>
        ) : null}
      </div>
      <Modal
        title="请确认是否需要报备"
        okText="已报备/不涉及"
        cancelText="取消复制"
        open={confirmCopyModalVisible}
        onOk={() => handleCopy(CLIENT.MULTI_CLIENT)}
        onCancel={hideModal}
      >
        <p>请确认您的业务是否涉及大流量场景（每小时访问UV超过3w）。</p>
        <p>如果涉及，请在报备后再使用一码通投能力</p>
      </Modal>
    </div>
  );
};
