import moment from 'moment';

export default function searchSchema(timeRadioList: any) {
  return {
    type: 'object',
    properties: {
      env: {
        title: '查询环境',
        type: 'string',
        widget: 'RadioGroup',
        default: 'prod',
        props: {
          options: [
            { label: '线上', value: 'prod' },
            { label: '预发', value: 'pre' },
          ],
        },
        width: '45%',
      },
      logType: {
        title: '日志类型',
        type: 'string',
        widget: 'RadioGroup',
        default: 'all',
        props: {
          options: [
            { label: '全部日志', value: 'all' },
            { label: '错误日志', value: 'error' },
          ],
        },
        width: '45%',
      },
      userId: {
        title: '用户Id',
        type: 'string',
        width: '45%',
      },
      userNick: {
        title: '用户名/昵称',
        type: 'string',
        width: '45%',
      },
      pagePath: {
        title: '页面path',
        type: 'string',
        props: {
          placeholder: '/app/trip/rx-member2023/pages/home'
        },
      },
      traceId: {
        title: '鹰眼traceId',
        type: 'string',
        props: {
          placeholder: '752230a617120585899823782e'
        },
      },
      sqlStr: {
        title: '高级搜索',
        type: 'string',
        props: {
          placeholder: "* and userId: '123123' and url: rx-journey-ssr and osName: Ios ",
        },
        description: '搜索预发参照sls日志语法（基本使用可看这里https://aliyuque.antfin.com/remtwr/sh17vq/cd8kktl3gqo688z3?singleDoc# 《SSR-SLS日志查询语法》）'
      },
      timeRadio: {
        title: '快捷查看',
        type: 'string',
        widget: 'RadioGroup',
        props: {
          options: timeRadioList,
        },
      },
      timeRange: {
        title: '选择时间',
        type: 'range',
        format: 'dateTime',
        width: '60%',
        props: {
          placeholder: [
            '开始时间',
            '结束时间'
          ]
        },
        default: [moment().subtract(3, 'minutes').format('YYYY-MM-DD HH:mm:ss'), moment().format('YYYY-MM-DD HH:mm:ss')]
      },
    }, 
  }
};
