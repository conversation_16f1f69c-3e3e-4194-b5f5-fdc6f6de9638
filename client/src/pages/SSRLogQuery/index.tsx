import { useEffect, useState, useRef,useMemo } from 'react';
import { history, useLocation } from 'umi';
import queryString from 'query-string';
import { TableProvider, Table, Search, useTable } from 'table-render';
import { Button, message, Modal, Drawer, Descriptions, Timeline, Collapse, Tag, Alert } from 'antd';
import { BugOutlined } from '@ant-design/icons';
const { Panel } = Collapse;
import moment from 'moment';
import { get } from 'lodash';
import Editor from '../../components/FR/editor';
import RadioGroup from '@/components/FR/radio-group';
import ssrDetail from '@/api/ssr-detail';
import searchSchema from './schema/searchSchema';
import { handleSlsLogData, getHsfTime, getSSRLogItemButtonList, formatErrMsg, isPreCache, isUrcConfig } from './utils';
import './index.less';


const timeRadioList = [
  {
    label: '近1分钟',
    value: '1m',
    timeNumber: 1,
    timeType: 'minute',
  },
  {
    label: '近3分钟',
    value: '3m',
    timeNumber: 3,
    timeType: 'minute',
  },
  {
    label: '近15分钟',
    value: '15m',
    timeNumber: 15,
    timeType: 'minute',
  },
  {
    label: '近30分钟',
    value: '近30分钟',
    timeNumber: 30,
    timeType: 'minute',
  },
  {
    label: '近1小时',
    value: '1h',
    timeNumber: 1,
    timeType: 'hours',
  },
  {
    label: '近24小时',
    value: '24h',
    timeNumber: 24,
    timeType: 'hours',
  },
];

const LogSearch = () => {
  return (
    <TableProvider>
      <TableBody />
    </TableProvider>
  );
};

const TableBody = () => {
  const { refresh, form }: any = useTable();
  const [drawerData, setDrawerData]: any = useState({});
  const [subDrawerData, setSubDrawerData]: any = useState({});
  const [address, setAddress] = useState('');
  const location = useLocation();

  const watch = {
    timeRadio: (val: any) => {
      if (!val) {
        return;
      }
      const {
        timeNumber,
        timeType,
      } = timeRadioList.find(item => item.value === val) || {};
      const startTime = moment().subtract(timeNumber, timeType).format('YYYY-MM-DD HH:mm:ss');
      const endTime = moment().format('YYYY-MM-DD HH:mm:ss');
  
      form.setValueByPath('timeRange', [startTime, endTime]);
    }
  };

  const columns = [
    {
      title: '日志时间',
      dataIndex: 'time',
      width: 120,
      render: (text: any) => {
        return <span>{moment.unix(text).format('YYYY-MM-DD HH:mm:ss')}</span>
      }
    },
    {
      title: 'userId',
      dataIndex: 'userId',
      width: 120,
    },
    {
      title: '页面地址',
      dataIndex: 'url',
      render: (text: string) => {
        return (
          <>
          {
            text && text.length > 100 ? 
            <span>
              {`${text.slice(0, 101)}...`}
              <span 
                className='url-more-div'
                onClick={() => {
                  Modal.success({
                    content: text,
                    maskClosable: true,
                    icon: '',
                  });
                }}
              >
                展开
                <img className='url-more-icon' src='https://gw.alicdn.com/imgextra/i3/O1CN014IaRQb1svH6txyzBk_!!6000000005828-2-tps-10-10.png' />
              </span>
            </span> : 
            <span>{text}</span>
          }
          </>
        )
      }
    },
    {
      title: '预加载状态',
      dataIndex: 'preCacheStatus',
      width: 60,
    },
    {
      title: 'URC状态',
      dataIndex: 'urcStatus',
      width: 160,
    },
    {
      title: '所在容器',
      dataIndex: 'appName',
      width: 60,
    },
    {
      title: '操作系统',
      dataIndex: 'osName',
      width: 80,
    },
    {
      title: 'traceId',
      dataIndex: 'traceId',
      width: 180,
    },
    {
      title: 'ip',
      dataIndex: 'ip',
      width: 180,
      render: (text: string) => {
        return (
          <>
          {
            text && text.length > 20 ? 
            <span>
              {`${text.slice(0, 20)}...`}
              <span 
                className='url-more-div'
                onClick={() => {
                  Modal.success({
                    content: text,
                    maskClosable: true,
                    icon: '',
                  });
                }}
              >
                展开
                <img className='url-more-icon' src='https://gw.alicdn.com/imgextra/i3/O1CN014IaRQb1svH6txyzBk_!!6000000005828-2-tps-10-10.png' />
              </span>
            </span> : 
            <span>{text}</span>
          }
          </>
        )
      }
    },
    {
      title: '操作',
      width: 100,
      fixed: 'right',
      render: (data: any) => {
        console.log("columes-data", data)
        return (
          <a onClick={() => {
            getIpAddressData(data.ip);
            setDrawerData({
              ...formatErrMsg(data || {}),
              show: true,
            })
          }}>
            查看详情
          </a>
        )
      }
    }
  ];

  const getListData = async (params: any) => {
    const {
      env,
      logType,
      userId,
      userNick,
      pagePath,
      traceId,
      sqlStr,
      timeRange,
    } = params;

    const startTime = timeRange[0] && moment(timeRange[0]).valueOf();
    const endTime = timeRange[1] && moment(timeRange[1]).valueOf();

    if (!userId && !userNick && !pagePath && !traceId && !sqlStr) {
      return;
    }

    const result = await ssrDetail.queryLogData({
        env,
        logType,
        userId,
        userNick,
        pagePath,
        traceId,
        sqlStr,
        startTime,
        endTime,
      });

    // 追加预加载的全链路日志查询


    if (!result.success || !result.data) {
      message.error(result.errorMsg || '查询出错');
      
      return {
        rows: [],
        total: 0,
      };
    }

    let list: any = [];
    const erEntryLog = get(result, 'data.erEntryLog.res.body');
    const erLog = get(result, 'data.erLog.res.body');
    const erStreamLog = get(result, 'data.erStreamLog.res.body');
    const ssrLog = get(result, 'data.ssrLog.res.body');
    const preloadLog = get(result, 'data.preloadLog.res.body');
    list = handleSlsLogData({
      env,
      logType,
      erEntryLog,
      erLog,
      erStreamLog,
      ssrLog,
      preloadLog
    });

    // 由于部分日志会在url上带上其他userId，故需要在此过滤掉userId不匹配的日志
    if(userId){
      list = list.filter((r: any) => !r.userId || r.userId === userId)
    }

    console.log("list", list)
    // 对日志追加信息处理
    list = list.map((r: any)=>{
      // 预加载信息
      let preCacheStatus = '无';
      // urc信息
      let urcStatus = '无';

      try{
        let urlObj;
        if(r.url[0] === '"' || r.url[0] === `'`){
          urlObj = new URL(r.url.slice(1, r.url.length - 1));
        }else{
          urlObj = new URL(r.url);
        }

        // 预加载信息判断
        if(urlObj?.searchParams?.get("_pressr")){
          preCacheStatus = '预请求'
        }else if(urlObj?.searchParams?.get("_er_cache")){
          // ER预加载
          preCacheStatus = '命中预加载'
        }else if(r.erLogItem.hit_cache){
          // ER预加载
          preCacheStatus = '命中预加载'
        }else if(r?.preloadLogItem?.isResultMatchPreload){
          // 确实返回预加载资源了
          preCacheStatus = '命中源码预加载'
        }else if(r?.preloadLogItem?.logType === 'preloadHitHasCache'){
          // 源码预加载
          preCacheStatus = '源码预加载匹配命中，但是源码链路更先返回'
        }

        // urc逻辑判断
        const urcConfig = isUrcConfig(r) || [];
        if(urcConfig[0] !== '未命中urc相关配置'){
          urcStatus = urcConfig.join(",")
        }

      }catch(e){
        if(r.url.indexOf("_pressr") !== -1){
          preCacheStatus = '预请求'
        }
      }

      return {...r, preCacheStatus, urcStatus }
    })

    

    const getSearchParams = ()=>{
      const paramsArr: any = {
        env,
        logType,
        userId,
        userNick,
        pagePath,
        traceId,
        sqlStr,
        startTime,
        endTime
      }
      let params: string = '';
      Object.keys(paramsArr).forEach(r=>{
        if(paramsArr[r]){
          params = params + `${params.length <= 1 ? '?' : '&'}${r}=${paramsArr[r]}`
        }
      })
      return params
    }

    // 修改query参数
    history.replace({
      pathname: history.location.pathname,
      search: getSearchParams()
    });
    

    return {
      rows: list.reverse(),
      total: list.length || 0,
    };
  };

  const getIpAddressData = async (ipStr: any) => {
    const ip = ipStr && ipStr.split && ipStr.split(',')[0] || '';
    if (!ip) {
      return '';
    }

    const result = await ssrDetail.queryIpData({ip}).catch(err => { return {} });
    const country = get(result, 'data.country') || '';
    const province = get(result, 'data.province') || '';
    const city = get(result, 'data.city') || '';

    setAddress(`${country}-${province}-${city}`);
  }
  
  useEffect(() => {
    const queryParam = queryString.parse(location.search);

    const searchKeys = [
      'env',
      'logType',
      'userId',
      'userNick',
      'pagePath',
      'traceId',
      'sqlStr',
    ];

    const params: any = {};
    searchKeys.forEach(key => {
      const value = queryParam[key];
      if (value) {
        params[key] = value;
      }
    });

    if (queryParam['startTime'] && queryParam['endTime']) {
      params['timeRange'] = [
        moment(Number(queryParam['startTime'])).format('YYYY-MM-DD HH:mm:ss'),
        moment(Number(queryParam['endTime'])).format('YYYY-MM-DD HH:mm:ss'),
      ];
    }

    form.setValues(params);
  }, []);

  // 对日志做时间轴细分处理
  const timeLine = useMemo(()=>{
    const timeLineArr = [];
    try{
      // SSR阶段日志
      if(drawerData?.erLogItem && drawerData?.erLogItem?.timeStamp){
        const erLogData = drawerData?.erLogItem;
        const { ssrLogItem, preloadLogItem } = drawerData;
        const { timeStamp = '', isForceCache = false, hit_cache} = erLogData;
        const timeStampObj = JSON.parse(timeStamp);
        // ER的参数
        const { er_start, page_er_config_err_time,first_part_start,first_part_end, second_part_duration, second_part_start,last_part_duration, other_part_start, er_cache_start, other_part_end, pre_delete_end, stream_start,last_part_start, er_end, static_cache_duration} = timeStampObj;
        // 源站的参数
        const { faas_req, faas_res, ssr_start, ssr_end} = timeStampObj
        // ER开始
        timeLineArr.push({content: `${moment(er_start).format('YYYY-MM-DD HH:mm:ss')} 用户进入ER`, desc: '', color: 'green'})
        // 超长剪裁
        // timeLineArr.push({content: `url若超长，会进行超长剪裁，页面URL会追加_er_error=urltoolong的标识`, desc: '', color: 'gray'})
        // 命中协商缓存
        if(isForceCache){
          timeLineArr.push({content: `命中协商缓存`, desc: '', color: 'green'});
          timeLineArr.push({content: `${moment(er_end).format('YYYY-MM-DD HH:mm:ss')} ER结束`, desc: '', color: 'green'});
          return timeLineArr;
        }
        // 静态缓存
        if(static_cache_duration){
          timeLineArr.push({content: `命中静态缓存名单`, desc: '', color: 'green'});
          if(hit_cache === '1'){
            timeLineArr.push({content: `返回静态缓存`, desc: '', color: 'green'});
            timeLineArr.push({content: `${moment(er_end).format('YYYY-MM-DD HH:mm:ss')} ER结束`, desc: '', color: 'green'});
            return timeLineArr;
          }
        }
        // 边缘预加载 - cache
        if(pre_delete_end && hit_cache === '1'){
          timeLineArr.push({content: `命中边缘预加载`, desc: '', color: 'green'});
          timeLineArr.push({content: `${moment(er_end).format('YYYY-MM-DD HH:mm:ss')} ER结束`, desc: '', color: 'green'});
          return timeLineArr;
        }
        // ER-主链路
        // 流式链路
        if(stream_start){
          timeLineArr.push({content: `${moment(er_cache_start).format('YYYY-MM-DD HH:mm:ss')} 进入流式逻辑`, desc: '', color: 'green'});
          // 流式配置错误
          if(page_er_config_err_time){
            timeLineArr.push({content: `如果流式配置不存在 或 不开启流式渲染`, desc: '', color: 'green'});
            timeLineArr.push({content: `${moment(er_end).format('YYYY-MM-DD HH:mm:ss')} ER结束`, desc: '', color: 'green'});
            return timeLineArr;
          }
          if(er_cache_start){
            timeLineArr.push({content: `${moment(er_cache_start).format('YYYY-MM-DD HH:mm:ss')} 成功获取首屏配置`, desc: '', color: 'green'});
          }
          if(first_part_start){
            timeLineArr.push({content: `${moment(first_part_start).format('YYYY-MM-DD HH:mm:ss')} 开始获取流式首屏骨架屏以及二三段流式`, desc: '', color: 'green'});
          }
          if(first_part_end){
            timeLineArr.push({content: `${moment(first_part_end).format('YYYY-MM-DD HH:mm:ss')} 成功获取流式首屏骨架屏，耗时${first_part_end - first_part_start}ms`, desc: '', color: 'green'});
          }
          // if(other_part_start){
          //   timeLineArr.push({content: `${moment(other_part_start).format('YYYY-MM-DD HH:mm:ss')} 开始请求二/三屏流式数据`, desc: '', color: 'green'});
          // }
          if(other_part_start && first_part_start){
            timeLineArr.push({content: `${moment(other_part_start).format('YYYY-MM-DD HH:mm:ss')} 一段流式写入完成，耗时${other_part_start - first_part_end}ms`, desc: '', color: 'green'});
          }
          if(typeof second_part_duration === 'number'){
            timeLineArr.push({content: `${moment(second_part_start + second_part_duration).format('YYYY-MM-DD HH:mm:ss')} 二段流式写入成功，耗时${second_part_duration}ms`, desc: '', color: 'green'});
          }
          // if(last_part_start){
          //   timeLineArr.push({content: `${moment(last_part_start).format('YYYY-MM-DD HH:mm:ss')} 开始请求最后一段流式`, desc: '', color: 'green'});
          // }
          if(typeof last_part_duration === 'number'){
            timeLineArr.push({content: `${moment(last_part_start + last_part_duration).format('YYYY-MM-DD HH:mm:ss')} 最后一段流式返回成功，耗时${last_part_duration}ms`, desc: '', color: 'green'});
          }
          if(other_part_end){
            timeLineArr.push({content: `${moment(other_part_end).format('YYYY-MM-DD HH:mm:ss')} 流式结束`, desc: '', color: 'green'});
          }
        }

        // 进入源站逻辑(流式就不展示这部分逻辑)
        if(ssrLogItem && Object.keys(ssrLogItem).length && !other_part_end){
          timeLineArr.push({content: `${moment(ssrLogItem.__time__*1000).format('YYYY-MM-DD HH:mm:ss')} 进入源站逻辑，耗时${ssrLogItem?.allDuration}ms`, desc: '', color: 'green'});
        }

        if(preloadLogItem && get(preloadLogItem, 'isResultMatchPreload')){
          timeLineArr.push({content: `命中源码预加载`, desc: '', color: 'green'});
        }

        if(preloadLogItem && get(preloadLogItem, 'logType') === 'preloadHitHasCache'){
          timeLineArr.push({content: `源码预加载匹配命中，但是源码链路更先返回`, desc: '', color: 'green'});
        }

        // ER结束
        timeLineArr.push({content: `${moment(er_end).format('YYYY-MM-DD HH:mm:ss')} ER结束`, desc: '', color: 'green'});
        return timeLineArr;

        

      }else{
        timeLineArr.push({content: `未成功获取到完整ER日志，请通过traceId查询，放宽时间范围，重新查询，若依旧无结果；存在两种可能：1、url过长，超过8k，被ER侧直接拦截；2、极少量日志丢失`, desc: '', color: 'red',})
      }
    }catch(e){}
    return timeLineArr;
  }, [drawerData])

  return (
    <>
      <Search
        schema={searchSchema(timeRadioList)}
        api={getListData}
        widgets={{ RadioGroup }}
        watch={watch}
      />

      <Table 
        size="small" 
        rowKey="traceId" 
        scroll={{ x: 1300 }} 
        columns={columns} 
        bordered={true} 
        pageChangeWithRequest={false} 
      />

      <Drawer 
        open={drawerData.show}
        width={'70%'}
        title="用户轨迹" 
        placement="right" 
        onClose={() => {
          setDrawerData({ show: false });
        }} 
      >
        <Descriptions title="" bordered>
          <Descriptions.Item label="用户Id">{drawerData.userId || '-'}</Descriptions.Item>
          <Descriptions.Item label="所在容器">{drawerData.appName || '-'}</Descriptions.Item>
          <Descriptions.Item label="操作系统">{drawerData.osName || '-'}</Descriptions.Item>
          <Descriptions.Item label="用户名/昵称">{drawerData.userNick || '-'}</Descriptions.Item>
          <Descriptions.Item label="地址">{address || '-'}</Descriptions.Item>
          <Descriptions.Item label="ip">{drawerData.ip || '-'}</Descriptions.Item>
          <Descriptions.Item label="traceId">{drawerData.traceId || '-'}</Descriptions.Item>
          <Descriptions.Item label="预加载" span={3}>{isPreCache(drawerData) ? <Tag color='green'>{"命中（命中预加载就不会有HSF请求数据返回）"}</Tag> : <Tag color='blue'>{"未命中/或未开通预加载"}</Tag>}</Descriptions.Item>
          <Descriptions.Item label="URC" span={3}>{isUrcConfig(drawerData).map((r:any)=> <Tag color={r === '未命中urc相关配置' ? 'blue' : 'green'}>{r}</Tag>)}</Descriptions.Item>
        </Descriptions>

        <br/>
        <Alert message="部分 ER/HSF 数据缺失？由于日志较多，部分卡在时间点上的日志无法被成功召回，请尝试放宽时间线查询 OR 使用精准traceId查询" type="info" />

        <Timeline style={{ marginTop: '30px' }}>
          {drawerData.erEntryItem && <Timeline.Item color="green">
            <Collapse ghost>
              <Panel key={'erEntryItem'} header={(
                <p>
                  {`${moment.unix(drawerData.erEntryItem.__time__).format('YYYY-MM-DD HH:mm:ss')} 用户进入ER`}
                </p>
              )}>
                <Editor 
                  value={JSON.stringify(drawerData.erEntryItem, null, '\t')} 
                  language="json"  
                  height={260}
                />
              </Panel>
            </Collapse>
          </Timeline.Item>}

          {drawerData.erStreamItem && <Timeline.Item color="red">
            <Collapse ghost>
              <Panel 
                key={'erStreamItem'}
                header={(
                  <>
                    <p>
                      {`${moment.unix(drawerData.erStreamItem.__time__).format('YYYY-MM-DD HH:mm:ss')} 执行流式逻辑`}
                    </p>
                    <p>
                      流式渲染出错：
                      <Tag color='error'>error</Tag>
                    </p>
                    <span>
                      <span>错误原因：</span>
                      <a target="_blank" href="https://aliyuque.antfin.com/remtwr/sh17vq/gllufd38gayqw2w2?singleDoc# 《SSR-问题收集》" style={{fontSize: '12px'}}>更多错误讲解以及示例，请点击这里</a>
                      <div className="error_msg_wrap">{drawerData.erStreamItem.errorMsg || '-'}</div>
                    </span>
                  </>
                )}
                
              >
                <Editor 
                  value={JSON.stringify(drawerData.erStreamItem, null, '\t')} 
                  language="json"  
                  height={260}
                />
              </Panel>
            </Collapse>
          </Timeline.Item>}


          {drawerData.secondSsrLogItem && <Timeline.Item color={drawerData.secondSsrLogItem.success === '1' ? 'green' : 'red'}>
            <Collapse ghost>
              <Panel 
                key={'secondSsrLogItem'}
                header={
                  <>
                    <p>{`${moment.unix(drawerData.secondSsrLogItem.__time__).format('YYYY-MM-DD HH:mm:ss')} 调用SSR函数（二段流式）`}</p>
                    <p>
                      函数调用结果：
                      <Tag color={drawerData.secondSsrLogItem.success === '1' ? 'success' : 'error'}>{drawerData.secondSsrLogItem.success === '1' ? 'success' : 'error'}</Tag>
                    </p>
                    {
                      drawerData.secondSsrLogItem.success === '0' ?
                      <>
                      <p>错误code：<Tag color='error'>{drawerData.secondSsrLogItem.errorCode}</Tag></p>
                      <p>错误原因：<a target="_blank" href="https://aliyuque.antfin.com/remtwr/sh17vq/gllufd38gayqw2w2?singleDoc# 《SSR-问题收集》" style={{fontSize: '12px'}}>更多错误讲解以及示例，请点击这里</a><div className="error_msg_wrap">{drawerData.secondSsrLogItem.errorMsg}</div></p>
                      </> : null
                    }
                    <p style={{ display: 'flex' }}>
                      HSF 耗时：
                      <span>
                      {
                        (getHsfTime(drawerData.secondSsrLogItem.hsfData)['hsfInfoList'] || []).map((item: any) => (
                          <div><Tag color='blue'>{`${item.id || ''}：${item.hsfDuration || '-'}ms`}</Tag></div>
                        ))
                      }
                      </span>
                    </p>
                    <p>
                      Render 耗时：
                      <Tag color='blue'>
                        {
                          Number(drawerData.secondSsrLogItem.perTime)
                          ? `${Number(drawerData.secondSsrLogItem.perTime) - getHsfTime(drawerData.secondSsrLogItem.hsfData)['maxHsfDuration']}ms`
                          : '-ms'
                        }
                      </Tag>
                    </p>
                    <p>
                      函数整体耗时：
                      <Tag color='blue'>{`${drawerData.secondSsrLogItem.allDuration || '-'}ms`}</Tag>
                    </p>
                  </>
                }                
              >
                {
                  getSSRLogItemButtonList(drawerData.secondSsrLogItem).map(item => {
                    return (
                      <Button size="small" type="primary" icon={<BugOutlined />} 
                        shape="round" style={{ marginRight: '5px' }}
                        onClick={() => {
                          setSubDrawerData({
                            show: true,
                            data: item.data,
                          });
                        }}
                      >{item.title}</Button>
                    )
                  })
                }
              </Panel>
            </Collapse>
          </Timeline.Item>}

          {drawerData.ssrLogItem && <Timeline.Item color={drawerData.ssrLogItem.success === '1' ? 'green' : 'red'}>
            <Collapse ghost>
              <Panel 
                key={'ssrLogItem'}
                header={
                  <>
                    <p>{`${moment.unix(drawerData.ssrLogItem.__time__).format('YYYY-MM-DD HH:mm:ss')} 调用SSR函数`}</p>
                    <p>
                      函数调用结果：
                      <Tag color={drawerData.ssrLogItem.success === '1' ? 'success' : 'error'}>{drawerData.ssrLogItem.success === '1' ? 'success' : 'error'}</Tag>
                    </p>
                    {
                      drawerData.ssrLogItem.success === '0' ?
                      <>
                      <p>错误code：<Tag color='error'>{drawerData.ssrLogItem.errorCode}</Tag></p>
                      <p>错误原因：<a target="_blank" href="https://aliyuque.antfin.com/remtwr/sh17vq/gllufd38gayqw2w2?singleDoc# 《SSR-问题收集》" style={{fontSize: '12px'}}>更多错误讲解以及示例，请点击这里</a><div className="error_msg_wrap">{drawerData.ssrLogItem.errorMsg}</div></p>
                      </> : null
                    }
                    <p style={{ display: 'flex' }}>
                      HSF 耗时：
                      <span>
                      {
                        (getHsfTime(drawerData.ssrLogItem.hsfData)['hsfInfoList'] || []).map((item: any) => (
                          <div><Tag color='blue'>{`${item.id || ''}：${item.hsfDuration || '-'}ms`}</Tag></div>
                        ))
                      }
                      </span>
                    </p>
                    <p>
                      Render 耗时：
                      <Tag color='blue'>
                        {
                          Number(drawerData.ssrLogItem.perTime)
                          ? `${Number(drawerData.ssrLogItem.perTime) - getHsfTime(drawerData.ssrLogItem.hsfData)['maxHsfDuration']}ms`
                          : '-ms'
                        }
                      </Tag>
                    </p>
                    <p>
                      函数整体耗时：
                      <Tag color='blue'>{`${drawerData.ssrLogItem.allDuration || '-'}ms`}</Tag>
                    </p>
                  </>
                }                
              >
                {
                  getSSRLogItemButtonList(drawerData.ssrLogItem).map(item => {
                    return (
                      <Button size="small" type="primary" icon={<BugOutlined />} 
                        shape="round" style={{ marginRight: '5px' }}
                        onClick={() => {
                          setSubDrawerData({
                            show: true,
                            data: item.data,
                          });
                        }}
                      >{item.title}</Button>
                    )
                  })
                }
              </Panel>
            </Collapse>
          </Timeline.Item>}


          {drawerData.erLogItem && <Timeline.Item color="green">
            <Collapse ghost>
              <Panel 
                key={'erLogItem'}
                header={
                  <>
                    <p>{`${moment.unix(drawerData.erLogItem.__time__).format('YYYY-MM-DD HH:mm:ss')} ER执行结束`}</p>
                    <p>
                      ER请求SSR函数整体耗时：
                      <Tag color='blue'>{`${drawerData.erLogItem.faas_duration || '-'}ms`}</Tag>
                    </p>
                    <p>
                      ER整体耗时：
                      <Tag color='blue'>{`${drawerData.erLogItem.er_duration || '-'}ms`}</Tag>
                    </p>
                  </>
                }
              >
                <Editor 
                  value={JSON.stringify(drawerData.erLogItem, null, '\t')} 
                  language="json"  
                  height={260}
                />
              </Panel>
            </Collapse>
          </Timeline.Item>}
        </Timeline>

        <Alert message="整体时序链路如下" type="info" style={{marginBottom: '20px'}}/>
        <Timeline>
          {
            timeLine && timeLine.length ? timeLine.map((r: any)=>{
              return  <Timeline.Item color={`${r.color || 'green'}`}>{r.content}</Timeline.Item>
            }) : null
          }
        </Timeline>

      </Drawer>

      <Drawer 
        open={subDrawerData.show}
        width={'50%'}
        title="" 
        placement="right" 
        onClose={() => {
          setSubDrawerData({ show: false });
        }} 
      >
        <Editor 
          value={JSON.stringify(subDrawerData.data, null, '\t')} 
          language="json"  
        />
      </Drawer>
    </>
  )
}

export default LogSearch;