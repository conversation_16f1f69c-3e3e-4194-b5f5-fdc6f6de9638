import { get } from 'lodash';

const handleErEntryLog = (erEntryLog: any, logMergeList: any, allMerge = true) => {
  erEntryLog &&
    Object.keys(erEntryLog)
      .toSorted((a, b) => Number(a) - Number(b))
      .forEach((key) => {
        const erEntryItem = erEntryLog[key] || {};
        const { traceId, appName, osName, ip, userId, __time__, timeStamp } = erEntryItem;

        let url = '';
        try {
          const timeStampObj = JSON.parse(timeStamp);
          url = timeStampObj.erRequestUrl;
        } catch {}

        const currentIndex = logMergeList.findIndex((item: any) => item.traceId === traceId);
        const currentItem = logMergeList[currentIndex];
        if (currentItem) {
          logMergeList.splice(currentIndex, 1, {
            ...currentItem,
            erEntryItem,
          });
        } else if (allMerge) {
          logMergeList.push({
            traceId,
            appName,
            osName,
            ip,
            url,
            userId,
            time: __time__,
            erEntryItem,
          });
        }
      });
};

const handleErStreamLog = (erStreamLog: any, logMergeList: any, allMerge = true) => {
  erStreamLog &&
    Object.keys(erStreamLog)
      .toSorted((a, b) => Number(a) - Number(b))
      .forEach((key) => {
        const erStreamItem = erStreamLog[key] || {};
        const { traceId, reqUrl, userId, __time__ } = erStreamItem;

        const currentIndex = logMergeList.findIndex((item: any) => item.traceId === traceId);
        const currentItem = logMergeList[currentIndex];
        if (currentItem) {
          logMergeList.splice(currentIndex, 1, {
            ...currentItem,
            erStreamItem,
          });
        } else if (allMerge) {
          logMergeList.push({
            traceId,
            url: reqUrl,
            userId,
            time: __time__,
            erStreamItem,
          });
        }
      });
};

const handlePreloadLog = (preloadLog: any, logMergeList: any, allMerge = true) => {
  preloadLog &&
    Object.keys(preloadLog)
      .toSorted((a, b) => Number(a) - Number(b))
      .forEach((key) => {
        const preloadLogItem = preloadLog[key] || {};
        // 返回预加载不做处理，因为如果有返回预加载，必有一个preloadHitHasCache
        if(preloadLogItem.logType === 'resultMatchPreload'){return}
        // 由于preload的数据存入有问题，traceId是"\"123123123\""，所以需要转换一下
        if(preloadLogItem.traceId.indexOf('"') !== -1) {preloadLogItem.traceId = JSON.parse(preloadLogItem.traceId)}
        if(preloadLogItem.href.indexOf('"') !== -1) {preloadLogItem.href = JSON.parse(preloadLogItem.href)}
        if(preloadLogItem.uid.indexOf('"') !== -1) {preloadLogItem.uid = JSON.parse(preloadLogItem.uid)}
        const { traceId, href, uid, __time__ } = preloadLogItem;
        const currentIndex = logMergeList.findIndex((item: any) => item.traceId === traceId );
        const currentItem = logMergeList[currentIndex];
        // 是否最终返回的结果就是预加载的资源，这个需要通过logType：resultMatchPreload的方式确认匹配
        try{
          const isResultMatchPreload = !!Object.keys(preloadLog).find((r: any)=> preloadLog[r].logType === 'resultMatchPreload');
          preloadLogItem.isResultMatchPreload = isResultMatchPreload;
        }catch(e){}
        if (currentItem) {
          logMergeList.splice(currentIndex, 1, {
            ...currentItem,
            preloadLogItem,
          });
        } else if (allMerge) {
          logMergeList.push({
            traceId,
            url: href,
            userId: uid,
            time: __time__,
            preloadLogItem,
          });
        }
      });
};

const handleSsrLog = (ssrLog: any, logMergeList: any, allMerge = true) => {
  ssrLog &&
    Object.keys(ssrLog)
      .toSorted((a, b) => Number(a) - Number(b))
      .forEach((key) => {
        const ssrLogItem = ssrLog[key] || {};
        const { traceId, appName, osName, ip, userId, userNick, url, __time__ } = ssrLogItem;

        const currentIndex = logMergeList.findIndex((item: any) => item.traceId === traceId);
        const currentItem = logMergeList[currentIndex];

        const isSecondSsrLog = url.indexOf('_cache_replace_') !== -1 || url.indexOf('_only_show_second_part') !== -1
        if (currentItem) {
          // 对于三段流式的第二段，不做覆盖处理
          if(isSecondSsrLog){
            logMergeList.splice(currentIndex, 1, {
              ...currentItem,
              secondSsrLogItem: ssrLogItem,
              userNick,
              userId,
              ip,
            });
          }else{
            logMergeList.splice(currentIndex, 1, {
              ...currentItem,
              ssrLogItem,
              userNick,
              userId,
              ip,
            });
          }
        } else if (allMerge) {
          const originData: any = {
            traceId,
            appName,
            osName,
            ip,
            url,
            userId,
            userNick,
            time: __time__,
          }
          if(isSecondSsrLog){
            originData.secondSsrLogItem = ssrLogItem
          }else{
            originData.ssrLogItem = ssrLogItem
          }
          logMergeList.push(originData);
        }
      });
};

const handleErLog = (erLog: any, logMergeList: any, allMerge = true) => {
  erLog &&
    Object.keys(erLog)
      .toSorted((a, b) => Number(a) - Number(b))
      .forEach((key) => {
        const erLogItem = erLog[key] || {};
        const { traceId, appName, osName, ip, url, userId, __time__ } = erLogItem;

        const currentIndex = logMergeList.findIndex((item: any) => item.traceId === traceId);
        const currentItem = logMergeList[currentIndex];
        if (currentItem) {
          logMergeList.splice(currentIndex, 1, {
            ...currentItem,
            erLogItem,
          });
        } else if (allMerge) {
          logMergeList.push({
            traceId,
            appName,
            osName,
            ip,
            url,
            userId,
            time: __time__,
            erLogItem,
          });
        }
      });
};

export const handleSlsLogData = (data: any) => {
  const { env, logType, erEntryLog, erLog, erStreamLog, ssrLog, preloadLog } = data;

  const logMergeList: any = [];

  // 如果是预发 或者 错误日志，则优先使用 SSR日志 作为基底进行整合。否则使用 ER入口日志。
  if (logType === 'error' || env === 'pre') {
    handleSsrLog(ssrLog, logMergeList);
    handleErEntryLog(erEntryLog, logMergeList, false);
    handleErStreamLog(erStreamLog, logMergeList, false);
    handleErLog(erLog, logMergeList, false);
    handlePreloadLog(preloadLog, logMergeList, false);
    return logMergeList;
  }

  handleErEntryLog(erEntryLog, logMergeList);
  handleErStreamLog(erStreamLog, logMergeList);
  handleSsrLog(ssrLog, logMergeList);
  handleErLog(erLog, logMergeList);
  handlePreloadLog(preloadLog, logMergeList);

  return logMergeList;
};

export const getHsfTime = (dataStr: any) => {
  let hsfData: any = {};

  try {
    hsfData = JSON.parse(dataStr) || {};
  } catch {
    hsfData = {};
  }

  const hsfInfoList: any = [];
  let maxHsfDuration = 0;

  Object.keys(hsfData).forEach((key) => {
    const value = hsfData[key] || {};
    const hsfDuration = value.hsfDuration || 0;
    hsfInfoList.push({
      id: key,
      hsfDuration,
    });

    if (hsfDuration > maxHsfDuration) {
      maxHsfDuration = hsfDuration;
    }
  });

  return {
    hsfInfoList,
    maxHsfDuration,
  };
};

export const getSSRLogItemButtonList = (ssrLogItem: any) => {
  const btnList = [];
  if (ssrLogItem) {
    btnList.push({
      title: '查看全部',
      data: ssrLogItem,
    });
  }

  if (ssrLogItem.cookie) {
    let data = {};
    try {
      data = JSON.parse(ssrLogItem.cookie) || {};
    } catch {
      data = {};
    }

    btnList.push({
      title: '查看Cookie',
      data,
    });
  }

  if (ssrLogItem.hsfData) {
    let data = {};
    try {
      data = JSON.parse(ssrLogItem.hsfData) || {};
    } catch {
      data = {};
    }

    btnList.push({
      title: '查看HSF数据',
      data,
    });
  }

  return btnList;
};

// 处理错误信息，丰富补全
export const formatErrMsg = (data: any) => {
  if (!data || typeof data !== 'object' || !Object.keys(data).length) {
    return {};
  }

  const newData = JSON.parse(JSON.stringify(data));

  const newSsrLogItem = get(newData, 'ssrLogItem', {});
  const newErStreamItem = get(newData, 'erStreamItem', {});

  // 源码错误
  if (data.ssrLogItem) {
    const { errorCode = '', errorMsg = '' } = newSsrLogItem;

    if (errorCode === 'FILE_ERROR' && !errorMsg) {
      newData.ssrLogItem.errorMsg = '正常现象，新版本发布或机器轮转会有这种情况，占比均值在千分之0.5';
    }

    if (errorCode === 'RENDER_ERROR') {
      newData.ssrLogItem.errorMsg = '渲染出错，请检查您业务代码，报错提示：' + errorMsg;
    }
  }
  // 流式错误
  if (data.erStreamItem) {
    const { errorMsg = '' } = newErStreamItem;

    if (errorMsg.indexOf('ssrHtml invalid(no cache)') !== -1) {
      newData.erStreamItem.errorMsg =
        '正常现象，ER流式存储在CDN单节点，首次访问会失败，超过有效期也会失败，会自动降级CSR，详细信息：' + errorMsg;
    }

    if (errorMsg.indexOf('ssrHtml invalid(use cache)') !== -1) {
      newData.erStreamItem.errorMsg = '获取流式二段/三段文档失败，强制回源' + errorMsg;
    }

    if (errorMsg.indexOf('completeSsrHtml invalid(use cache)') !== -1) {
      newData.erStreamItem.errorMsg = '流式最后一屏数据获取失败，强制回源' + errorMsg;
    }
  }

  return newData;
};

// 判断是否是命中预加载
export const isPreCache = (data: any) => {
  console.log("isPreCache", data)
  // url.indexOf('_fli_pressr') !== -1    客户端现在已经无法通过_fli_pressr来判断是否命中预加载了，URC也在滥用这个字段
  if (get(data, 'erLogItem.hit_cache', '') === '1' || get(data, 'url', '').indexOf('_er_cache') !== -1 || get(data, 'preloadLogItem.logType', '') === 'preloadHitHasCache' || get(data, 'preCacheStatus','') === '命中预加载') {
    return true;
  }
  return false;
};

// 是否有URC相关策略命中
export const isUrcConfig = (data: any) => {
  if (data && Object.keys(data).length && data.url) {
    const tar = [
      {
        key: '_fli_preload_from=titan',
        value: '泰坦预下载任务、包括refershTime过期更新、位置变化更新',
      },
      {
        key: '_fli_preload_from=publish',
        value: '新发布平台预下载任务',
      },
      {
        key: '_fli_preload_from=intercept',
        value: '拦截：mtop和主文档拦截',
      },
      {
        key: '_fli_preload_from=bridge',
        value: '业务调用桥下载',
      },
      {
        key: '_fli_preload_from=policyUpdate',
        value: '页面更新策略：文档命中缓存更新、页面离开更新',
      },
      {
        key: '_fli_preload_from=native',
        value: 'native业务主动调用的下载任务',
      },
      {
        key: '_fli_preload_from=linkpages',
        value: 'Manifest页面配置的下载任务',
      },
      {
        key: '_fli_preload_from=prefetch',
        value: '路由预请求',
      },
      {
        key: '_fli_preload_from=versionUpdate',
        value: '页面更新策略：manifest 下的页面版本变化',
      },
    ];
    const result:any = [];
    tar.forEach(r=>{
      if(data.url.indexOf(r.key) !== -1){
        result.push(r.value)
      }
    })
    if(result.length){
      return result
    }else{
      return ['未命中urc相关配置'];
    }
  } else {
    return ['未命中urc相关配置'];
  }
};
