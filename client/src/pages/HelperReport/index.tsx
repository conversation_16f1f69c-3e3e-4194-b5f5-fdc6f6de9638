import {useLocation} from 'umi';
import {useState, useEffect, SetStateAction} from 'react';
import {Table, Statistic, Row, Col, Card, Space, Tabs} from 'antd';
import queryString from 'query-string';
import {PlusCircleTwoTone, MinusCircleTwoTone} from "@ant-design/icons";
import {CaseDetail, PackageDetail, TaskDetail} from '@/interface/task-detail';
import taskApi from '@/api/task';

import './index.less';

const { TabPane } = Tabs;

export default () => {
  const location = useLocation();
  const { towerId } = queryString.parse(location.search) as { towerId?: string; };

  const [taskList, setTaskList] = useState<TaskDetail[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalNum, setTotalNum] = useState(0);
  const [successNum, setSuccessNum] = useState(0);
  const [failNum, setFailNum] = useState(0);


  function showTaskStatus(value: number) {
    switch (value) {
      case 1: {
        return (
          <span
            style={{
              backgroundColor: 'grey',
              color: 'white',
              padding: '6px',
              borderRadius: '5px',
              fontSize: '12px',
            }}
          >
            初始化
          </span>
        )
        break
      }
      case 2: {
        return (
          <span
            style={{
              backgroundColor: 'rgb(45, 183, 245)',
              color: 'white',
              padding: '6px',
              borderRadius: '5px',
              fontSize: '12px',
            }}
          >
            执行中
          </span>
        )
        break
      }
      case 3: {
        return (
          <span
            style={{
              backgroundColor: 'rgb(135, 208, 104)',
              color: 'white',
              padding: '6px',
              borderRadius: '5px',
              fontSize: '12px',
            }}
          >
            执行成功
          </span>
        )
        break
      }
      case 4: {
        return (
          <span
            style={{
              backgroundColor: 'red',
              color: 'white',
              padding: '6px',
              borderRadius: '5px',
              fontSize: '12px',
            }}
          >
            执行失败
          </span>
        )
        break
      }
      default: {
        return <span>状态异常</span>
      }
    }
  }

  function showCaseResult(value: boolean) {
    switch (value) {
      case true: {
        return (
          <span
            style={{
              backgroundColor: 'rgb(135, 208, 104)',
              color: 'white',
              padding: '6px',
              borderRadius: '5px',
              fontSize: '12px',
            }}
          >
            校验成功
          </span>
        )
        break
      }
      case false: {
        return (
          <span
            style={{
              backgroundColor: 'red',
              color: 'white',
              padding: '6px',
              borderRadius: '5px',
              fontSize: '12px',
            }}
          >
            校验失败
          </span>
        )
        break
      }
      default: {
        return <span>无结果</span>
      }
    }
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: '10%',
    },
    {
      title: '任务名',
      dataIndex: 'taskName',
      key: 'taskName',
      width: '30%',
    },
    {
      title: '任务结果',
      dataIndex: 'h5Url',
      width: '20%',
      key: 'action',
      render: (value: string, record: any) => (
        record.taskType == "自动化UI检查" ? (
          <div>
            <Space size="middle">
              {showTaskStatus(record.taskStatus)}
              {showCaseResult(record.result)}
            </Space>
            <div style={{marginTop:'10px'}}>
              <span style={{fontSize:'16px', fontWeight: 'bold',color:'rgb(135, 208, 104)'}}>{record.successNum}</span>(成功) /
              <span style={{fontSize:'16px', fontWeight: 'bold',color:'#ffc53d'}}> {record.totalNum}</span>(总量)
            </div>
          </div>
        ) : (
          <Space size="middle">
            {showTaskStatus(record.taskStatus)}
            {showCaseResult(record.result)}
          </Space>
        )
      ),
    },
    {
      title: '详情',
      dataIndex: 'h5Url',
      width: '40%',
      render: (value: string, record: any) => (
        <Space size="middle">
          {showDetail(record)}
        </Space>
      )
    },
  ];

  function showDetail(record:any){
    switch (record.taskType) {
      case "自动化UI检查": {
        return (
            <a target="_blank" href={record.resultUrl}>任务明细</a>
        )
        break
      }
      case "包大小检查": {
        return (
          <span>
            <span style={{fontSize:'16px', fontWeight: 'bold',color:'rgb(135, 208, 104)'}}>{record.standardResult} KB</span>(标准) /
            <span style={{fontSize:'16px', fontWeight: 'bold',color:'#ffc53d'}}>{record.realResult} KB</span> (实际)
          </span>
        )
        break
      }
      default: {
        return (
          <a target="_blank" href={record.resultUrl}>任务明细</a>
        )
      }
    }
  }

  function expandedRowTable(data: CaseDetail[]) {
    const columns = [
      {title: 'case名称', dataIndex: 'caseName', key: 'caseName', width: '40%'},
      {
        title: '验证结果', dataIndex: 'result', key: 'result', width: '20%', render: (value: string, record: any) => (
          <Space size="middle">
            {showCaseResult(record.result)}
          </Space>
        ),
      },
      {title: '错误信息', dataIndex: 'msg', key: 'msg', width: '40%'},
    ];

    return data == null || data.length == 0 ? null :
      <Table columns={columns} dataSource={data} pagination={false} rowClassName="table-component"/>;
  };

  function expandedPackageRowTable(data: PackageDetail[]) {
    const columns = [
      {title: '业务线', dataIndex: 'bizLine', key: 'bizLine', width: '40%'},
      {title: '子包名', dataIndex: 'subPackageName', key: 'subPackageName', width: '20%'},
      {title: '包大小', dataIndex: 'buildHumanSize', key: 'buildHumanSize', width: '40%'},
    ];

    return data == null || data.length == 0 ? null :
      <Table columns={columns} dataSource={data} pagination={false} rowClassName="table-component"/>;
  };

  useEffect(fetchData, [])

  function fetchData() {
    setLoading(true);

    taskApi.getTaskResult(towerId).then(res => {
      if (!res || !res.data || !res.data.success) throw Error('empty');
      setTaskList(res.data.data.miniAppResultVOList);
      setTotalNum(res.data.data.totalCount);
      setSuccessNum(res.data.data.successCount);
      setFailNum(res.data.data.failCount);

    })
      .catch(err => {
        console.log("error:" + err);
        setTaskList([])
      })
      .finally(() => setLoading(false))
  }

  function showTable (value: number){

    const taskUI: SetStateAction<TaskDetail[]> = [];
    const taskBAO: SetStateAction<TaskDetail[]> = [];
    const taskAUTO: SetStateAction<TaskDetail[]> = [];
    const taskPerformance: SetStateAction<TaskDetail[]> = [];
    taskList.map((item, i) => { 
      switch (item.taskType) {
        case "自动化UI检查": {
          taskUI.push(item);
          break
        }
        case "包大小检查": {
          taskBAO.push(item);
          break
        }
        case "奥特自动化": {
          taskAUTO.push(item);
          break
        }
        case "性能自动化": {
          taskPerformance.push(item);
          break
        }
        default: {
        }
      } 
    })

    let taskListResult: SetStateAction<TaskDetail[]> = [];
    let tabName = "自动化测试结果";
    switch (value) {
      case 1: {
        taskListResult = taskUI;
        tabName = "自动化UI检查";
        break
      }
      case 2: {
        taskListResult = taskBAO;
        tabName = "包大小检查";
        break
      }
      case 3: {
        taskListResult = taskAUTO;
        tabName = "奥特自动化";
        break
      }
      case 4: {
        taskListResult = taskPerformance;
        tabName = "性能自动化";
        break
      }
      default: {
      }
    } 
    if(taskListResult.length == 0) {
      return null;
    }
    return (
        <TabPane tab={tabName} key={value}>
          <Table
            loading={loading}
            columns={columns}
            dataSource={taskListResult}
            rowClassName="table-component"
            rowKey={record => record.id}
            expandable={{
              expandedRowRender: record => record.taskType == '自动化UI检查' ? expandedRowTable(record.taskDetail as CaseDetail[]) : expandedPackageRowTable(record.taskDetail as PackageDetail[]),
              expandIcon: ({expanded, onExpand, record}) => {
                if (record.taskType !== "自动化UI检查" && record.taskType !== "包大小检查")
                {
                  return null;
                }
                return expanded ? (
                  <MinusCircleTwoTone onClick={e => onExpand(record, e)}/>
                ) : (
                  <PlusCircleTwoTone onClick={e => onExpand(record, e)}/>
                )
              },
              expandRowByClick: true,
              rowExpandable: ((record) => {
                if (record.taskType === "自动化UI检查" || record.taskType === "包大小检查")
                {
                  return true;
                }
                return false;
              })
            }}
          />
        </TabPane>
    )

  }
  return (
    <div className="helper-report-container">
      <Row gutter={16} style={{marginBottom:'20px'}}>
        <Col span={8}>
          <Card style={{textAlign: 'center'}}>
            <Statistic title={<h2>执行总任务量</h2>} value={totalNum} valueStyle={{
              color: '#ffc53d',
            }}/>
          </Card>
        </Col>
        <Col span={8}>
          <Card style={{textAlign: 'center'}}>
            <Statistic title={<h2>任务成功量</h2>} value={successNum} valueStyle={{
              color: '#3f8600',
            }}/>
          </Card>
        </Col>
        <Col span={8}>
          <Card style={{textAlign: 'center'}}>
            <Statistic title={<h2>任务失败量</h2>} value={failNum} valueStyle={{
              color: '#cf1322',
            }}/>
          </Card>
        </Col>
      </Row>

      <Tabs defaultActiveKey="1" type="card" size='large'>
        {showTable(1)}
        {showTable(2)}
        {showTable(3)}
        {showTable(4)}
      </Tabs>

    </div>
  );
}
