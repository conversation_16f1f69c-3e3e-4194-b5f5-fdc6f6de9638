import { useState } from 'react';
import { Input, message, Alert } from 'antd';
import ShortLinkApi from '@/api/short-link';

const { Search } = Input;

import './index.less';

export default () => {
  const [shortLink, setShortLink] = useState('');
  const onSearch = (value: string) => {
    if (!value) {
      message.error('请输入正确的url');
      return;
    }
    ShortLinkApi.creat({ longLink: value }).then(res => {
      if (res.success) {
        setShortLink(res?.data?.shortKey);
      }
    });
  };

  return (
    <div className="short-link-container">
      <Alert message="短链默认一年有效期，请注意使用场景！" type="warning" showIcon />

      <div className="content">
        <h3>短链生成</h3>

        <div className="long-link">
          <Search
            style={{
              width: '1000px'
            }}
            placeholder="请输入正确的页面链接"
            allowClear
            enterButton="生成"
            size="large"
            onSearch={onSearch}
          />
        </div>
        {
          shortLink && <div className="short-link-result">
            <h3>短链地址</h3>
            <div>{shortLink}</div>
          </div>
        }
      </div>
    </div>
  );
}
