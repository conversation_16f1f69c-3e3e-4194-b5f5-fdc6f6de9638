import { useEffect, useState } from 'react';
import { List, message, Form, Card, Button, InputNumber, Select, Input } from 'antd';
import SSRApi from '@/api/ssr-detail';
import { useModel } from 'umi';
import { formateDate } from '@/utils/date';

export default () => {
  const [form] = Form.useForm();
  const [config, setConfig] = useState<any>([]);
  const currentUser = useModel('user');

  const onConfirm = () => {
    form.validateFields().then(formRes => {
      SSRApi.preUpdate(formRes).then(res => {
        message.success('修改成功');
      })
    });
  };

  const onReset = () => {
    SSRApi.preUpdate({reset: true}).then(res => {
      message.success('重置成功');
    })
  }

  useEffect(() => {
    SSRApi.preUpdate({}).then(res => {
      form.setFieldsValue({
        open: res.data.open || false,
        timeRange: res.data.timeRange || 30,
        whiteList: res.data.whiteList || '',
        tStart: res.data.tStart || '09:00:00',
        tEnd: res.data.tEnd || '09:00:00'
      })
      if (res.data.timeRange) {
        const list = (res.data.list || []).map((item:any) => {
          return `页面路径：${item.url}；刷新时间：${item.refreshTime}；是否在当前任务：${item.isUpdate ? '是' : '否'}`
        })
        setConfig([
          `开关：${res.data.open ? '开启' : '关闭'}`,
          `灰度进度：${res.data.ratio}`,
          `灰度间隔：${res.data.timeRange}分钟`,
          `上次更新时间：${formateDate('YYYY-MM-DD hh:mm:ss', new Date(res.data.updateTime))}`,
          `页面白名单：${res.data.whiteList || ''}`,
          '页面列表：',
        ].concat(list));
      }
    })
  }, [])

  return (
    <>
    <Card title='预加载当前配置' style={{marginBottom: '12px'}}>
      <List
        size="small"
        dataSource={config}
        renderItem={item => (
          <List.Item>
            {item}
          </List.Item>
        )}
      />
    </Card>
    {['205521', '359190'].includes(currentUser.workid) ? <Card title="预加载更新配置">
      <Form
        name="basic"
        form={form}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 12 }}
        initialValues={{ handleType: 'get' }}
        style={{ width: '60%' }}
      >

        <Form.Item
          label="开关"
          name="open"
          rules={[{ required: true }]}
        >
          <Select
              options={[
                { value: true, label: '开启' },
                { value: false, label: '关闭' }
              ]}
            />
        </Form.Item>

        <Form.Item
          label="时长"
          name="timeRange"
          rules={[{ required: true }]}
        >
          <InputNumber style={{width: '100%'}} />
        </Form.Item>

        <Form.Item
          label="修改灰度比例"
          name="ratio"
        >
          <InputNumber style={{width: '100%'}} />
        </Form.Item>

        <Form.Item
          label="白名单"
          name="whiteList"
        >
          <Input style={{width: '100%'}} />
        </Form.Item>

        <Form.Item
          label="任务开始时间"
          name="tStart"
        >
          <Input style={{width: '100%'}} />
        </Form.Item>

        <Form.Item
          label="任务结束时间"
          name="tEnd"
        >
          <Input style={{width: '100%'}} />
        </Form.Item>

        <Form.Item
          label="开启任务页面"
          name="startPath"
        >
          <Input style={{width: '100%'}} />
        </Form.Item>

        <Form.Item
          label="删除任务页面"
          name="deletePath"
        >
          <Input style={{width: '100%'}} />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" onClick={onConfirm}>提交</Button>
          <Button style={{marginLeft: 12}} onClick={onReset}>任务重置</Button>
        </Form.Item>
      </Form>
    </Card> : null}
    </>
  );
}
