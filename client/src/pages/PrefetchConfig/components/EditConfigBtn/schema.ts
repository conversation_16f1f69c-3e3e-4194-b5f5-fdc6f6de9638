export default {
  "type": "object",
  "properties": {
    "path": {
      "title": "页面path",
      "type": "string",
      "displayType": "row",
      "props": {},
      "required": true
    },
    "needLogin": {
      "title": "是否需要登陆",
      "type": "boolean",
      "widget": "checkbox",
    },
    "mtopApi": {
      "title": "mtopApi",
      "type": "string",
      "displayType": "row",
      "props": {},
      "width": "50%"
    },
    "mtopVersion": {
      "title": "mtopVersion",
      "type": "string",
      "props": {},
      "width": "50%"
    },
    "verifyKeys": {
      "title": "强匹配参数",
      "description": "强匹配参数，匹配不上不返回数据",
      "type": "array",
      "widget": "wList",
      "props": {
        "action": "",
        "addBtnName": "添加强匹配参数",
        "items": {
          "name": {
            "title": "key",
            "placeholder": "",
            "validator": (value: string) => {
              let errMsg;

              if (!value) {
                errMsg = '请填写key';
              }

              return {
                error: errMsg && new Error(errMsg)
              }
            }
          }
        }
      },
      "width": "100%"
    },
    "staticParams": {
      "title": "获取数据时静态参数",
      "type": "string",
      "format": "textarea",
      "description": "将对象JSON.stringify后填入",
      "placeholder": "",
      "props": {}
    },
    "dynamicParams": {
      "title": "动态参数",
      "description": "获取数据时需要从query中获取的动态参数",
      "type": "array",
      "widget": "wList",
      "props": {
        "action": "",
        "addBtnName": "添加动态参数",
        "items": {
          "urlParamKey": {
            "title": "urlParamKey",
            "placeholder": "url Pramas中key名",
            "validator": (value: string) => {
              let errMsg;

              if (!value) {
                errMsg = '请填写key';
              }

              return {
                error: errMsg && new Error(errMsg)
              }
            }
          },
          "actuallyParamKey": {
            "title": "actuallyParamKey",
            "placeholder": "实际接口需要key名",
            "validator": (value: string) => {
              let errMsg;

              if (!value) {
                errMsg = '请填写key';
              }

              return {
                error: errMsg && new Error(errMsg)
              }
            }
          }
        }
      },
      "width": "100%",
    },
    "isGray": {
      "title": "是否灰度",
      "type": "boolean",
      "widget": "checkbox",
      "width": "100%"
    },
    "grayNumber": {
      "title": "灰度进度",
      "description": "灰度进度",
      "type": "number",
      "widget": "stepSlider",
      "props": {
        "marks": {
          0: '集团内灰',
          25: '外灰1%',
          50: '外灰10%',
          75: "外灰30%",
          100: "外灰50%"
        }
      },
      "width": "100%",
      "hidden": "{{formData.isGray !== true}}",
    },
    "expiryTimeMs": {
      "title": "过期时间(ms)",
      "type": "number",
      "width": "50%"
    },
    "mtopTimeOutMs": {
      "title": "获取mtop时的超时时间(ms)",
      "type": "number",
      "width": "50%"
    },
    "needGeolocation": {
      "title": "是否需要定位信息",
      "type": "boolean",
      "widget": "checkbox",
      "width": "50%"
    },
    "isNeedGetThenRemove": {
      "title": "是否获取完就删除",
      "type": "boolean",
      "widget": "checkbox",
      "width": "50%"
    },
    "hsfId": {
      "title": "hsf id",
      "type": "string",
      "readOnly": true,
    },
    "hsfMethod": {
      "title": "hsf method",
      "type": "string",
      "readOnly": true,
    },
    "hsfParameterTypes": {
      "title": "hsf parameterTypes",
      "type": "string",
      "readOnly": true,
    },
    "hsfParamsObj": {
      "title": "hsf调用参数结构",
      "description": "hsf调用参数结构（冷启动场景填写）",
      "type": "array",
      "widget": "wList",
      "props": {
        "readOnly": true,
        "action": "",
        "addBtnName": "添加hsf参数",
        "items": {
          "key": {
            "title": "hsf params key",
            "placeholder": "hsf params key",
            "validator": (value: string) => {
              let errMsg;

              if (!value) {
                errMsg = '请填写key';
              }

              return {
                error: errMsg && new Error(errMsg)
              }
            }
          },
          "defaultValue": {
            "title": "默认值",
            "placeholder": "填写默认值，不填为空默认为空字符串",
          }
        }
      },
      "width": "100%"
    }
  },
  "labelWidth": 180,
  "displayType": "row"
};
