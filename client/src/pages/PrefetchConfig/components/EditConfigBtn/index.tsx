import { Fragment, useState, useMemo, useEffect } from 'react';
import { Modal, message, Spin, Button, Input } from 'antd';
import { useForm } from 'form-render';
import _ from 'lodash';
import FormRender from '@/components/FR/index';
import ReactJson from 'react-json-view'
import { IPrefetchConfig, IPrefetchDynamicKeys, IPrefetchHsfParams } from '@/interface/prefetch-config';
import prefetchConfigApi, {
  IPrefetchConfigUpdateRes,
  IPrefetchConfigCreateRes,
  IPrefetchConfigUpdateReq,
  IPrefetchConfigCreateReq,
} from '@/api/prefetch-config';
import schema from './schema';
import newSchema from './new-schema';

import './index.less';

export enum Action {
  Create = 'create', // 创建
  Update = 'update', // 更新
  Read = 'read', // 查看
}
interface Props extends React.HTMLAttributes<HTMLDivElement> {
  action: Action;
  onEditEnd?: (action: Action, res: any) => void;
  projectName: string;
  prefetchConfig?: IPrefetchConfig;
  setLoading?: (loading: any) => void;
}

export default ({ prefetchConfig, children, action, onEditEnd, projectName, setLoading}: Props) => {
  const id = prefetchConfig?.id;
  const [showModal, setShowModal] = useState(false);
  const [showNewModal, setShowNewModal] = useState(true);
  const [showTest, setShowTest] = useState(false);
  const [path, setPath] = useState('');
  const [query, setQuery] = useState('');
  const [testCode, setTestCode] = useState('');
  const [testUserId, setTestUserId] = useState('');
  const [testLongitude, setTestLongitude] = useState('');
  const [testLatitude, setTestLatitude] = useState('');
  const [testData, setTestData] = useState(null);
  const [testLoading, setTestLoading] = useState(false);

  const form = useForm();

  // 行动名称
  let actionName = '';
  if (action === Action.Create) actionName = '创建';
  else if (action === Action.Update) actionName = '更新';
  else if (action === Action.Read) actionName = '查看';

  const onFormMount = () => {
    // 当有值时，回填表单
    if (prefetchConfig) {
      form.setValues(dataToFormData(prefetchConfig));
    }
  };

  const onFormSubmit = () => {
    form.submit();
  };

  const onFormCancel = () => {
    form.resetFields();
    setShowModal(false);
  };

  const onFormFinish = (formData: IPrefetchConfig, errors: any) => {
    formData.projectName = projectName;

    setLoading && setLoading(true);
    console.log(formDataHandle(formData), errors);

    if (errors.length > 0) {
      setLoading && setLoading(false);
      return;
    }

    prefetchConfigApi[action === Action.Update ? 'update' : 'create'](formDataHandle(formData))
      .then((res: void | IPrefetchConfigUpdateRes | IPrefetchConfigCreateRes) => {
        if (res?.success) {
          message.info(`${actionName}成功`);
          setShowModal(false);
          setTimeout(() => form.resetFields(), 500); // 迟一点清空，省得闪一下
          onEditEnd && onEditEnd(action, res);
        } else {
          message.error(res?.errorMsg || `${actionName}失败`);
        }
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => setLoading && setLoading(false));
  };

  const formDataHandle = (formData: IPrefetchConfig): IPrefetchConfigUpdateReq => {
    let formDataChangeItems: IPrefetchConfigCreateReq = {
      projectName: formData.projectName,
      path: formData.path,
      needLogin: formData.needLogin ? 1 : 0,
      needGeolocation: 1,
      hasColdStart: 1,
      notColdPrefetch: formData.notColdPrefetch ? 1 : 0,
      verifyKeys: '',
      dynamicParams: '',
      hsfParamsObj: '',
      isGray: formData.isGray ? 1 : 0,
      grayNumber: formData.isGray ? formData.grayNumber || 0 : 0,
      paramsHandleFunc: formData.paramsHandleFunc,
      checkParamsFunc: formData.checkParamsFunc,
      mtopApi: formData.mtopData ? formData.mtopData.mtopApi : '',
      mtopVersion: formData.mtopData ? formData.mtopData.mtopVersion : '',
      hsfId: formData.mtopData ? formData?.mtopData?.hsfData?.hsfId : '',
      hsfMethod: formData.mtopData ? formData?.mtopData?.hsfData?.hsfMethod : '',
      hsfParameterTypes: formData.mtopData ? formData?.mtopData?.hsfData?.hsfParameterTypes : '',
      hsfParamsMapping: formData.mtopData ? formData?.mtopData?.hsfData?.hsfParamsMapping : '',
      mtopParamsMapping: formData.mtopData ? formData?.mtopData?.hsfData?.mtopParamsMapping : '',
    };
    if (formData.verifyKeys && formData.verifyKeys.length) {
      formData.verifyKeys.map((verifyKey: any, index: number) => {
        // 多个用,隔开
        if (index) {
          formDataChangeItems.verifyKeys += `,${verifyKey.name}`;
        } else {
          formDataChangeItems.verifyKeys = verifyKey.name;
        }
      });
    }

    if (formData.dynamicParams && formData.dynamicParams.length) {
      // 多个用,隔开，url param:实际param
      formData.dynamicParams.map((dynamicParam: any, index: number) => {
        if (index) {
          formDataChangeItems.dynamicParams += `,${dynamicParam.urlParamKey || ''}:${dynamicParam.actuallyParamKey || ''
            }`;
        } else {
          formDataChangeItems.dynamicParams = `${dynamicParam.urlParamKey || ''}:${dynamicParam.actuallyParamKey || ''
            }`;
        }
      });
    }

    if (formData.hsfParamsObj && formData.hsfParamsObj.length) {
      // 多个用,隔开，key:defaultValue
      formData.hsfParamsObj.map((hsfParamObj: any, index: number) => {
        if (index) {
          formDataChangeItems.hsfParamsObj += `,${hsfParamObj.key || ''}:${hsfParamObj.defaultValue || ''}`;
        } else {
          formDataChangeItems.hsfParamsObj = `${hsfParamObj.key || ''}:${hsfParamObj.defaultValue || ''}`;
        }
      });
    }

    return Object.assign({}, formData, formDataChangeItems);
  };

  const dataToFormData = (prefetchConfig: any) => {
    let formData: any = null;
    formData = {
      id: prefetchConfig.id,
      path: prefetchConfig.path,
      mtopApi: prefetchConfig.mtopApi,
      mtopVersion: prefetchConfig.mtopVersion,
      staticParams: prefetchConfig.staticParams,
      needGeolocation: true,
      expiryTimeMs: prefetchConfig.expiryTimeMs,
      isNeedGetThenRemove: !!prefetchConfig.isNeedGetThenRemove,
      mtopTimeOutMs: prefetchConfig.mtopTimeOutMs,
      hasColdStart: 1,
      notColdPrefetch: !!prefetchConfig.notColdPrefetch,
      isGray: !!prefetchConfig.isGray,
      grayNumber: prefetchConfig.grayNumber,
      paramsHandleFunc: prefetchConfig.paramsHandleFunc || '',
      checkParamsFunc: prefetchConfig.checkParamsFunc || '',
      mtopData: {
        mtopApi: prefetchConfig.mtopApi,
        mtopVersion: prefetchConfig.mtopVersion,
        hsfData: {
          hsfId: prefetchConfig.hsfId,
          hsfMethod: prefetchConfig.hsfMethod,
          hsfParameterTypes: prefetchConfig.hsfParameterTypes,
          hsfParamsMapping: prefetchConfig?.hsfParamsMapping,
          mtopParamsMapping: prefetchConfig?.mtopParamsMapping,
        }
      }
    };

    if (prefetchConfig.verifyKeys) {
      const verifyKeyArr = prefetchConfig.verifyKeys.split(',').map((item: string) => {
        return { name: item };
      });
      formData.verifyKeys = verifyKeyArr;
    } else {
      formData.verifyKeys = [];
    }

    if (prefetchConfig.dynamicParams) {
      const dynamicParamsArr = prefetchConfig.dynamicParams.split(',').map((dynamicParam: string) => {
        const dynamicParamKeyValue = dynamicParam.split(':');
        return {
          urlParamKey: dynamicParamKeyValue[0],
          actuallyParamKey: dynamicParamKeyValue[1],
        };
      });
      formData.dynamicParams = dynamicParamsArr;
    } else {
      formData.dynamicParams = [];
    }

    if (prefetchConfig.hsfParamsObj) {
      const hsfParamsObjArr = prefetchConfig.hsfParamsObj.split(',').map((hsfParam: string) => {
        const hsfParamKeyValue = hsfParam.split(':');
        return {
          key: hsfParamKeyValue[0],
          defaultValue: hsfParamKeyValue[1],
        };
      });
      formData.hsfParamsObj = hsfParamsObjArr;
    } else {
      formData.hsfParamsObj = [];
    }

    return formData;
  };

  function extendsSchema(originSchema: typeof schema) {
    const newSchema = _.cloneDeep(originSchema);

    if (action === Action.Create || action === Action.Update) {
      newSchema.properties.verifyKeys.props.action = action;
      newSchema.properties.dynamicParams.props.action = action;
      newSchema.properties.hsfParamsObj.props.action = action;
    }

    return newSchema;
  }

  function extendsNewSchema(originSchema: typeof newSchema) {
    const newSchema = _.cloneDeep(originSchema);

    return newSchema;
  }

  function changeShowModalFunc() {
    setShowNewModal(!showNewModal);
  }

  function onFormTest() {
    if (path && path.length > 8) {
      setShowTest(true);
    } else {
      message.error('请输入正确的页面path');
    }
  }

  function onTestSubmit() {
    setTestLoading(true);
    prefetchConfigApi['getPrefetchData']({
      path,
      query,
      code: testCode,
      userId: testUserId,
      end: projectName,
      longitude: testLongitude,
      latitude: testLatitude,
      id
    })
      .then((res: void | any) => {
        setTestLoading(false);
        const success = res?.success;
        if (success) {
          const data = res?.invokeData?.invokeData?.data;
          const useTime = res?.invokeData?.invokeData?.useTime;
          setTestData(data);
          console.log(data, useTime);
        } else {
          message.error(JSON.stringify(res));
        }
        console.log(res);
      })
      .catch((err) => {
        setTestLoading(false);
        message.error(err.message);
      })
  }

  function onTestCancel() {
    setShowTest(false);
  }

  function onTestQueryChange(val: string) {
    setQuery(val);
  }

  function onTestUserIdChange(val: string) {
    setTestUserId(val);
  }

  function onTestLongitudeChange(val: string) {
    setTestLongitude(val);
  }

  function onTestLatitudeChange(val: string) {
    setTestLatitude(val);
  }

  const watch = {
    path: (val: any) => {
      setPath(val);
    },
  };

  return (
    <>
      <div
        onClick={() => {
          setShowModal(true);
        }}
      >
        {children}
      </div>


      <Modal
        title="测试"
        visible={showTest}
        onOk={onTestSubmit}
        onCancel={onTestCancel}
        style={{ zIndex: '99999' }}
      >
        <Spin size="large" spinning={testLoading}>
          <Input value={query} addonBefore={`${path}?`} onChange={(e) => onTestQueryChange(e.target.value.trim())} />
          <Input.Group style={{ marginTop: '18px' }}>
            <Input style={{ width: '35%', marginRight: '18px' }} value={testUserId} onChange={(e) => onTestUserIdChange(e.target.value.trim())} placeholder="测试用户ID，非必填" />
            <Input style={{ width: '30%' }} value={testLongitude} onChange={(e) => onTestLongitudeChange(e.target.value.trim())} placeholder="测试经度，非必填" />
            <Input style={{ width: '30%' }} value={testLatitude} onChange={(e) => onTestLatitudeChange(e.target.value.trim())} placeholder="测试维度，非必填" />
            {projectName === 'fliggy-weixin' && <Input style={{ width: '35%', marginRight: '18px' }} value={testCode} onChange={(e) => setTestCode(e.target.value.trim())} placeholder="测试微信登录凭证，非必填" />}
          </Input.Group>
          {
            testData ? <div style={{ maxHeight: '500px', marginTop: '18px', overflow: 'scroll' }}>
              <ReactJson src={testData || {}} />
            </div> : null
          }
        </Spin>
      </Modal>

      <Modal
        title={`${actionName}配置`}
        visible={showModal}
        onOk={onFormSubmit}
        onCancel={onFormCancel}
        maskClosable={false}
        okButtonProps={{ disabled: form.errorFields?.length > 0 }}
        width={800}
        destroyOnClose
        footer={
          <div className="prefetch-config-model-footer-container">
            <Button type="primary" onClick={changeShowModalFunc} className="change-show-model-status-btn" ghost>
              切换到{showNewModal ? '老' : '新'}配置
            </Button>
            <div className="prefetch-config-model-footer-container-right">
              <Button key="test" onClick={onFormTest}>
                测试
              </Button>
              <Button key="back" onClick={onFormCancel}>
                返回
              </Button>
              <Button key="submit" type="primary" onClick={onFormSubmit}>
                提交
              </Button>
            </div>
          </div>
        }
      >
        <Spin spinning={false} wrapperClassName="form-wrapper">
          <FormRender
            form={form}
            removeHiddenData={false}
            watch={watch}
            schema={showNewModal ? extendsNewSchema(newSchema) : extendsSchema(schema)}
            disabled={!!(Action.Read === action)}
            onMount={onFormMount}
            onFinish={onFormFinish}
            displayType="row"
          />
        </Spin>
      </Modal>
    </>
  );
};
