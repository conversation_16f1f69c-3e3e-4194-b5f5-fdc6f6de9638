export default {
  "type": "object",
  "properties": {
    "path": {
      "title": "页面path",
      "type": "string",
      "displayType": "row",
      "props": {},
      "required": true
    },
    "expiryTimeMs": {
      "title": "过期时间(ms)",
      "type": "number",
      "width": "50%"
    },
    "mtopTimeOutMs": {
      "title": "获取mtop时的超时时间(ms)",
      "type": "number",
      "width": "50%"
    },
    "needLogin": {
      "title": "是否需要登陆",
      "type": "boolean",
      "widget": "checkbox",
      "width": "30%"
    },
    "isNeedGetThenRemove": {
      "title": "是否获取完就删除",
      "type": "boolean",
      "widget": "checkbox",
      "width": "35%"
    },
    "notColdPrefetch": {
      "title": "关闭冷启预请求",
      "type": "boolean",
      "widget": "checkbox",
      "width": "35%"
    },
    "mtopData": {
      "title": "mtop信息",
      "type": "object",
      "widget": "mtopInput",
      "width": "100%"
    },
    "paramsHandleFunc": {
      "title": "入参处理函数",
      "description": "入参处理函数",
      "type": "string",
      "widget": "editor",
      "props": {
      },
      "width": "100%"
    },
    "checkParamsFunc": {
      "title": "取数据校验参数函数",
      "description": "取数据校验参数函数",
      "type": "string",
      "widget": "editor",
      "props": {
      },
      "width": "100%"
    },
    "isGray": {
      "title": "是否灰度",
      "type": "boolean",
      "widget": "checkbox",
      "width": "100%"
    },
    "grayNumber": {
      "title": "灰度进度",
      "description": "灰度进度",
      "type": "number",
      "widget": "stepSlider",
      "props": {
        "marks": {
          0: '集团内灰',
          25: '外灰1%',
          50: '外灰10%',
          75: "外灰30%",
          100: "外灰50%"
        }
      },
      "width": "100%",
      "hidden": "{{formData.isGray !== true}}",
    }
  },
  "labelWidth": 180,
  "displayType": "row"
};
