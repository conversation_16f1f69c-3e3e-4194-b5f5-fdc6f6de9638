import { useEffect, useState } from 'react';
import { Row, Col, Divider, Spin, Popconfirm } from 'antd';
import { useAccess, Access } from 'umi';
import { IPrefetchConfig } from '@/interface/prefetch-config';
import prefetchConfigApi, { IPrefetchConfigUpdateRes, IPrefetchConfigCreateRes, IPrefetchConfigUpdateReq, IPrefetchConfigCreateReq } from '@/api/prefetch-config';
import EditConfigBtn, { Action } from '../EditConfigBtn';

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  data: IPrefetchConfig;
  projectName: string;
  refresh: () => void;
  allowOpration: boolean;
}

export default ({ data, projectName, refresh, allowOpration }: Props) => {
  const [latestData, setLatestData] = useState(data);
  const [loading, setLoading] = useState(false);
  const access = useAccess();

  const Actions = () => {
    return <>
      <EditConfigBtn
        prefetchConfig={latestData}
        projectName={projectName}
        onEditEnd={onEditEnd}
        setLoading={setLoading}
        action={Action.Read}>
        <a>详情</a>
      </EditConfigBtn>
      <Divider type="vertical" />
      {
        // access.isAdmin &&
        allowOpration &&
        <EditConfigBtn
          prefetchConfig={latestData}
          projectName={projectName}
          onEditEnd={onEditEnd}
          setLoading={setLoading}
          action={Action.Update}>
          <a>编辑</a>
        </EditConfigBtn>
      }
      {
       allowOpration && latestData.isStop ? <div>
          <Divider type="vertical" />
          <Popconfirm title="确认启用？" okText="是" cancelText="否" onConfirm={() => changeStopStatus(0)}>
            <a>启用</a>
          </Popconfirm>
        </div> : null
      }
      {
        allowOpration && !latestData.isStop ? <div>
          <Divider type="vertical" />
          <Popconfirm title="确认停用？" okText="是" cancelText="否" onConfirm={() => changeStopStatus(1)}>
            <a>停用</a>
          </Popconfirm>
        </div> : null
      }
      {
        access.isAdmin && <div>
          <Divider type="vertical" />
          <Popconfirm title="确认删除？" okText="是" cancelText="否" onConfirm={deletePrefetchConfig}>
            <a>删除</a>
          </Popconfirm>
        </div>
      }
    </>
  };

  function onEditEnd(_action: any, res: { data: IPrefetchConfig }) {
    setLatestData(res.data);
  }

  function deletePrefetchConfig() {
    prefetchConfigApi.delete(latestData.id);
    refresh();
  }

  function changeStopStatus(status: number) {
    setLoading(true);
    prefetchConfigApi.changeStopStatus(latestData.id, status).then((res) => {
      setLoading(false);
      if (res.success && res.data) {
        setLatestData(res.data);
      } else {
        console.log(res.errorMsg);
      }
    });
  }

  let innerClassName = 'inner';

  return <Spin spinning={loading} wrapperClassName='prefetch-config-list-item'>
    <Row className={`${innerClassName}${latestData.isStop ? ' stop' : ''}`}>
      <Col span={20} className="col">
        { latestData.id ? <div className="order-idx">ID: {latestData.id}</div> : '' }
        <div>{latestData.path}</div>
        {
          latestData.isStop ? <div className="stop-status">已停用</div> : null
        }
      </Col>
      <Col span={4} className="col actions">
        <Actions />
      </Col>
    </Row>
  </Spin>
}