.prefetch-config-list-item {
  width: 100%;

  .stop {
    color: #999999;
  }

  .stop-status {
    margin-left: 14px;
    background-color: #d95140;
    border-radius: 6px;
    padding: 0 8px;
    height: 24px;
    color: #ffffff;
  }

  .order-idx {
    margin-left: 4px;
    margin-right: 10px;
    background-color: #ffa54d;
    border-radius: 6px;
    padding: 0 6px;
    height: 20px;
    line-height: 20px;
    color: #ffffff;
  }

  .inner {
    padding: 16px 24px;

    &.ready {
      --icon: url('https://gw.alicdn.com/imgextra/i4/O1CN01FUFIrS1OZnwAjchKX_!!6000000001720-2-tps-210-210.png');
      --bgc: linear-gradient(to right, rgba(255, 255, 255, 0.2), rgba(112, 182, 3, 0.2));
    }

    &.abandon {
      --icon: url('https://gw.alicdn.com/imgextra/i1/O1CN01jyVC7n1JIn9Pnlkma_!!6000000001006-2-tps-200-200.png');
      --bgc: linear-gradient(to right, rgba(255, 255, 255, 0.2), rgba(182, 3, 3, 0.2));
    }

    &.ready, &.abandon {
      --transparent: url('https://gw.alicdn.com/imgextra/i3/O1CN01U7MJsm1SueQVZ1oeW_!!6000000002307-2-tps-2-2.png');
      background-image: var(--bgc),
        cross-fade(var(--transparent), var(--icon), 0.2);
      background-image: var(--bgc),
        -webkit-cross-fade(var(--transparent), var(--icon), 0.2);
      background-size: auto, 90px;
      background-repeat: initial, no-repeat;
      background-position: initial, -25px 20px;
    }
  }

  .col {
    display: flex;
    align-items: center;
  }

  .split-comma {
    &:last-child:after {
      display: none;
    }

    &:after {
      content: '、';
      color: #ccc;
    }
  }

  .empty-text {
    color: #999;
  }

  .actions {
    justify-content: flex-end;
    white-space: nowrap;
  }
}