import { useEffect, useState } from 'react';
import { List } from 'antd';
import { IProject } from '@/interface/project';
import { IPrefetchConfig } from '@/interface/prefetch-config';
import Item from './item';

import './index.less';

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  list: IPrefetchConfig[] | undefined;
  projectName: string;
  refresh: () => void;
  allowOpration: boolean;
}

export default ({ list, projectName, refresh, allowOpration }: Props) => {
  list = list?.filter(item => !!item);

  return <List
    itemLayout="horizontal"
    size="large"
    pagination={{
      pageSize: 10,
    }}
    dataSource={list}
    renderItem={item => (
      <List.Item
        key={item.id}
        style={{ padding: 0 }}
      >
        <Item key={item.id} data={item} projectName={projectName} refresh={refresh} allowOpration={allowOpration}/>
      </List.Item>
    )}
  />
}