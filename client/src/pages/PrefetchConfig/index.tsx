import { useEffect, useMemo, useState } from 'react';
import { <PERSON>, <PERSON>H<PERSON><PERSON>, Result, Button, Typography } from 'antd';
import { ResultStatusType } from 'antd/es/result';
import { useModel } from 'umi';
import { IPrefetchConfig } from '@/interface/prefetch-config';
import { IProject } from '@/interface/project';
import prefetchConfigApi from '@/api/prefetch-config';
import ProjectSelect from '@/components/ProjectSelect';
import ConfigList from './components/ConfigList';
import EditConfigBtn, { Action } from './components/EditConfigBtn';

import './index.less';
const { Text } = Typography;

const mtopmock = {
  "data": {
    "creator": "yanchao.yc",
    "gmtModified": *************,
    "dailyPublishedRegions": null,
    "apiDevelopStatus": "API_NOT_DEVELOP",
    "apiMappingProtocol": "HSF",
    "apiDesc": "微信-码上游-根据景区id查询对应的信息",
    "gmtCreate": *************,
    "apiBaseInfo": {
      "miniProgramSessionTypes": [
        "tao_account_mp"
      ],
      "allowedHttpMethodType": "MIX",
      "servletTimeout": 4,
      "sessionType": "TRY_ACQUIRE_SESSION",
      "allowedEntrances": "api4;mtop4_h5",
      "bizSessionType": "TB_SESSION",
      "supportSubAccount": true
    },
    "version": 5,
    "productPublishedRegions": null,
    "apiCategoryCode": "mashangyou",
    "apiProductEnvStatus": "ONLINE",
    "systemCode": "traveldc",
    "v": "1.1",
    "apiResponseResultInfo": {
      "resultErrorInfos": [],
      "resultModalInfos": []
    },
    "hsf": "com.fliggy.distribution.client.mashangyou.service.MsyConfigMtopService",
    "developer": "",
    "apiRequestParamInfo": [
      {
        "paramType": "Number",
        "paramRequired": false,
        "description": "景点id",
        "paramName": "scenicId",
        "paramDefaultValue": ""
      },
      {
        "paramType": "String",
        "paramRequired": false,
        "description": "二维码",
        "paramName": "id",
        "paramDefaultValue": ""
      },
      {
        "paramType": "String",
        "paramRequired": false,
        "description": "活码code",
        "paramName": "hcode",
        "paramDefaultValue": ""
      }
    ],
    "api": "mtop.fliggy.traveldc.wx.mashangyou.config.get",
    "id": 359410,
    "prePublishedRegions": null,
    "apiMappingInfo": {
      "apiMappingHttpInfo": null,
      "passRequestParams": null,
      "null": false,
      "apiMappingFunctionInfo": null,
      "apiMappingHsfInfo": {
        "hsfDailyVersion": "1.0.0.daily",
        "hsfService": "com.fliggy.distribution.client.mashangyou.service.MsyConfigMtopService",
        "hsfMethodParamMappings": [
          {
            "leafHsfMethodParamMappings": [
              {
                "leafHsfMethodParamMappings": [],
                "hsfParamMapping": "scenicId",
                "hsfParamName": "scenicId",
                "hsfParamType": ""
              },
              {
                "leafHsfMethodParamMappings": [],
                "hsfParamMapping": "id",
                "hsfParamName": "id",
                "hsfParamType": ""
              },
              {
                "leafHsfMethodParamMappings": [],
                "hsfParamMapping": "!userid",
                "hsfParamName": "userId",
                "hsfParamType": ""
              },
              {
                "leafHsfMethodParamMappings": [],
                "hsfParamMapping": "hcode",
                "hsfParamName": "hcode",
                "hsfParamType": ""
              },
              {
                "leafHsfMethodParamMappings": [],
                "hsfParamMapping": "!headers.B-ticket_wireless_platform_type",
                "hsfParamName": "clientType",
                "hsfParamType": ""
              }
            ],
            "hsfParamMapping": "",
            "hsfParamName": "msyScenicQueryMtopReq",
            "hsfParamType": "com.fliggy.distribution.client.mashangyou.dto.req.MsyScenicQueryMtopReq"
          }
        ],
        "hsfProductVersion": "1.0.0",
        "hsfGroup": "HSF",
        "hsfPreVersion": "1.0.0",
        "hsfMethodName": "query4Wx",
        "hsfDaily2Version": "1.0.0.daily2"
      },
      "passRequestHeaders": null,
      "passRequestCookies": null
    }
  },
  "success": true,
  "errorCode": null,
  "errorMsg": null,
  "statusCode": 200
}

export default () => {
  const [prefetchConfigList, setPrefetchConfigList] = useState<IPrefetchConfig[]>();
  const [loading, setLoading] = useState(false);
  const [errorCode, setErrorCode] = useState<ResultStatusType>();
  const [project, setProject] = useState<IProject>();
  const currentUser = useModel('user');

  useEffect(refresh, [project?.name])

  function refresh() {
    if (!project?.name) return;

    setLoading(true);

    prefetchConfigApi.findByKeyValue("project_name", project.name).then(res => {
      console.log('12345', res.data);
      setLoading(false);
      if (!res?.data) throw Error();
      setPrefetchConfigList(res.data);
    }).catch(() => {
      setErrorCode(500);
    })
  }

  function onProjectSelect(project: IProject) {
    setProject(project);
  }

  if (errorCode) {
    return <div className="page-container">
      <Result
        status={errorCode}
        title={errorCode}
        subTitle="系统出错了~"
      ></Result>
    </div>
  }
  const allowOpration = useMemo(() => {
    const WHITE_USER_LIST: any = ['390773', '191128', '135378', '414026', '406575'];
    if(currentUser && currentUser.workid) {
      return WHITE_USER_LIST.includes(currentUser.workid)
    } else {
      return false
    }
  }, [currentUser])

  return <Spin wrapperClassName="page-container" size="large" spinning={loading}>
    <PageHeader
      className="prefetch-config-header"
      title=""
    >
      <div className="prefetch-config-header-content">
        <ProjectSelect onSelect={onProjectSelect} style={{}} />
        {
          // access.isAdmin && project?.name && <EditConfigBtn action={Action.Create} onEditEnd={refresh} projectName={project.name}>
          allowOpration && project?.name && <EditConfigBtn action={Action.Create} onEditEnd={refresh} projectName={project.name}>
            <Button type="primary">新建配置</Button>
          </EditConfigBtn>
        }
      </div>
      <Text type="danger" strong>📢📢📢：新增&修改、开启&关闭预加载，请先联系 @得渏 进行确认。</Text>
    </PageHeader>
    {
      prefetchConfigList && project?.name && <ConfigList list={prefetchConfigList} projectName={project.name} refresh={refresh} allowOpration={allowOpration}/>
    }
  </Spin>
};
