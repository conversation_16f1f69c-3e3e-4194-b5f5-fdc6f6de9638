import React, { useState } from 'react';
import { Link } from 'umi';
import { Popover, Modal, Typography, message, Spin } from 'antd';
import Size<PERSON>iff<PERSON>, { SampleSizeDiffer } from '../SizeDiffer';
import freeDevBranchApi from '@/api/free-dev-branch';

interface IBranchSizeProps {
  dist?: string;
  branchName?: string;
  projectName?: string;
  gmtModified?: string;
}

interface IterBranchSizeProps {
  data?: Record<string, number>;
}

interface IProps extends IBranchSizeProps, IterBranchSizeProps {
  type?: string;
}

/** 参与集成的迭代分支体积 */
function IterBranchSize({ data }: IterBranchSizeProps) {
  if (!data) return null;
  return (
    <Popover trigger="click" content={<SampleSizeDiffer data={data} />}>
      <div>体积分析：<Typography.Link>查看</Typography.Link></div>
    </Popover>
  );
}

function BranchSize(props: IBranchSizeProps) {
  const { projectName, branchName, dist, gmtModified } = props;
  const [info, setInfo] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);

  const onAnalyze = () => {
    if (!dist || !branchName || !projectName || loading) return;
    // 生成过，直接展示
    if (info) {
      setShowModal(true);
      return;
    }
    // 生成
    setLoading(true);
    freeDevBranchApi.analyzeSize({
      dist,
      branchName,
      projectName,
      gmtModified,
      // noticeUsers: '414026',
    }).then((res: any) => {
      if (res?.msg || res.data.msg) {
        return Promise.reject(res);
      }
      setLoading(false);
      setInfo(res.data);
      setShowModal(true);
    }).catch(err => {
      setLoading(false);
      message.error(err?.msg || err?.data?.msg || '体积分析失败');
    })
  };

  const renderText = () => {
    if (loading) {
      return <Spin tip="分析中" />
    }
    return '体积变更';
  };

  // 仅支持微信
  if (!dist || projectName !== 'fliggy-weixin') {
    return null;
  }

  return (
    <>
      <div>体积分析：
        <Typography.Link onClick={onAnalyze}>{renderText()}</Typography.Link>、
        <Link to={`/package/home-size?dist=${encodeURIComponent(dist)}`} target="_blank">首页体积</Link>
      </div>
      <Modal width={650} title={`${branchName}体积分析`} footer={null} open={showModal} onCancel={() => setShowModal(false)}>
        <SizeDiffer data={info || {}} />
      </Modal>
    </>
  )
}

export default (props: IProps) => {
  const { data, type, ...other } = props
  if (type === 'branch') {
    return <BranchSize {...other} />;
  }
  return <IterBranchSize data={data} />
}