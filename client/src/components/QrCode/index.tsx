import { useState, useEffect, useRef } from 'react';
import { Image, Input, Tooltip, Tag, Button, message, Select, Empty } from 'antd';
import QRCode from 'qrcode.react';
import { EProjectType } from '@/const/project';
import { makeWechatExpLink, makeAlipayLink, makeTaobaoLink } from '@/utils/access-link';
import { IProject } from '@/interface/project';
import { IAppStructure } from '@/interface/package';
import packageApi from '@/api/package';

import './index.less';

interface Props {
  // 默认页面地址
  defaultPage?: string;
  // 项目
  project?: IProject;
}

const Comp: React.FC<Props> = ({ defaultPage, project }) => {
  const [pagePath, setPagePath] = useState('');
  const [pageQuery, setPageQuery] = useState('');
  const [appQuery, setAppQuery] = useState('');
  const [appStructure, setAppStructure] = useState<IAppStructure | null>();
  const [structureLoading, setStructureLoading] = useState(false);
  // 页面集合
  const pages = appStructure?.packages.flatMap(pkg => pkg.pages?.flatMap(page => [page.source]) || []) || [];
  let link = '';
  let tag;
  if (project?.type === EProjectType.WEIXIN) {
    link = makeWechatExpLink(defaultPage || pagePath, pageQuery)
    tag = <Tag color="cyan" className="tag">体验版</Tag>
  } else if (project?.type === EProjectType.ALIPAY) {
    link = makeAlipayLink(defaultPage || pagePath, pageQuery, appQuery)
    tag = <Tag color="volcano" className="tag">线上版</Tag>
  } else if (project?.type === EProjectType.TAOBAO) {
    link = makeTaobaoLink(defaultPage || pagePath, pageQuery, appQuery)
    tag = <Tag color="volcano" className="tag">线上版</Tag>
  }
  const notSupport = !link;

  useEffect(() => {
    if (!project?.name) return;

    // 重置state
    setPagePath('');
    setPageQuery('');
    setAppQuery('');
    setAppStructure(null);

    // 获取小程序最新结构
    if (!defaultPage && !notSupport) {
      getStructureByBranch(project.name)
    }
  }, [project?.name])

  /** 获取小程序结构 */
  function getStructureByBranch(projectName: string) {
    setStructureLoading(true);

    packageApi.getStructureByProject({
      projectName,
    })
      .then((res) => {
        if (res?.success && res.data) {
          setAppStructure(res.data);
        } else {
          throw Error(res?.errorMsg)
        }
      }).finally(() => {
        setStructureLoading(false)
      });
  }

  function copyLink() {
    navigator.clipboard.writeText(link)
      .then(() => {
        message.success('复制成功')
      }).catch((err) => {
        message.error(err?.message || '复制失败！')
      });
  }

  if (notSupport) return <Empty description="暂不支持" />;

  return (
    <div className="qr-code">
      <div className="panel">
        <div className="img">
          {
            link ? <Tooltip placement="top" title={link}><QRCode size={150} value={link} /></Tooltip>
              : <Image preview={false} width={150} height={150} src="https://img.alicdn.com/imgextra/i3/O1CN01Qa0qBB1Y9NFZWM5UY_!!6000000003016-2-tps-200-200.png" />
          }
        </div>
        <div className="params">
          <div className="item">{tag}</div>
          <div className="item">
            <span className="label">页面地址:</span>
            <Select
              className="input"
              showSearch
              loading={structureLoading}
              size="middle"
              disabled={!!defaultPage}
              value={defaultPage || pagePath || undefined}
              onChange={value => setPagePath(value)}
              placeholder={`${structureLoading ? '加载中，' : ''}可不选`}
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              allowClear
              options={pages.map(page => ({ label: page, value: page }))}
            />
          </div>
          {
            (defaultPage || pagePath) &&
            <div className="item">
              <span className="label">页面参数:</span>
              <Input
                className="input"
                placeholder="如 id=a&type=hotel，可不填"
                value={pageQuery}
                onChange={(e) => setPageQuery(e.target.value.trim())} />
            </div>
          }
          {
            project?.type === EProjectType.ALIPAY || project?.type === EProjectType.TAOBAO &&
            <div className="item">
              <span className="label">App参数:</span>
              <Input
                className="input"
                placeholder="如 ttid=1234&source=home，可不填"
                value={appQuery}
                onChange={(e) => setAppQuery(e.target.value.trim())} />
            </div>
          }
        </div>
      </div>
      <Input.Group compact className="whole-link">
        <span className="label">完整链接:</span>
        <Input disabled={true} style={{ flex: 1 }} value={link || '暂不支持'} />
        <Button type="primary" onClick={copyLink}>复制</Button>
      </Input.Group>
    </div>
  )
}

export default Comp;