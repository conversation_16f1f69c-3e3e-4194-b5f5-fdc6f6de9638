import React, { Fragment, useMemo } from 'react';
import { Row, Col, Statistic, Typography } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import './index.less';

const { Title, Text } = Typography;

const CORE_NAME_MAP: Record<string, string> = {
  __FULL__: '总包',
  __APP__: '主包',
  total: '总包',
  main: '主包'
}

function formatSize(val: number, force?: boolean) {
  if (val === 0) {
    return '-';
  }
  const size = Math.abs(val);
  if (size > 1024 || force) {
    const unit = force ? '' : ' KB';
    return `${(size / 1024).toFixed(2)}${unit}`;
  }
  return `${size} B`;
}


function formatSizeInfo(originData: Record<string, any>) {
  const { version, prevIterVersion, packageInfo } = originData;
  const coreKeys = Object.keys(CORE_NAME_MAP);
  
  const packages = Object.keys(packageInfo).map(name => {
    const subInfo = packageInfo[name];
    const { diff } = subInfo;
    const strong = Math.abs(diff) / 1024 > 15;
    const prefixText = diff > 0 ? '+' : '-';
    return {
      ...subInfo,
      originName: name,
      name: CORE_NAME_MAP[name] || name,
      strong,
      value: formatSize(subInfo[version], true),
      prevValue: formatSize(subInfo[prevIterVersion], true),
      diffValue: formatSize(diff),
      prefix: diff !== 0 ? prefixText : '',
      type: diff > 0 ? 'danger' : 'success',
    } ;
  });

  return {
    coreInfo: packages.filter(val => coreKeys.indexOf(val.originName) > -1).sort((a, b) => b.value - a.value),
    detailInfo: packages
      .filter(val => val.diff !== 0 && coreKeys.indexOf(val.originName) === -1)
      .sort((a, b) => Math.abs(b.diff) - Math.abs(a.diff)),
    otherInfo: packages.filter(val => val.diff === 0 && coreKeys.indexOf(val.originName) === -1),
  };
}

function formatSampleInfo(originData: Record<string, number>) {
  const data = originData.main ? originData : { ...originData, main: 0 };
  const list = Object.keys(data).map((pkg) => {
    const val = data[pkg];
    const absVal = Math.abs(val);
    const value = formatSize(absVal);
    const strong = absVal / 1024 > 15;
    return {
      value,
      name: pkg,
      strong,
      originValue: val,
    }
  });
  return {
    coreInfo: list.filter(item => !!CORE_NAME_MAP[item.name]),
    detailInfo: [
      {
        title: '分包体积增加明细',
        type: 'danger' as any,
        prefix: '+',
        list: list.filter(item => item.originValue > 0 && !CORE_NAME_MAP[item.name]),
      },
      {
        title: '分包体积减少明细',
        type: 'success' as any,
        prefix: '-',
        list: list.filter(item => item.originValue < 0 && !CORE_NAME_MAP[item.name]),
      }
    ],
  };
}


export function SampleSizeDiffer({ data }: { data: Record<string, number> }) {
  const { coreInfo, detailInfo } = useMemo(() => formatSampleInfo(data), [data]);
  return (
    <div className="size-differ-info--sample">
      <Row gutter={16}>
        {coreInfo.map(item => (
          <Col span={12} key={item.name}>
            <Statistic
              title={CORE_NAME_MAP[item.name]}
              value={item.value}
              precision={2}
              valueStyle={{ color: item.originValue > 0 ? '#cf1322': '#3f8600' }}
              prefix={item.originValue > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
            />
          </Col>
        ))}
      </Row>
      {detailInfo.map((item, index) => !!item.list.length && (
        <Row style={{ marginTop: '16px' }} gutter={16} key={index}>
          <Col span={24}>
            <Title level={5}>{item.title}</Title>
          </Col>
          {item.list.map(val => (
            <Col span={12} key={val.name} style={{ whiteSpace: 'nowrap' }}>
              <span className="size-differ-info-name">{val.name}</span>
              <Text strong={val.strong} type={val.strong ? item.type : undefined}>{item.prefix}{val.value}</Text>
            </Col>
          ))}
        </Row>
      ))}
    </div>
  );
}


export default function SizeDiffer({ data }: { data?: Record<string, number>; }) {
  if (!data) {
    return null;
  }
  const { version, prevIterVersion } = data;

  const { coreInfo, detailInfo, otherInfo } = useMemo(() => formatSizeInfo(data), [JSON.stringify(data || '')]);

  return (
    <div className="size-differ-info">
      <Row gutter={16}>
        {coreInfo.map(item => (
          <Col span={12} key={item.name}>
            <Statistic
              title={item.name}
              value={item.diffValue}
              precision={2}
              valueStyle={{ color: item.diff > 0 ? '#cf1322': '#3f8600' }}
              prefix={item.diff > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
              valueRender={(node) => (
                <>
                  {node}
                  <div className="size-differ-info-text">
                    <div>{prevIterVersion} <span>{item.prevValue}</span></div>
                    <div>{version} <span>{item.value}</span></div>
                  </div>
                </>
              )}
            />
          </Col>
        ))}
      </Row>
      <Row style={{ marginTop: '16px' }} gutter={16}>
        <Col span={24}>
          <Title level={5}>体积差异明细</Title>
        </Col>

        <Col className="size-differ-info-lable" span={6}>分包</Col>
        <Col className="size-differ-info-lable" span={6}>{prevIterVersion}（KB）</Col>
        <Col className="size-differ-info-lable" span={6}>{version}（KB）</Col>
        <Col className="size-differ-info-lable" span={6}>体积对比</Col>
      </Row>
      <Row gutter={16}>
        {detailInfo.map(val => (
          <Fragment key={val.name}>
            <Col span={6}>{val.name}</Col>
            <Col span={6}>{val.prevValue}</Col>
            <Col span={6}>{val.value}</Col>
            <Col span={6}>
              <Text
                strong={val.strong}
                type={val.strong ? val.type : undefined}
              >
                {val.prefix}{val.diffValue}
              </Text>
            </Col>
          </Fragment>
        ))}
      </Row>
      <Row style={{ marginTop: '16px' }} gutter={16}>
        <Col span={24}>
          <Title level={5}>其他分包体积明细（KB）</Title>
        </Col>
        {otherInfo.map(val => (
          <>
            <Col span={6}>{val.name}</Col>
            <Col span={6}>{val.value}</Col>
          </>
        ))}
      </Row>
  </div>
  );
}