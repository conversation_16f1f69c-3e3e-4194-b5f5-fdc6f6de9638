import { useState, useEffect, useMemo } from 'react';
import { unstable_batchedUpdates } from 'react-dom'
import { Select } from 'antd';
import { useModel } from '@umijs/max';
import { IProject } from '@/interface/project';
import { EProjectType } from '@/const/project';
import { STORAGE_PROJECT_NAME } from '@/const/storage';

interface Props {
  onSelect: (project: IProject) => void;
  style?: React.CSSProperties
}

interface IProjectGroups {
  label: string;
  key: string;
  list: IProject[];
}

const Comp: React.FC<Props> = ({ style, onSelect }) => {
  const [projectName, setProjectName] = useProjectName('COMMON');
  const [projectList] = useModel('projectList');
  const loading = projectList.length === 0;

  const projectGroups = useMemo<IProjectGroups[]>(() => {
    const miniappGroups: IProject[] = [];
    const componentGroups: IProject[] = [];
    projectList.forEach((project) => {
      if (project.type === EProjectType.COMPONENT) {
        componentGroups.push(project);
      } else {
        miniappGroups.push(project);
      }
    });
    return [{
      label: '小程序',
      key: 'miniapp',
      list: miniappGroups,
    }, {
      label: '组件',
      key: 'component',
      list: componentGroups,
    }];
  }, [projectList]);
  
  useEffect(() => {
    if (projectList.length === 0) return;

    if (projectName) { // 如果有历史选中，则匹配选中
      onSelect(projectList.find(item => item.name === projectName) || projectList[0]);
    } else { // 否则默认取第一个
      setProjectName(projectList[0].name);
      onSelect(projectList[0]);
    }
  }, [projectList])

  function onProjectChange(projectName: string) {
    setProjectName(projectName)
    onSelect(projectList.find(item => item.name === projectName) || projectList[0]);
  }

  return (
    <Select
      value={loading ? '' : projectName}
      style={{ width: '210px', ...style }}
      loading={loading}
      onChange={onProjectChange}>
      {
        projectGroups.map(group => (<Select.OptGroup label={group.label} key={group.key}>
          {
            group.list.map(project => <Select.Option value={project.name} key={project.name}>
              <div style={{ alignItems: 'center', display: 'flex' }}>
                {<img width={20} height={20} src={project.icon} style={{ marginRight: 4 }} />}
                {project.cnName}
              </div>
            </Select.Option>)
          }
        </Select.OptGroup>))
      }
    </Select>
  );
}

function useProjectName(storageTag: string): [string, React.Dispatch<string>] {
  const PROJECT_NAME_KEY = `${STORAGE_PROJECT_NAME}${storageTag}`;
  // 创建 state
  const [projectName, setProjectName] = useState<string>(() => {
    // 获取默认加载项目
    let defaultProjectName = '';
    try {
      defaultProjectName = localStorage.getItem(PROJECT_NAME_KEY) || '';
    } catch (err) { }
    return defaultProjectName;
  });

  return [
    projectName,
    function (value: string) {
      // 记录当前选择项目，用作下次默认加载项目
      try {
        localStorage.setItem(PROJECT_NAME_KEY, value)
      } catch (err) { }
      return setProjectName(value);
    }
  ]
}

export default Comp;