.diff-dependencies {
  min-height: 400px;

  .main-info {
    margin-bottom: 30px;
    padding: 24px;
    border-bottom: 1px solid #f2f2f2;
  }

  .tree-info {
    padding: 0 24px;
  }

  .tree-wrapper {
    border-right: 1px solid #f2f2f2;
  }

  .empty {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .diff-tabs {
    padding: 0 24px;
  }

  .diff-list {
    &-label {
      font-size: 14px;
      font-weight: 700;
      margin-bottom: 8px;
      padding: 15px 9px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }

    &-wrap {
      max-height: 540px;
      overflow-y: scroll;
    }

    &-item {
      padding: 5px;

      &:hover {
        background-color: #f2f2f2;
      }
    }
  }
}

.diff-list-deps {
  max-width: 500px;
  max-height: 500px;
  overflow: scroll;

  &__tag {
    cursor: pointer;
  }

  &__detail {
    color: #666;
    font-size: 12px;
  }
}

.notice-pkgs {

  &-pkg {
    margin-bottom: 10px;

    &__name {
      margin-right: 15px;
    }

    &__icon {
      margin-right: 6px;
    }
  }

  &-dep {
    display: flex;
    margin-left: 40px;
    margin-bottom: 8px;
    color: #999;
    font-size: 13px;

    .ant-tag-has-color {
      padding: 0 3px;
    }
  }
}