import { useEffect, useState, useRef, useMemo } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { Tree, Spin, Row, Col, Statistic, Empty, Skeleton, Tag, Result, Alert, Popover, Tabs, Table } from 'antd';
import { FileExclamationOutlined, FileAddOutlined, FileExcelOutlined, FileOutlined, DoubleRightOutlined } from '@ant-design/icons';
import helperApi, { IAnalyzeDependenciesReq, IDiffDependenciesReq } from '@/api/helper';
import { IDependencies, IDependenciesDiff, IDependenciesTreeNode, DiffType, IFlatDependenciesItem } from '@/interface/helper';
import { IIterBranch } from '@/interface/iter-branch';
import IconFont, { iconType } from '@/components/IconFont';
import { BIZ_LINE, BIZ_TITLE, BIZ_TITLE_COLOR } from '@/const/biz';

import './index.less';
import { pick } from 'lodash';
import classNames from 'classnames';

interface Props {
  /** 项目名称 */
  projectName?: string;
  /** 迭代分支id */
  iterId?: string;
  /** 对照的迭代分支id */
  fromIterId?: string;
}

const diffTypeMap = {
  1: DiffType.Delete,
  2: DiffType.Add,
  3: DiffType.Modify,
};

const Comp: React.FC<Props> = (props) => {
  const { projectName, iterId, fromIterId } = props;
  const [dependenciesDiff, setDependenciesDiff] = useState<IDependenciesDiff | null>();
  const [dependencies, setDependencies] = useState<IDependencies | null>();
  const [iterBranch, setIterBranch] = useState<IIterBranch | null>();
  const [prevPublishedIterBranch, setPrevPublishedIterBranch] = useState<IIterBranch | null>();
  const [tips, setTips] = useState('');
  const [loading, setLoading] = useState(true);
  const [err, setError] = useState('');
  const propsRef = useRef<Props>({});

  useEffect(() => {
    propsRef.current = props;

    setDependencies(null);
    setDependenciesDiff(null);
    setIterBranch(null);
    setPrevPublishedIterBranch(null);
    setTips('');

    if (iterId) {
      diffDependencies({ iterId, fromIterId })
    } else if (projectName) {
      analyzeDependencies({ projectName })
    } else {
      return;
    }
  }, [projectName, iterId])

  function analyzeDependencies(params: IAnalyzeDependenciesReq) {
    setLoading(true);

    helperApi.analyzeDependencies(params)
      .then(res => {
        // 如果和最新state不一致，则不做进一步处理
        if (params.projectName !== propsRef.current.projectName) return;

        unstable_batchedUpdates(() => {
          if (!res || !res.data) throw Error(res.errorMsg || '依赖分析失败');

          const { dependencies, iterBranch } = res.data;
          setIterBranch(iterBranch);
          setDependencies(dependencies);
          setLoading(false);
        })
      })
      .catch(err => {
        unstable_batchedUpdates(() => {
          setLoading(false);
          setError(err.message)
        })
      })
  }

  function diffDependencies(params: IDiffDependenciesReq) {
    setLoading(true);

    helperApi.diffDependencies(params)
      .then(res => {
        // 如果和最新state不一致，则不做进一步处理
        if (params.iterId !== propsRef.current.iterId) return;

        unstable_batchedUpdates(() => {
          if (!res || !res.data) throw Error(res.errorMsg || '依赖diff失败');
          const { dependencies, dependenciesDiff, iterBranch, prevPublishedIterBranch, tips } = res.data;
          setDependencies(dependencies);
          setDependenciesDiff(dependenciesDiff);
          setIterBranch(iterBranch);
          setPrevPublishedIterBranch(prevPublishedIterBranch);
          setTips(tips);
          setLoading(false)
        })
      })
      .catch(err => {
        unstable_batchedUpdates(() => {
          setLoading(false)
          setError(err.message)
        })
      })
  }

  const noticeList: IFlatDependenciesItem[] = useMemo(() => {
    const noticeUserInfo = dependenciesDiff?.noticeUserInfo || {};
    const { noticeInfo } = dependenciesDiff?.diffFlatInfo || {};
    if (!noticeInfo || JSON.stringify(noticeInfo) === '{}') {
      return [];
    }
    const BIZ_LINE_MAP: Record<string, BIZ_LINE> = {
      travel: BIZ_LINE.Vacation,
      rentcar: BIZ_LINE.Vehicle,
    };
    return Object.entries(noticeInfo).reduce((prev, [name, item], index) => {
      const { bizType, noticeUser } = item;
      const bizName = BIZ_LINE_MAP[bizType] || bizType;
      const empNames = noticeUser.split(',').map(v => noticeUserInfo[v] || v).join('、');
      prev.push({
        ...item,
        name,
        key: index,
        bizName,
        empNames,
      });
      return prev;
    }, [] as any);
  }, [dependenciesDiff?.diffFlatInfo]);

  function renderDiffTypeTag(diffType?: DiffType) {
    switch (diffType) {
      case DiffType.Add: return <Tag color="#237804">新增</Tag>;
      case DiffType.Modify: return <Tag color="#faad14">修改</Tag>;
      case DiffType.Delete: return <Tag color="#cf1322">删除</Tag>;
      default: return null;
    }
  }

  function renderTreeNodeIcon(node: IDependenciesTreeNode) {
    if(node.isVersion) {
      return (<IconFont type={iconType.npm} style={{ color: '#292C33A0' }} />);
    } else if(node.isChain) {
      return (<IconFont type={iconType.chain} style={{ color: '#292C33A0' }} />);
    } else {
      return (<IconFont type={iconType.npm} style={{ color: node.isReal ? '#E00000' : '#292C33A0' }} />);
    }
  }

  function renderTips() {
    if (dependenciesDiff) {
      return <Alert
        message={<>
          <a href={iterBranch?.rcGitBranch?.url} target="_blank">v{iterBranch?.version} </a>
          对比
          <a href={prevPublishedIterBranch?.rcGitBranch?.url} target="_blank"> v{prevPublishedIterBranch?.version} </a>
          的依赖变更:
        </>}
        type="info" />
    } else if (iterBranch) {
      return <Alert
        message={<>
          {tips && <span>{tips}，降级到展示 </span>}
          <a href={iterBranch?.rcGitBranch?.url} target="_blank">v{iterBranch?.version} </a>
          的依赖分析:
        </>}
        type={tips ? 'warning' : 'success'} />
    }
  }

  function renderLeftTreeNode(data: IDependenciesTreeNode) {
    // 获取当前版本url
    const url = `https://npm.alibaba-inc.com/package/${data.name}/v/${data.version}`;
    // 获取diffUrl
    const diffUrl = data.diffVersion ? getNpmVersionDiffUrl(data.name, data.version, data.diffVersion) : '';

    return (<Popover
      title={data.name}
      trigger="click"
      content={(
        <>
          <p><b>包版本：</b><a onClick={() => window.open(url)}>{data.version}</a></p>
          {data.diffVersion && (<p><b>原版本：</b><a onClick={() => window.open(diffUrl)}>{data.diffVersion}</a></p>)}
        </>
      )}
    >
      {renderDiffTypeTag(data.diffType)}
      <span>{data.name}</span>
    </Popover>);
  }

  function renderRightTreeNode(data: IDependenciesTreeNode) {
    return (<>
      {renderDiffTypeTag(data.diffType)}
      <span>{data.name}</span>
    </>);
  }

  function renderLeftSection() {
    return (
      <>
        <h1>依赖树</h1>
        <Tree
          height={600}
          showIcon={true}
          titleRender={renderLeftTreeNode}
          icon={<IconFont type={iconType.npm} style={{ color: '#292C33A0' }} />}
          treeData={dependenciesDiff?.diffTree || dependencies?.tree}
        />
      </>
    );
  }

  // 渲染右半部分区域
  function renderRightSection() {
    if (
      (!dependencies?.multiVersionTree || !dependencies.multiVersionTree.length) &&
      (!dependenciesDiff?.multiVersionTree || !dependenciesDiff?.multiVersionTree.length)
    ) {
      return (<Empty description={false} className="empty" />);
    } else {
      return (
        <>
          <h1>重复依赖</h1>
          <Tree
            height={600}
            showIcon={true}
            titleRender={renderRightTreeNode}
            icon={renderTreeNodeIcon}
            treeData={dependenciesDiff?.multiVersionTree || dependencies?.multiVersionTree}
          />
        </>
      );
    }
  }

  function renderVersions(color: string, item: IFlatDependenciesItem, key: string) {
    if (!item[key] || !item[key].length) {
      return <span>-</span>;
    }
    return (
      <>
        {item[key].map((version: string, index: number) => {
          const depsList = item[`${key}-${version}`] || [];
          const linkStr = ' -> ';
          const nameWithVersion = `${item.name}@${version}`;
          return depsList.length
            ? (
              <Popover
                key={index}
                trigger="click"
                title={`${nameWithVersion}的依赖关系`}
                content={(
                  <div className="diff-list-deps">
                  {depsList.map((dep: string) => (
                    <div className="diff-list-deps__detail">
                      <IconFont type={iconType.chain} style={{ color: '#292C33A0' }} />
                      <span> {dep}{linkStr}{nameWithVersion}</span>
                    </div>
                  ))}
                  </div>
                )}
              >
                <Tag color={color} className="diff-list-deps__tag">{version}</Tag>
              </Popover>
            )
            : <Tag color={color} key={index}>{version}</Tag>
        })}
      </>
    )
  }

  /** 变更的依赖列表 */
  function FlatDepsList() {
    if (!dependenciesDiff?.diffFlatInfo || !dependenciesDiff?.diffFlatInfo.list.length) {
      return (<Empty description={false} className="empty" />);
    }
    
    const diffList = dependenciesDiff.diffFlatInfo.list.filter(val => !!val.type);
    return (
      <>
        <Row className="diff-list-label">
          <Col span={12}>依赖名</Col>
          <Col span={6}>V{prevPublishedIterBranch?.version}</Col>    
          <Col span={6}>V{iterBranch?.version}</Col>
        </Row>
        <div className="diff-list-wrap">
          {diffList.map((item, index) => (
            <Row className="diff-list-item" key={index}>
              <Col span={12}>
                {renderDiffTypeTag(diffTypeMap[item.type])}{item.name}
              </Col>
              <Col span={6}>{renderVersions('geekblue', item, 'prevVersion')}</Col>
              <Col span={6}>{renderVersions('blue', item, 'version')}</Col>
            </Row>
          ))}
        </div>
      </>
    )
  }

  const DiffTreeContent = () => (
    <Row gutter={48} className="tree-info">
     <Skeleton loading={loading} active>
       {/* 包结构树 */}
       <Col span={12} className="tree-wrapper">{renderLeftSection()}</Col>

       {/* 重复依赖结构树 */}
       <Col span={12}>{renderRightSection()}</Col>
     </Skeleton>
   </Row>
  );

  /** 依赖变更通知信息 */
  const NoticePkgInfo = ({ item, className,  showIcon }: {
    item: IFlatDependenciesItem,
    showIcon?: boolean;
    className?: string;
  }) => {
    const type = item.type as 1 | 2 | 3;
    return (
      <div className={classNames('notice-pkgs-pkg', className)}>
        {showIcon && <IconFont type={iconType.chain} className="notice-pkgs-pkg__icon" />}
        {renderDiffTypeTag(diffTypeMap[type])}
        <span className="notice-pkgs-pkg__name">{item.name}</span>
        <span>{renderVersions('geekblue', item, 'prevVersion')}</span>
        <DoubleRightOutlined style={{ marginRight: '8px' }} />
        <span>{renderVersions('blue', item, 'version')}</span>
      </div>
    )
  }

  const NoticeList = () => {
    if (!noticeList.length) {
      return (<Empty description={false} className="empty" />);
    }
    const columns = [
      {
        title: '业务一级依赖包',
        key: 'name',
        render: (_:IFlatDependenciesItem[], record: IFlatDependenciesItem) => (
          <>
           {renderDiffTypeTag(diffTypeMap[record.type])}
           <span className="notice-pkgs-pkg__name">{record.name}</span>
          </>
        )
      },
      {
        title: '业务线',
        dataIndex: 'bizType',
        key: 'bizType',
        width: '10%',
        render: (_:IFlatDependenciesItem[], record: IFlatDependenciesItem) => {
          const bizName: BIZ_LINE = record.bizName;
          return <Tag color={BIZ_TITLE_COLOR[bizName]}>{BIZ_TITLE[bizName]}</Tag>
        },
      },
      {
        title: '负责人',
        dataIndex: 'empNames',
        key: 'empNames',
        width: '18%',
      },
      {
        title: `V${prevPublishedIterBranch?.version}`,
        key: 'preVersion',
        width: '16%',
        render: (_:IFlatDependenciesItem[], record: IFlatDependenciesItem) =>
          renderVersions('geekblue', record, 'prevVersion')
      },
      {
        title: `V${iterBranch?.version}`,
        key: 'version',
        width: '16%',
        render: (_:IFlatDependenciesItem[], record: IFlatDependenciesItem) => 
          renderVersions('blue', record, 'version')
      },
    ];

    const expandedRowRender = (record: IFlatDependenciesItem) => (
      <div>
        {record.deps.map((dep: IFlatDependenciesItem) => (
          <NoticePkgInfo
            item={dep}
            key={dep.name}
            showIcon={true}
            className="notice-pkgs-dep"
          />
        ))}
      </div>
    )

    return (
      <Table
        scroll={{ y: 540 }}
        pagination={false}
        columns={columns}
        expandable={{
          expandedRowRender,
          rowExpandable: (record) => !!(record.deps && record.deps.length),
        }}
        dataSource={noticeList}
      />
    );
  };

  const tabItems = [
    {
      key: '1',
      label: '依赖树',
      children: <DiffTreeContent />,
    },
    {
      key: '2',
      label: '依赖变更明细',
      children: <FlatDepsList />,
    },
    {
      key: '3',
      label: '依赖变更通知',
      children: <NoticeList />,
    }
  ];

  const { modifyCount, addCount, deleteCount } = useMemo(() => {
    if (dependenciesDiff?.diffFlatInfo) {
      return pick(dependenciesDiff.diffFlatInfo, ['modifyCount', 'addCount', 'deleteCount']);
    }
    return pick(dependenciesDiff, ['modifyCount', 'addCount', 'deleteCount']);
  }, [dependenciesDiff]);

  const renderDiffContent = () => {
    if (!dependenciesDiff) return null;
    return (
      <>
        <div className="main-info">
          <Row align="middle">
            <Col span={3}>
              <Statistic title="修改的依赖包数" value={modifyCount} prefix={<FileExclamationOutlined />} />
            </Col>
            <Col span={3}>
              <Statistic title="新增的依赖包数" value={addCount} prefix={<FileAddOutlined />} />
            </Col>
            <Col span={3}>
              <Statistic title="删除的依赖包数" value={deleteCount} prefix={<FileExcelOutlined />} />
            </Col>
            <Col span={3}>
              <Statistic title="重复依赖包总数" value={dependenciesDiff.multiVersionPackageCount} prefix={<FileOutlined />} />
            </Col>
            <Col span={3}>
              <Statistic title="重复依赖包版本总数" value={dependenciesDiff.multiVersionCount} prefix={<FileOutlined />} />
            </Col>
        </Row>
        </div>
         <Tabs
          defaultActiveKey="1"
          items={tabItems}
          className="diff-tabs"
        />
      </>
    )
  };

  const renderDepsContent = () => {
    if (!dependencies) return null;
    return (
      <>
        <div className="main-info">
          <Row align="middle">
            <Col span={3}>
              <Statistic title="依赖包总数" value={dependencies.totalCount} prefix={<FileOutlined />} />
            </Col>
            <Col span={3}>
              <Statistic title="真实依赖包总数" value={dependencies.realCount} prefix={<FileOutlined />} />
            </Col>
            <Col span={6}>
              <Statistic title="重复依赖包总数（无法确认个数）" value={`${dependencies.realMultiVersionPackageCount}(+${dependencies.multiVersionPackageCount - dependencies.realMultiVersionPackageCount})`} prefix={<FileOutlined />} />
            </Col>
            <Col span={6}>
              <Statistic title="重复依赖包版本总数（无法确认个数）" value={`${dependencies.realMultiVersionCount}(+${dependencies.multiVersionCount - dependencies.realMultiVersionCount})`} prefix={<FileOutlined />} />
            </Col>
          </Row>
        </div>
        <DiffTreeContent />
      </>
    )
  };

  if (err && !loading) {
    return <Result
      status="500"
      title="请稍后再试"
      subTitle={err}
    // extra={
    //   <Button type="primary" onClick={() => fetchPackageStructure()}>
    //     刷新
    //   </Button>
    // }
    />
  }

  return (
    <div className="diff-dependencies">
      {/* 包结构整体信息 */}
      <Spin spinning={loading} tip="依赖分析时间较长，请耐心等待~">
        {renderTips()}
        {renderDiffContent()}
        {renderDepsContent()}
      </Spin>
    </div>
  );
}

function getNpmVersionDiffUrl(npmName: string, version: string, diffVersion: string) {
  // 飞猪git分组
  const fliggyGitGroupList = ['rxpi', 'mautils', 'mapi', 'mamods', 'wapi', 'mpi', 'rmpi', 'rpi',]
  const fliggyGitGroup = fliggyGitGroupList.find(item => npmName.indexOf(`@ali/${item}-`) === 0);

  if (fliggyGitGroup) { // 飞猪通用git分组
    return getGitDiffUrl(fliggyGitGroup, npmName.substring(`@ali/${fliggyGitGroup}-`.length), version, diffVersion);
  } else if (npmName.indexOf('@ali/rx-crm') === 0) { // crm老组件
    return getGitDiffUrl('rxpi', npmName.substring('@ali/'.length), version, diffVersion);
  }
  return '';
}

function getGitDiffUrl(gitGroup: string, gitRep: string, version: string, diffVersion: string) {
  return `http://gitlab.alibaba-inc.com/${gitGroup}/${gitRep}/compare/${getBranchName(diffVersion)}...${getBranchName(version)}`
}

function getBranchName(version: string) {
  if (version.indexOf('beta') > -1) {
    return `daily%2F${version.split('-beta')[0]}`
  } else {
    return `publish%2F${version}`
  }
}

export default Comp;