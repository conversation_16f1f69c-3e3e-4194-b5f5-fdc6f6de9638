import { useEffect, useState } from 'react';
import { Spin } from 'antd';

export default (props: { src: string; tip?: string }) => {
  const { src, tip = '加载中...' } = props;
  const [iframeLoading, setIframeLoading] = useState(true);

  useEffect(() => {
    // 超时自动关闭loading，原因是某些情况下 iframe 的 onLoad 不会触发
    setTimeout(() => {
      if (iframeLoading) setIframeLoading(false)
    }, 3000)
  }, [])

  const onIframeLoad = () => {
    setIframeLoading(false);
  }

  return (
    <Spin spinning={iframeLoading} size="large" tip={tip}>
      <iframe onLoad={() => onIframeLoad()} className='iframe' src={src} />
    </Spin>
  );
};
