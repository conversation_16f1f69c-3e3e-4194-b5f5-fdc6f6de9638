import { useState, useRef, useMemo, useEffect } from 'react';
import { Slider } from 'antd';

interface SliderProps {
  marks: {
    [key: number]: string
  };
  defaultValue: number;
  onChange: Function;
  value: number;
}

export default ({ marks, onChange, value }: SliderProps) => {
  useEffect(() => {
    // 设置默认值
    onChange(value || 0);
  }, []);

  function onValuesChange(value: any) {
    onChange(value);
  }
  return (
    <div style={{ width: '90%' }}>
      <Slider
        marks={marks}
        step={null}
        defaultValue={value || 0}
        onChange={onValuesChange}
      />
    </div>
  );
}
