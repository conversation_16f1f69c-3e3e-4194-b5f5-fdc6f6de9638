import React, { useState, useEffect } from 'react';
import { Upload as AntdUpload, Button, message, Space, Input } from 'antd';
import CodeMirror from '@uiw/react-codemirror';
import {atomone} from '@uiw/codemirror-theme-atomone';
import { langs } from '@uiw/codemirror-extensions-langs';
import { basicSetup, minimalSetup } from '@uiw/codemirror-extensions-basic-setup';
import { get } from 'lodash';

export default (props: any) => {
  const { value, onChange } = props;

  useEffect(() => {
    // 设置默认值
    onChange && onChange(value || '');
  }, []);

  function onValuesChange(value: any) {
    onChange(value);
  }

  // const [codeValue, setCodeValue] = useState<string>();
  return (
    <div style={{width: '100%'}}>
      <CodeMirror 
        value={value} 
        extensions={[
          langs.json(),
          basicSetup({
            foldGutter: false,
            // indentOnInput: false,
          })
        ]} 
        // theme={atomone}
        editable
        // height="200px"
        onChange={(newValue: any) => onValuesChange(newValue)}
      />
    </div>
  );
}
