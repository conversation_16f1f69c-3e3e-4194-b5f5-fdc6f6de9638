import { useEffect, useRef, useState } from 'react';
import { Form, Button, Input, Row, Col, Select } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { FormListFieldData, FormListOperation } from 'antd/es/form/FormList';
import { ValidateStatus } from 'antd/es/form/FormItem';

export enum Action {
  Create = 'create', // 创建
  Update = 'update', // 更新
  Read = 'read', // 查看
}

interface ItemProps {
  // 字段名
  title: string;
  // 控件类型，默认 input
  type?: 'input' | 'radio';
  // 验证函数
  validator?: (value: string) => { isWarn?: boolean; error?: Error };
  // [input] 占位
  placeholder?: string;
  // [radio] 枚举名
  enumNames?: string[];
  // [radio] 枚举值
  enum?: string[];
}
interface FieldProps {
  readOnly: boolean;
  field: FormListFieldData;
  disabled: boolean;
  isInit?: boolean;
  parentName: string;
  schema: {
    [key: string]: ItemProps
  };
  remove: (index: number | number[]) => void;
  setErrorField: (name: string, error: Error) => void;
  removeErrorField: (name: string) => void;
}

const Field: React.FC<FieldProps> = ({ field, disabled, schema, isInit, parentName, setErrorField, removeErrorField, remove, readOnly }) => {
  const [validateStatusMap, setValidateStatusMap] = useState<{ [key: string]: ValidateStatus }>({});
  const items = Object.entries(schema);
  useEffect(() => {
    if (!isInit) {
      items.forEach(([key, { title }]) => {
        const namePath = `${parentName}.${field.key}.${key}`;
        setErrorField(namePath, new Error(`请填写${title}`));
      })
    }

    return () => {
      items.forEach(([key]) => {
        const namePath = `${parentName}.${field.key}.${key}`;
        removeErrorField(namePath);
      })
    }
  }, [])

  function renderItem(item: ItemProps) {
    if (item.type === 'radio') {
      return <Select>
        {
          (item.enum || []).map((value, index) => <Select.Option value={value} key={index}> {item.enumNames ? item.enumNames[index] : ''} </Select.Option>)
        }
      </Select>
    } else {
      return <Input disabled={disabled || readOnly} placeholder={item.placeholder} />
    }
  }

  return (
    <Row
      align="top"
      justify="space-between"
    >
      {
        items.map(([key, item]) => (
          <Col key={key} span={Math.floor(22 / items.length)}>
            <Form.Item
              name={[field.name, key]}
              validateTrigger={['onChange', 'onBlur']}
              validateStatus={validateStatusMap[key] || ''}
              hasFeedback
              rules={[
                {
                  validator: async (_: any, value: string) => {
                    const namePath = `${parentName}.${field.key}.${key}`;

                    if (!item.validator) {
                      removeErrorField(namePath);
                      setValidateStatusMap({ ...validateStatusMap, [key]: '' })
                      return;
                    };

                    const { error, isWarn } = item.validator(value);

                    if (error) {
                      if (isWarn) {
                        removeErrorField(namePath);
                        setValidateStatusMap({ ...validateStatusMap, [key]: 'warning' })
                      } else {
                        setErrorField(namePath, error);
                        setValidateStatusMap({ ...validateStatusMap, [key]: 'error' })
                      }
                      return Promise.reject(error)
                    } else {
                      removeErrorField(namePath);
                      setValidateStatusMap({ ...validateStatusMap, [key]: '' })
                    }
                  },
                },
              ]}
            >
              {renderItem(item)}
            </Form.Item>
          </Col>
        ))
      }
      <Col span={1}>
        {!disabled && <MinusCircleOutlined style={{ marginTop: '10px' }} onClick={() => remove(field.name)} />}
      </Col>
    </Row>
  )
}

interface Props extends React.FC {
  readOnly: boolean,
  value: {
    name: string;
    value: string;
  }[];
  onChange: Function;
  disabled: boolean;
  addons: {
    setErrorFields: (error: any) => void;
    removeErrorField: (path: string) => void;
  };
  action: Action;
  addBtnName?: string,
  items: any;
  schema: {
    $id: string;
  }
}

export default ({ value, onChange, disabled, addBtnName, action, addons, items, schema, readOnly }: Props) => {
  const initFieldRef = useRef<number[]>();
  const errorMapRef = useRef<{ [props: string]: Error }>({});
  const [form] = Form.useForm();
  
  function onValuesChange(_changedValues: any, allValues: any) {
    onChange(allValues[schema.$id]);
  }

  function setErrorField(name: string, error: Error) {
    errorMapRef.current = { ...errorMapRef.current, [name]: error };
    // 触发 form-render 的错误校验
    addons.setErrorFields({ name: schema.$id, error: new Error() })
  }

  function removeErrorField(name: string) {
    delete errorMapRef.current[name];
    // 移除 form-render 的错误校验
    if (Object.keys(errorMapRef.current).length === 0) addons.removeErrorField(schema.$id);
  }

  // 如果是非创建模式，但值为空，则直接返回
  if (action !== Action.Create && value === undefined) {
    return null;
  }

  function renderFields(fields: FormListFieldData[], { add, remove }: FormListOperation) {
    if (!initFieldRef.current) {
      initFieldRef.current = fields.map(item => item.key);
    }

    return (
      <>
        {fields.map((field) => (
          <Field
            schema={items}
            parentName={schema.$id}
            key={field.key}
            isInit={initFieldRef.current?.includes(field.key)}
            field={field}
            disabled={disabled}
            setErrorField={setErrorField}
            removeErrorField={removeErrorField}
            remove={remove}
            readOnly={readOnly} />
        ))}
        <Button
          disabled={disabled || readOnly}
          type="dashed"
          onClick={() => add()}
          style={{ width: '60%' }}
          icon={<PlusOutlined />}
        >
          {addBtnName || '新增一条'}
        </Button>
      </>
    )
  }

  return (
    <Form form={form} onValuesChange={onValuesChange} style={{ width: '100%' }}>
      <Form.List name={schema.$id} initialValue={value}>{renderFields}</Form.List>
    </Form>
  );
}

