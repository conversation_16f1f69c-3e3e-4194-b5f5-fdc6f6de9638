import { useState, useRef, useMemo, useEffect } from 'react';
import { Input, Button, Descriptions, Table, message } from 'antd';
import prefetchConfigApi from '@/api/prefetch-config';
import { IPrefetchMtopData } from '@/interface/prefetch-config';

interface mtopInputProps {
  defaultValue: number;
  onChange: Function;
  value: IPrefetchMtopData;
  disabled: boolean;
}

const hsfParamsColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '对应参数',
    dataIndex: 'mapping',
    key: 'mapping',
  }
];
const mtopParamsColumns = [
  {
    title: '名称',
    dataIndex: 'mtopName',
    key: 'mtopName',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '是否必须',
    dataIndex: 'required',
    key: 'required',
  }
]

export default ({ onChange, value, disabled }: mtopInputProps) => {
  const [messageApi, contextHolder] = message.useMessage();

  const [mtopApi, setMtopApi] = useState(value?.mtopApi || '');
  const [mtopVersion, setMtopVersion] = useState(value?.mtopVersion || '');
  const [hsfId, setHsfId] = useState(value?.hsfData?.hsfId || '');
  const [hsfMethod, setHsfMethod] = useState(value?.hsfData?.hsfMethod || '');
  const [hsfParameterTypes, setHsfParameterTypes] = useState(value?.hsfData?.hsfParameterTypes || '');
  const [hsfParamsMapping, setHsfParamsMapping] = useState(value?.hsfData?.hsfParamsMapping || null);
  const [mtopParamsMapping, setMtopParamsMapping] = useState(value?.hsfData?.mtopParamsMapping || null);

  useEffect(() => {
    try {
      // 设置默认值
      if (value?.mtopApi && value?.mtopVersion) {
        setMtopVersion(value.mtopVersion);
        setMtopApi(value.mtopApi);
      }
      if (value && value.hsfData) {
        setHsfId(value?.hsfData?.hsfId || '');
        setHsfMethod(value?.hsfData?.hsfMethod || '');
        setHsfParameterTypes(value?.hsfData?.hsfParameterTypes || '');
        value?.hsfData?.hsfParamsMapping && setHsfParamsMapping(JSON.parse(value?.hsfData?.hsfParamsMapping));
        value?.hsfData?.mtopParamsMapping && setMtopParamsMapping(JSON.parse(value?.hsfData?.mtopParamsMapping));
      }
    } catch (error) {
      console.error(error);
    }
  }, [value])

  function onMtopApiChange(value: any) {
    setMtopApi(value);
    onChange({ mtopApi: value, mtopVersion })
  }
  function onMtopVersionChange(value: any) {
    setMtopVersion(value);
    onChange({ mtopApi, mtopVersion: value })
  }
  function onHsfDataChange(value: any) {
    if (value?.mtopData?.success) {
      const hsfInfo = value?.mtopData?.data?.apiMappingInfo?.apiMappingHsfInfo;
      const mtopInfo = value?.mtopData?.data?.apiRequestParamInfo;
      const hsfIdValue = hsfInfo?.hsfService + ':' + hsfInfo?.hsfProductVersion;
      const hsfMethodValue = hsfInfo?.hsfMethodName;
      const hsfParameterTypesValueArr = hsfInfo?.hsfMethodParamMappings.map((item: any) => {
        return item.hsfParamType
      });
      const hsfParamsMappingValue = parseHsfParamsMapping(hsfInfo);
      const mtopParamsMappingValue = parseMtopParamsMapping(mtopInfo);
      onChange({
        mtopApi,
        mtopVersion,
        hsfData: {
          hsfId: hsfIdValue,
          hsfMethod: hsfMethodValue,
          hsfParameterTypes: JSON.stringify(hsfParameterTypesValueArr),
          hsfParamsMapping: JSON.stringify(hsfParamsMappingValue),
          mtopParamsMapping: JSON.stringify(mtopParamsMappingValue)
        }
      })
    } else {
      messageApi.open({
        type: 'error',
        content: '获取hsf信息失败',
      });
    }
  }
  function getHsfData() {
    if (mtopApi && mtopApi.length > 8 && mtopVersion && mtopVersion.length === 3) {
      prefetchConfigApi.getMtopData(mtopApi, mtopVersion).then(res => {
        console.log('getMtopDataSuccess', res)
        onHsfDataChange(res);
      }).catch((error) => {
        messageApi.open({
          type: 'error',
          content: '获取hsf信息失败',
        });
        console.log('getMtopDataError', error)
      })
    } else {
      messageApi.open({
        type: 'warning',
        content: 'mtop api 或者 version 填写不正确',
      });
    }
  }
  function parseHsfParamsMapping(hsfInfo: any) {
    const leafHsfMethodParamMappings = hsfInfo?.hsfMethodParamMappings[0].leafHsfMethodParamMappings || [];
    const newHsfParamsMapping = leafHsfMethodParamMappings.map((item: any) => {
      return {
        name: item.hsfParamName,
        mapping: item.hsfParamMapping
      }
    })
    return newHsfParamsMapping;
  }
  function parseMtopParamsMapping(mtopInfo: Array<any> = []) {
    const newMtopParamsMapping = mtopInfo.map((item: any) => {
      return {
        required: String(item.paramRequired),
        type: item.paramType,
        description: item.description,
        name: item.paramName
      }
    });
    return newMtopParamsMapping;
  }
  return (
    <div style={{ width: '100%' }} className="mtop-input-container">
      {contextHolder}
      <Input
        placeholder="mtop api"
        style={{ width: '50%' }}
        onChange={(e) => onMtopApiChange(e.target.value.trim())}
        value={mtopApi}
        disabled={disabled}
      />
      <Input
        placeholder="mtop version"
        style={{ width: '20%', marginLeft: '12px' }}
        value={mtopVersion}
        disabled={disabled}
        onChange={(e) => onMtopVersionChange(e.target.value.trim())}
      />
      {
        disabled ? null : <Button type="primary" style={{ marginLeft: '12px' }} onClick={getHsfData}>获取hsf信息</Button>
      }
      {
        hsfId ? <Descriptions bordered column={1} style={{ marginTop: '12px' }} contentStyle={{ height: '24px' }}>
          <Descriptions.Item label="hsfId">{hsfId}</Descriptions.Item>
          <Descriptions.Item label="hsfMethod">{hsfMethod}</Descriptions.Item>
          <Descriptions.Item label="hsfParameterTypes">{hsfParameterTypes}</Descriptions.Item>
          {
            hsfParamsMapping ? <Descriptions.Item label="hsfParams" contentStyle={{ padding: '0' }}>
              <Table dataSource={hsfParamsMapping} columns={hsfParamsColumns} bordered={false} size="small" />
            </Descriptions.Item> : null
          }
          {
            mtopParamsMapping ? <Descriptions.Item label="mtopParams" contentStyle={{ padding: '0' }}>
              <Table dataSource={mtopParamsMapping} columns={mtopParamsColumns} bordered={false} size="small" />
            </Descriptions.Item> : null
          }
        </Descriptions> : null
      }
      <div>
      </div>
    </div>
  );
}
