import { useState, useRef, useMemo } from 'react';
import { Select, Spin } from 'antd';
import { SelectProps } from 'antd/es/select';
import debounce from 'lodash/debounce';
import aoneApi from '@/api/aone';
import { IAone } from '@/interface/aone';

interface ValueType {
  label: React.ReactNode;
  value: string;
}

export interface Props<T> extends SelectProps<T> {
  debounceTimeout?: number;
}

export default ({ debounceTimeout = 800, ...selectProps }: Props<ValueType>) => {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState<ValueType[]>([]);
  const fetchRef = useRef(0);

  const debounceFetcher = useMemo(() => {
    const loadOptions = async (value: string) => {
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      setOptions([]);
      setFetching(true);

      const newOptions = await fetchOptions(value);

      if (fetchId !== fetchRef.current) return;
      setOptions(newOptions);
      setFetching(false);
    };

    return debounce(loadOptions, debounceTimeout);
  }, [fetchOptions, debounceTimeout]);

  return (
    <Select
      mode="multiple"
      placeholder="请填写 Aone ID"
      onSearch={debounceFetcher}
      labelInValue
      notFoundContent={fetching ? <Spin size="small" /> : null}
      style={{ width: '100%' }}
      options={options}
      {...selectProps}
    />
  );
}

async function fetchOptions(id: string): Promise<ValueType[]> {
  const res = await aoneApi.get(id);

  if (!res?.data?.id) return [];

  return [convertToFormFields(res.data)];
}

export function convertToFormFields(aone: IAone) {
  const { id, stamp, subject } = aone;
  const stampMap = {
    Req: {
      name: 'req',
      cnName: '需求'
    },
    Bug: {
      name: 'issue',
      cnName: '缺陷'
    },
    Issue: { // Issue类型为临时兼容酒店发布后台创建游离分支的场景，实际对应Bug
      name: 'issue',
      cnName: '缺陷'
    },
    Task: {
      name: 'task',
      cnName: '任务'
    },
    Risk: {
      name: 'risk',
      cnName: '风险'
    },
  } 
  
  const title = subject || `${stampMap[stamp].cnName}:${id}`;
  const url = `https://aone.alibaba-inc.com/${stampMap[stamp].name}/${id}`;

  return {
    label: <a href={url} target="_blank">{title}</a>,
    value: JSON.stringify({ id, stamp, subject })
  } 
}