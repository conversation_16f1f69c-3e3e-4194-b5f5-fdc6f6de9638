import React from 'react';
import FormRender from 'form-render';
import Aone from './aone';
import User from './user';
import User2 from './user2';
import WList from './w-list';
import StepSlider from './step-slider';
import Editor from './editor';
import MtopInput from './mtop-input';

// 建议统一使用一个form-render，并把所有的自定义组件统一注入
const FR = (props: any) => {
  const { removeHiddenData = false, widgets = {} } = props;
  return <FormRender {...props} removeHiddenData={removeHiddenData} widgets={{ aone: Aone, user: User, user2: User2, wList: WList, stepSlider: StepSlider, editor: Editor, mtopInput: MtopInput, ...widgets }} />;
};

export default FR;
