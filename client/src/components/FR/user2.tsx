import { useState, useRef, useMemo, useEffect } from 'react';
import { Select, Spin, Avatar } from 'antd';
import { SelectProps } from 'antd/es/select';
import debounce from 'lodash/debounce';
import amdpApi from '@/api/amdp';
import { IUser } from '@/interface/user';
import { IBucSimpleUser } from '@/interface/buc';

interface OptionType {
  label: React.ReactNode;
  value: string;
}

export interface Props<T> extends SelectProps<T> {
  debounceTimeout?: number;
}

export default ({ debounceTimeout = 800, ...selectProps }: Props<OptionType>) => {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState<OptionType[]>([]);
  const fetchRef = useRef(0);

  const debounceFetcher = useMemo(() => {
    const loadOptions = async (value: string) => {
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      setOptions([]);
      setFetching(true);

      const newOptions = await fetchOptions(value);

      if (fetchId !== fetchRef.current) return;
      setOptions(newOptions);
      setFetching(false);
    };

    return debounce(loadOptions, debounceTimeout);
  }, [fetchOptions, debounceTimeout]);

  return (
    <Select
      mode="multiple"
      onSearch={debounceFetcher}
      labelInValue
      filterOption={false}
      notFoundContent={fetching ? <Spin size="small" /> : <span>无结果</span>}
      style={{ width: '100%' }}
      options={options}
      {...selectProps}
    />
  );
}

async function fetchOptions(keyword: string): Promise<OptionType[]> {
  const res = await amdpApi.searchByKeyword(keyword);
  if (!res?.success || !res.data) return [];

  return res.data.list.map(amdpToFormFields);
}

export function amdpToFormFields(user: IUser, index: number) {
  const { workid, name, lastName } = user;

  return {
    label: <span style={{ display: 'inline-flex', alignItems: 'center' }}><Avatar key={index} src={`https://work.alibaba-inc.com/photo/${workid}.100x100.jpg`} size={18} style={{ marginRight: '6px' }} />{name}（{lastName}-{workid}）</span>,
    value: workid
  }
}


export function bucToFormFields(user: IBucSimpleUser, index: number) {
  const { empId, nickNameCn, lastName } = user;

  return {
    label: <span style={{ display: 'inline-flex', alignItems: 'center' }}><Avatar key={index} src={`https://work.alibaba-inc.com/photo/${empId}.100x100.jpg`} size={18} style={{ marginRight: '6px' }} />{nickNameCn}（{lastName}-{empId}）</span>,
    value: empId
  }
}