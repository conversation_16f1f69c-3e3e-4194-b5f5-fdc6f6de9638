import React from 'react';
import { Radio } from 'antd';


const RadioGroup = (props: any) => {
  const { schema, onChange: formChange, value, options } = props;

  return (
    <>
      <Radio.Group
        options={options}
        onChange={formChange}
        value={value}
        size="small"
        optionType="button"
        // buttonStyle="solid"
      />
    </>
  );
};

export default RadioGroup;
