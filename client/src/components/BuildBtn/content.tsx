import React, { useEffect, useState, useRef } from 'react';
import { unstable_batchedUpdates } from "react-dom";
import { Tag, Image, Space, Tooltip, List, Spin, Row, Col, Empty, Alert, Popconfirm } from 'antd';
import { DownloadOutlined, LinkOutlined, CopyOutlined, ProfileOutlined } from '@ant-design/icons';
import { IBuildTask } from '@/interface/build';
import { IGitRepoInfo } from '@/interface/git';
import buildApi from '@/api/build';

import './content.less';
import { BUILD_PLAtFORM_CN_NAME } from '@/const/build';
import AnalyzeSize from '../AnalyzeSize';

const PAGE_SIZE = 5;

/** Tab内容模板 */
interface ContentProps extends React.HTMLAttributes<HTMLDivElement> {
  /** 项目名称 */
  projectName: string;
  /** 代码分支 */
  branchName: string;
  /** 是否只展示自己的 */
  filterMine?: boolean;
  /** 更新标识 */
  refreshTag: number;
  /** 项目仓库 */
  gitRepo: IGitRepoInfo;
  /** 构建方法 */
  build: ({ pagePath, pageQuery, env, customArgv }: { pagePath?: string; pageQuery?: string; env?: string; customArgv?: IBuildTask['customArgv'] }) => void;
}

export default function Content({ projectName, branchName, filterMine, refreshTag, gitRepo, build }: ContentProps) {
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(true);
  const [buildTasks, setBuildTasks] = useState<IBuildTask[]>([]);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const isEmpty = buildTasks.length === 0;

  useEffect(() => {
    setPage(1);
    getBuildTasks(1);
  }, [refreshTag])

  function onPageChange(page: number) {
    setPage(page);
    getBuildTasks(page)
  }

  /** 查询打码任务 */
  function getBuildTasks(page: number) {
    setLoading(true);

    buildApi.list({
      branchName,
      projectName,
      filterMine,
      page,
      pageSize: PAGE_SIZE
    })
      .then((res) => {
        unstable_batchedUpdates(() => {
          if (res.success && res.data) {
            const { list, total } = res.data;
            setBuildTasks(list)
            setTotal(total)
          } else {
            throw Error(res?.errorMsg)
          }
        })
      })
      .catch((err: any) => {
        setError(err?.message || '查询失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  return (
    <Spin wrapperClassName="build-content" spinning={loading} size="large">
      {/* 报错 */}
      {
        error &&
        <Alert
          message="构建错误"
          description={error}
          type="error"
          showIcon
        />
      }

      <List
        itemLayout="vertical"
        pagination={{
          current: page,
          onChange: onPageChange,
          pageSize: PAGE_SIZE,
          total
        }}
      >
        {buildTasks.map((task, index) => <Task build={build} gitRepo={gitRepo} projectName={projectName} branchName={branchName} buildTask={task} filterMine={filterMine} key={index} />)}

        {/* 为空 */}
        {
          !loading && isEmpty &&
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        }
      </List>
    </Spin>
  )
}

interface IPollTaskProps {
  /** 项目名称 */
  projectName: string;
  /** 云构建任务id */
  taskId: string;
  /** 是否只展示自己的 */
  filterMine?: boolean;
  /** 轮询完成 */
  onComplete: (data: IBuildTask) => void;
}
class PollTask {
  projectName: string;

  taskId: string;

  filterMine: boolean | undefined;

  pollTid = 0;

  onComplete: (data: IBuildTask) => void;

  constructor({ projectName, taskId, filterMine, onComplete }: IPollTaskProps) {
    this.projectName = projectName;
    this.taskId = taskId;
    this.filterMine = filterMine;
    this.onComplete = onComplete;

    this.query();
  }

  query = () => {
    let shouldPoll = true;

    buildApi.get({
      projectName: this.projectName,
      taskId: this.taskId,
      filterMine: this.filterMine
    })
      .then((res) => {
        // 如果构建状态变更为非构建中，则更新 buildingTask 并取消轮询
        if (res.data && res.data.status !== 0) {
          shouldPoll = false;
          this.onComplete(res.data);
        }
      })
      .finally(() => {
        if (!shouldPoll) return;

        this.pollTid = window.setTimeout(this.query, 5000)
      });
  }

  destroy() {
    window.clearTimeout(this.pollTid)
  }
}

/** 任务模板 */
interface TaskProps extends React.HTMLAttributes<HTMLDivElement> {
  /** 项目名称 */
  projectName: string;
  /** 项目仓库 */
  gitRepo: IGitRepoInfo;
  /** 构建任务 */
  buildTask: IBuildTask;
  /** 是否只展示自己的 */
  filterMine?: boolean;
  /** 分支名 */
  branchName: string;
  /** 构建方法 */
  build: ({ pagePath, pageQuery, env, customArgv }: { pagePath?: string; pageQuery?: string; env?: string; customArgv?: IBuildTask['customArgv'] }) => void;
}

function Task({ branchName, projectName, gitRepo, buildTask, filterMine, build }: TaskProps) {
  const [latestData, setLatestData] = useState(buildTask);
  const pollTaskRef = useRef<PollTask | null>();
  const {
    commitId,
    env, pagePath, pageQuery, platform,
    status, dist, imgUrl,
    creator, gmtCreate,
    taskId, customArgv
  } = buildTask.taskId === latestData.taskId ? latestData : buildTask;
  const { minifyBuild, minifyBuildPackages, remark } = customArgv;
  const platformCnName = BUILD_PLAtFORM_CN_NAME[platform];

  let statusNode = <Tag color="Tag">未知</Tag>;
  if (status === -1) {
    statusNode = <Tag color="#f5222d">失败</Tag>
  } else if (status === 0) {
    statusNode = <Tag color="#096dd9">构建中</Tag>
  } else if (status === 1) {
    statusNode = <Tag color="#389e0d">成功</Tag>
  }

  useEffect(() => {
    // 如有存在的轮询任务，则先销毁
    if (pollTaskRef.current) pollTaskRef.current.destroy();

    // 如果处在构建中，则开始轮询
    if (status === 0) {
      pollTaskRef.current = new PollTask({
        projectName,
        taskId,
        filterMine,
        onComplete: (data) => setLatestData(data)
      });
    } else {
      pollTaskRef.current = null;
    }

    return () => {
      if (pollTaskRef.current) pollTaskRef.current.destroy();
    }
  }, [taskId])

  function cloneAndBuild() {
    build({ pagePath, pageQuery, env, customArgv })
  }

  return (
    <List.Item>
      <Row align="middle">
        <Col style={{ display: 'flex', fontSize: 0 }} span={3}>
          <Spin spinning={status === 0} size="default">
            <Image
              width={90}
              height={90}
              preview={status === 1}
              src={imgUrl}
            />
          </Spin>
        </Col>
        <Col span={5}>
          <Space className="col-item" direction="vertical">
            <div className="single-row">构建详情：<a href={`https://builder.alibaba-inc.com/task/${taskId}`} target="_blank">{taskId}</a></div>
            <div className="single-row">构建结果：{statusNode}</div>
            <div className="single-row">构建环境：{env}</div>
            <div className="single-row">构建平台：{platformCnName || <span className="empty-text">未知</span>}</div>
          </Space>
        </Col>
        <Col span={6}>
          <Space className="col-item" direction="vertical">
            <div className="flex-wrapper">
              页面路径：
              {pagePath ? <Tooltip placement="top" title={pagePath}><div className="single-row">{pagePath}</div></Tooltip> : <span className="empty-text">无</span>}
            </div>
            <div className="flex-wrapper">
              页面参数：
              {pageQuery ? <Tooltip placement="top" title={pageQuery}><div className="single-row">{pageQuery}</div></Tooltip> : <span className="empty-text">无</span>}
            </div>
            <div className="flex-wrapper">
              备注：
              {remark ? <Tooltip placement="top" title={remark}><div className="single-row">{remark}</div></Tooltip> : <span className="empty-text">无</span>}
            </div>
            {
              minifyBuild !== undefined ?
                <div className="flex-wrapper">
                  启用按需构建：
                  {<span className="single-row">{minifyBuild ? <Tooltip placement="top" title={minifyBuildPackages?.join(',')}>是 <ProfileOutlined style={{ color: '#1890ff' }} /></Tooltip> : '否'}</span>}
                </div> : null
            }
          </Space>
        </Col>
        <Col span={4}>
          <Space className="col-item" direction="vertical">
            <div className="single-row">Commit 记录：{commitId ? <Tooltip placement="top" title={commitId}><a href={`${gitRepo.url}/commit/${commitId}`} target="_blank"><LinkOutlined /></a></Tooltip> : <span className="empty-text">无</span>}</div>
            <div className="single-row">构建产物：{dist ? <a href={dist}><DownloadOutlined /></a> : <span className="empty-text">无</span>}</div>
            <Popconfirm
              title="确定复用参数新打码？"
              onConfirm={cloneAndBuild}
              okText="确定"
              cancelText="取消"
            ><a><CopyOutlined /> 复用参数新打码</a></Popconfirm>
          </Space>
        </Col>
        <Col span={6}>
          <Space className="col-item" direction="vertical">
            <div className="single-row">构建者：{creator}</div>
            <div className="single-row">构建时间：{gmtCreate}</div>
            <AnalyzeSize type="branch" dist={dist} branchName={branchName} projectName={projectName} gmtModified={gmtCreate} />
          </Space>
        </Col>
      </Row>
    </List.Item>
  )
}

