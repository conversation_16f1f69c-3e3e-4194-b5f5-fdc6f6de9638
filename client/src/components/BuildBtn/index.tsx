import { useState } from 'react';
import { Modal, Tabs, message } from 'antd';
import { UserOutlined, UsergroupAddOutlined } from '@ant-design/icons';
import buildApi, { ICreateReq } from '@/api/build';
import { IGitRepoInfo } from '@/interface/git';
import { IProject } from '@/interface/project';
import Content from './content';
import BuildButton from './build-button';

import './index.less';
import { EBuildPlatform } from '@/const/build';

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  /** 项目 */
  project: IProject;
  /** 代码分支 */
  branchName: string;
  /** 项目仓库 */
  gitRepo: IGitRepoInfo;
}

export default ({ children, project, branchName, gitRepo }: Props) => {
  const [showModal, setShowModal] = useState(false);
  const [showBuildForm, setShowBuildForm] = useState(false);
  const [buildLoading, setBuildLoading] = useState(false);
  const [refreshTag, setRefreshTag] = useState(0);

  /** 打码 */
  function build({ pagePath, pageQuery, env, platform, customArgv }: { pagePath?: string; pageQuery?: string; env?: string; platform?: EBuildPlatform; customArgv?: ICreateReq['customArgv'] }) {
    setBuildLoading(true);
    setShowBuildForm(false);

    buildApi.create({
      branchName,
      projectName: project.name,
      pagePath,
      pageQuery,
      customArgv,
      env,
      platform
    })
      .then((res) => {
        if (res?.success) {
        setRefreshTag(prevState => prevState + 1);
        } else {
          throw Error(res?.errorMsg)
        }
      })
      .catch((err: any) => {
        message.error(err.message || '打码失败，请重试~');
      }).finally(() => {
        setBuildLoading(false)
      });
  }

  function closeModal() {
    setShowModal(false);
  }

  function onClick() {
    setShowModal(true);
  }

  function handleBtnPopoverShow(visible: boolean) {
    setShowBuildForm(visible)
  }

  return <>
    <div onClick={onClick}>
      {children}
    </div>

    <Modal
      open={showModal}
      title=""
      className="build-modal"
      width={1060}
      destroyOnClose
      maskClosable={false}
      onCancel={closeModal}
      footer={[
        <BuildButton build={build} project={project} branchName={branchName} gitRepo={gitRepo} showBuildForm={showBuildForm} buildLoading={buildLoading} handleBtnPopoverShow={handleBtnPopoverShow} />
      ]}
    >
      <div className="tips">
        当前构建分支为 {branchName}
      </div>

      <Tabs
        tabBarStyle={{ marginBottom: '10px' }}>
        <Tabs.TabPane
          tab={<span><UserOutlined />我的打码</span>}
          key="mine"
        >
          <Content build={build} refreshTag={refreshTag} projectName={project.name} gitRepo={gitRepo} branchName={branchName} filterMine />
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={<span><UsergroupAddOutlined />全部打码</span>}
          key="all"
        >
          <Content build={build} refreshTag={refreshTag} gitRepo={gitRepo} projectName={project.name} branchName={branchName} />
        </Tabs.TabPane>
      </Tabs>
    </Modal >
  </>
}
