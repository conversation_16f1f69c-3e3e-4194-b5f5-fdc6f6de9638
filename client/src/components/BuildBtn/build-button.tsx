import { useEffect, useState } from 'react';
import { Button, Popover, Input, Form, Radio, Switch, Select, Checkbox, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { EProjectType } from '@/const/project';
import { EPackageType } from '@/const/package';
import packageApi from '@/api/package';
import { IGitRepoInfo } from '@/interface/git';
import { IAppStructure } from '@/interface/package';
import { IProject } from '@/interface/project';
import { trimCollection } from '@/utils/base';
import { IBuildTask } from '@/interface/build';

import './index.less';
import { BUILD_PLAtFORM_CN_NAME, EBuildPlatform } from '@/const/build';

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  /** 项目 */
  project: IProject;
  /** 代码分支 */
  branchName: string;
  /** 项目仓库 */
  gitRepo: IGitRepoInfo;
  /** ”新打码“按钮是否loading */
  buildLoading: boolean;
  /** ”新打码“按钮悬浮后表单是否展示 */
  showBuildForm: boolean;
  /** 支持按需构建(外部传入) */
  minifyBuild?: boolean;
  /** 支持快速构建(外部传入) */
  fastBuild?: boolean;
  /** 支持切换环境(外部传入) */
  switchEnv?: boolean;
  /** 构建方法 */
  build: ({ pagePath, pageQuery, env, platform, customArgv }: { pagePath?: string; pageQuery?: string; env?: string; platform?: EBuildPlatform; customArgv?: IBuildTask['customArgv'] }) => void;
  /** ”新打码“按钮悬浮浮层是否展示的处理 */
  handleBtnPopoverShow: (visible: boolean) => void;
}

export default ({ project, branchName, gitRepo, buildLoading, showBuildForm,
  minifyBuild, fastBuild, switchEnv, build, handleBtnPopoverShow }: Props) => {
  const [pagePath, setPagePath] = useState('');
  const [appStructure, setAppStructure] = useState<IAppStructure>();
  const [structureLoading, setStructureLoading] = useState(true);
  // 是否支持按需构建TODO: 后续需要改成根据项目“是否支持按需构建”来判断，前提是项目表里新增这个字段
  const supportMinifyBuild = [EProjectType.ALIPAY, EProjectType.WEIXIN].includes(project.type) || minifyBuild;
  // 是否支持快速构建TODO: 后续需要改成根据项目“是否支持快速构建”来判断，前提是项目表里新增这个字段
  const supportFastBuild = [EProjectType.WEIXIN].includes(project.type) || fastBuild;
  // 是否支持切换环境TODO: 后续需要改成根据项目“是否支持切换环境”来判断，前提是项目表里新增这个字段
  const supportSwitchEnv = [EProjectType.WEIXIN, EProjectType.BYTEDANCE, EProjectType.ALIPAY].includes(project.type) || switchEnv;
  // 默认勾选异步包
  const checkedAsyncPkg = [EProjectType.ALIPAY].includes(project.type);
  // 页面集合
  const pages = appStructure?.packages.flatMap(pkg => pkg.pages?.flatMap(page => [page.source]) || []) || [];
  // 支持的构建平台
  const platformList = project.wsyConfig?.platformList;
  
  // 以页面形式展示
  useEffect(() => {
    if (!branchName) return;
    getStructureByBranch({ branchName, branchUrl: gitRepo.sshUrl })
  }, [branchName])

  /** 获取小程序结构 */
  function getStructureByBranch({ branchName, branchUrl }: { branchName: string; branchUrl: string; }) {
    setStructureLoading(true);

    packageApi.getStructureByBranch({
      branchName,
      branchUrl
    })
      .then((res) => {
        if (res?.success && res.data) {
          setAppStructure(res.data);
        } else {
          throw Error(res?.errorMsg)
        }
      }).finally(() => {
        setStructureLoading(false)
      });
  }

  function onFormSubmit({ pagePath, pageQuery, env, platform, minifyBuild, remark, minifyBuildPackages }: { pagePath?: string; pageQuery?: string; env?: string; platform?: EBuildPlatform; minifyBuild?: boolean, minifyBuildPackages?: string[], remark?: string }) {
    build(trimCollection({
      pagePath,
      pageQuery,
      env,
      platform,
      customArgv: {
        minifyBuild,
        minifyBuildPackages,
        fastbuild: minifyBuild ? 0 : 1, // 开启按需构建时需要关闭快速构建
        remark,
      }
    }))
  }

  function MinifyBuildFormItem() {
    const defaultValue = appStructure?.minifyBuildPackages?.map(pkg => {
      return pkg.packageName
    }) || [];
    const options = appStructure?.packages.map(pkg => {
      const isMain = pkg.packageType.name === EPackageType.Main;
      const isMainAsyncPkg = checkedAsyncPkg && ['main-async', 'main-async2'].includes(pkg.packageName);
      // 如果是主包，但又没被包含到默认勾选值里，则再添加一下
      if ((isMainAsyncPkg || isMain) && !defaultValue.includes(pkg.packageName)) {
        defaultValue.push(pkg.packageName)
      }
      return {
        label: `${pkg.packageName}${isMain ? '(主包)' : isMainAsyncPkg ? '（异步包）' : ''}`,
        value: pkg.packageName,
        disabled: isMain || isMainAsyncPkg // 主包 & 支端主包异步包 必须参与构建
      }
    }) || [];

    return (
      <>
        <Form.Item
          name="minifyBuild"
          valuePropName="checked"
          initialValue={false}
          label="启用按需构建"
          tooltip="按需构建：只构建选中的分包，以提速打码"
          extra={supportFastBuild ? <Tooltip title="快速构建：若基于当前分支最新变更打过码（区分构建环境，不区分页面路径等参数），则本次打码会复用已有的构建产物；建议能使用快速构建的情况尽量使用快速构建"> 启用后无法使用快速构建<QuestionCircleOutlined /></Tooltip> : null}
        >
          <Switch loading={structureLoading} />
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.minifyBuild !== currentValues.minifyBuild}
        >
          {({ getFieldValue }) => appStructure && getFieldValue('minifyBuild') === true ? (
            <Form.Item
              name="minifyBuildPackages"
              label="参与构建的包"
              tooltip="如果分支代码中已有app.dev.json，则会解析并默认选中；勾选操作不会变更分支代码"
              initialValue={defaultValue}
            >
              <Checkbox.Group
                options={options}
              />
            </Form.Item>
          ) : null}
        </Form.Item>
      </>
    )
  }

  function PageInfoFormItem() {
    return (
      <>
        <Form.Item name="pagePath" label="页面路径">
          <Select
            showSearch
            loading={structureLoading}
            size="middle"
            onChange={value => setPagePath(value)}
            placeholder={`${structureLoading ? '加载中，' : ''}可不选`}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            allowClear
            options={pages.map(page => ({ label: page, value: page }))}
          />
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.pagePath !== currentValues.pagePath}
        >
          {({ getFieldValue }) => getFieldValue('pagePath') ? (
            <Form.Item name="pageQuery" label="页面参数">
              <Input
                size="middle"
                disabled={!pagePath}
                placeholder="如 id=a&type=hotel，可不填"
              />
            </Form.Item>
          ) : null}
        </Form.Item>
      </>
    )
  }

  function BuildForm() {
    const envOptions = [
      { label: '线上', value: 'online' },
      { label: '预发', value: 'pre' },
    ];
    return (
      <Form
        className="build-form"
        onFinish={onFormSubmit}
      >
        <PageInfoFormItem />

        <Form.Item name="remark" label="打码备注">
          <Input
            size="middle"
            placeholder="可不填"
          />
        </Form.Item>

        {supportSwitchEnv ? <Form.Item name="env" label="构建环境" initialValue="online" tooltip="选择预发会构建预发的mtop环境，线上同理">
          <Radio.Group options={envOptions} />
        </Form.Item> : null}

        {platformList ? <Form.Item name="platform" label="构建平台" initialValue={platformList[0]}>
          <Select options={platformList.map(item => ({ value: item, label: BUILD_PLAtFORM_CN_NAME[item] }))} />
        </Form.Item> : null}

        {supportMinifyBuild ? <MinifyBuildFormItem /> : null}

        <Button className="build-submit" type="primary" htmlType="submit">确定</Button>
      </Form >
    )
  }

  return <>
    <Popover
      visible={showBuildForm}
      onVisibleChange={handleBtnPopoverShow}
      key="build"
      content={BuildForm}
      placement="left"
      trigger="click">
      <Button type="primary" loading={buildLoading}>新打码</Button>
    </Popover>
  </>
}
