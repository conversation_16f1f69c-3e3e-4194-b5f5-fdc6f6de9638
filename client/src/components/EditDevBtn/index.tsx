import { useEffect, useState } from 'react';
import { Modal, message, Spin, notification, Table } from 'antd';
import type { TableProps } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { useForm } from 'form-render';
import { useModel } from 'umi';
import { IIterBranch } from '@/interface/iter-branch';
import { IFreeDevBranch } from '@/interface/free-dev-branch';
import { IDevBranch } from '@/interface/dev-branch';
import { IProject } from '@/interface/project';
import { EProjectType } from '@/const/project';
import { formateDate } from '@/utils/date';
import FormRender from '@/components/FR';
import { convertToFormFields as convertToQaListFields } from '@/components/FR/user';
import { convertToFormFields as convertToAoneListFields } from '@/components/FR/aone';
import devBranchApi from '@/api/dev-branch';
import freeDevBranch<PERSON>pi, { IFreeDevBranchEditRes } from '@/api/free-dev-branch';
import { trimCollection } from '@/utils/base';
import { getNewAndBiggestVersion } from '@/utils/git';
import getSchema from './schema';


import './index.less';

export enum Action {
  Create = 'create', // 创建
  Update = 'update', // 更新
  Read = 'read', // 查看
}

interface DataType {
  key: number;
  projectTitle: string;
  branchName: string;
  isSuccess: boolean;
  failReason: string;
}

interface Props {
  /** 是否是游离开发分支 */
  isFreeDevBranch?: boolean;
  /** 开发分支 */
  devBranch?: IFreeDevBranch | IDevBranch;
  /** 项目信息 */
  project?: IProject;
  /** 所属迭代分支详情，普通开发分支才有 */
  iterBranch?: IIterBranch;
  /** 动作 */
  action: Action;
  /** 创建完成回调 */
  onCreateEnd?: (devBranch: IDevBranch | IFreeDevBranch) => void;
  /** 更新完成回调 */
  onUpdateEnd?: (devBranch: IDevBranch | IFreeDevBranch) => void;
}

const Comp: React.FC<Props> = ({
  children, project,
  isFreeDevBranch = false,
  devBranch, iterBranch,
  action, onCreateEnd, onUpdateEnd
}) => {
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [branchNamePrefix, setBranchNamePrefix] = useState('');
  const [branchMaxLength, setBranchMaxLength] = useState<number>(25);
  const [projectList] = useModel('projectList');
  const form = useForm();
  const currentUser = useModel('user');
  const { name: projectName, type: projectType, deliverConfig: { gitProjectId = 0 } = {} } = project || {};
  const isComponentProject = projectType === EProjectType.COMPONENT;

  // 设置分支名前缀
  const setBranchName = async () => {
    setLoading(true);
    let _branchNamePrefix = '';
    if (isFreeDevBranch) {
      if (devBranch) {
        const normalReg = /free-dev\/\d{8}-/;
        const moduleReg = /free-dev\/\d{8}-(\d+\.\d+\.\d+)-/;
        const matchRes = devBranch.branchName.match(isComponentProject ? moduleReg : normalReg);
        _branchNamePrefix = matchRes ? matchRes[0] : '';
      } else {
        const ver = isComponentProject ? await getNewAndBiggestVersion(gitProjectId, action) : '';
        _branchNamePrefix = isComponentProject
          ? `free-dev/${formateDate('YYYYMMDD')}-${ver}-`
          : `free-dev/${formateDate('YYYYMMDD')}-`;
      }
    } else {
      if (devBranch) {
        const normalReg = /^dev\/(\d+\.\d+\.\d+)-/;
        const moduleReg = /^dev\/(?:\d+\.\d+\.\d+)-(\d+\.\d+\.\d+)-/;
        const matchRes = devBranch.branchName.match(isComponentProject ? moduleReg : normalReg);
        _branchNamePrefix = matchRes ? matchRes[0] : '';
      } else {
        const ver = isComponentProject ? await getNewAndBiggestVersion(gitProjectId, action) : '';
        _branchNamePrefix = isComponentProject ? `dev/${iterBranch?.version}-${ver}-` : `dev/${iterBranch?.version}-`;
      }
    }
    setBranchNamePrefix(_branchNamePrefix);
    setBranchMaxLength(100 - (_branchNamePrefix || '').length);
    form?.setSchemaByPath('branchName', {
      props: {
        addonBefore: _branchNamePrefix,
      },
    });
    setLoading(false);
  };

  // 行动名称
  let actionName = '';
  if (action === Action.Create) actionName = '创建';
  else if (action === Action.Update) actionName = '更新';
  else if (action === Action.Read) actionName = '查看';

  // 接口
  const api = isFreeDevBranch ? freeDevBranchApi : devBranchApi;

  const onFormMount = () => {
    // 当有值时，回填表单
    if (devBranch) {
      form.setValues({
        devId: devBranch.devId,
        branchName: devBranch.branchName.split(branchNamePrefix)[1],
        description: devBranch.description,
        aoneList: devBranch.aoneList.map(convertToAoneListFields),
        mergeCode: devBranch.mergeCode,
        npmList: devBranch.npmList,
        npmResolutionList: devBranch.npmResolutionList,
        bizLine: devBranch.bizLine,
        qaList: devBranch.qaList?.map(convertToQaListFields) || []
      })
    }
  }

  const onFormSubmit = () => {
    form.submit();
  };

  const onFormCancel = () => {
    form.resetFields();
    setShowModal(false);
  };

  const onFormFinish = (formData: any, errors: any) => {
    if (loading || errors.length > 0) return;

    // branchName 加上前缀
    formData.branchName = branchNamePrefix + formData.branchName;
    // 处理 qaList
    formData.qaList = formData.qaList?.map((item: any) => JSON.parse(item.value)) || [];
    // 处理 aoneList
    formData.aoneList = formData.aoneList?.map((item: any) => JSON.parse(item.value)) || [];
    // 处理pkgVersion
    const pkgVer = isFreeDevBranch ? /^free-dev\/\d{8}-(\d+\.\d+\.\d+)-/ig.exec(branchNamePrefix)?.[1]
    : /^dev\/(?:\d+\.\d+\.\d+)-(\d+\.\d+\.\d+)-/ig.exec(branchNamePrefix)?.[1];
    formData.pkgVersion = isComponentProject ? pkgVer : '';
    setLoading(true);

    const params = Object.assign({}, formData);
    if (action === Action.Create) {
      // 项目类型
      params.projectType = projectType;
      if (isFreeDevBranch) {
        params.projectName = projectName;
      } else {
        params.iterId = iterBranch?.iterId;
      }
    }

    // 过滤空字符串
    api[action === Action.Create ? 'create' : 'update'](trimCollection(params)).then((res) => {
      if (res.success && res.data) {
        setShowModal(false);
        setTimeout(() => form.resetFields(), 500); // 迟一点清空，省得闪一下

        // 游离分支会有额外的“项目同步”逻辑
        if (isFreeDevBranch) {
          const editRes = res.data as IFreeDevBranchEditRes['data'];
          if (Object.keys(editRes.otherFreeDevBranchMap).length > 0) {
            showSyncProjectRes(editRes.freeDevBranch.branchName, editRes.otherFreeDevBranchMap)
          } else {
            message.info(`${actionName}成功`);
          }

          if (action === Action.Create) {
            onCreateEnd && onCreateEnd(editRes.freeDevBranch)
          } else {
            onUpdateEnd && onUpdateEnd(editRes.freeDevBranch)
          }
        } else {
          message.info(`${actionName}成功`);
          if (action === Action.Create) {
            onCreateEnd && onCreateEnd(res.data as IDevBranch)
          } else {
            onUpdateEnd && onUpdateEnd(res.data as IDevBranch)
          }
        }
      } else {
        throw Error(res?.errorMsg || `${actionName}失败`);
      }
    })
      .catch((err) => {
        message.error(err.message, 10);
      })
      .finally(() => setLoading(false));
  }

  function extendsSchema(schema: ReturnType<typeof getSchema>) {
    schema.properties.npmList.props.action = action;
    schema.properties.npmResolutionList.props.action = action;
    // 添加 branchName 字段的前缀
    schema.properties.branchName.props.addonBefore = branchNamePrefix;

    // “同步项目”字段处理
    if (projectList && isFreeDevBranch) {
      const projectName = project ? project.name : devBranch?.projectName;
      const { syncSupported, canSyncProjectList } = isSyncSupported(projectName);

      if (syncSupported) {
        schema.properties.syncProjects.hidden = false;
        schema.properties.syncProjects.props.options = projectList.filter(project => projectName !== project.name && canSyncProjectList.includes(project.name))
          .map((project) => {
            return {
              label: project.cnName,
              value: project.name,
            }
          })
      }
    }

    if (action === Action.Update) {
      schema.properties.branchName.disabled = true; // 禁止修改分支名
    }

    if ((action === Action.Update || action === Action.Read) && devBranch) {
      if (!devBranch.qaList) schema.properties.qaList.hidden = true; // 兼容存量数据
      if (!devBranch.bizLine) schema.properties.bizLine.hidden = true; // 兼容存量数据
    }

    return schema;
  }

  const handleClick = async () => {
    setBranchName();
    setShowModal(true)
  }

  function showSyncProjectRes(branchName: string, otherFreeDevBranchMap: IFreeDevBranchEditRes['data']['otherFreeDevBranchMap']) {
    const columns: TableProps<DataType>['columns'] = [
      {
        title: '结果',
        dataIndex: 'isSuccess',
        key: 'isSuccess',
        align: 'center',
        width: 100,
        render: (isSuccess) => isSuccess ? <span style={{ color: '#389e0d' }}><CheckCircleOutlined /> 成功</span> : <span style={{ color: '#cf1322' }}><CloseCircleOutlined /> 失败</span>,
      },
      {
        title: '同步项目',
        dataIndex: 'projectTitle',
        align: 'center',
        width: 180,
        key: 'projectTitle',
      },
      {
        title: '游离分支',
        dataIndex: 'branchName',
        align: 'center',
        width: 250,
        key: 'branchName',
      },
      {
        title: '失败原因',
        dataIndex: 'failReason',
        align: 'center',
        key: 'failReason',
      }
    ];
    const data: DataType[] = Object.entries(otherFreeDevBranchMap).map(([projectName, otherFreeDevBranch], index) => {
      const isSuccess = typeof otherFreeDevBranch !== 'string';
      return {
        key: index,
        projectTitle: projectList?.find(project => project.name === projectName)?.cnName || projectName,
        branchName,
        isSuccess,
        failReason: isSuccess ? '' : otherFreeDevBranch
      }
    })

    const Description = <Table style={{ marginTop: '20px' }} columns={columns} dataSource={data} pagination={{ hideOnSinglePage: true }} />

    notification.info({
      message: `${actionName}成功，同步其他项目结果如下：`,
      description: Description,
      placement: 'top',
      style: {
        width: '880px'
      },
      duration: null
    });
  }

  return <>
    <div onClick={handleClick}>
      {children}
    </div>

    <Modal
      title={`${actionName}开发分支`}
      open={showModal}
      onOk={onFormSubmit}
      onCancel={onFormCancel}
      maskClosable={false}
      okButtonProps={{
        loading,
        disabled: form.errorFields?.length > 0
      }}
      width={800}
      destroyOnClose
    >
      <Spin spinning={loading} wrapperClassName="form-wrapper">
        <FormRender
          form={form}
          schema={extendsSchema(getSchema({ currentUser, creatorWorkid: devBranch?.creatorWorkid, branchMaxLength, action, isComponentProject }))}
          removeHiddenData={false}
          disabled={Action.Read === action}
          onMount={onFormMount}
          onFinish={onFormFinish}
          displayType="row" />
      </Spin>
    </Modal>
  </>
}

export default Comp;

function isSyncSupported(projectName?: string) {
  // 支持相互同步的项目
  const supportSyncProject = [
    ['fliggy-allinone', 'fliggy-weixin']
  ];
  let canSyncProjectList = [] as string[];
  const syncSupported = projectName ? !!supportSyncProject.find(arr => {
    if (arr.includes(projectName)) {
      canSyncProjectList = arr;
      return true;
    }
    return false;
  }) : false;

  return {
    syncSupported,
    canSyncProjectList
  }
}