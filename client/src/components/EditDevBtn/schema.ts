import { EMergeCode } from "@/const/dev-branch";
import { IUser } from '@/interface/user';

enum Action {
  Create = 'create', // 创建
  Update = 'update', // 更新
  Read = 'read', // 查看
}

export default function getSchema({ currentUser, creatorWorkid, branchMaxLength, action, isComponentProject }: { currentUser: IUser; creatorWorkid?: string; branchMaxLength: number; action: Action; isComponentProject: boolean; }) {
  return {
    "type": "object",
    "properties": {
      "branchName": {
        "title": "分支名称",
        "type": "string",
        "disabled": false,
        "props": {
          "addonBefore": ""
        },
        "required": true,
        "placeholder": "建议填写语义化分支名",
        "width": "90%",
        "rules": [
          // hack 这里是hack兼容投放类型的小程序分支在编辑时可以通过正则匹配
          // 普通小程序分支名字编写时，不允许有. 但是组件投放生成的小程序分支是带.的 然后两种分支目前都长一样
          {
            "pattern": action === Action.Update ? "^[0-9a-z._\-]+$" : "^[0-9a-z_\-]+$",
            "message": action === Action.Update ? "限小写字母、数字、中划线、下划线、点号" : "限小写字母、数字、中划线、下划线"
          },
          {
            "max": branchMaxLength,
            "message": "分支名称最长100个字符"
          }
        ]
      },
      "description": {
        "title": "分支描述",
        "type": "string",
        "format": "textarea",
        "props": {},
        "required": true,
        "width": "90%",
        "rules": [
          {
            "max": 100
          }
        ]
      },
      "bizLine": {
        "title": "业务线",
        "type": "string",
        "required": true,
        "default": 'common',
        "enum": ['common', 'vacation', 'hotel', 'train', 'flight', 'vehicle', 'bus', 'ship', 'growth', 'member', 'user', 'shop'],
        "enumNames": ['公共', '度假', '酒店', '火车票', '机票', '用车', '汽车票', '船票', '用增', '会员', '用户产品', '店铺'],
        "widget": "select",
        "width": "90%",
        "hidden": false
      },
      "qaList": {
        "title": "测试负责人",
        "type": "array",
        "required": true,
        "items": {
          "type": "string"
        },
        "rules": [
          {
            "validator": (_rule: string, res: { value: string }[]) => {
              let errMsg = '';
              const checkRes = res.every(item => {
                const value = item.value && JSON.parse(item.value);

                if (action === Action.Update) {
                  if (creatorWorkid === value.workid) {
                    errMsg = '测试负责人不能是创建者哦~如果是自测保障，也请同步到QA';
                    return false;
                  }
                } else if (action === Action.Create) {
                  if (currentUser.workid === value.workid) {
                    errMsg = '测试负责人不能是自己哦~如果是自测保障，也请同步到QA';
                    return false;
                  }
                }
                return true;
              });

              return checkRes || Error(errMsg);
            }
          },
        ],
        "props": {},
        "width": "90%",
        "widget": "user",
        "hidden": false
      },
      "aoneList": {
        "title": "关联Aone",
        "type": "array",
        "required": true,
        "items": {
          "type": "string"
        },
        "props": {},
        "width": "90%",
        "widget": "aone"
      },
      // 下单页时集成模式被隐藏，但是表单还是会把默认值1带过去
      "mergeCode": {
        "title": "集成模式",
        "description": "如果选择“合并代码”，则会把开发分支代码完整合并（有风险！）；如果选择“更新依赖包”，则只会更新package.json中的依赖包版本号",
        "type": "number",
        "required": true,
        "default": isComponentProject ? 1 : 0,
        "enum": [1, 0],
        "enumNames": ["合并代码", "更新依赖包"],
        "widget": "radio",
        "readOnly": isComponentProject
      },
      "npmList": {
        "title": "需更新的依赖包",
        "description": "所填写的包会更新到主仓库package.json的dependencies中",
        "type": "array",
        "widget": "wList",
        "props": {
          "action": "",
          "addBtnName": "添加依赖包",
          "items": {
            "name": {
              "title": "包名",
              "placeholder": "包名，如 @ali/rxpi-utils",
              "validator": (value: string) => {
                let errMsg;

                if (!value) {
                  errMsg = '请填写包名';
                }

                return {
                  error: errMsg && new Error(errMsg)
                }
              }
            },
            "value": {
              "title": "包版本号",
              "placeholder": "包版本号，如 ^1.1.0",
              "validator": (value: string) => {
                let errMsg;
                let isWarn = false;

                if (!value) {
                  errMsg = '请填写包版本号';
                } else if (!/^(\*|latest|beta|alpha|[~^]{0,1}(x|[0-9]+)\.(x|[0-9]+)\.(x|[0-9]+)(|-beta(|(\.){0,1}[0-9]*)|-alpha(|(\.){0,1}[0-9]+)))$/.test(value)) {
                  errMsg = '请填写正确格式的版本号';
                } else if (/beta/.test(value)) {
                  errMsg = '建议不要使用 beta 版本';
                  isWarn = true;
                }

                return {
                  error: errMsg && new Error(errMsg),
                  isWarn
                }
              }
            }
          }
        },
        "width": "100%",
        "hidden": `{{formData.mergeCode === ${EMergeCode.YES}}}`,
      },
      "npmResolutionList": {
        "title": "需更新的resolution",
        "description": "所填写的包会更新到主仓库package.json的resolutions中",
        "type": "array",
        "widget": "wList",
        "props": {
          "action": "",
          "addBtnName": "添加resolution",
          "items": {
            "name": {
              "title": "包名",
              "placeholder": "包名，如 @ali/rxpi-utils",
              "validator": (value: string) => {
                let errMsg;

                if (!value) {
                  errMsg = '请填写包名';
                }

                return {
                  error: errMsg && new Error(errMsg)
                }
              }
            },
            "value": {
              "title": "包版本号",
              "placeholder": "包版本号，如 ^1.1.0",
              "validator": (value: string) => {
                let errMsg;
                let isWarn = false;

                if (!value) {
                  errMsg = '请填写包版本号';
                } else if (!/^(\*|latest|beta|alpha|[~^]{0,1}(x|[0-9]+)\.(x|[0-9]+)\.(x|[0-9]+)(|-beta(|(\.){0,1}[0-9]*)|-alpha(|(\.){0,1}[0-9]+)))$/.test(value)) {
                  errMsg = '请填写正确格式的版本号';
                } else if (/beta/.test(value)) {
                  errMsg = '建议不要使用 beta 版本';
                  isWarn = true;
                }

                return {
                  error: errMsg && new Error(errMsg),
                  isWarn
                }
              }
            }
          }
        },
        "width": "100%",
        "hidden": `{{formData.mergeCode === ${EMergeCode.YES}}}`,
      },
      "syncProjects": {
        "title": "同步项目",
        "description": "既在选中的项目中同步创建/更新同名的游离分支",
        "type": "array",
        "hidden": true,
        "props": {
          "options": [
            {
              "label": "loading",
              "value": "加载中..."
            }
          ],
          "direction": "row"
        },
        "widget": "checkboxes"
      },
    },
    "labelWidth": 180,
    "displayType": "row"
  }
}
