/** 投放项（包含投放信息和按钮） */
import { useEffect, useState, useRef } from 'react';
import { message, Row, Col, Space, Spin, Button, Tag, Popover, Radio } from 'antd';
import { useModel } from 'umi';
import { EPubTypeEnv, DEF_ENV_TYPE_WORDING, EDefBuildStatus } from '@/const/def';
import { EDeliverBranchType } from '@/const/component-deliver';
import { IComponentDeliver } from '@/interface/component-deliver';
import { IProject } from '@/interface/project';
import { INpm } from '@/interface/base-dev-branch';
import { getDeliverStatusWording } from '@/const/component-deliver';
import componentDeliverApi from '@/api/component-deliver';
import defApi from '@/api/def';
import { parseGitRepoInfo } from '@/utils/git';
import { getDefIterationUrl, getCRUserByBizLine } from '@/utils/def';
import DeliverBtn from './deliver-btn';
import CRDetail from '../CRDetail';

import './index.less';

interface Props {
  /** 迭代分支 */
  deliverItem: IComponentDeliver;
  /** 项目 */
  project: IProject;
  /** 代码分支 */
  branchName?: string;
  /** 分支类型（1表示游离开发分支，2表示开发分支，3表示迭代） */
  branchType: EDeliverBranchType;
  /** 开发分支id */
  devId?: number;
  /** 挂载的迭代id */
  iterId?: number;
  /** 需要更新的npm包，json格式的数组 */
  npmList?: INpm[];
  /** def绑定的aone id */
  aoneBinds: string[];
  /** def迭代描述 */
  description: string;
  /** 是否可以操作投放 */
  canDeliver?: boolean;
  /** 构建任务环境
   * 和taskEnv的区别是：defEnvType是创建构建任务成功后记录在DB里的状态，taskEnv是即将创建任务的传参
   * 这样在迭代发布阶段，预发和正式发布两个按钮才可以独立，不然这两种情况都会触发轮询 */
  // defEnvType?: EPubTypeEnv;

  /** 构建任务环境 */
  // taskEnv: EPubTypeEnv;
  /** 查询整个列表 */
  getDeliverList: () => void;
}

interface CommitInfo {
  newCommitId: string;
  isSync: boolean;
  crStatus: any;
}
interface TaskInfo {
  hasErrorJob: boolean;
  taskCommitId: string;
}


const DeliverItem: React.FC<Props> = ({ deliverItem, project, devId, iterId, branchType, npmList, aoneBinds, description, canDeliver, getDeliverList }) => {
  const [latestDeliverItem, setLatestDeliverItem] = useState(deliverItem);
  const [commitInfo, setCommitInfo] = useState<CommitInfo>();
  const [taskInfo, setTaskInfo] = useState<TaskInfo>();
  const [loading, setLoading] = useState(false);
  const [CRLoading, setCRLoading] = useState(false);
  const currentUser = useModel('user');
  const [projectList] = useModel('projectList');
  const CRDetailRef: any = useRef(null);
  const { url } = parseGitRepoInfo(latestDeliverItem.gitRepo)!;
  // gitlab 分支链接
  const gitBranchUrl = `${url}/tree/daily/${latestDeliverItem.projectVersion}`
  // 如果是迭代类型的发布，有预发和线上两个环境的发布按钮。其他类型只有预发一个按钮
  const isIteration = branchType === EDeliverBranchType.ITERATION;
  // 如果线上已构建成功
  const isProdBuildSuccess = latestDeliverItem.defEnvType === EPubTypeEnv.Prod && latestDeliverItem.defBuildStatus === EDefBuildStatus.BUILD_SUCCESS;
  const { defIterId, defBranchId, defTaskId } = latestDeliverItem || {};
  // def链接
  const DEF_URL = getDefIterationUrl(defIterId);
  // 页面使用的组件信息
  const { name: npmName, value: npmVersion } = latestDeliverItem?.npmList?.[0] || {};

  // const { key, name, cnName, gitRepo, appId} = deliverItem;

  useEffect(() => {
    if (defIterId) {
      getLastCommit();
    }
  }, [defIterId]);

  useEffect(() => {
    if (defTaskId) {
      getTaskInfo();

    }
  }, [defTaskId])

  /** 查询上一个commit的信息 */
  const getLastCommit = () => {
    defApi.getLastCommit({
      iterationId: defIterId!
    }).then(res => {
      if (res.success) {
        Object.values(res.data).forEach((branch: any) => {
          setCommitInfo({
            newCommitId: branch?.id, // 提交版本
            isSync: branch?.sync_status?.is_sync, // 主干同步状态
            crStatus: branch?.cr_status?.data?.state // CR状态
          })
        })
      }
    })
  }

  /** 查询构建任务详情 */
  const getTaskInfo = () => {
    defApi.getIterPublishTaskDetail({
      defTaskId: defTaskId!
    }).then(res => {
      if (res.success) {
        const hasErrorJob = res.data?.runtime?.stages?.some((stage: any) => {
          return stage.jobs.some((job: any) => job.status === 'ERROR')
        })
        setTaskInfo({
          hasErrorJob, // 检测任务
          taskCommitId: res?.data?.task?.commit_id// 部署版本
        })
        // console.log('hasErrorJob', hasErrorJob);
      }
    })
  }

  let innerClassName = 'inner';
  switch(latestDeliverItem.defBuildStatus) {
    case EDefBuildStatus.BUILD_ERROR: {
      innerClassName += ' error';
      break;
    }
    case EDefBuildStatus.BUILD_SUCCESS: {
      innerClassName += ' success';
      break;
    }
  }

  const getDeliverItem = () => {
    setLoading(true);
    componentDeliverApi.get({
      deliverId: latestDeliverItem.deliverId
    }).then(res => {
      setLatestDeliverItem({ ...latestDeliverItem, ...res.data})
    }).catch(() => {
      message.error({ content: '查询投放信息失败', duration: 10 })
    }).finally(() => {
      setLoading(false);
    })
  }

  const renderSyncStatus = () => {
    switch(commitInfo?.isSync) {
      case true:
        return <Tag color="green">已同步</Tag>;
      case false:
        return <Tag color="red">未同步</Tag>;
      case true:
        return '-';
    }
  }

  const renderCheckTask = () => {
    if (!defTaskId) { return '-'; }
    return taskInfo?.hasErrorJob ? '失败' : '成功'
  }

  // 获取离线包地址
  const getPackageUrl = (offlineType: 'fpackage' | 'zcache') => {
    return `//fliggy-publish.alibaba-inc.com/publish/integration/setting/${latestDeliverItem.defProjectId}?version=${latestDeliverItem.projectVersion}&offlineType=${offlineType}&desc=${description}`
  }

  // 自动发起投放页面的CR
  const createCR = async () => {
    if (!projectList) return;

    setCRLoading(true);
    let bizLine = 'common';
    const projectName = latestDeliverItem.name || latestDeliverItem.projectName;
    projectList.forEach((project) => {
      if (!project.deliverConfig?.deliverList?.h5List?.length) {
        return;
      }
      project.deliverConfig.deliverList.h5List.map((h5: any) => {
        if (h5.name === projectName) {
          switch (projectName) {
            case 'rx-buy':
              bizLine = 'vacation';
              break;
            case 'rx-hotel-buy':
              bizLine = 'hotel';
              break;
            case 'rx-flight-buy':
              bizLine = 'flight';
              break;
            case 'rx-train-buy':
              bizLine = 'grabTrain';
              break;
            case 'rx-train-main-buy':
              bizLine = 'train';
              break;
            case 'rx-bus-buy':
              bizLine = 'bus';
              break;
            case 'rx-ship-buy':
              bizLine = 'ship';
              break;
            case 'rx-vehicle-buy':
              bizLine = 'vehicle';
              break;
            case 'rx-travel-detail':
            case 'rx-travel-detailssr':
              bizLine = 'travelDetail';
              break;
            case 'h5-bd-platform-test':
              bizLine = 'test';
              break;
          }
        }
      })
    })
    const crWorkIdList = getCRUserByBizLine([{ creatorWorkid: currentUser.workid, bizLine }], currentUser.workid)
    const res = await defApi.createCR({
      projectName: projectName!,
      iterationId: defIterId!,
      devbranchId: defBranchId!,
      title: `${projectName} 变更 ${latestDeliverItem.projectVersion} 的代码评审`,
      description: description,
      ids: crWorkIdList.join(','),
    })
    if (res?.success) {
      message.success('创建CR成功！');
      // CR状态重新获取和更新
      CRDetailRef?.current?.getCR();
    } else if (res?.rawData?.indexOf('alread exist')){
      message.error({ content: 'CR已存在不可重复创建', duration: 5 });
    } else {
      message.error({ content: <a href={DEF_URL} target="_blank">自动创建CR失败，请去def手动创建</a>, duration: 10 });
    }
    setCRLoading(false);
  }

  return (
    <Spin spinning={loading} wrapperClassName="component-deliver-item">
      <Row className={innerClassName} gutter={8}>
        <Col span={5} className="col">
          <Space direction="vertical" size={4}>
            <div>
              应用：
              <a href={DEF_URL} target="_blank">
                {latestDeliverItem.name || latestDeliverItem.projectName}
              </a>
            </div>
            <div>分支：{latestDeliverItem.projectVersion ? <a href={gitBranchUrl} target="_blank">{latestDeliverItem.projectVersion}</a> : '-'}</div>
            <Popover content={`${npmName}: ${npmVersion}`} trigger="hover">
              <div>组件版本：{ npmVersion ? `${npmVersion}` : '-'}</div>
            </Popover>
            <div>环境：{latestDeliverItem.defEnvType ? DEF_ENV_TYPE_WORDING[latestDeliverItem.defEnvType] : '-'}</div>
          </Space>
        </Col>
        <Col span={5} className="col">
          <Space direction="vertical" size={4}>
            <div>主干同步状态：{renderSyncStatus()} </div>
            <div>检测任务：{renderCheckTask()} </div>
            <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
              代码CR：
              <CRDetail ref={CRDetailRef} defIterId={defIterId!} />
            </div>
          </Space>
        </Col>
        <Col span={5} className="col">
          <Space direction="vertical" size={4}>
            <div>提交版本：{ commitInfo?.newCommitId?.slice(0, 8) || '-'} </div>
            <div>部署版本：{ taskInfo?.taskCommitId?.slice(0, 8) || '-'} </div>
            <div>投放状态：{getDeliverStatusWording({ defEnvType: latestDeliverItem.defEnvType!, defBuildStatus: latestDeliverItem.defBuildStatus!, defIterId: defIterId! })} </div>
          </Space>
        </Col>
        <Col span={4} className="col">
          <Space direction="vertical" size={4}>
            <div>
              创建：{latestDeliverItem.creator ? `${latestDeliverItem.creator}` : '-'}
              <br/>
              <span>{latestDeliverItem.gmtCreate || ''}</span>
            </div>
            {latestDeliverItem.modifier && (
              <>
                <div>
                  最近修改：
                  {latestDeliverItem.modifier ? `${latestDeliverItem.modifier}` : '-'}
                  <br/>
                  <span>{latestDeliverItem.gmtModified || ''}</span>
                </div>
              </>
            )}
          </Space>
        </Col>
        {
          canDeliver
          ? <Col span={5} className="col actions">
            {
              isProdBuildSuccess
              ? <>
                <Button type="primary" href={DEF_URL} target="_blank">手动DEF灰度放量</Button>
                {latestDeliverItem.hasFpackage ? <Button type="primary" href={getPackageUrl('fpackage')} target="_blank" style={{ marginTop: 10 }}>飞猪离线包发布</Button> : null}
                {latestDeliverItem.hasZcache ? <Button type="primary" href={getPackageUrl('zcache')} target="_blank" style={{ marginTop: 10 }}>手淘离线包发布</Button> : null}
              </>
              : <>
                  <DeliverBtn
                    btnType="primary"
                    deliverItem={latestDeliverItem}
                    project={project}
                    devId={devId}
                    iterId={iterId}
                    branchType={branchType}
                    taskEnv={EPubTypeEnv.Beta}
                    npmList={npmList}
                    aoneBinds={aoneBinds}
                    description={description}
                    refreshAllList={getDeliverList}
                    refreshBuildStatus={getDeliverItem}
                  />
                  {
                    !commitInfo?.crStatus && defIterId && defBranchId
                    && <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'flexStart' }}>
                      <Button type="primary" loading={CRLoading} onClick={createCR}>一键CR</Button>
                      <Button href={DEF_URL} target="_blank">手动CR</Button>
                   </div>
                  }
                  {
                    isIteration && defIterId
                    && <DeliverBtn
                      style={{ marginTop: 10 }}
                      btnType="primary"
                      deliverItem={latestDeliverItem}
                      project={project}
                      devId={devId}
                      iterId={iterId}
                      branchType={branchType}
                      taskEnv={EPubTypeEnv.Prod}
                      npmList={npmList}
                      aoneBinds={aoneBinds}
                      description={description}
                      refreshAllList={getDeliverList}
                      refreshBuildStatus={getDeliverItem}
                    />
                  }
                </>
            }
          </Col>
          : null
        }
      </Row>
    </Spin>
  );
}

export default DeliverItem;
