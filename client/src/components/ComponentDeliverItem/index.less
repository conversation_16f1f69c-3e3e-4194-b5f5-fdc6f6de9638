.component-deliver-item {
  width: 100%;
  .inner {
    position: relative;
    padding: 20px 24px;
    margin-bottom: 10px;
    background-color: #eeeeee73;
    border-radius: 5px;

    &.success {
      --icon: url('https://gw.alicdn.com/imgextra/i4/O1CN01FUFIrS1OZnwAjchKX_!!6000000001720-2-tps-210-210.png');
      --bgc: linear-gradient(to right, rgba(255, 255, 255, 0.2), rgba(112, 182, 3, 0.2));
    }

    &.error {
      --icon: url('https://gw.alicdn.com/imgextra/i3/O1CN01U7MJsm1SueQVZ1oeW_!!6000000002307-2-tps-2-2.png');
      --bgc: linear-gradient(to right, rgba(255, 255, 255, 0.2), rgba(182, 3, 3, 0.2));
    }

    &.success,
    &.error {
      --transparent: url('https://gw.alicdn.com/imgextra/i3/O1CN01U7MJsm1SueQVZ1oeW_!!6000000002307-2-tps-2-2.png');
      background-image: var(--bgc),
        cross-fade(var(--transparent), var(--icon), 0.2);
      background-image: var(--bgc),
        -webkit-cross-fade(var(--transparent), var(--icon), 0.2);
      background-size: auto, 90px;
      background-repeat: initial, no-repeat;
      background-position: initial, bottom -16px left -24px;
    }
  }
  .col {
    display: flex;
    align-items: center;
    &.actions {
      flex-direction: column;
      justify-content: center;
      align-items: start;
      button {
        margin-bottom: 10px;
      }
    }
  }
}
