/** 投放按钮（包含轮询投放结果逻辑） */
import { useEffect, useState, useRef } from 'react';
import { Button, message, Modal } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { IProject } from '@/interface/project';
import { IComponentDeliver } from '@/interface/component-deliver';
import { EPubTypeEnv, EDefBuildStatus, EDefBuildTaskType } from '@/const/def';
import { EDeliverType, EDeliverBranchType } from '@/const/component-deliver';
import defApi, { IDefRes } from '@/api/def';
import componentDeliverApi from '@/api/component-deliver';
import { INpm } from '@/interface/base-dev-branch';

const { confirm } = Modal;

// 轮询def任务构建结果
class PollTask {
  project: IProject;
  deliverId: number;
  defTaskId: number;
  npmList: INpm[];
  pollTid = 0;
  onComplete: (res: IDefRes) => void;

  constructor({ project, deliverId, defTaskId, npmList, onComplete }: { project: IProject; deliverId: number; defTaskId: number; npmList: INpm[]; onComplete: (res: IDefRes) => void; }) {
    this.project = project;
    this.deliverId = deliverId;
    this.defTaskId = defTaskId;
    this.npmList = npmList;
    this.onComplete = onComplete;
    this.query();
  }

  query = () => {
    let shouldPoll = true;
    componentDeliverApi.getPublishDetail({
      project: this.project,
      deliverId: this.deliverId,
      defTaskId: this.defTaskId,
      npmList: this.npmList,
    })
      .then((res) => {
        // 如果构建状态变更为非构建中，则取消轮询
        if (res?.data?.task?.pub_status !== EDefBuildStatus.BUILDING) {
          shouldPoll = false;
          if (res.success) {
            message.success('查询def构建信息成功！');
          } else if (res.errorMsg) {
            message.error({ content: res?.errorMsg || '查询def构建信息失败', duration: 10 });
          }
          this.onComplete(res);
        }
      })
      .finally(() => {
        if (!shouldPoll) return;

        // 60秒轮询一次
        this.pollTid = window.setTimeout(this.query, 60000)
      });
  }

  destroy = () => {
    window.clearTimeout(this.pollTid);
  }
}

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  /** 按钮类型 */
  btnType?: 'link' | 'primary';
  /** 迭代分支 */
  deliverItem: IComponentDeliver;
  /** 项目 */
  project: IProject;
  /** 代码分支 */
  branchName?: string;
  /** 分支类型（1表示游离开发分支，2表示开发分支，3表示迭代） */
  branchType: EDeliverBranchType;
  /** 开发分支id */
  devId?: number;
  /** 挂载的迭代id */
  iterId?: number;
  /** 需要更新的npm包，json格式的数组 */
  npmList?: INpm[];
  /** def绑定的aone id */
  aoneBinds: string[];
  /** def迭代描述 */
  description: string;
  /** 按钮样式 */
  style?: {};
  /** 构建任务环境
   * 和taskEnv的区别是：defEnvType是创建构建任务成功后记录在DB里的状态，taskEnv是即将创建任务的传参
   * 这样在迭代发布阶段，预发和正式发布两个按钮才可以独立，不然这两种情况都会触发轮询 */
  // defEnvType?: EPubTypeEnv;

  /** 构建任务环境 */
  taskEnv: EPubTypeEnv;
  /** 查询整个列表，更新数据 */
  refreshAllList: () => void;
  /** 查询当前构建状态的回调，更新defBuildStatus */
  refreshBuildStatus: () => void;
  /** 构建前回调，如果返回true继续执行，false暂停 */
  onBeforeBuild?: () => Promise<boolean>;
}
// ,,  , branchName,, , , , , , , ,
// , projectName, deliverId, defIterId, defTaskId, defBuildStatus, defEnvType
export default ({ children, btnType = "link", deliverItem, project, branchName, branchType, devId, iterId, npmList, aoneBinds, description, taskEnv, style = {}, refreshAllList, refreshBuildStatus, onBeforeBuild }: Props) => {
  const { gitRepo, gitProjectId, name: projectName, deliverId, defIterId, defTaskId, defBuildStatus, defEnvType, npmList: dbNpmList } = deliverItem;
  // console.log('deliver btn props', deliverItem, btnType, project, branchName, gitRepo, gitProjectId, devId, npmList, aoneBinds, description, taskEnv, refreshBuildStatus, onBeforeBuild,);

  const [btnText, setBtnText] = useState<string>('初始化');
  const [btnDisabled, setBtnDisabled] = useState<boolean>(false);
  const pollTaskRef = useRef<PollTask | null>();

  useEffect(() => {
    if (!defIterId) {
      setBtnText('初始化');
    } else {
      if (taskEnv === EPubTypeEnv.Beta) {
        setBtnText('投放预发');
      } else {
        setBtnText('确认回归通过 & 投放线上');
      }
    }
  }, [defIterId, taskEnv])

  useEffect(() => {
    // 迭代发布时，只有当前DB里记录的构建环境和选择的环境吻合时，才会触发轮询
    // 开发分支发布时，只有预发这一个环境，就不用判断
    if (branchType === EDeliverBranchType.ITERATION && defEnvType !== taskEnv) {
      return;
    }

    // 如有存在的轮询任务，则先销毁
    if (pollTaskRef.current) pollTaskRef.current.destroy();

    // 如果处在构建中，则开始轮询
    if (defBuildStatus === EDefBuildStatus.BUILDING) {
      pollTaskRef.current = new PollTask({
        project,
        deliverId,
        defTaskId: defTaskId!,
        npmList: npmList!,
        onComplete: () => refreshBuildStatus()
      });
    } else {
      pollTaskRef.current = null;
    }
    setBtnDisabled(defBuildStatus === EDefBuildStatus.BUILDING);

    return () => {
      if (pollTaskRef.current) pollTaskRef.current.destroy();
    }
  }, [defBuildStatus])

  const onClick = async () => {
    // 先执行构建前的回调
    if (onBeforeBuild) {
      const isOk = await onBeforeBuild();
      if (!isOk) return;
    }
    if (!defIterId) {
      // 如果没有def迭代id，需要初始化（创建git分支、def迭代）
      setBtnDisabled(true);
      componentDeliverApi.init({
        deliverId,
        devId: devId!,
        iterId: iterId!,
        deliverType: EDeliverType.H5,
        branchType,
        pub_env: taskEnv,
        projectName,
        gitRepo,
        gitProjectId,
        npmList,
        aoneBinds,
        description,
      }).then(res => {
        if (res.success) {
          refreshAllList();
        } else {
          message.error({ content: res?.errorMsg || '初始化投放失败', duration: 10 });
        }
      }).finally(() => {
        setBtnDisabled(false);
      })
    } else if (defBuildStatus !== EDefBuildStatus.BUILDING) {
      if (defBuildStatus === EDefBuildStatus.BUILD_SUCCESS && npmList?.[0]?.name === dbNpmList?.[0]?.name && npmList?.[0]?.value === dbNpmList?.[0]?.value) {
        confirm({
          title: '引用模块的版本已经是最新的，是否还需要构建?',
          icon: <ExclamationCircleOutlined />,
          content: `模块名：${npmList?.[0]?.name} 版本：${npmList?.[0]?.value}`,
          onOk() {
            createPublish();
          },
        })
      } else {
        createPublish();
      }
    }
  }

  const createPublish = () => {
    // 如果是非构建中的状态，则创建def发布任务
    setBtnDisabled(true);
    componentDeliverApi.createPublish({
      deliverId,
      pub_env: taskEnv,
      gitRepo,
      npmList,
      defIterId: defIterId!,
    }).then(res => {
      if (res?.success) {
        // 构建成功后需要更新一下构建状态字段，然后就会触发轮询任务（查询构建结果）
        refreshBuildStatus();
      } else if (/conflict/i.test(res?.errorMsg || '')) {
        message.error({ content: '自动合并master分支存在冲突，请手动合并后再尝试！', duration: 10 })
      } else {
        message.error({ content: res?.errorMsg || '创建def构建任务失败', duration: 10 });
      }
    }).finally(() => {
      setBtnDisabled(false);
    })
  }

  return (
    <>
      <Button type={btnType} disabled={btnDisabled} loading={btnDisabled} onClick={onClick} style={{ padding: btnType === 'link' ? 0 : '4px 15px', ...style }}>
        {btnText}
        {children}
      </Button>
    </>
  );
}
