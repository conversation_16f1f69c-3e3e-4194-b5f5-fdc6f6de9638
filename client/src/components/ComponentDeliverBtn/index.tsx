/** 投放按钮及浮层（兼容开发/游离开发分支、迭代） */
import { useEffect, useState } from 'react';
import { Modal, Button, message, Tabs, Descriptions, List, Spin, Row, Col, Space } from 'antd';
import { UserOutlined, UsergroupAddOutlined, AlipayCircleOutlined, We<PERSON>tOutlined, YoutubeOutlined, TabletOutlined  } from '@ant-design/icons';
import componentDeliverApi from '@/api/component-deliver';
import buildApi, { ICreateReq } from '@/api/build';
import { IProject } from '@/interface/project';
import { IComponentDeliver } from '@/interface/component-deliver';
import { IDevBranch } from '@/interface/dev-branch';
import { IFreeDevBranch } from '@/interface/free-dev-branch';
import { IIterBranch } from '@/interface/iter-branch';
import { EPubTypeEnv, DEF_ENV_TYPE_WORDING } from '@/const/def';
import { EDeliverBranchType, EMinaStatus, EDeliverActionType, getMinaStatusWording, getDeliverTypeInfo } from '@/const/component-deliver';
import { EMergeCode } from '@/const/dev-branch';
import { parseGitRepoInfo } from '@/utils/git';
import ModuleDeliverItem from '../ComponentDeliverItem';
import BuildButton from '@/components/BuildBtn/build-button';
import Content from '@/components/BuildBtn/content';

import './index.less';

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  /** 组件可操作类型 */
  actionType?: EDeliverActionType;
  /** 按钮类型 */
  btnType?: "link" | "text" | "default" | "ghost" | "primary" | "dashed" ;
  /** 项目 */
  project: IProject;
  /** （游离）开发分支 */
  latestDevBranch: Partial<IDevBranch | IFreeDevBranch | IIterBranch> & { [key: string]: any };
  /** 开发分支类型（1表示游离开发分支，2表示开发分支，3表示迭代） */
  branchType: EDeliverBranchType;
  /** 构建任务环境 */
  taskEnv: EPubTypeEnv;
  /** 投放描述 */
  description: string;
  /** 查询当前构建状态的回调，更新defBuildStatus */
  refreshBuildStatus?: () => void;
  /** 构建前回调，如果返回true继续执行，false暂停 */
  onBeforeBuild?: () => Promise<boolean>;
}
const PAGE_SIZE = 10;
export default ({ children, actionType = EDeliverActionType.DELIVER, btnType = "link", project, latestDevBranch, branchType, taskEnv, description }: Props) => {
  // 如果是开发/游离开发分支时，devId就是分支id， iterId就是挂载的迭代id
  // 如果是迭代iterId就是本身id
  const { devId, iterId, branchName, pkgPublishVersion, aoneList, bizLine, qaList } = latestDevBranch;
  const aoneBinds = [latestDevBranch.aoneList?.[0]?.id];
  // 投放测试按钮是否禁用
  const [btnDisabled, setBtnDisabled] = useState<boolean>(false);
  // 投放弹窗是否展示
  const [showModal, setShowModal] = useState<boolean>(false);
  // 投放H5列表源数据
  const [h5ListData, setH5ListData] = useState<IComponentDeliver[]>([]);
  // 投放多类小程序列表源数据
  const [miniappListData, setMiniappListData] = useState<IComponentDeliver[]>([]);
  // 当前选中tab
  const [activeTab, setActiveTab] = useState<string>('h5');
  // 投放接口分页
  const [pageNum, setPageNum] = useState(1);
  // 投放弹层loading
  const [modalLoading, setModalLoading] = useState<boolean>(false);
  // 预发小程序初始化loading
  const [minaInitLoading, setMinaInitLoading] = useState<boolean>(false);
  // 预发小程序打码loading
  const [buildLoading, setBuildLoading] = useState(false);
  // 线上小程序更新依赖loading
  const [minaUpdateLoading, setMinaUpdateLoading] = useState<boolean>(false);
  // 是否展示小程序打码表单
  const [showBuildForm, setShowBuildForm] = useState(false);
  // 小程序打码内容刷新
  const [refreshTag, setRefreshTag] = useState(0);

  // 如果是迭代类型的发布
  const isIteration = branchType === EDeliverBranchType.ITERATION;
  // 可以操作投放
  const canDeliver = actionType === EDeliverActionType.DELIVER;
  const { moduleName, deliverList: { h5List = [], miniprogramList = [] } = {} } = project?.deliverConfig || {};
  const hasH5Deliver = h5List.length;
  const hasMiniprogramDeliver = miniprogramList.length;
  // 线上投放时
  const isProd = taskEnv === EPubTypeEnv.Prod;

  // 如果分支还没有构建版本则投放按钮不可点击
  useEffect(() => {
    setBtnDisabled(!latestDevBranch.pkgPublishVersion);
  }, [latestDevBranch])

  useEffect(() => {
    if (!showModal) return;

    getDeliverList();
  }, [showModal, pageNum, h5List])

  /** 查询投放列表 */
  const getDeliverList = () => {
    setModalLoading(true);

    const conditions = Object.assign({},
      branchType === EDeliverBranchType.ITERATION ? { iterId } : { devId, branchType }
    )
    // 请求list接口，然后和config一起结合后返回作为数据
    componentDeliverApi.list({
      pageSize: 20,
      pageNum,
      conditions,
    }).then(res => {
      const h5ListResult: IComponentDeliver[] = [];
      const miniappListResult: IComponentDeliver[] = [];
      h5List.forEach((item: any) => {
        const isFind = res.data.list.some((listItem: IComponentDeliver) => {
          // hack 95行 迭代类型的时候，会返回挂载在迭代下的全部h5和小程序分支，小程序全部要用，而h5只需要branchType为迭代的分支
          if (item.name === listItem.projectName && branchType === listItem.branchType) {
            h5ListResult.push({
              ...item,
              ...listItem,
            })
            return true;
          }
          return false;
        })
        if (!isFind) {
          h5ListResult.push(item as IComponentDeliver)
        }
      })

      miniprogramList.forEach((item: any) => {
        const isFind = res.data.list.some((listItem: IComponentDeliver) => {
          if (item.name === listItem.projectName) {
            miniappListResult.push({
              ...item,
              ...listItem,
            })
            return true;
          }
          return false;
        })
        if (!isFind) {
          miniappListResult.push(item as IComponentDeliver)
        }
      })
      // console.log('result', h5ListResult, miniappListResult);
      setH5ListData(h5ListResult);
      setMiniappListData(miniappListResult);
    }).finally(() => {
      setModalLoading(false);
    })
  }

  const handleTabChange = (activeKey: string) => {
    setActiveTab(activeKey);
  }

  /** 渲染投放概览 */
  const renderSummary = ({ type, config } : {type: string; config?: any;}) => {
    let descComp = null;
    if (type === 'h5') {
      descComp = h5ListData.map(item =>
        <Descriptions.Item label={`${item.cnName}(${item.name})`}>{item.projectVersion || '-'}</Descriptions.Item>
      )
    } else {
      const { url } = parseGitRepoInfo(config.gitRepo)!;
      const gitBranchUrl = `${url}/tree/${config.miniappInfo?.branchName}`;
      descComp = <Descriptions.Item label={`${config.cnName}(${config.name})`}><a href={gitBranchUrl} target="_blank">{config.miniappInfo?.branchName || '-'}</a></Descriptions.Item>
    }
    return <div style={{ marginBottom: 20, padding: 20, backgroundColor: 'rgba(218,236,255,.7)', borderRadius: 5 }}>
      <h1 style={{ fontSize: 16 }}>投放概览</h1>
      <h2 style={{ fontSize: 14 }}>组件</h2>
      <Descriptions size="small">
        <Descriptions.Item label={`${moduleName}您构建的最新版本`}>{pkgPublishVersion || '-'}</Descriptions.Item>
      </Descriptions>
      <h2 style={{ fontSize: 14, marginTop: 10 }}>页面</h2>
      <Descriptions size="small">
        {descComp}
      </Descriptions>
    </div>
  }

  /** 渲染H5投放列表 */
  const renderH5List = () => {
    return (
      <>
        <h1 style={{ fontSize: 16 }}>投放详情</h1>
        <List
          itemLayout="horizontal"
          size="large"
          pagination={{
            pageSize: PAGE_SIZE,
            onChange: (page) => {
              setPageNum(page);
            },
          }}
          dataSource={h5ListData}
          renderItem={(item) => (
            <List.Item key={item.projectName} style={{ padding: 0 }}>
              <ModuleDeliverItem
                key={item.key}
                deliverItem={item}
                project={project}
                devId={devId}
                iterId={iterId}
                branchType={branchType}
                npmList={[{ name: moduleName!, value: pkgPublishVersion! }]}
                aoneBinds={aoneBinds}
                description={description}
                getDeliverList={getDeliverList}
                canDeliver={canDeliver}
              />
            </List.Item>
          )}
        />
      </>
    );
  }

  /** 渲染预发环境小程序投放列表 */
  const renderBetaMiniappList = (program: IComponentDeliver) => {
    const { branchName } = program?.miniappInfo || {};

    return (
      <div>
        <h1 style={{ fontSize: 16 }}>投放详情</h1>
        {!program.miniappInfo ? (
          <Button type="primary" loading={minaInitLoading} disabled={!canDeliver || minaInitLoading} onClick={() => initMiniapp(program)}>
            初始化
          </Button>
        ) : (
          <div>
            { canDeliver
              ? <BuildButton
                  key={program.key}
                  build={(params) => build(params, program)}
                  project={project}
                  branchName={branchName!}
                  gitRepo={parseGitRepoInfo(program.gitRepo)!}
                  showBuildForm={showBuildForm}
                  buildLoading={buildLoading}
                  minifyBuild={['fliggy-allinone', 'fliggy-weixin'].includes(program.key)}
                  fastBuild={['fliggy-weixin'].includes(program.key)}
                  switchEnv={['fliggy-weixin', 'fliggy-bytedance'].includes(program.key)}
                  handleBtnPopoverShow={handleBtnPopoverShow}
                />
              : null
            }
            <Tabs tabBarStyle={{ marginBottom: '10px' }}>
              <Tabs.TabPane
                tab={
                  <span>
                    <UserOutlined />
                    我的打码
                  </span>
                }
                key="mine"
              >
                <Content
                  build={(params) => build(params, program)}
                  refreshTag={refreshTag}
                  projectName={program.name}
                  gitRepo={parseGitRepoInfo(program.gitRepo)!}
                  branchName={branchName!}
                  filterMine
                />
              </Tabs.TabPane>
              <Tabs.TabPane
                tab={
                  <span>
                    <UsergroupAddOutlined />
                    全部打码
                  </span>
                }
                key="all"
              >
                <Content
                  build={(params) => build(params, program)}
                  refreshTag={refreshTag}
                  gitRepo={parseGitRepoInfo(program.gitRepo)!}
                  projectName={program.name}
                  branchName={branchName!}
                />
              </Tabs.TabPane>
            </Tabs>
          </div>
        )}
      </div>
    );
  }

  /** 预发环境 初始化小程序投放 */
  const initMiniapp = (program: IComponentDeliver) => {
    setMinaInitLoading(true);
    const { projectType, projectName, deliverType } = getDeliverTypeInfo(program.name);
    let params = {
      // 写入投放db的参数
      deliverId: program.deliverId,
      devId,
      iterId,
      deliverType,
      branchType,
      pub_env: taskEnv,

      // 创建小程序的游离开发分支参数
      projectName,
      branchName: `${branchName}-${projectType}`,
      description,
      mergeCode: EMergeCode.NO,
      npmList: [{ name: moduleName, value: pkgPublishVersion }],
      npmResolutionList: [],
      bizLine,
      qaList,
      aoneList,
      projectType,
    } as any;
    componentDeliverApi.initMiniapp(params)
    .then(res => {
      getDeliverList();
    }).finally(() => {
      setMinaInitLoading(false);
    })
  }

  /** 预发环境 更新当前小程序分支的依赖 */
  const updateBetaMiniapp = (program: IComponentDeliver) => {
    const { npmList: dbNpmList } = program;
    // 引用模块的版本已经是最新 不用更新DB
    if (moduleName === dbNpmList?.[0]?.name && pkgPublishVersion === dbNpmList?.[0]?.value) {
      return Promise.resolve(true);
    }

    return componentDeliverApi.updateBetaMiniapp({
      deliverId: program.deliverId,
      npmList: [{ name: moduleName!, value: pkgPublishVersion! }],
    })
    .then(res => {
      getDeliverList();
      return true;
    }).catch((err: any) => {
      message.error(err.message || '更新小程序依赖失败，请重试~');
      return false;
    }).finally(() => {
      setMinaInitLoading(false);
    })
  }

  /** 预发环境 打码 */
  const build = async ({ pagePath, pageQuery, env, customArgv }: { pagePath?: string; pageQuery?: string; env?: string; customArgv?: ICreateReq['customArgv'] }, program: IComponentDeliver) => {
    setBuildLoading(true);
    setShowBuildForm(false);

    // 先更新依赖
    const updateResult = await updateBetaMiniapp(program);

    if (!updateResult) return;

    // 然后打码
    const { projectName } = getDeliverTypeInfo(program.name);
    const { branchName } = program?.miniappInfo || {};
    buildApi.create({
      branchName: branchName!,
      projectName,
      pagePath,
      pageQuery,
      customArgv,
      env
    })
      .then((res) => {
        if (res?.success) {
        setRefreshTag(prevState => prevState + 1);
        } else {
          throw Error(res?.errorMsg)
        }
      })
      .catch((err: any) => {
        message.error(err.message || '打码失败，请重试~');
      }).finally(() => {
        setBuildLoading(false)
      });
  }

  /** 打码弹窗展示 */
  const handleBtnPopoverShow = (visible: boolean) => {
    setShowBuildForm(visible)
  }

  /** 线上环境渲染小程序列表 */
  const renderProdMiniappList = () => {
    return <Spin spinning={minaUpdateLoading}>
      {
        miniappListData.map((program) => {
          const { url } = parseGitRepoInfo(program.gitRepo)!;
          return <div className="component-deliver-item">
              <Row className="inner" gutter={8}>
              <Col span={5} className="col">
                <Space direction="vertical" size={4}>
                  <div>
                    应用：
                    <a href={url} target="_blank">
                      {program.name || program.projectName}
                    </a>
                  </div>
                  <div>环境：{taskEnv ? DEF_ENV_TYPE_WORDING[taskEnv] : '-'}</div>
                  <div>投放状态：{getMinaStatusWording(program?.miniappInfo?.status)} </div>
                </Space>
              </Col>
              <Col span={7} className="col">
                <Space direction="vertical" size={4}>
                  <div>集成模式：更新依赖 </div>
                  <div>依赖包名：{moduleName || '-'} </div>
                  <div>依赖包版本：{pkgPublishVersion || '-'}</div>
                </Space>
              </Col>
              <Col span={5} className="col">
                <Space direction="vertical" size={4}>
                  <div>创建：{program.creator ? `${program.creator}（${program.gmtCreate}）` : '-'}</div>
                  <div>最近修改：{program.modifier ? `${program.modifier}（${program.gmtModified}）` : '-'} </div>
                </Space>
              </Col>
              <Col span={5} className="col actions">
                <Button type="primary" disabled={program?.miniappInfo?.status === EMinaStatus.PROD} onClick={() => updateProdMiniapp(program)}>更新已有小程序分支的依赖</Button>
              </Col>
            </Row>
          </div>
        })
      }
    </Spin>
  }

  /** 线上环境 更新全部挂载的小程序分支的依赖 */
  const updateProdMiniapp = (program: IComponentDeliver) => {
    setMinaUpdateLoading(true);
    const { projectName, deliverType } = getDeliverTypeInfo(program.name);
    let params = {
      // 写入投放db的参数
      deliverId: program.deliverId,
      iterId,
      deliverType,
      npmList: [{ name: moduleName, value: pkgPublishVersion }],
      projectName,
    } as any;
    componentDeliverApi.updateProdMiniapp(params)
    .then(res => {
      getDeliverList();
    }).catch((err: any) => {
      message.error(err.message || '更新小程序依赖失败，请重试~');
    }).finally(() => {
      setMinaUpdateLoading(false);
    })
  }

  /** 获取小程序的icon */
  const renderAppIcon = (program: IComponentDeliver) => {
    switch(program.key) {
      case 'fliggy-allinone': return <AlipayCircleOutlined />;
      case 'fliggy-weixin': return <WechatOutlined />;
      case 'fliggy-bytedance': return <YoutubeOutlined />;
    }
    return null;
  }

  return (
    <>
      <Button
        type={btnType}
        disabled={btnDisabled}
        style={{ padding: btnType === 'link' ? 0 : '4px 15px' }}
        onClick={() => {
          setShowModal(true);
        }}
      >
        {children}
      </Button>

      <Modal
        visible={showModal}
        title="投放列表"
        className="deliver-modal"
        width={1060}
        destroyOnClose
        maskClosable={false}
        onCancel={() => {
          setShowModal(false);
        }}
        footer={null}
      >
        <div className="tips">当前{isIteration ? '迭代' : '构建'}分支为 {branchName}</div>
          <Spin spinning={modalLoading}>
            {/* 切换tab的时候需要销毁DOM，否则会出现多个小程序打码按钮混淆 */}
            <Tabs activeKey={activeTab} destroyInactiveTabPane={true} tabBarStyle={{ marginBottom: '10px' }} onChange={handleTabChange}>
              {/* H5页面投放列表 */}
              {hasH5Deliver && (
                <Tabs.TabPane
                  tab={
                    <span>
                      <TabletOutlined />
                      H5页面
                    </span>
                  }
                  key="h5"
                >
                  {renderSummary({ type: 'h5' })}
                  {renderH5List()}
                </Tabs.TabPane>
              )}
              {/* 预发小程序投放，打码列表 */}
              {hasMiniprogramDeliver && !isProd && (
                <>
                  {miniappListData.map((program) => (
                    <Tabs.TabPane
                      tab={
                        <span>
                          {renderAppIcon(program)}
                          {program.cnName}
                        </span>
                      }
                      key={program.key}
                    >
                      {renderSummary({ type: program.key, config: program })}
                      {renderBetaMiniappList(program)}
                    </Tabs.TabPane>
                  ))}
                </>
              )}
              {/* 线上小程序投放列表 */}
              {
                hasMiniprogramDeliver && isProd &&
                <Tabs.TabPane
                  tab={
                    <span>
                      <UsergroupAddOutlined />
                      小程序
                    </span>
                  }
                  key="apps"
                >
                  {renderProdMiniappList()}
                </Tabs.TabPane>
              }
            </Tabs>
          </Spin>
      </Modal>
    </>
  );
}
