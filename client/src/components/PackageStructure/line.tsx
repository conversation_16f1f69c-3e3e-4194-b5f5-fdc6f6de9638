import { useState, useMemo, useEffect, useRef } from 'react';
import { Link } from 'umi';
import { Select, Checkbox, Space } from 'antd';
import { Line } from '@ant-design/plots';
import _ from 'lodash';
import { formatFileSize } from '@/utils/file';
import { BIZ_LINE, BIZ_LINE_OPTIONS } from '../../const/biz';
import './index.less';

interface IDataItem {
  size: number;
  subPackageName: string;
  branchName: string;
  iterId: number;
  bizType: BIZ_LINE;
}

interface IProps {
  data: IDataItem[];
}

const Comp = ({ data: propsData = [] }: IProps) => {
  const [data, setData] = useState<IDataItem[]>([]);
  const [pkg, setPkg] = useState(BIZ_LINE.Common);

  // 取最小的包体积值
  const minBuildSize = useMemo(() => {
    let miniSize = _.get(data, '[0].size', 0) || 0;
    data.forEach(item => {
      miniSize = item.size < miniSize ? item.size : miniSize;
    });
    // 最小值再减去2M，然后把这个值当做纵坐标的最小值
    miniSize = miniSize - 2 * 1024;
    return miniSize;
  }, [data]);

  const packageSizeAreaConfig = {
    data,
    height: 600,
    renderer: 'svg',
    xField: 'branchName',
    seriesField: 'subPackageName',
    yField: 'size',
    xAxis: {
      label: {
        style: {
          fill: '#1890ff',
        }
      },
    },
    yAxis: {
      min: minBuildSize > 0 ? minBuildSize : 0,
      label: {
        formatter: (name: any) => formatFileSize(name),
      },
    },
    label: {
      formatter: ({ size = 0 }) => formatFileSize(size),
    },
    point: {
      size: 5,
      shape: 'diamond',
      style: {
        fill: 'white',
        stroke: '#5B8FF9',
        lineWidth: 2,
      },
    },
    tooltip: {
      showMarkers: false,
      formatter: ({ size = 0, subPackageName = '' }) => {
        return { 
          name: subPackageName,
          value: `${formatFileSize(size)}（${size}）`,
        };
      },
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: '#000',
          fill: 'red',
        },
      },
    },
    interactions: [
      {
        type: 'marker-active',
      },
    ],
  };

  function clickXAxisLabel(element: any) {
    const target = element.target || {};
    const id = target.id || '';
    const offset = id.indexOf('rc/');
    let branchName = '';
    if (offset > -1) {
      branchName = id.slice(offset);
    }
    const { iterId = '' } = data.find(item => item.branchName === branchName) || {};
    iterId ? window.open(`/#/iter/detail?iterId=${iterId}`) : null;
  }

  useEffect(() => {
    const filterData = propsData.filter(item => item.bizType === pkg);
    setData(filterData);
  }, [pkg]);

  return (
    <div className='package-area-content' onClick={(e) => { clickXAxisLabel(e) }}>
      <div style={{
        color: 'rgba(0, 0, 0, 0.85)',
        fontSize: '16px',
        marginBottom: '15px',
        fontWeight: 'bold',
      }}>微信小程序行业历史版本包体积趋势图</div>
      <Space>
        <span>行业：</span>
        <Select
          defaultValue={BIZ_LINE.Common}
          value={pkg}
          placeholder="Please select"
          style={{ width: '120px' }}
          onChange={setPkg}
          options={BIZ_LINE_OPTIONS}
        />
      </Space>
      <Line {...packageSizeAreaConfig} style={{margin: '20px 0 0'}}  />
    </div>  
  );
}

export default Comp;