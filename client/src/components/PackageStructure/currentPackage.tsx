import { useState, useMemo, useEffect, useRef } from 'react';
import { Tree, Spin, Descriptions, Row, Col, Statistic, Empty, Skeleton, Tag, Divider, Alert, } from 'antd';
import { FolderOpenOutlined, FileTextOutlined, FileZipOutlined, FolderOutlined, } from '@ant-design/icons';
import _ from 'lodash';
import QrCode from '@/components/QrCode';
import { IPackageSize, IPackageInfo, IPageInfo } from '@/interface/package';
import { IProject } from '@/interface/project';
import { EPackageType, EBuildType } from '@/const/package';
import Pie from './pie';

import './index.less';


interface Props {
  /** 项目 */
  project?: IProject;
  /** 迭代分支id */
  iterId?: string;
  /** 开发分支id */
  devId?: string;
  /** 游离开发分支id */
  freeDevId?: string;
}

const CurrentPackage: React.FC<Props> = (props) => {
  const {
    loading = false,
    structureData = {},
  } = props;
  const [selectedNode, setSelectedNode] = useState<IPackageInfo | IPageInfo | null>();
  const { appStructure, packageSize, dist, branchName, branchUrl, publishedIterBranchList, lastPublishedStructure, } = structureData || {};
  const packageStructure = appStructure?.packages || [];
  const project = props.project || structureData?.project;
  const packages = appStructure?.packages;
  const pages = (selectedNode as IPackageInfo | null)?.pages;
  const treeData = packages?.map(pkg => {
    return {
      ...pkg,
      key: pkg.packageName,
      title: pkg.packageName,
      children: pkg.pages?.map((page, index) => {
        return {
          ...page,
          key: `${page.source}_${index}`,
          title: page.pageName,
          isLeaf: true
        }
      })
    }
  });

  useEffect(() => {
    const defaultSelectedNode = _.get(structureData, 'appStructure.packages.[0]', {}) || {};
    setSelectedNode(defaultSelectedNode);
  }, [JSON.stringify(structureData)]);

  // 包构建类型占比
  const packageBuildTypePercent = useMemo(getPercent('buildType', packages), [packages])
  // 包业务类型占比
  const packageBizTypePercent = useMemo(getPercent('bizType', packages), [packages])
  // 包体积占比
  const packageSizePercent = useMemo(getPackageSizePercent(packageSize), [packageSize])
  // 业务体积占比
  const bizSizePercent = useMemo(getBizSizePercent(packageSize, packages), [packageSize, packages])
  // 页面业务类型占比
  const pageBizTypePercent = useMemo(getPercent('bizType', pages), [pages])


  /**
   * 节点选择回调
   */
  function onSelect(_keys: any, info: any) {
    setSelectedNode(info.node)
  };

  function renderPackageTypeTag(selectedNode: IPackageInfo) {
    switch (selectedNode?.packageType?.name) {
      case EPackageType.Main: return <Tag color="green">{selectedNode.packageType.title}</Tag>;
      case EPackageType.SubPackage: return <Tag color="blue">{selectedNode.packageType.title}</Tag>;
      case EPackageType.IndependentSubPackage: return <Tag color="orange">{selectedNode.packageType.title}</Tag>;
      default: return;
    }
  }

  function renderBuildTypeTag(selectedNode: IPackageInfo) {
    switch (selectedNode?.buildType?.name) {
      case EBuildType.CompileTime: return <Tag color="#3b5999">{selectedNode.buildType.title}</Tag>;
      case EBuildType.Runtime: return <Tag color="#08979c">{selectedNode.buildType.title}</Tag>;
      default: return;
    }
  }

  function renderPackageDetail(selectedNode: IPackageInfo) {
    return <Descriptions title={'包信息'} size="default" column={1}>
      <Descriptions.Item label="包名" labelStyle={{ fontWeight: 500 }}>
        {selectedNode.packageName}&nbsp;&nbsp;{renderPackageTypeTag(selectedNode)}
      </Descriptions.Item>
      <Descriptions.Item label="构建类型" labelStyle={{ fontWeight: 500 }}>
        {renderBuildTypeTag(selectedNode)}
      </Descriptions.Item>
      <Descriptions.Item label="GitLab链接" labelStyle={{ fontWeight: 500 }}><a href={selectedNode.gitlabHref} target="_blank">{selectedNode.gitlabHref}</a></Descriptions.Item>
      <Descriptions.Item label="包含页面数" labelStyle={{ fontWeight: 500 }}>{selectedNode.pages?.length || 0}</Descriptions.Item>
      <Descriptions.Item label="页面业务类型分布" labelStyle={{ fontWeight: 500 }}><Pie data={pageBizTypePercent} /></Descriptions.Item>
    </Descriptions>
  }

  function renderPageDetail(selectedNode: IPageInfo) {
    return <Descriptions title={'包信息'} size="default" column={1}>
      <Descriptions.Item label="页面名" labelStyle={{ fontWeight: 500 }}>{selectedNode.pageName}</Descriptions.Item>
      <Descriptions.Item label="页面路径" labelStyle={{ fontWeight: 500 }}>{selectedNode.source}</Descriptions.Item>
      {selectedNode.pageTitle && <Descriptions.Item label="页面标题" labelStyle={{ fontWeight: 500 }}>{selectedNode.pageTitle}</Descriptions.Item>}
      <Descriptions.Item label="GitLab链接" labelStyle={{ fontWeight: 500 }}><a href={selectedNode.gitlabHref} target="_blank">{selectedNode.gitlabHref}</a></Descriptions.Item>
      <Descriptions.Item label="扫码访问" labelStyle={{ fontWeight: 500 }}>
        <QrCode defaultPage={selectedNode.source} project={project} />
      </Descriptions.Item>
    </Descriptions>
  }

  const pagesCount = packages?.reduce((per, cur) => {
    if (cur.pages) return per + cur.pages.length;
    return per;
  }, 0) || 0;

  return (
    <div className="package-structure">
      {/* 分支信息 */}
      {
        structureData &&
        <Alert
          message={<>
            {/* 如果 props 中传了 project, 则不展示所属项目 */}
            {!props.project && <><span>所属项目: {project?.cnName}</span><Divider type="vertical" /></>}
            <span>代码分支: <a href={branchUrl} target="_blank">{branchName}</a></span>
          </>}
        />
      }

      {/* 包结构整体信息 */}
      <div className="main-info">
        <Row align="middle" style={{ marginBottom: '8px' }}>
          <Col span={3}>
            <Statistic title="分包总数" value={packages?.length || 0} prefix={<FolderOutlined />} />
          </Col>
          <Col span={3}>
            <Statistic title="页面总数" value={pagesCount} prefix={<FileTextOutlined />} />
          </Col>
          <Col span={7}>
            {packageBuildTypePercent.length > 0 && <Pie title="包构建类型分布" data={packageBuildTypePercent} />}
          </Col>
          <Col span={7}>
            {packageBizTypePercent.length > 0 && <Pie title="包业务类型分布" data={packageBizTypePercent} />}
          </Col>
        </Row>
        {
          packageSize &&
          <Row align="middle">
            <Col span={3}>
              <Statistic title={<a href={dist} target="_blank">源码体积（小程序源码）</a>} value={packageSize.sourceHumanSize || 0} prefix={<FolderOpenOutlined />} />
            </Col>
            <Col span={3}>
              <Statistic title="构建产物体积（不含插件）" value={packageSize.buildHumanSize || 0} prefix={<FileZipOutlined />} />
            </Col>
            <Col span={7}>
              {packageSizePercent.length > 0 && <Pie title="包体积占比" data={packageSizePercent} type="size" />}
            </Col>
            <Col span={7}>
              {packageSizePercent.length > 0 && <Pie title="业务体积占比" data={bizSizePercent} type="size" />}
            </Col>
          </Row>
        }
      </div>

      <Row gutter={48} className="tree-info">
        {/* 包结构树 */}
        <Col span={12} className="tree-wrapper">
          <Skeleton loading={loading} active>
            <Tree.DirectoryTree
              multiple
              height={600}
              defaultExpandAll
              onSelect={onSelect}
              treeData={treeData}
              defaultSelectedKeys={treeData ? [treeData[0]?.key] : []}
            />
          </Skeleton>
        </Col>

        {/* 包/页面详细信息 */}
        <Col span={12}>
          {
            (selectedNode as IPackageInfo | null)?.packageName && renderPackageDetail(selectedNode as IPackageInfo)
          }
          {
            (selectedNode as IPageInfo | null)?.pageName && renderPageDetail(selectedNode as IPageInfo)
          }
          {!selectedNode && <Empty description={false} className="empty" />}
        </Col>
      </Row>
    </div>
  );
}

function getPercent(typeVar: string, list?: any[]) {
  return () => {
    if (!list) return [];

    const typeMap = {} as { [key: string]: { type: string; value: number; } };
    list.forEach(item => {
      const { name = 'unknown', title = '未知' } = item[typeVar] ?? {};
      if (!typeMap[name]) {
        typeMap[name] = { type: title, value: 1 };
      } else {
        typeMap[name].value += 1
      }
    })
    return Object.entries(typeMap).map(([_key, value]) => value);
  }
}


function getPackageSizePercent(packageSize?: IPackageSize) {
  return () => {
    if (!packageSize) return [];

    const packageSizeList = packageSize.subPackage;
    if (!packageSizeList) return [];

    let subPackageTotalSize = 0;
    const percentRes = packageSizeList.map(item => {
      subPackageTotalSize += item.buildSize;
      return {
        type: item.subPackageName,
        value: item.buildSize
      }
    })

    return percentRes.concat({
      type: '其他',
      value: packageSize.buildSize - subPackageTotalSize
    });
  }
}

function getBizSizePercent(packageSize: IPackageSize | undefined, packageTree?: IPackageInfo[]) {
  return () => {
    if (!packageSize || !packageTree) return [];

    const packageSizeList = packageSize.subPackage;
    if (!packageSizeList) return [];

    const typeMap = {} as { [key: string]: { type: string; value: number; } };
    let subPackageTotalSize = 0;
    packageSizeList.forEach(item => {
      const packageInfo = packageTree.find(packageInfo => packageInfo.packageName === item.subPackageName)
      const { name = 'unknown', title = '未知' } = packageInfo?.bizType || {};

      subPackageTotalSize += item.buildSize;
      if (!typeMap[name]) {
        typeMap[name] = { type: title, value: item.buildSize };
      } else {
        typeMap[name].value += item.buildSize
      }
    })
    const percentRes = Object.entries(typeMap).map(([_key, value]) => value);

    return percentRes.concat({
      type: '其他',
      value: packageSize.buildSize - subPackageTotalSize
    });
  }
}

export default CurrentPackage;