import { useState, useMemo, useEffect, useRef } from 'react';
import _ from 'lodash';
import { IPackageSize } from '@/interface/package';
import { IProject } from '@/interface/project';
import { GROUP_SIZE } from '@/const/biz';
import Area from './area';
import GroupColumn from './groupColumn';
import SubPackageColumn from './subPackageColumn';
import Line from './line';
import './index.less';

interface Props {
  /** 项目 */
  project?: IProject;
  /** 迭代分支id */
  iterId?: string;
  /** 开发分支id */
  devId?: string;
  /** 游离开发分支id */
  freeDevId?: string;
}

const Comp: React.FC<Props> = (props) => {
  const {
    structureData = {},
  } = props;

  const { appStructure, packageSize, dist, branchName, branchUrl, publishedIterBranchList, lastPublishedStructure, } = structureData || {};
  const packageStructure = appStructure?.packages || [];

  // 包体积趋势图
  const publishedPackageSizeList = useMemo(getPublishedPackageSizeList(publishedIterBranchList), [publishedIterBranchList]);
  // 行业体积大小
  const groupSizeList = useMemo(getGroupSizeList(lastPublishedStructure, publishedIterBranchList, packageSize, packageStructure, branchName), [lastPublishedStructure, publishedIterBranchList, packageSize, packageStructure, branchName])
  // 分包体积大小
  const packageSizeList = useMemo(getPackageSizeList(publishedIterBranchList, packageSize, branchName), [publishedIterBranchList, packageSize, branchName])
  // 行业包体积趋势图
  const publishedSizeList = useMemo(getPublishedSizeList(publishedIterBranchList, packageStructure), [publishedIterBranchList, packageStructure])

  return (
    <div style={{padding: '0 20px'}}>
      <Area data={publishedPackageSizeList} />

      <GroupColumn data={groupSizeList} />

      <SubPackageColumn data={packageSizeList} />

      <Line data={publishedSizeList} />
    </div>
  );
}

function getPublishedPackageSizeList(publishedIterBranchList: any[]) {
  return () => {
    if (!publishedIterBranchList || publishedIterBranchList.length < 1) return [];

    const packageSizeList = publishedIterBranchList.map(item => {
      const iterId = _.get(item, 'iterId', '') || '';
      const size = _.get(item, 'packageSize.buildSize', 0) || 0;
      const branchName = _.get(item, 'rcGitBranch.name', 'unknown') || 'unknown';
      return {
        iterId,
        branchName,
        size,
      }
    });

    return packageSizeList;
  }
}

function getPublishedSizeList(publishedIterBranchList: any[], packageStructure: any[]) {
  return () => {
    if (!publishedIterBranchList || publishedIterBranchList.length < 1) return [];
    const bizMap: any = {};
    packageStructure.forEach(item => {
      bizMap[item.packageName] = item.bizType.name;
    });
    const res: any[] = [];
    publishedIterBranchList.forEach(item => {
      const iterId = _.get(item, 'iterId', '') || '';
      const branchName = _.get(item, 'rcGitBranch.name', 'unknown') || 'unknown';
      const subPackage = _.get(item, 'packageSize.subPackage') || [];
      subPackage.forEach((val: any) => {
        res.push({
          iterId,
          branchName,
          size: val.buildSize,
          subPackageName: val.subPackageName,
          bizType: bizMap[val.subPackageName],
        });
      });
    });

    return res;
  }
}

function getPackageSizeList(publishedIterBranchList: any[], packageSize?: IPackageSize, branchName: string) {
  return () => {
    if (!publishedIterBranchList ||!packageSize) return [];
    const lastPublishedBranchName = _.get(publishedIterBranchList, `[${publishedIterBranchList.length - 1}].rcGitBranch.name`, '');
    const lastPublishPackageList = _.get(publishedIterBranchList, `[${publishedIterBranchList.length - 1}].packageSize.subPackage`, []) || [];
    const currentPackageList = packageSize?.subPackage || [];
    
    const currentPackageSizeList: { label: string; type: string; value: any; }[] = [];
    const lastPublishedPackageSizeList: { label: string; type: string; value: any; }[] = [];
    
    currentPackageList.forEach(item => {
      const currentItem = {
        label: item.subPackageName,
        type: `${branchName}_当前分支`,
        value: item.buildSize,
      };
      currentPackageSizeList.push(currentItem);

      // 如果当前分支和最近一次发布分支名一致，则不再处理
      if (branchName !== lastPublishedBranchName) {
        const { buildSize: lastBuildSize } = lastPublishPackageList.find(l_item => l_item.subPackageName === item.subPackageName) || {};
        const lastItem = {
          label: item.subPackageName,
          type: `${lastPublishedBranchName}_最近一次发布版本`,
          value: lastBuildSize,
        };
        lastPublishedPackageSizeList.push(lastItem);  
      }
    });
    // 当前分支，按照包体积从大到小排序
    currentPackageSizeList.sort((a, b) => b.value - a.value);
  
    return currentPackageSizeList.concat(lastPublishedPackageSizeList);  
  }
}

function getBizSize(packageSize: IPackageSize | undefined, packageTree: any[]) {
  if (!packageSize || !packageTree) return [];

  const packageSizeList = packageSize.subPackage;
  if (!packageSizeList) return [];

  const typeMap = {} as { [key: string]: { subPackageTitle: string; subPackageName: string; buildSize: number; } };
  
  packageSizeList.forEach(item => {
    const packageInfo = packageTree.find(packageInfo => packageInfo.packageName === item.subPackageName)
    const { name = 'unknown', title = '未知' } = packageInfo?.bizType || {};

    if (!typeMap[name]) {
      typeMap[name] = { subPackageTitle: title, subPackageName: name, buildSize: item.buildSize };
    } else {
      typeMap[name].buildSize += item.buildSize
    }
  });
  const percentRes = Object.entries(typeMap).map(([_key, value]) => value);
  return percentRes;
}

function getGroupSize(bizSizeList: any[]) {
  const groupBizeMap = {} as { [key: string]: { group: string; groupName: string; groupMaxSize: number; buildSize: number;} };

  bizSizeList.forEach(bizItem => {
    const groupItem = GROUP_SIZE.find(g_item => g_item.bizLines.includes(bizItem.subPackageName)) || { group: 'unknown', name: '未知', size: 0 };
    const {
      group,
      name: groupName,
      size: groupMaxSize,
    } = groupItem;

    if (groupBizeMap[group]) {
      groupBizeMap[group]['buildSize'] += bizItem.buildSize;
    } else {
      groupBizeMap[group] = {
        group,
        groupName,
        groupMaxSize,
        buildSize: bizItem.buildSize,
      }
    }
  });

  const groupBizeList = Object.keys(groupBizeMap).map(key => groupBizeMap[key]);

  return groupBizeList;
}

function getColumnList(currentGroupSize: any[], lastGroupSize: any[], currentBranchName: string, lastPublishedBranchName: string) {
  const columnList = [];

  currentGroupSize.forEach(currentItem => {
    const {
      group,
      groupName,
      buildSize: currentBuildSize,
      groupMaxSize,
    } = currentItem;
    const { buildSize: lastBuildSize } = lastGroupSize.find(l_item => l_item.group === group) || {};

    // 用最大包体积 减去 真实包大小，得到根据差值的正负
    // 根据正负来区分【剩余包体积】和【超过包体积】的柱状图区域颜色
    const currentSubtractMaxValue = groupMaxSize - currentBuildSize;
    const lastSubtractMaxValue = groupMaxSize - lastBuildSize;

    // 由于堆叠柱状图没办法控制【堆叠顺序】，只能把数据处理成以下顺序：
    // 同一柱状图从上到下分别是：超出限制的值（beyond）、小于限制体积的剩余值（remainder）、真实体积和限制体积的差值（current）
    // 处理当前分支
    ['beyond', 'remainder', 'current', ].forEach(type => {
      const columnItemData = {
        branch: currentBranchName,
        groupName, 
        type: `currentBranch-${type}_${currentBranchName}`,
      };

      if (type === 'beyond') {
        columnItemData.value = currentSubtractMaxValue < 0 ? Math.abs(currentSubtractMaxValue) : 0;
      } else if (type === 'remainder') {
        columnItemData.value = currentSubtractMaxValue > 0 ? Math.abs(currentSubtractMaxValue) : 0;
      } else {
        const isExceed = currentSubtractMaxValue < 0;

        columnItemData.type = `currentBranch-${type}${isExceed ? '-isExceed' : ''}_${currentBranchName}`,
        columnItemData.value = currentSubtractMaxValue > 0 ? currentBuildSize : groupMaxSize;
      }
      columnList.push(columnItemData);
    });

    // 处理最近一次发布分支，当前分支如果和上一次发布分支名一致就不再操作
    if (currentBranchName !== lastPublishedBranchName) {
      ['beyond', 'remainder', 'current', ].forEach(type => {
        const columnItemData = {
          branch: lastPublishedBranchName,
          groupName,
          type: `lastPublishedBranch-${type}_${lastPublishedBranchName}`,
        };
  
        if (type === 'beyond') {
          columnItemData.value = lastSubtractMaxValue < 0 ? Math.abs(lastSubtractMaxValue) : 0;
        } else if (type === 'remainder') {
          columnItemData.value = lastSubtractMaxValue > 0 ? Math.abs(lastSubtractMaxValue) : 0;
        } else {
          const isExceed = lastSubtractMaxValue < 0;

          columnItemData.type = `lastPublishedBranch-${type}${isExceed ? '-isExceed' : ''}_${lastPublishedBranchName}`,
          columnItemData.value = lastSubtractMaxValue > 0 ? lastBuildSize : groupMaxSize;
        }
        columnList.push(columnItemData);
      });
    }
  });

  return columnList;
}

function getGroupSizeList(lastPublishedStructure: any, publishedIterBranchList: any[], packageSize: IPackageSize, packageStructure: any[], branchName: string) {
  return () => {
    if (!lastPublishedStructure || !publishedIterBranchList || !packageSize || !packageStructure) return [];

    // 先把分包转换成bizLine维度
    const lastPackageStructure = lastPublishedStructure?.structureList || [];
    const { packageSize: lastPackageSize } = publishedIterBranchList.find(item => item?.rcGitBranch.name === lastPublishedStructure.branName) || {};
    const lastBizSize = getBizSize(lastPackageSize, lastPackageStructure);
    const currentBizSize = getBizSize(packageSize, packageStructure);
    // 再把bizLine转换成group维度
    const lastGroupSize = getGroupSize(lastBizSize);
    const currentGroupSize = getGroupSize(currentBizSize);
    // 再把groupList转化成柱状图所需数据格式
    const columnList = getColumnList(currentGroupSize, lastGroupSize, branchName, lastPublishedStructure.branName);

    return columnList;
  }
}

export default Comp;