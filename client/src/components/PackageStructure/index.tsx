import { useState, useMemo, useEffect, useRef } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { Result, But<PERSON>, Ta<PERSON>, Spin, } from 'antd';
import { PieChartOutlined, AreaChartOutlined, } from '@ant-design/icons';
import packageApi, { IGetStructureRes } from '@/api/package';
import { IProject } from '@/interface/project';
import CurrentPackage from './currentPackage';
import LastPublishedPackage from './lastPublishedPackage';

interface Props {
  /** 项目 */
  project?: IProject;
  /** 迭代分支id */
  iterId?: string;
  /** 开发分支id */
  devId?: string;
  /** 游离开发分支id */
  freeDevId?: string;
}

const Comp: React.FC<Props> = (props) => {
  const { iterId, devId, freeDevId, } = props;
  const [activeTab, setActiveTab] = useState('0');
  const [structureData, setStructureData] = useState<IGetStructureRes['data'] | null>();
  const [loading, setLoading] = useState(true);
  const [err, setError] = useState('');
  const propsRef = useRef<Props>(props);
  const project = props.project || structureData?.project;

  useEffect(() => {
    propsRef.current = props;
    setStructureData(null);
    setError('');

    if (project?.name || iterId || devId || freeDevId) fetchPackageStructure()
  }, [JSON.stringify(props), iterId, devId, freeDevId])

  /**
   * 获取包结构
   */
  function fetchPackageStructure() {
    setLoading(true);

    packageApi.getStructure({ projectName: project?.name, iterId, devId, freeDevId }).then(res => {
      // 如果入参有变更，则返回不做处理
      if (iterId !== propsRef.current.iterId || devId !== propsRef.current.devId || project?.name !== propsRef.current.project?.name) return;

      unstable_batchedUpdates(() => {
        if (!res || !res.data) throw Error(res.errorMsg || '结构分析失败');
        setStructureData(res.data);
        setLoading(false)
      })
    })
      .catch(err => {
        unstable_batchedUpdates(() => {
          setStructureData(null);
          setLoading(false)
          setError(err.message)
        })
      })
  }

  if (err && !loading) {
    return <Result
      status="500"
      title="请稍后再试"
      subTitle={err}
      extra={
        <Button type="primary" onClick={() => fetchPackageStructure()}>
          刷新
        </Button>
      }
    />
  }

  return (
    <Spin spinning={loading} tip="包结构分析时间较长，请耐心等待~">
      {
        project?.name === 'fliggy-weixin' ? 
        <Tabs
          activeKey={activeTab}
          items={[PieChartOutlined, AreaChartOutlined].map((Icon, index) => {
            return {
              label: (
                <span>
                  <Icon />
                  {index === 0 ? '当前版本分析' : '历史版本分析'}
                </span>
              ),
              key: String(index),
            };
          })}
          style={{padding: '0 20px'}}
          onChange={(key) => {
            setActiveTab(key);
          }}
        /> : null
      }

      {
        (project?.name !== 'fliggy-weixin' || activeTab === '0') ? 
          <CurrentPackage 
            loading={loading}
            project={project}
            structureData={structureData}
            setLoading={setLoading}
          /> : 
          <LastPublishedPackage 
            structureData={structureData}
          />
      }
    </Spin>
  );
}

export default Comp;