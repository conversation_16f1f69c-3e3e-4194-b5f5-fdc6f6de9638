import { useState, useMemo, useEffect, useRef } from 'react';
import { Link } from 'umi';
import { Area } from '@ant-design/plots';
import _ from 'lodash';
import { formatFileSize } from '@/utils/file';
import './index.less';

const Comp = ({ data = [] }) => {
  // 取最小的包体积值
  let minBuildSize = _.get(data, '[0].size', 0) || 0;
  data.forEach(item => {
    minBuildSize = item.size < minBuildSize ? item.size : minBuildSize;
  });
  // 最小值再减去2M，然后把这个值当做纵坐标的最小值
  minBuildSize = minBuildSize - 2 * 1024 * 1024;

  const packageSizeAreaConfig = {
    data,
    height: 300,
    renderer: 'svg',
    xField: 'branchName',
    yField: 'size',
    xAxis: {
      label: {
        style: {
          fill: '#1890ff',
        }
      },
    },
    yAxis: {
      min: minBuildSize > 0 ? minBuildSize : 0,
      label: {
        formatter: (name: any) => formatFileSize(name),
      },
    },
    label: {
      formatter: ({ size = 0 }) => formatFileSize(size),
    },
    point: {
      size: 5,
      shape: 'diamond',
      style: {
        fill: 'white',
        stroke: '#5B8FF9',
        lineWidth: 2,
      },
    },
    tooltip: {
      showMarkers: false,
      formatter: ({ size = 0 }) => {
        return { 
          name: '包大小',
          value: `${formatFileSize(size)}（${size}）`,
        };
      },
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: '#000',
          fill: 'red',
        },
      },
    },
    interactions: [
      {
        type: 'marker-active',
      },
    ],
  };

  function clickXAxisLabel(element: any) {
    const target = element.target || {};
    const id = target.id || '';
    const offset = id.indexOf('rc/');
    let branchName = '';
    if (offset > -1) {
      branchName = id.slice(offset);
    }
    const { iterId = '' } = data.find(item => item.branchName === branchName) || {};
    iterId ? window.open(`/#/iter/detail?iterId=${iterId}`) : null;
  }


  return (
    <div className='package-area-content' onClick={(e) => { clickXAxisLabel(e) }}>
      <span style={{
        color: 'rgba(0, 0, 0, 0.85)',
        fontSize: '16px',
        marginBottom: '6px',
        fontWeight: 'bold',
        textAlign: 'center',
      }}>微信小程序历史版本包体积趋势图</span>
      <Area {...packageSizeAreaConfig} style={{margin: '20px 0 0'}}  />
    </div>  
  );
}

export default Comp;