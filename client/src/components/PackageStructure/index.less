.package-structure {
  .main-info {
    background-color: #f9f9f9;
    padding: 12px 24px;
    
    .main-info-statistic-content {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;

      .main-info-statistic-item {
        width: 50% !important;
      }
    }
  }

  .tree-info {
    padding: 0 24px;
    margin-top: 20px;
  }

  .tree-wrapper {
    border-right: 1px solid #f2f2f2;
  }

  .empty {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.package-area-content {
  width: 100%;
  padding-bottom: 20px;
  margin-top: 20px;
  overflow: auto;
}