import { useState } from 'react';
import { Pie, measureTextWidth } from '@ant-design/charts';
import { formatFileSize } from '@/utils/file';

import './index.less';

const basicConfig = {
  width: 300,
  height: 120,
  padding: 0,
  appendPadding: [8, 80, 8, 0],
  angleField: 'value',
  colorField: 'type',
  radius: 1,
  label: {
    type: 'spider',
    content: ({ percent }: any) => `${(percent * 100).toFixed(0)}%`,
  },
  pieStyle: {
    strokeOpacity: 0.7
  },
  interactions: [{ type: 'element-active' }],
}

const donutConfig = {
  width: 250,
  height: 200,
  appendPadding: [8, 30, 8, 0],
  angleField: 'value',
  colorField: 'type',
  radius: 1,
  innerRadius: 0.64,
  label: {
    offset: '-50%',
    style: {
      textAlign: 'center',
    },
    autoRotate: false,
    content: '{value}',
  },
  pieStyle: {
    strokeOpacity: 0.7
  },
  interactions: [
    { type: 'element-active' },
    { type: 'element-active' },
    { type: 'pie-statistic-active' }
  ],
  statistic: {
    title: {
      offsetY: -4,
      customHtml: (container: HTMLElement, _view: any, datum: { type: string; value: number }) => {
        const { width, height } = container.getBoundingClientRect();
        const d = Math.sqrt(Math.pow(width / 2, 2) + Math.pow(height / 2, 2));
        const text = datum ? `${datum.type}占比` : '总计';
        return renderStatistic(d, text, {
          fontSize: 28
        });
      },
    },
    content: {
      offsetY: 4,
      style: {
        fontSize: '22px',
      },
      customHtml: (container: HTMLElement, _view: any, datum: { value: number }, data: { value: number }[]) => {
        const { width } = container.getBoundingClientRect();
        const total = data.reduce((r, d) => r + d.value, 0);
        const text = datum ? `${((datum.value / total) * 100).toFixed(0)}%` : `${total}`;
        return renderStatistic(width, text, {
          fontSize: 32,
        });
      }
    },
  }
}

function renderStatistic(containerWidth: number, text: string, style: { [key: string]: string | number }) {
  const { width: textWidth, height: textHeight } = measureTextWidth(text, style);
  const R = containerWidth / 2; // r^2 = (w / 2)^2 + (h - offsetY)^2

  let scale = 1;

  if (containerWidth < textWidth) {
    scale = Math.min(Math.sqrt(Math.abs(Math.pow(R, 2) / (Math.pow(textWidth / 2, 2) + Math.pow(textHeight, 2)))), 1);
  }

  return `<div style="width:${containerWidth}px;font-size:${scale}em;line-height:${scale < 1 ? 1 : 'inherit'};text-align:center">${text}</div>`;
}

interface Props {
  title?: string;
  data: Record<string, any>[]
  type?: 'basic' | 'size'
}

const Comp: React.FC<Props> = ({ data, title, type = 'basic' }) => {
  let config: any = basicConfig;
  if (type === 'size') {
    config = {
      ...config,
      tooltip: {
        formatter: (datum: any) => {
          return { name: datum.type, value: formatFileSize(datum.value) };
        },
      }
    };
  }

  return <div>
    {title && <div style={{
      color: 'rgba(0, 0, 0, 0.45)',
      fontSize: '14px',
      marginBottom: '6px',
      textAlign: 'center'
    }}>{title}</div>}
    <Pie data={data} {...config} />
  </div>;
};

export default Comp;