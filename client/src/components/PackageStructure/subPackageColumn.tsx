import { useState, useMemo, useEffect, useRef } from 'react';
import { Column } from '@ant-design/plots';
import _ from 'lodash';
import './index.less';
import { formatFileSize } from '@/utils/file';

const Comp = ({ data = [] }) => {
  const columnConfig = {
    data,
    xField: 'label',
    yField: 'value',
    seriesField: 'type',
    isGroup: true,
    dodgePadding: 2,
    intervalPadding: 20,
    minColumnWidth: 40,
    maxColumnWidth: 40,
    color: ({ type = '' }) => {
      const typeArr = type.split('_') || [];
      type = typeArr.length >= 2 ? type.split('_')[1] : '';

      return type === '当前分支' ? '#1AAF8B' : '#406C85';
    },
    yAxis: {
      label: {
        formatter: (name: any) => formatFileSize(name),
      },
    },
    tooltip: {
      showMarkers: false,
      formatter: ({ type = '', value = 0 }) => {
        return {
          name: type,
          value: formatFileSize(value),
        };
      },
    },
    label: {
      style: {
        fontSize: 10,
      },
      position: 'middle',
      formatter: ({ value = 0 }) => {
        return value ? formatFileSize(value) : null;
      },
      layout: [
        {
          type: 'interval-adjust-position',
        },
        {
          type: 'interval-hide-overlap',
        },
        {
          type: 'adjust-color',
        },
      ],
    },
  };

  return (
    <div className='package-area-content'>
      <span style={{
        color: 'rgba(0, 0, 0, 0.85)',
        fontSize: '16px',
        marginBottom: '6px',
        fontWeight: 'bold',
        textAlign: 'center',
      }}>各个分包体积大小</span>
      <Column {...columnConfig} style={{width: '2500px', margin: '20px 0 0'}} />
    </div>  
  );
}

export default Comp;