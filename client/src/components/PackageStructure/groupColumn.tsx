import { useState, useMemo, useEffect, useRef } from 'react';
import { Column } from '@ant-design/plots';
import _ from 'lodash';
import { formatFileSize } from '@/utils/file';
import './index.less';

const seriesFieldMap = {
  'currentBranch-current': {
    name: '当前包体积',
    color: '#1AAF8B',
  },
  'currentBranch-current-isExceed': {
    name: '当前包体积配额',
    color: '#1AAF8B',
  },
  'lastPublishedBranch-current': {
    name: '当前包体积',
    color: '#406C85',
  },
  'lastPublishedBranch-current-isExceed': {
    name: '当前包体积配额',
    color: '#406C85',
  },
  'currentBranch-remainder': {
    name: '剩余包体积',
    color: 'rgba(26, 175, 139, 0.5)',
  },
  'lastPublishedBranch-remainder': {
    name: '剩余包体积',
    color: 'rgba(64, 108, 133, 0.5)',
  },
  'currentBranch-beyond': {
    name: '超出限制体积',
    color: '#FF4500',
  },
  'lastPublishedBranch-beyond': {
    name: '超出限制体积',
    color: '#FF4500',
  },
}

const Comp = ({ data = [] }) => {
  const barConfig = {
    data,
    xField: 'groupName',
    yField: 'value',
    seriesField: 'type',
    isStack: true,
    isGroup: true,
    groupField: 'branch',
    maxColumnWidth: 50,
    color: ({ type = '' }) => {
      type = type.split('_')[0];

      const color = _.get(seriesFieldMap, `${type}.color`, '') || '';
      return color;
    },
    yAxis: {
      label: {
        formatter: (name: any) => formatFileSize(name),
      },
    },
    legend: {
      itemName: {
        formatter: (name: any) => {
          const [key, branchName] = name.split('_') || [];
          const title = _.get(seriesFieldMap, `${key}.name`, '');
          return `${branchName}_${title}`;
        },
      }
    },
    tooltip: {
      showMarkers: false,
      formatter: ({ branch = '', type = '', value = 0 }) => {
        type = type.split('_')[0];

        return {
          name: `${branch}-${_.get(seriesFieldMap, `${type}.name`, '') || ''}`,
          value: formatFileSize(value),
        };
      },
    },
    label: {
      position: 'middle',
      formatter: ({value = 0}) => {
        return value ? formatFileSize(value) : null;
      },
      layout: [
        {
          type: 'interval-adjust-position',
        },
        {
          type: 'interval-hide-overlap',
        },
        {
          type: 'adjust-color',
        },
      ],
    },
  };

  return (
    <div className='package-area-content'>
      <span style={{
        color: 'rgba(0, 0, 0, 0.85)',
        fontSize: '16px',
        marginBottom: '6px',
        fontWeight: 'bold',
        textAlign: 'center',
      }}>行业包体积大小</span>
      <Column {...barConfig} style={{margin: '20px 0 0'}} />
    </div>  
  );
}

export default Comp;