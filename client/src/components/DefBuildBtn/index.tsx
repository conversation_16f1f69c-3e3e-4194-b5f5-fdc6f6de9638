import { useEffect, useState, useRef } from 'react';
import { Button, message } from 'antd';
import { IGitRepoInfo } from '@/interface/git';
import { IProject } from '@/interface/project';
import { EPubTypeEnv, EDefBuildStatus, EDefBuildTaskType } from '@/const/def';
import defApi, { IDefRes } from '@/api/def';

// 轮询def任务构建结果
class PollTask {
  taskType: EDefBuildTaskType;
  devId: number;
  iterId: number;
  defTaskId: number;
  pollTid = 0;
  onComplete: (res: IDefRes) => void;

  constructor({ taskType, devId, iterId, defTaskId, onComplete }: { taskType: EDefBuildTaskType; devId?: number; iterId?: number; defTaskId: number; onComplete: (res: IDefRes) => void; }) {
    this.taskType = taskType;
    this.devId = devId || 0;
    this.iterId = iterId || 0;
    this.defTaskId = defTaskId;
    this.onComplete = onComplete;
    this.query();
  }

  query = () => {
    let shouldPoll = true;

    defApi.getIterPublishTaskDetail({
      updateDB: true,
      taskType: this.taskType,
      devId: this.devId,
      iterId: this.iterId,
      defTaskId: this.defTaskId
    })
      .then((res) => {
        if (res.success) {
          // 如果构建状态变更为非构建中，或者接口报错，则取消轮询
          const buildStatus = res.data?.task?.pub_status;
          if (buildStatus !== EDefBuildStatus.BUILDING) {
            shouldPoll = false;

            // 文案提示
            if (buildStatus === EDefBuildStatus.BUILD_SUCCESS) {
              message.success('def构建成功！');
            } else if (buildStatus === EDefBuildStatus.BUILD_ERROR) {
              const errorStage = res.data?.runtime?.stages.filter(stage => stage.status === 'ERROR')?.[0];
                const errorMsg = `${errorStage.displayName}(${errorStage.name})失败，请去def平台处理`
              message.error({ content: errorMsg || 'def构建失败，请去def平台处理', duration: 10 });
            }

            this.onComplete(res);
          }
        } else {
          shouldPoll = false;
          message.error({ content: res?.errorMsg || '查询def构建信息失败', duration: 10 });
          this.onComplete(res);
        }
      })
      .finally(() => {
        if (!shouldPoll) return;

        // 60秒轮询一次
        this.pollTid = window.setTimeout(this.query, 60000)
      });
  }

  destroy = () => {
    window.clearTimeout(this.pollTid);
  }
}

interface Props extends React.HTMLAttributes<HTMLDivElement> {
  /** 按钮类型 */
  btnType?: "link" | "primary" | "default" | "text" | "ghost" | "dashed";
  /** 项目 */
  project?: IProject;
  /** 代码分支 */
  branchName?: string;
  /** 项目仓库 */
  gitRepo?: IGitRepoInfo;
  /** 开发分支id */
  devId?: number;
  /** 迭代id */
  iterId?: number;
  /** def迭代id */
  defIterId: number;
  /** def构建任务id */
  defTaskId: number;
  /** 构建状态（1：未构建、2：构建中、3：构建成功、4：构建失败） */
  defBuildStatus: EDefBuildStatus;
  /** 构建任务环境
   * 和taskEnv的区别是：defEnvType是创建构建任务成功后记录在DB里的状态，taskEnv是即将创建任务的传参
   * 这样在迭代发布阶段，预发和正式发布两个按钮才可以独立，不然这两种情况都会触发轮询 */
  defEnvType?: EPubTypeEnv;
  /** 是否是游离开发分支 */
  isFreeDevBranch?: boolean;
  /** 构建任务类型 */
  taskType: EDefBuildTaskType;
  /** 构建任务环境 */
  taskEnv: EPubTypeEnv;
  /** 查询当前构建状态的回调，更新defBuildStatus */
  refreshBuildStatus: () => void;
  /** 构建前回调，如果返回true继续执行，false暂停 */
  onBeforeBuild?: () => Promise<boolean>;
}


export default ({ children, btnType = "link", project, branchName, gitRepo, devId, iterId, defIterId, defTaskId, defBuildStatus, defEnvType, taskType, taskEnv, refreshBuildStatus, onBeforeBuild }: Props) => {
  const [btnDisabled, setBtnDisabled] = useState(false);
  const pollTaskRef = useRef<PollTask | null>();

  useEffect(() => {
    // 迭代发布时，只有当前DB里记录的构建环境和选择的环境吻合时，才会触发轮询
    // 开发分支发布时，只有预发这一个环境，就不用判断
    if (taskType === EDefBuildTaskType.ITERATION && defEnvType !== taskEnv) {
      return;
    }

    // 如有存在的轮询任务，则先销毁
    if (pollTaskRef.current) pollTaskRef.current.destroy();

    // 如果处在构建中，则开始轮询
    if (defBuildStatus === EDefBuildStatus.BUILDING) {
      pollTaskRef.current = new PollTask({
        taskType,
        devId,
        iterId,
        defTaskId,
        onComplete: () => refreshBuildStatus()
      });
    } else {
      pollTaskRef.current = null;
    }
    setBtnDisabled(defBuildStatus === EDefBuildStatus.BUILDING);

    return () => {
      if (pollTaskRef.current) pollTaskRef.current.destroy();
    }
  }, [defBuildStatus])

  const onClick = async () => {
    // 先执行构建前的回调
    if (onBeforeBuild) {
      const isOk = await onBeforeBuild();
      if (!isOk) return;
    }

    // 如果是非构建中的状态，则创建def发布任务
    if (defBuildStatus !== EDefBuildStatus.BUILDING) {
      setBtnDisabled(true);
      defApi.createIterPublishTask({
        updateDB: true,
        taskType,
        pub_env: taskEnv,
        devId,
        iterId,
        iterationId: defIterId,
      }).then(res => {
        if (res.success) {
          // 构建成功后需要更新一下构建状态字段，然后就会触发轮询任务（查询构建结果）
          refreshBuildStatus();
        } else if (/conflict/i.test(res?.errorMsg || '')) {
          message.error({ content: '自动合并master分支存在冲突，请手动合并后再尝试！', duration: 10 })
        } else {
          message.error({ content: res?.errorMsg || '创建def迭代失败', duration: 10 });
        }
      }).finally(() => {
        setBtnDisabled(false);
      })
    }
  }

  return (
    <>
      <Button type={btnType} disabled={btnDisabled} loading={btnDisabled} onClick={onClick} style={{ padding: btnType === 'link' ? 0 : '4px 15px' }}>
        {children}
      </Button>
    </>
  );
}
