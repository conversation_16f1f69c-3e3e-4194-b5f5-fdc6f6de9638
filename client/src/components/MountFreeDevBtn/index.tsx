import { useEffect, useState } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { Modal, Button, message, Popconfirm, Alert } from 'antd';
import freeDevBranchApi from '@/api/free-dev-branch';
import { IIterBranch } from '@/interface/iter-branch';
import { IFreeDevBranch } from '@/interface/free-dev-branch';
import { IterStatus } from '@/const/iter-branch';
import IterBranchList from '../../pages/IterFreeBranchList/components/IterBranchList';
import { checkSizeBeforeBranchAction } from '@/utils/perf';

import './index.less';

interface Props {
  /** 游离开发分支 */
  freeDevBranch: IFreeDevBranch;
  /** 操作类型 */
  action: 'mount' | 'unmount';
  /** 挂载完成回调 */
  onMountEnd?: (freeDevBranch: IFreeDevBranch) => void;
  /** 取消挂载完成回调 */
  onUnmountEnd?: (freeDevBranch: IFreeDevBranch) => void;
}

const Comp: React.FC<Props> = ({ freeDevBranch, action, children, onMountEnd, onUnmountEnd }) => {
  const [mountLoading, setMountLoading] = useState(false);
  const [mountModalVisible, setMountModalVisible] = useState(false);
  const [unmountLoading, setUnmountLoading] = useState(false);
  const [unmountPopVisible, setUnmountPopVisible] = useState(false);
  const [iterBranch, setIterBranch] = useState<IIterBranch>();

  function mountBranch() {
    if (!iterBranch) return;

    setMountLoading(true);
    return freeDevBranchApi.mount(freeDevBranch.devId, iterBranch.iterId)
      .then((res) => {
        if (res.data) {
          unstable_batchedUpdates(() => {
            setMountModalVisible(false);
            setMountLoading(false);
          })
          message.success('挂载成功');
          onMountEnd && onMountEnd(res.data)
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        const errMsg = err.message || '挂载失败';
        if (/conflict/i.test(errMsg)) {
          Modal.error({
            title: errMsg,
            content: `请先手动 merge ${iterBranch.gitBranch.name} 分支后，再尝试挂载（务必注意 merge 指向，不要误动 stable 分支！）`,
          });
        } else {
          Modal.error({ title: errMsg });
        }
        setMountLoading(false);
      });
  }

  function unmountBranch() {
    setUnmountLoading(true);
    setUnmountPopVisible(false);

    return freeDevBranchApi.unmount(freeDevBranch.devId)
      .then((res) => {
        if (res.data) {
          setUnmountLoading(false);
          message.success('取消挂载成功');
          onUnmountEnd && onUnmountEnd(res.data)
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '取消挂载失败');
        setUnmountLoading(false);
      });
  }

  /** 挂载时检查体积并通知 */
  function mountBranchWithCheckSize() {
    checkSizeBeforeBranchAction(
      freeDevBranch,
      freeDevBranch.projectName,
      { needNotice: true, holdUp: false },
      () => mountBranch()
    );
  }

  return <>
    {action === 'unmount'
      ? <Popconfirm
        visible={unmountPopVisible}
        title="确认取消挂载？"
        okText="是"
        cancelText="否"
        okButtonProps={{ loading: unmountLoading }}
        cancelButtonProps={{ disabled: unmountLoading }}
        onConfirm={unmountBranch}
        onCancel={() => setUnmountPopVisible(false)}
      >
        <div onClick={() => setUnmountPopVisible(true)}>{children}</div>
      </Popconfirm>
      : <div onClick={() => setMountModalVisible(true)}>{children}</div>
    }

    <Modal
      open={mountModalVisible}
      title={<span>将游离开发分支 <a>{freeDevBranch.branchName}</a> 挂载到如下迭代上:</span>}
      className="mount-modal"
      width={1060}
      destroyOnClose
      maskClosable={false}
      onCancel={() => setMountModalVisible(false)}
      bodyStyle={{ paddingTop: '12px' }}
      footer={
        <Popconfirm title={`确认挂载到迭代 ${iterBranch?.version} 上？`} okText="是" cancelText="否" onConfirm={mountBranchWithCheckSize}>
          <Button type="primary" loading={mountLoading} disabled={!iterBranch}>挂载</Button>
        </Popconfirm>
      }
    >
      <Alert showIcon message="1. 仅 ”计划发布“ 状态的迭代可被挂载；2. 若游离开发分支的集成模式为 “合并代码”，则挂载时会自动 merge 迭代分支" type="info" style={{ marginBottom: '12px' }} />
      <Alert showIcon message="1. 若 merge 时有冲突需手动解决，请注意 merge 指向，不要误动 stable 分支！" type="warning" style={{ marginBottom: '12px' }} />
      <IterBranchList
        projectName={freeDevBranch.projectName}
        defaultFilteredStatus={[IterStatus.PLAN]}
        disableStatusFilter
        onCheck={(iterBranch) => setIterBranch(iterBranch)} />
    </Modal >
  </>
}

export default Comp;
