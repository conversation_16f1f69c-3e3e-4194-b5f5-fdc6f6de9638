import { useEffect, useState } from 'react';
import { message, Row, Col, Space, Menu, MenuProps, Dropdown, Popconfirm, Divider, Spin, Tag, Popover, Modal } from 'antd';
import { Link, useAccess, useModel } from 'umi';
import { DownOutlined, CheckCircleOutlined } from '@ant-design/icons';
import devBranchApi from '@/api/dev-branch';
import freeDevBranchApi from '@/api/free-dev-branch';
import { IDevBranch } from '@/interface/dev-branch';
import { IIterBranch } from '@/interface/iter-branch';
import { EDevStatus, EMergeCode, EChecked } from '@/const/dev-branch';
import { IterStatus } from '@/const/iter-branch';
import { isSupportBuild } from '@/const/build';
import { EProjectType } from '@/const/project';
import { BIZ_TITLE, BIZ_TITLE_COLOR } from '@/const/biz';
import { EDefBuildTaskType, EPubTypeEnv, DEF_BUILD_STATUS_WORDING } from '@/const/def';
import { EDeliverBranchType, EDeliverActionType } from '@/const/component-deliver';
import EditDevBtn, { Action } from '@/components/EditDevBtn';
import MountFreeDevBtn from '@/components/MountFreeDevBtn';
import BuildBtn from '@/components/BuildBtn';
import DefBuildBtn from '@/components/DefBuildBtn';
import ComponentDeliverBtn from '@/components/ComponentDeliverBtn';
import AnalyzeSize from '@/components/AnalyzeSize';
import { IFreeDevBranch } from '@/interface/free-dev-branch';
import { EFreeDevMountStatus } from '@/const/free-dev-branch';
import { convertToFormFields as convertToAoneListFields } from '@/components/FR/aone';
import { IProject } from '@/interface/project';
import { checkSizeBeforeBranchAction } from '@/utils/perf';

import './index.less';

interface Props {
  /** （游离）开发分支 */
  devBranch: IDevBranch | IFreeDevBranch;
  /** 项目 */
  project: IProject;
  /** 是否是挂载模式 */
  isMountMode?: boolean;
  /** 迭代分支 */
  iterBranch?: IIterBranch;
  /** 体积分析信息 */
  sizeDiffInfo?: Record<string, number>;
}

const Comp: React.FC<Props> = ({ iterBranch, devBranch, project, isMountMode = false, sizeDiffInfo }) => {
  const [latestDevBranch, setLatestDevBranch] = useState(devBranch);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(true);
  const access = useAccess();
  const currentUser = useModel('user');
  const { type: projectType } = project || {};
  // 开发分支是否处于“就绪”状态
  const isReady = latestDevBranch.status === EDevStatus.READY;
  // 开发分支是否处于“废弃”状态
  const isAbandon = latestDevBranch.status === EDevStatus.ABANDON;
  // 开发分支是否处于“开发”状态
  const isDevelop = latestDevBranch.status === EDevStatus.DEVELOP;
  // 游离开发分支是否处于“挂载”状态
  const isMounted = (latestDevBranch as IFreeDevBranch).mountStatus === EFreeDevMountStatus.MOUNTED;
  // 开发分支是否已回归完成
  const isChecked = latestDevBranch.checked === EChecked.YES;
  // 迭代是否处于“计划”状态
  const iterIsPlan = iterBranch?.status === IterStatus.PLAN;
  // 迭代是否处于“集成回归”状态
  const iterIsMerge = iterBranch?.status === IterStatus.MERGE;
  // 是否是游离开发分支
  const isFreeDevBranch = (latestDevBranch as IFreeDevBranch).mountStatus !== undefined;
  // 是否展示”包体积分析“按钮
  const isShowPackageAnalyzedBtn = !!latestDevBranch?.reportAnalyzed;
  // 接口
  const api = isFreeDevBranch ? freeDevBranchApi : devBranchApi;
  // 项目类型是模块类型
  const isComponentProject = projectType === EProjectType.COMPONENT;

  if (!visible) return null;

  const renderActions = () => {
    // 动作列表
    const actionList = [];

    // 筛选是否支持打码
    if (isSupportBuild(project)) {
      actionList.push(<BuildBtn
        key="build"
        project={project}
        gitRepo={latestDevBranch.gitRepo}
        branchName={latestDevBranch.gitBranch.name}
      >
        <a>打码</a>
      </BuildBtn>)
    }

    // 处于“开发”状态，才可以构建beta包
    if (isComponentProject && isDevelop) {
      actionList.push(<DefBuildBtn
        key="defBuild"
        project={project}
        gitRepo={latestDevBranch.gitRepo}
        branchName={latestDevBranch.gitBranch.name}
        devId={latestDevBranch.devId}
        defIterId={latestDevBranch.defIterId}
        defTaskId={latestDevBranch.defTaskId}
        defBuildStatus={latestDevBranch.defBuildStatus}
        taskType={isFreeDevBranch ? EDefBuildTaskType.FREE_DEV_BRANCH : EDefBuildTaskType.DEV_BRANCH}
        taskEnv={EPubTypeEnv.Beta}
        refreshBuildStatus={getBranch}
      >
        构建beta包
      </DefBuildBtn>)
    }

    // 投放测试
    if (isComponentProject) {
      actionList.push(<ComponentDeliverBtn
        key="deliver"
        actionType={isDevelop ? EDeliverActionType.DELIVER : EDeliverActionType.VIEW}
        project={project}
        latestDevBranch={latestDevBranch}
        branchType={isFreeDevBranch ? EDeliverBranchType.FREE_DEV_BRANCH : EDeliverBranchType.DEV_BRANCH}
        taskEnv={EPubTypeEnv.Beta}
        description={`${latestDevBranch.description}的页面投放(组件v${latestDevBranch.pkgInitialVersion})`}
      >
        {isDevelop ? '投放测试' : '投放记录'}
      </ComponentDeliverBtn>)
    }

    if (isMountMode) {
      // （取消）挂载按钮
      if ((isMounted && !isReady) || (!isMounted && isDevelop)) {
        actionList.push(<MountFreeDevBtn
          key="mount"
          freeDevBranch={latestDevBranch as IFreeDevBranch}
          action={isMounted ? 'unmount' : 'mount'}
          onMountEnd={devBranch => setLatestDevBranch(devBranch)}
          onUnmountEnd={devBranch => setLatestDevBranch(devBranch)}
        >
          <a>{isMounted ? '取消挂载' : '挂载'}</a>
        </MountFreeDevBtn>)
      }
    } else {
      // （取消）就绪按钮
      if (!isAbandon && iterIsPlan) {
        actionList.push(<a key="ready" onClick={isReady ? cancelReady : ready}>{isReady ? '取消就绪' : '就绪'}</a>)
      }

      // 确认测试回归完成按钮
      if (isReady && iterIsMerge && devBranch.qaList?.length && latestDevBranch.checked !== null && !isComponentProject) {
        actionList.push(<a key="check" onClick={isChecked ? uncheck : check}>{isChecked ? '取消回归确认' : '确认已回归'}</a>)
      }
    }

    // 编辑按钮
    const editDevBtn = <EditDevBtn
      key="edit"
      isFreeDevBranch={isFreeDevBranch}
      devBranch={latestDevBranch}
      iterBranch={iterBranch}
      project={project}
      onUpdateEnd={devBranch => setLatestDevBranch(devBranch)}
      action={isDevelop ? Action.Update : Action.Read}
    >
      <a>{isDevelop ? '编辑' : '详情'}</a>
    </EditDevBtn>;

    // 更多动作列表
    const moreActionList: MenuProps['items'] = [];

    // 项目类型是模块模式，当“开发”状态时按钮放不下的放更多里
    if (isComponentProject && isDevelop) {
      moreActionList.push({
        key: 'edit',
        label: editDevBtn
      });
    } else {
      actionList.push(editDevBtn);
    }

    // 包体积分析
    if (isShowPackageAnalyzedBtn) {
      moreActionList.push({
        key: 'package',
        label: <Link to={`/package/structure?${isFreeDevBranch ? 'freeDevId' : 'devId'}=${latestDevBranch.devId}`} target="_blank">包体积分析</Link>
      });
    }

    if (!isMountMode && isMounted && !isReady) {
      moreActionList.push({
        key: 'unmount',
        label:
          <MountFreeDevBtn
            key="mount"
            freeDevBranch={latestDevBranch as IFreeDevBranch}
            action="unmount"
            onUnmountEnd={() => setVisible(false)}
          >
            <a>取消挂载</a>
          </MountFreeDevBtn>
      })
    }

    if (isDevelop) {
      moreActionList.push({
        key: 'discard',
        danger: true,
        label:
          <Popconfirm title={`确认废弃${isMounted ? '（检测到该分支已挂载，请谨慎操作）' : ''}？`} okText="是" cancelText="否" onConfirm={discardBranch}>
            废弃
          </Popconfirm>
      })
    }

    if (isDevelop || isAbandon) {
      moreActionList.push({
        key: 'delete',
        danger: true,
        label:
          <Popconfirm title={`确认删除（会删库及代码分支${isMounted ? '，检测到该分支已挂载，请谨慎操作' : ''}）？`} okText="是" cancelText="否" onConfirm={deleteBranch}>
            删除
          </Popconfirm>
      })
    }

    if (moreActionList.length > 0) {
      actionList.push(<Dropdown key="more" menu={{ items: moreActionList }} trigger={['click']}>
        <a onClick={e => e.preventDefault()}>
          更多 <DownOutlined />
        </a>
      </Dropdown>)
    }

    return actionList.map((Item, index) => index === 0 ? Item : [<Divider key={`divider-${index}`} type="vertical" />, Item]);
  };

  // 删除分支
  function deleteBranch() {
    setLoading(true);

    api.delete(latestDevBranch.devId)
      .then((res) => {
        if (res.success) {
          setVisible(false);
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || '删除分支失败');
      }).finally(() => {
        setLoading(false)
      });
  }

  // 查询分支
  function getBranch() {
    commonHandler('get', '查询分支');
  }

  // 废弃分支
  function discardBranch() {
    commonHandler('discard', '废弃分支');
  }

  // 准备就绪
  function ready() {
    // 就绪前检查体积
    const isProjectAdmin = project?.adminWorkidList?.includes(currentUser.workid);
    checkSizeBeforeBranchAction(latestDevBranch as IFreeDevBranch, project.name, () => {
      commonHandler('ready', '准备就绪');
    }, { holdUp: true, isAdmin: isProjectAdmin });
  }

  // 取消就绪
  function cancelReady() {
    commonHandler('cancelReady', '取消就绪');
  }

  // 确认已回归
  function check() {
    if (access.isAdmin || isInQaList()) {
      commonHandler('check', '确认已回归');
    } else {
      message.error('仅测试成员或管理员有权限操作')
    }
  }

  // 取消回归确认
  function uncheck() {
    if (access.isAdmin || isInQaList()) {
      commonHandler('uncheck', '取消回归确认');
    } else {
      message.error('仅测试成员或管理员有权限操作')
    }
  }

  function commonHandler(apiName: 'get' | 'discard' | 'ready' | 'cancelReady' | 'check' | 'uncheck', actionName: string) {
    setLoading(true);
    
    api[apiName](latestDevBranch.devId)
      .then((res) => {
        if (res.success && res.data) {
          setLatestDevBranch(res.data);
        } else {
          throw Error(res?.errorMsg);
        }
      })
      .catch((err) => {
        message.error(err.message || `${actionName}:失败`);
      }).finally(() => {
        setLoading(false)
      });
  }

  function isInQaList() {
    return !!devBranch.qaList?.find(item => item.workid === currentUser.workid);
  }

  let innerClassName = 'inner';
  if (isReady) innerClassName += ' ready'
  else if (isAbandon) innerClassName += ' abandon';

  return <Spin spinning={loading} wrapperClassName="dev-branch-item">
    {/* 行业线 */}
    {latestDevBranch.bizLine && <Tag color={BIZ_TITLE_COLOR[latestDevBranch.bizLine]} className="tag">{BIZ_TITLE[latestDevBranch.bizLine]}</Tag>}

    {/* 角标：已挂载 */}
    {isMountMode && isMounted && <div className="tag-mounted" />}

    <Row className={innerClassName} gutter={8}>
      <Col span={5} className="col">
        <Space direction="vertical" size={4}>
          <a href={latestDevBranch.gitBranch.url} target="_blank">{latestDevBranch.branchName}</a>
          {isMountMode && latestDevBranch.iterBranch &&
            <span>挂载迭代：<Link to={`/iter/detail?iterId=${latestDevBranch.iterId}`} target="_blank">v{latestDevBranch.iterBranch.version}</Link></span>
          }
          {
            !isMountMode && latestDevBranch.towerId &&
            <Link to={`/helper/report?devId=${latestDevBranch.devId}&towerId=${latestDevBranch.towerId}`} target="_blank">
              测试报告
            </Link>
          }
        </Space>
      </Col>
      <Col span={4} className="col">
        <div className="description">{latestDevBranch.description}</div>
      </Col>
      <Col span={5} className="col">
        <Space direction="vertical" size={4}>
          <div>集成模式：{latestDevBranch.mergeCode === EMergeCode.NO ? '更新依赖包' : '合并代码'}</div>
          <div>关联 Aone：{
            latestDevBranch.aoneList.length > 0 ?
              <Popover
                content={<div style={{ maxWidth: '1000px', lineHeight: '30px' }}>
                  {latestDevBranch.aoneList.map((aone, index) => <Tag key={index} className="aone-tag">{convertToAoneListFields(aone).label}</Tag>)}
                </div>}
              >
                <a>查看</a>
              </Popover>
              : <span className="empty-text">无</span>
          }</div>
          {
            isComponentProject
            ? <>
                <div>构建状态：
                  <a href={`//space.o2.alibaba-inc.com/iteration/${latestDevBranch.defIterId}/basic`} target="_blank">{ DEF_BUILD_STATUS_WORDING[latestDevBranch.defBuildStatus || 0] }</a>
                </div>
                <div>最新构建版本：{ latestDevBranch.pkgPublishVersion || '-'}</div>
            </>
            : null
          }
          <AnalyzeSize data={sizeDiffInfo} />
        </Space>
      </Col>
      <Col span={5} className="col">
        <Space direction="vertical" size={4}>
          <div>创建：{latestDevBranch.creator}<span className="time">{latestDevBranch.gmtCreate}</span></div>
          {
            latestDevBranch.modifier &&
            <>
              <div>修改：{latestDevBranch.modifier}<span className="time">{latestDevBranch.gmtModified}</span></div>
            </>
          }
          {
            latestDevBranch.qaList?.length &&
            <>
              <div>
                测试：{latestDevBranch.qaList.map(item => item.name).join('、')}
                {isChecked && <CheckCircleOutlined style={{ marginLeft: '6px', color: '#389e0d', verticalAlign: 'middle' }} />}
              </div>
            </>
          }
        </Space>
      </Col>
      <Col span={5} className="col actions">
        {renderActions()}
      </Col>
    </Row>
  </Spin>
}

export default Comp;
