.dev-branch-item {
  width: 100%;

  .tag {
    position: absolute;
    top: 0;
    left: 0;
  }

  .tag-mounted {
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    height: 60px;
    background-image: url('https://gw.alicdn.com/imgextra/i1/O1CN01kuMyiy1U3dqw5wlvR_!!6000000002462-2-tps-205-200.png');
    background-size: 100%;
    background-repeat: no-repeat;
    background-position: top -13px right 0;
  }

  .inner {
    position: relative;
    padding: 20px 24px;

    &.ready {
      --icon: url('https://gw.alicdn.com/imgextra/i4/O1CN01FUFIrS1OZnwAjchKX_!!6000000001720-2-tps-210-210.png');
      --bgc: linear-gradient(to right, rgba(255, 255, 255, 0.2), rgba(199, 221, 165, 0.2));
    }

    &.abandon {
      --icon: url('https://gw.alicdn.com/imgextra/i1/O1CN01jyVC7n1JIn9Pnlkma_!!6000000001006-2-tps-200-200.png');
      --bgc: linear-gradient(to right, rgba(255, 255, 255, 0.2), rgba(214, 132, 132, 0.2));
    }

    &.ready,
    &.abandon {
      --transparent: url('https://gw.alicdn.com/imgextra/i3/O1CN01U7MJsm1SueQVZ1oeW_!!6000000002307-2-tps-2-2.png');
      background-image: var(--bgc),
        cross-fade(var(--transparent), var(--icon), 0.2);
      background-image: var(--bgc),
        -webkit-cross-fade(var(--transparent), var(--icon), 0.2);
      background-size: auto, 90px;
      background-repeat: repeat, no-repeat;
      background-position: 0 0, bottom -16px left -24px;
    }
  }

  .col {
    display: flex;
    align-items: center;
  }

  .split-comma {
    &:last-child:after {
      display: none;
    }

    &:after {
      content: '、';
      color: #ccc;
    }
  }

  .description {
    word-break: break-word;
  }

  .empty-text {
    color: #999;
  }

  .actions {
    justify-content: flex-end;
    white-space: nowrap;
  }

  .time {
    margin-left: 6px;
    font-size: 12px;
    color: #999;
  }
}

.aone-tag:hover {
  border-color: #1890ff;

  a {
    color: #1890ff;
  }
}