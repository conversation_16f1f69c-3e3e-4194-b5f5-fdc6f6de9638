import { useState, useEffect, useImperativeHandle, forwardRef } from 'react';
import { message, } from 'antd';
import { ReloadOutlined, ShareAltOutlined } from '@ant-design/icons';
import defApi from '@/api/def';
import { ECR_STATUS, CR_STATUS_WORDING } from '@/const/def';
import classnames from 'classnames';
import './index.less';

interface Props {
  ref: any;
  defIterId: number;
}

const CRDetail = forwardRef(({ defIterId }: Props, ref) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>({});

  // 查询CR是否通过等信息
  const getCR = () => {
    setLoading(true);
    defApi.getCR({
      iterationId: defIterId,
    }).then((res) => {
      if (res.success) {
        setData(res?.data?.aonecr);
      } else {
        message.error({ content: res?.errorMsg || '查询CR信息失败', duration: 10 });
      }
    }).finally(() => {
      setLoading(false);
    })
  }

  useEffect(() => {
    if (defIterId) {
      getCR();
    }
  }, [defIterId])

  useImperativeHandle(ref, () => ({
    getCR
  }))

  return (
    <div className="cr-container">
      {
        data?.detail_url
        ? <>
           <a
            href={data.detail_url}
            target="_blank"
            className={classnames('cr-status', {
              'is-approved': [ECR_STATUS.ACCEPTED, ECR_STATUS.MERGED].includes(data.state),
              'not-approved': [ECR_STATUS.OPENED, ECR_STATUS.RE_OPENED].includes(data.state),
            })}
          >
            <ShareAltOutlined />
            {CR_STATUS_WORDING[data.state as ECR_STATUS] || '-'}
          </a>
        </>
        : <div className="cr-status is-initial">待创建</div>
      }
      <ReloadOutlined spin={loading} onClick={getCR} />
    </div>
  );

})

export default CRDetail;
