import { runApi } from '@/utils/request';

interface ISyncBranchReq {
  /** 操作的分支名 */
  branchName: string;
  /** 操作的分支url */
  branchUrl: string;
  /** 目标分支名 */
  targetBranchName: string;
}

interface ICommonRes {
  success: boolean;
  errorMsg?: string;
}

interface IGitBranchesRes {
  success: boolean;
  data: {
    commit: any;
    name: string;
    protected: boolean;
  }[]
}

export default {
  /** 同步分支 */
  syncBranch: (params: ISyncBranchReq) => runApi<ICommonRes>('/git/sync-branch', params),
  /** 查询gitlab现有分支(被删除的查不到) */
  getBranches: (params: { projectId: number; page?: number; per_page?: number; }) => runApi<IGitBranchesRes>('/git/get-branches', params, 'get'),
  /** 查询git branch -a 分支 */
  getRemoteBranches: (params: { url: string; }) => runApi<IGitBranchesRes>('/git/get-remote-branches', params, 'get'),
};
