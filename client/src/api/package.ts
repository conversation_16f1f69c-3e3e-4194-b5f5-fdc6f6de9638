import { runApi } from '@/utils/request';
import { IAppStructure, IPackageSize } from '@/interface/package';
import { IProject } from '@/interface/project';
interface IGetStructureReq {
  /** 项目名称 */
  projectName?: string;
  /** 迭代分支id */
  iterId?: string;
  /** 开发分支id */
  devId?: string;
  /** 游离开发分支id */
  freeDevId?: string;
}

interface IGetStructureByBranchReq {
  /** 分支名 */
  branchName: string;
  /** 分支地址 */
  branchUrl: string;
}

interface IGetStructureByProjectReq {
  /** 项目名 */
  projectName: string;
}

export interface IGetStructureRes {
  success: boolean;
  errorMsg?: string;
  data?: {
    publishedIterBranchList: any;
    lastPublishedStructure: any;
    appStructure: IAppStructure;
    packageSize: IPackageSize;
    dist: string;
    reportAnalyzed: string;
    project: IProject;
    branchName: string;
    branchUrl: string;
  }
}

interface IGetStructureByBranchRes {
  success: boolean;
  errorMsg?: string;
  data?: IAppStructure;
}

interface IAnalyzeBranchSizeReq {
  branchList: Array<{name: string;reportAnalyzed: string}>
  projectName: string;
  gmtModified?: string;
}

interface IAnalyzeIterSizeReq {
  defUploadPkgRes: string;
  projectName: string;
  gmtModified?: string;
}

interface IAnalyzeBranchSizeRes {
  success: boolean;
  errorMsg?: string;
  data?: {
    branchSizeMapList: Array<{
      branchName: string;
      sizeDiffInfo: Record<string, number>;
    }>
  }
} 

interface IAnalyzeIterSizeRes {
  success: boolean;
  errorMsg?: string;
  data?: {
    version: string;
    prevIterVersion: string;
    packageInfo: Record<string, Record<string, number>>;
    prevDefPkgRes: string;
  }
} 


export default {
  getStructure: (params: IGetStructureReq) => runApi<IGetStructureRes>('/package/get-structure', params),
  getStructureByBranch: (params: IGetStructureByBranchReq) => runApi<IGetStructureByBranchRes>('/package/get-structure-by-branch', params),
  getStructureByProject: (params: IGetStructureByProjectReq) => runApi<IGetStructureByBranchRes>('/package/get-structure-by-project', params),
  analyzeBranchSize: (params: IAnalyzeBranchSizeReq) => runApi<IAnalyzeBranchSizeRes>('/package/analyze-branch-size', params, 'post'),
  analyzeIterSize: (params: IAnalyzeIterSizeReq) => runApi<IAnalyzeIterSizeRes>('/package/analyze-iter-size', params, 'post')
};