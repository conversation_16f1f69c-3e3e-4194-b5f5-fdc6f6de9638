import { runApi } from '@/utils/request';
import { IIterBranch, IDeliverClientWithTask } from '@/interface/iter-branch';
import { IDevBranch } from '@/interface/dev-branch';
import { IFreeDevBranch } from '@/interface/free-dev-branch';
import { IProject } from '@/interface/project';
import { EClient } from '@/const/iter-deliver';

interface IIterListReq {
  pageSize?: number;
  pageNum?: number;
  conditions?: {
    projectName?: string;
    statusList?: number[];
  };
}

interface IIterCreateReq {
  version: IIterBranch['version'];
  description: IIterBranch['description'];
  publishDay: IIterBranch['publishDay'];
  projectName: IIterBranch['projectName'];
}

interface IIterUpdateReq {
  iterId: IIterBranch['iterId'];
  description?: IIterBranch['description'];
  status?: IIterBranch['status'];
  grayStatus?: IIterBranch['grayStatus'];
}

interface IIterPublishReq {
  iterId: IIterBranch['iterId'];
  // 是否灰度
  isGray: boolean;
  grayStatus?: IIterBranch['grayStatus'];
}

interface IIterRollbackReq {
  iterId: IIterBranch['iterId'];
}

interface IIterReviewReq {
  iterId: IIterBranch['iterId'];
}

interface IIterMergeReq {
  iterId: IIterBranch['iterId'];
  /** 是否忽略待集成开发分支为空的情况 */
  ignoreEmpty?: boolean;
}

interface IIterListDeliverClientReq {
  iterId: IIterBranch['iterId'];
}

interface IIterGetDeliverClientReq {
  iterId: IIterBranch['iterId'];
  clientName?: EClient;
  miniAppId: string;
}

interface IIterSkipDeliverReq {
  iterId: IIterBranch['iterId'];
  clientName: EClient;
  miniAppId: string;
}

interface IIterCancelSkipDeliverReq {
  iterId: IIterBranch['iterId'];
  clientName: EClient;
  miniAppId: string;
}

interface IIterUnbindDeliverTaskReq {
  iterId: IIterBranch['iterId'];
  clientName?: EClient;
  miniAppId?: string;
}

interface IIterBindDeliverTaskReq {
  iterId: IIterBranch['iterId'];
  deliverTaskId: number;
  miniAppId: string;
  clientName?: EClient;
  clientList?: EClient[];
}

interface IIterStartCompDeliverReq {
  iterId: IIterBranch['iterId'];
  status: IIterBranch['status'];
}

interface IIterCheckedReq {
  iterId: IIterBranch['iterId'];
  checked: IIterBranch['checked'];
}

interface IIterCommonRes {
  success: boolean;
  errorMsg?: string;
}

interface IIterListRes extends IIterCommonRes {
  data: {
    list: IIterBranch[];
    total: number;
  }
}

interface IIterGetRes extends IIterCommonRes {
  data: {
    detail: IIterBranch;
    project: IProject;
    devBranchList: IDevBranch[];
    freeDevBranchList: IFreeDevBranch[];
  };
}

interface IIterCreateRes extends IIterCommonRes {
  data: IIterBranch;
}

interface IIterUpdateRes extends IIterCommonRes {
  data: IIterBranch;
}

export interface IIterMergeResData extends IDevBranch {
  /** merge 结果 */
  mergeRes?: {
    /** 是否 merge 成功 */
    success: boolean;
  },
}
interface IIterMergeRes extends IIterCommonRes {
  data?: {
    data: IIterMergeResData[],
    defBuildRes?: boolean
  };
}

interface IIterPublishRes extends IIterCommonRes {
  data: IIterBranch;
}

interface IIterRollbackRes extends IIterCommonRes {
  data: IIterBranch;
}

interface IIterListDeliverClientRes extends IIterCommonRes {
  data: IDeliverClientWithTask[];
}

interface IIterGetDeliverClientRes extends IIterCommonRes {
  data: IDeliverClientWithTask | null;
}

interface IIterSkipDeliverRes extends IIterCommonRes {
  data: IDeliverClientWithTask;
}

interface IIterCancelSkipDeliverRes extends IIterCommonRes {
  data: IDeliverClientWithTask;
}

interface IIterUnbindDeliverTaskRes extends IIterCommonRes {
  data: IDeliverClientWithTask;
}


export default {
  list: (params: IIterListReq) => runApi<IIterListRes>('/iter-branch/list', params),
  get: (iterId: IIterBranch['iterId']) => runApi<IIterGetRes>('/iter-branch/get', { iterId }),
  create: (params: IIterCreateReq) => runApi<IIterCreateRes>('/iter-branch/create', params, 'post'),
  update: (params: IIterUpdateReq) => runApi<IIterUpdateRes>('/iter-branch/update', params, 'post'),
  delete: (iterId: IIterBranch['iterId']) => runApi<IIterCommonRes>('/iter-branch/delete', { iterId }),
  discard: (iterId: IIterBranch['iterId']) => runApi<IIterCommonRes>('/iter-branch/discard', { iterId }),
  merge: (params: IIterMergeReq) => runApi<IIterMergeRes>('/iter-branch/merge', params),
  cancelMerge: (iterId: number) => runApi<IIterCommonRes>('/iter-branch/cancel-merge', { iterId }),
  review: (params: IIterReviewReq) => runApi<IIterCommonRes>('/iter-branch/review', params),
  publish: (params: IIterPublishReq) => runApi<IIterPublishRes>('/iter-branch/publish', params, 'post'),
  rollback: (params: IIterRollbackReq) => runApi<IIterRollbackRes>('/iter-branch/rollback', params, 'post'),
  listDeliverClient: (params: IIterListDeliverClientReq) => runApi<IIterListDeliverClientRes>('/iter-branch/list-deliver-client', params),
  getDeliverClient: (params: IIterGetDeliverClientReq) => runApi<IIterGetDeliverClientRes>('/iter-branch/get-deliver-client', params),
  skipDeliver: (params: IIterSkipDeliverReq) => runApi<IIterSkipDeliverRes>('/iter-branch/skip-deliver', params, 'post'),
  cancelSkipDeliver: (params: IIterCancelSkipDeliverReq) => runApi<IIterCancelSkipDeliverRes>('/iter-branch/cancel-skip-deliver', params, 'post'),
  unbindDeliverTask: (params: IIterUnbindDeliverTaskReq) => runApi<IIterUnbindDeliverTaskRes>('/iter-branch/unbind-deliver-task', params, 'post'),
  bindDeliverTask: (params: IIterBindDeliverTaskReq) => runApi<IIterUnbindDeliverTaskRes>('/iter-branch/bind-deliver-task', params, 'post'),
  startComponentDeliver: (params: IIterStartCompDeliverReq) => runApi<IIterUpdateRes>('/iter-branch/start-component-deliver', params, 'post'),
  defHookText: () => runApi<IIterUpdateRes>('/iter-branch/def-hook-trigger', {}, 'post'),
  switchChecked: (params: IIterCheckedReq) => runApi<IIterCommonRes>('/iter-branch/switch-checked', params, 'post'),
  addDist: (params: { iterId: number; dist?: string; }) => runApi('/iter-branch/add-dist', params, 'post'),
};
