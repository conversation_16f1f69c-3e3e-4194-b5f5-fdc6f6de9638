import { runApi } from '@/utils/request';
import { EPubTypeEnv, EDefBuildTaskType } from '@/const/def';

export interface IDefRes {
  success: boolean;
  data?: any;
  rawData?: string;
  errorMsg?: string;
  errorCode?: string;
}

export default {
  createIteration: (params: { repo: string; name: string; description: string; branch: string; version: string; trunk: string; aoneBinds: string[]; }) =>
    runApi<IDefRes>('/def/create-iteration', params, 'post'),
  getIterationId: (params: { repo: string; branch: string }) =>
    runApi<IDefRes>('/def/get-iteration-id', params, 'get'),
  getIterationId2: (params: { repo: string; branch: string }) =>
    runApi<IDefRes>('/def/getIterationId2', params, 'get'),
  getIterationInfoById: (params: { iterationId: number }) =>
    runApi<IDefRes>('/def/get-iteration-info', params, 'get'),
  abandonIteration: (params: { iterationId: number }) => runApi<IDefRes>('/def/abandon-iteration', params, 'post'),
  bindBranchToIteration: (params: { iterationId: number }) =>
    runApi<IDefRes>('/def/bind-branch-to-iteration', params, 'post'),
  createIterPublishTask: (params: { updateDB?: boolean; taskType?: EDefBuildTaskType; devId?: number; iterId?: number; iterationId: number; pub_env: EPubTypeEnv; }) =>
    runApi<IDefRes>('/def/create-iter-publish-task', params, 'post'),
  getIterPublishTaskDetail: (params: { updateDB?: boolean; taskType?: EDefBuildTaskType; devId?: number; iterId?: number; defTaskId: number; }) =>
    runApi<IDefRes>('/def/get-iter-publish-task-detail', params, 'get'),
  checkMensheng: (params: any) => runApi<IDefRes>('/def/check-mensheng', params, 'get'),
  publishCheck: (params: { defIterId: number; }) => runApi<IDefRes>('/def/publish-check', params, 'get'),
  getIterPublishTasks: (params: any) => runApi<IDefRes>('/def/get-iter-publish-tasks', params, 'get'),
  createBranch: (params: any) => runApi<IDefRes>('/def/create-branch', params, 'post'),
  deleteBranch: (params: any) => runApi<IDefRes>('/def/delete-branch', params, 'post'),
  getLastCommit: (params: { iterationId: number; }) => runApi<IDefRes>('/def/get-last-commit', params, 'get'),
  getCR: (params: { iterationId: number; }) => runApi<IDefRes>('/def/get-cr', params, 'post'),
  createCR: (params: {
    projectName: string;
    iterationId: number;
    devbranchId: number;
    title: string;
    description: string;
    ids: string;
  }) => runApi<IDefRes>('/def/create-cr', params, 'post'),
  updateCR: (params: any) => runApi<IDefRes>('/def/update-cr', params, 'post'),
  getDefMember: (params: any) => runApi<IDefRes>('/def/get-def-member', params, 'post'),
};
