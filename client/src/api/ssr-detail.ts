import { runApi, run2ndApi } from '@/utils/request';

interface ISSRMonitorReq {
  sunfirePluginId: number;
  projectName: string;
  pageName: string;
  from: number;
  to: number;
}

interface ISSRMonitorRes {
  success: boolean;
  errorMsg?: string;
  data: any;
}

export default {
  ssrList: (params: any) => runApi<any>('/ssr-detail/ssr-list', params),
  logList: (params: any) => runApi<any>('/ssr-detail/log-list', params),
  addProject: (params: any) => runApi<any>('/ssr-detail/add-project', params, 'post'),
  updateProject: (params: any) => runApi<any>('/ssr-detail/update-project', params, 'post'),
  updatePage: (params: any) => runApi<any>('/ssr-detail/update-page', params, 'post'),
  addPage: (params: any) => runApi<any>('/ssr-detail/add-page', params, 'post'),
  ssrPrepub: (params: any) => runApi<any>('/ssr-detail/prepub', params, 'post'),
  ssrGray: (params: any) => runApi<any>('/ssr-detail/gray', params, 'post'),
  ssrPublish: (params: any) => runApi<any>('/ssr-detail/publish', params, 'post'),
  ssrRollback: (params: any) => runApi<any>('/ssr-detail/rollback', params, 'post'),
  getDiamondWithDownGrade: (params: any) => runApi<any>('/ssr-detail/getDiamondWithDownGrade', params, 'post'),
  setDiamondWithDownGrade: (params: any) => runApi<any>('/ssr-detail/setDiamondWithDownGrade', params, 'post'),
  delete: (params: any) => runApi<any>('/ssr-detail/delete', params, 'post'),
  setDowngrade: (params: any) => runApi<any>('/ssr-detail/setDowngrade', params, 'post'),
  setPreheatConfig: (params: any) => runApi<any>('/ssr-detail/preheat', params, 'post'),
  queryPage: (params: any) => runApi<any>('/ssr-detail/query-page', params),
  preloadApply: (params: any) => runApi<any>('/ssr-detail/preload-apply', params, 'post'),
  preloadPublish: (params: any) => runApi<any>('/ssr-detail/preload-publish', params, 'post'),
  monitorDetail: (params: any) => runApi<ISSRMonitorRes>('/ssr-detail/detail-data', params),
  getDcdnKV: (params: any) => runApi<any>('/ssr-detail/get-dcdnkv', params, 'post'),
  setDcdnKV: (params: any) => runApi<any>('/ssr-detail/set-dcdnkv', params, 'post'),
  preUpdate: (params: any) => runApi<any>('/ssr-detail/pre-update', params, 'post'),
  queryPressr: (params: any) => runApi<any>('/ssr-detail/pre-ssr', params),
  queryPressrDetail: (params: any) => runApi<any>('/ssr-detail/pre-ssr-detail', params),
  getDefIteration: (params: any) => runApi<any>('/ssr-detail/getDefIteration', params),
  queryLogData: (params: any) => runApi<any>('/ssr-detail/log-query', params, 'post'),
  queryIpData: (params: any) => runApi<any>('/ssr-detail/ip-query', params, 'post'),
  addCdnHot: (params: any) => runApi<any>('/ssr-detail/cdn-hot', params, 'post'),
  getPreloadCacheKey: (params: any) => runApi<any>('/ssr-detail/get-preload-cache-key', params)
}