import { runApi } from '@/utils/request';

interface IGenerateTbSharePasswordReq {
  link: string;
  title: string | undefined;
  image: string | undefined;
}

interface IGenerateTbSharePasswordRes {
  success: boolean;
  errorMsg?: string;
  data?: any;
}


export default {
  generateTbSharePassword: (params: IGenerateTbSharePasswordReq) => runApi<IGenerateTbSharePasswordRes>('/flian/generateTbSharePassword', params, 'get'),
};