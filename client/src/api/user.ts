import { runApi } from '@/utils/request';
import { IUser, UserType } from '@/interface/user';

interface IUserCurrentRes {
  success: boolean;
  errorMsg?: string;
  data?: IUser;
}

interface IUserListRes {
  success: boolean;
  errorMsg?: string;
  data?: IUser[];
}

export interface IUserCreateReq {
  userList: [{ value: string }];
  userType: UserType;
}

export interface IUserCreateRes {
  success: boolean;
  errorMsg?: string;
}

export interface IUserUpdateReq {
  userList: [{ value: string }];
  userType: UserType;
}

export interface IUserUpdateRes {
  success: boolean;
  errorMsg?: string;
  data?: IUser[];
}

export interface IUserDeleteRes {
  success: boolean;
  errorMsg?: string;
}

export default {
  current: () => runApi<IUserCurrentRes>('/user/current'),
  list: () => runApi<IUserListRes>('/user/list'),
  createMulti: (params: IUserCreateReq) => runApi<IUserCreateRes>('/user/create-multi', params, 'post'),
  updateMulti: (params: IUserUpdateReq) => runApi<IUserUpdateRes>('/user/update-multi', params, 'post'),
  delete: (workid: string) => runApi<IUserDeleteRes>('/user/delete', { workid }, 'get'),
};