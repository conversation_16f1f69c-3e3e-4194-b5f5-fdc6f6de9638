import { runApi } from '@/utils/request';
import { IIterDeliverTask } from '@/interface/iter-deliver';
import { EClient } from '@/const/iter-deliver';

interface IIterDeliverUploadReq {
  iterId: number;
  clientName?: EClient;
  miniAppId: string;
  miniAppVersion?: string;
}

interface IIterDeliverListReq {
  iterId: number;
  clientName?: EClient;
}

interface IIterDeliverGrayReq {
  id: number;
  grayStrategy: string;
}

interface IIterDeliverAuditReq {
  id: number;
  clientList?: EClient[];
}

interface IIterDeliverUniversalReq {
  id: number;
}

interface IIterDeliverCommonRes {
  success: boolean;
  errorMsg?: string;
}

interface IIterDeliverUniversalRes extends IIterDeliverCommonRes {
  data: boolean;
}

interface IIterDeliverListRes extends IIterDeliverCommonRes {
  data: {
    list: IIterDeliverTask[];
    total: number;
  }
}

export default {
  list: (params: IIterDeliverListReq) => runApi<IIterDeliverListRes>('/iter-deliver/list', params),
  upload: (params: IIterDeliverUploadReq) => runApi<IIterDeliverUniversalRes>('/iter-deliver/upload', params, 'post'),
  delete: (params: IIterDeliverUniversalReq) => runApi<IIterDeliverUniversalRes>('/iter-deliver/delete', params, 'post'),
  audit: (params: IIterDeliverAuditReq) => runApi<IIterDeliverUniversalRes>('/iter-deliver/audit', params, 'post'),
  passAudit: (params: IIterDeliverUniversalReq) => runApi<IIterDeliverUniversalRes>('/iter-deliver/pass-audit', params, 'post'),
  cancelAudit: (params: IIterDeliverUniversalReq) => runApi<IIterDeliverUniversalRes>('/iter-deliver/cancel-audit', params, 'post'),
  backDev: (params: IIterDeliverUniversalReq) => runApi<IIterDeliverUniversalRes>('/iter-deliver/back-dev', params, 'post'),
  gray: (params: IIterDeliverGrayReq) => runApi<IIterDeliverUniversalRes>('/iter-deliver/gray', params, 'post'),
  finishGray: (params: IIterDeliverUniversalReq) => runApi<IIterDeliverUniversalRes>('/iter-deliver/finish-gray', params, 'post'),
  online: (params: IIterDeliverUniversalReq) => runApi<IIterDeliverUniversalRes>('/iter-deliver/online', params, 'post'),
  offline: (params: IIterDeliverUniversalReq) => runApi<IIterDeliverUniversalRes>('/iter-deliver/offline', params, 'post'),
  rollback: (params: IIterDeliverUniversalReq) => runApi<IIterDeliverUniversalRes>('/iter-deliver/rollback', params, 'post'),
};