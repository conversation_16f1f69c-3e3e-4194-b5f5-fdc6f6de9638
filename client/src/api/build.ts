import { runApi } from '@/utils/request';
import { IBuildTask } from '@/interface/build';
import { EBuildPlatform } from '@/const/build';

export interface ICreateReq {
  /** 代码分支 */
  branchName: string;
  /** 项目名称 */
  projectName: string;
  /** 页面路径 */
  pagePath?: string;
  /** 页面参数 */
  pageQuery?: string;
  /** 构建环境，只对微信小程序有效 */
  env?: string;
  /** 构建平台 */
  platform?: EBuildPlatform;
  /** 自定义参数 */
  customArgv?: {
    /** 启用按需构建，只对支付宝小程序有效 */
    minifyBuild?: boolean;
    /** 参与按需构建的包 */
    minifyBuildPackages?: string[];
    /** 启用快速构建，只对微信小程序有效。与按需构建冲突，按需构建优先 */
    fastbuild?: 1 | 0;
    /** 备注 */
    remark?: string;
  };
}

interface ICreateRes {
  success: boolean;
  errorMsg?: string;
  data?: IBuildTask;
}

interface IListReq {
  /** 项目名称 */
  projectName: string;
  /** 代码分支 */
  branchName: string;
  /** 是否只展示自己的 */
  filterMine?: boolean;
  /** 请求页数 */
  page?: number;
  /** 每页条数 */
  pageSize?: number;
}

interface IListRes {
  success: boolean;
  errorMsg?: string;
  data?: {
    total: number;
    list: IBuildTask[];
  };
}

interface IGetReq {
  /** 项目名称 */
  projectName: string;
  /** 云构建任务id */
  taskId: string;
  /** 是否只展示自己的 */
  filterMine?: boolean;
}

interface IGetRes {
  success: boolean;
  errorMsg?: string;
  data?: IBuildTask;
}

export default {
  list: (params: IListReq) => runApi<IListRes>('/build/list', params),
  create: (params: ICreateReq) => runApi<ICreateRes>('/build/create', params, 'post'),
  get: (params: IGetReq) => runApi<IGetRes>('/build/get', params),
};