import { runApi } from '@/utils/request';
import { IFreeDevBranch } from '@/interface/free-dev-branch';
import { EProjectType } from '@/const/project';

interface IFreeDevBranchListReq {
  pageNum?: number;
  pageSize?: number;
  conditions?: {
    projectName?: string;
    filterMine?: boolean;
    searchBranchName?: string;
  };
  /** 是否需要返回挂载的迭代分支详情 */
  returnIterBranchDetail?: boolean;
}

interface IFreeDevBranchCreateReq {
  projectName: string;
  branchName: string;
  description: string;
  mergeCode: IFreeDevBranch['mergeCode'];
  npmList: IFreeDevBranch['npmList'];
  npmResolutionList: IFreeDevBranch['npmResolutionList'];
  bizLine: IFreeDevBranch['bizLine'];
  qaList: IFreeDevBranch['qaList'];
  aoneList?: IFreeDevBranch['aoneList'];
  pkgVersion?: string;
  projectType: EProjectType;
}

interface IFreeDevBranchUpdateReq {
  devId: number;
  description?: string;
  mergeCode?: IFreeDevBranch['mergeCode'];
  npmList?: IFreeDevBranch['npmList'];
  npmResolutionList?: IFreeDevBranch['npmResolutionList'];
  bizLine?: IFreeDevBranch['bizLine'];
  qaList?: IFreeDevBranch['qaList'];
  aoneList?: IFreeDevBranch['aoneList'];
  pkgVersion?: string;
}

interface IFreeDevBranchBaseRes {
  success: boolean;
  errorMsg?: string;
}

interface IFreeDevBranchCommonRes extends IFreeDevBranchBaseRes {
  data: IFreeDevBranch
}

interface IFreeDevBranchListRes extends IFreeDevBranchBaseRes {
  data: {
    list: IFreeDevBranch[];
    total: number;
  }
}

export interface IFreeDevBranchEditRes extends IFreeDevBranchBaseRes {
  data: {
    freeDevBranch: IFreeDevBranch,
    otherFreeDevBranchMap: { [projectName: string]: IFreeDevBranch | string }
  }
}

interface IReportAnalyzedReq {
  devId: number;
  branchName: string;
  dist?: string; 
}

interface IAnalyzeSizeReq {
  branchName: string;
  dist: string; 
  projectName: string;
  gmtModified?: string;
  noticeUsers?: string; // 体积超限通知者
}

export default {
  list: (params: IFreeDevBranchListReq) => runApi<IFreeDevBranchListRes>('/free-dev-branch/list', params),
  create: (params: IFreeDevBranchCreateReq) => runApi<IFreeDevBranchEditRes>('/free-dev-branch/create', params, 'post'),
  update: (params: IFreeDevBranchUpdateReq) => runApi<IFreeDevBranchEditRes>('/free-dev-branch/update', params, 'post'),
  get:  (devId: number) => runApi<IFreeDevBranchCommonRes>('/free-dev-branch/get', { devId }, 'get'),
  delete: (devId: number) => runApi<IFreeDevBranchCommonRes>('/free-dev-branch/delete', { devId }, 'post'),
  discard: (devId: number) => runApi<IFreeDevBranchCommonRes>('/free-dev-branch/discard', { devId }, 'post'),
  ready: (devId: number) => runApi<IFreeDevBranchCommonRes>('/free-dev-branch/ready', { devId }, 'post'),
  cancelReady: (devId: number) => runApi<IFreeDevBranchCommonRes>('/free-dev-branch/cancel-ready', { devId }, 'post'),
  mount: (devId: number, iterId: number) => runApi<IFreeDevBranchCommonRes>('/free-dev-branch/mount', { devId, iterId }, 'post'),
  unmount: (devId: number) => runApi<IFreeDevBranchCommonRes>('/free-dev-branch/unmount', { devId }, 'post'),
  check: (devId: number) => runApi<IFreeDevBranchCommonRes>('/free-dev-branch/check', { devId }, 'post'),
  uncheck: (devId: number) => runApi<IFreeDevBranchCommonRes>('/free-dev-branch/uncheck', { devId }, 'post'),
  makeReportAnalyzed: (branchList: IReportAnalyzedReq[]) => runApi('/free-dev-branch/make-report-analyzed', { branchList }, 'post'),
  analyzeSize: (params: IAnalyzeSizeReq) => runApi('/free-dev-branch/analyze-size', params, 'post')
};
