import { runApi } from '@/utils/request';
import { IIterBranch } from '@/interface/iter-branch';
import { IDevBranch } from '@/interface/dev-branch';
import { EProjectType } from '@/const/project';

export interface IDevCreateReq {
  iterId: IIterBranch['iterId'];
  branchName: IDevBranch['branchName'];
  description: IDevBranch['description'];
  aoneList: IDevBranch['aoneList'];
  mergeCode: IDevBranch['mergeCode'];
  npmList: IDevBranch['npmList'];
  npmResolutionList: IDevBranch['npmResolutionList'];
  bizLine: IDevBranch['bizLine'];
  qaList: IDevBranch['qaList'];
  pkgVersion?: string;
  projectType: EProjectType;
}

export interface IDevUpdateReq {
  devId: IDevBranch['devId'];
  description?: IIterBranch['description'];
  status?: IIterBranch['status'];
  grayStatus?: IIterBranch['grayStatus'];
  bizLine?: IDevBranch['bizLine'];
  qaList?: IDevBranch['qaList'];
  pkgVersion?: string;
}

export interface IDevCommonRes {
  success: boolean;
  errorMsg?: string;
  data?: IDevBranch;
}

export interface IReportAnalyzedReq {
  devId: number;
  branchName: string;
  dist?: string; 
}

export default {
  get: (devId: IDevBranch['devId']) => runApi('/dev-branch/get', { devId }, 'get'),
  create: (params: IDevCreateReq) => runApi<IDevCommonRes>('/dev-branch/create', params, 'post'),
  update: (params: IDevUpdateReq) => runApi<IDevCommonRes>('/dev-branch/update', params, 'post'),
  delete: (devId: number) => runApi<IDevCommonRes>('/dev-branch/delete', { devId }, 'post'),
  discard: (devId: number) => runApi<IDevCommonRes>('/dev-branch/discard', { devId }, 'post'),
  ready: (devId: number) => runApi<IDevCommonRes>('/dev-branch/ready', { devId }, 'post'),
  cancelReady: (devId: number) => runApi<IDevCommonRes>('/dev-branch/cancel-ready', { devId }, 'post'),
  check: (devId: number) => runApi<IDevCommonRes>('/dev-branch/check', { devId }, 'post'),
  uncheck: (devId: number) => runApi<IDevCommonRes>('/dev-branch/uncheck', { devId }, 'post'),
  makeReportAnalyzed: (branchList: IReportAnalyzedReq[]) => runApi('/dev-branch/make-report-analyzed', { branchList }, 'post')
};
