import { runApi } from '@/utils/request';
import { IIterApplication } from '@/interface/iter-application';
import { IIterBranch } from '@/interface/iter-branch';

interface IIterApplicationListReq {
  pageNum?: number;
  pageSize?: number;
}

interface IApplyExtraVersionReq {
  /** 项目名称 */
  projectName: string;
  /** 加版原因 */
  reason: string;
  /** 不加版的影响 */
  effect: string;
  /** 期望发版日期，YYYY-MM-DD */
  publishDay: string;
}

interface IApplyRollbackReq {
  /** 回滚迭代id */
  iterId: number;
  /** 回滚原因 */
  reason: string;
}

interface ICommonRes {
  success: boolean;
  errorMsg?: string;
}

interface IIterApplicationListRes extends ICommonRes {
  data: {
    list: IIterApplication[];
    total: number;
  }
}

interface IIterApplicationCreateRes extends ICommonRes {
  data: IIterApplication
}

export default {
  listExtraVersion: (params: IIterApplicationListReq) => runApi<IIterApplicationListRes>('/iter-application/list-extra-version', params),
  applyExtraVersion: (params: IApplyExtraVersionReq) => runApi<IIterApplicationCreateRes>('/iter-application/apply-extra-version', params, 'post'),
  listRollback: (params: IIterApplicationListReq) => runApi<IIterApplicationListRes>('/iter-application/list-rollback', params),
  applyRollback: (params: IApplyRollbackReq) => runApi<IIterApplicationCreateRes>('/iter-application/apply-rollback', params, 'post'),
};
