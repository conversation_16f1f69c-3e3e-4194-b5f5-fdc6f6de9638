import { runApi } from '@/utils/request';
import { IIterQaReview } from '@/interface/iter-qa-review';
import { BIZ_LINE } from '@/const/biz';

interface IIterQaReviewCreateReq {
  iterId: number;
  bizNames: BIZ_LINE[];
}

interface IIterQaReviewDeleteReq {
  id: number;
}

interface IIterQaReviewCancelReq {
  iterId: number;
}

interface IIterQaReviewListReq {
  iterId: number;
}

interface IIterQaReviewAddReviewResReq {
  id: number;
  pass: boolean;
  title: string;
  comment: string;
}

interface IIterQaReviewModifyReviewResReq {
  id: number;
  resId: number;
  pass: boolean;
  title: string;
  comment: string;
}

interface IIterQaReviewDeleteReviewResReq {
  id: number;
  resId: number;
}

interface IIterQaReviewCommonRes {
  success: boolean;
  errorMsg?: string;
}

interface IIterQaReviewUpdateRes extends IIterQaReviewCommonRes {
  data: IIterQaReview;
}

interface IIterQaReviewDeleteRes extends IIterQaReviewCommonRes {
  data: boolean;
}

interface IIterQaReviewCancelRes extends IIterQaReviewCommonRes {
  data: boolean;
}

interface IIterQaReviewListRes extends IIterQaReviewCommonRes {
  data: {
    list: IIterQaReview[];
    total: number;
  }
}

export default {
  create: (params: IIterQaReviewCreateReq) => runApi<IIterQaReviewListRes>('/iter-qa-review/create', params, 'post'),
  cancel: (params:IIterQaReviewCancelReq) => runApi<IIterQaReviewCancelRes>('/iter-qa-review/cancel', params, 'post'),
  delete: (params: IIterQaReviewDeleteReq) => runApi<IIterQaReviewDeleteRes>('/iter-qa-review/delete', params, 'post'),
  list: (params: IIterQaReviewListReq) => runApi<IIterQaReviewListRes>('/iter-qa-review/list', params),
  addReviewRes: (params: IIterQaReviewAddReviewResReq) => runApi<IIterQaReviewUpdateRes>('/iter-qa-review/add-review-res', params, 'post'),
  modifyReviewRes: (params: IIterQaReviewModifyReviewResReq) => runApi<IIterQaReviewUpdateRes>('/iter-qa-review/modify-review-res', params, 'post'),
  deleteReviewRes: (params: IIterQaReviewDeleteReviewResReq) => runApi<IIterQaReviewUpdateRes>('/iter-qa-review/delete-review-res', params, 'post'),
}