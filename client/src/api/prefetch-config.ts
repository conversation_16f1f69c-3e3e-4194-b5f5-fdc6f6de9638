import { runApi } from '@/utils/request';
import { IPrefetchConfig } from '@/interface/prefetch-config';

interface IPrefetchConfigListRes {
  success: boolean;
  errorMsg?: string;
  data?: IPrefetchConfig[];
}

export interface IPrefetchConfigCreateReq {
  /** 所属项目名 */
  projectName: string;
  /** 页面path */
  path: string;
  /** 是否需要登陆 */
  needLogin: number;
  /** mtop api */
  mtopApi?: string;
  /** mtop version */
  mtopVersion?: string;
  /** 强匹配参数，匹配不上不返回数据 */
  verifyKeys?: string;
  /** 获取数据时静态参数 */
  staticParams?: string;
  /** 获取数据时需要从query中获取的动态参数 */
  dynamicParams?: string;
  /** 是否需要定位信息 */
  needGeolocation: number;
  /** 过期时间 */
  expiryTimeMs?: number;
  /** 是否获取完就删除 */
  isNeedGetThenRemove?: number;
  /** 获取mtop时的超时时间 */
  mtopTimeOutMs?: number;
  /** 是否有冷启动场景 */
  hasColdStart: number;
  /** hsf id（冷启动场景） */
  hsfId?: string;
  /** hsf method（冷启动场景） */
  hsfMethod?: string;
  /** hsf parameterTypes（冷启动场景） */
  hsfParameterTypes?: string;
  /** hsf调用参数结构（冷启动场景） */
  hsfParamsObj?: string;
  /** 是否灰度 */
  isGray?: number;
  /** 灰度进度 */
  grayNumber?: number;
  /** 入参处理函数 */
  paramsHandleFunc?: string;
  /** 取数据校验参数函数 */
  checkParamsFunc?: string;
  /** hsf 入参结构 */
  hsfParamsMapping?: string;
  /** mtop 入参结构 */
  mtopParamsMapping?: string;
  /** 不开启冷启动*/
  notColdPrefetch?: number;
}

export interface IPrefetchConfigCreateRes {
  success: boolean;
  errorMsg?: string;
}

export interface IPrefetchConfigUpdateReq {
  /** 项目id */
  id: number;
  /** 所属项目名 */
  projectName: string;
  /** 页面path */
  path: string;
  /** 是否需要登陆 */
  needLogin: number;
  /** mtop api */
  mtopApi?: string;
  /** mtop version */
  mtopVersion?: string;
  /** 强匹配参数，匹配不上不返回数据 */
  verifyKeys?: string;
  /** 获取数据时静态参数 */
  staticParams?: string;
  /** 获取数据时需要从query中获取的动态参数 */
  dynamicParams?: string;
  /** 是否需要定位信息 */
  needGeolocation: number;
  /** 过期时间 */
  expiryTimeMs?: number;
  /** 是否获取完就删除 */
  isNeedGetThenRemove?: number;
  /** 获取mtop时的超时时间 */
  mtopTimeOutMs?: number;
  /** 是否有冷启动场景 */
  hasColdStart: number;
  /** hsf id（冷启动场景） */
  hsfId?: string;
  /** hsf method（冷启动场景） */
  hsfMethod?: string;
  /** hsf parameterTypes（冷启动场景） */
  hsfParameterTypes?: string;
  /** hsf调用参数结构（冷启动场景） */
  hsfParamsObj?: string;
  /** 是否灰度 */
  isGray?: number;
  /** 灰度进度 */
  grayNumber?: number;
  /** 不开启冷启动*/
  notColdPrefetch?: number;
}

export interface IPrefetchConfigUpdateRes {
  success: boolean;
  errorMsg?: string;
  data?: IPrefetchConfig[];
}

export interface IPrefetchConfigChangeRes {
  success: boolean;
  errorMsg?: string;
  data?: IPrefetchConfig;
}

export interface IPrefetchConfigDeleteRes {
  success: boolean;
  errorMsg?: string;
}

export interface IPrefetchConfigGetPrefetchDataReq {
  path?: string,
  userId?: string,
  query?: string,
  code?: string,
  end?: string,
  longitude?: string,
  latitude?: string,
  id?: string | number,
}


export default {
  list: () => runApi<IPrefetchConfigListRes>('/prefetch-config/list'),
  create: (params: IPrefetchConfigCreateReq) => runApi<IPrefetchConfigCreateRes>('/prefetch-config/create', params, 'post'),
  update: (params: IPrefetchConfigUpdateReq) => runApi<IPrefetchConfigUpdateRes>('/prefetch-config/update', params, 'post'),
  delete: (id: number) => runApi<IPrefetchConfigDeleteRes>('/prefetch-config/delete', { id }, 'get'),
  findByKeyValue: (key: string, value: any) => runApi<IPrefetchConfigListRes>('/prefetch-config/find-by-key-value', { key, value }),
  changeStopStatus: (id: number, status: number) => runApi<IPrefetchConfigChangeRes>('/prefetch-config/changeStopStatus', { id, status }, 'get'),
  getMtopData: (api: string, version: string) => runApi<any>('/prefetch-config/getMtopData', { api, version }, 'get'),
  getPrefetchData: (params: IPrefetchConfigGetPrefetchDataReq) => runApi<any>('/prefetch-config/getPrefetchData', params, 'post')
};