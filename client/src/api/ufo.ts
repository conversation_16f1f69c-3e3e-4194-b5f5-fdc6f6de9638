import { runApi } from '@/utils/request';
import { IPerformanceList, IIterBranchResult } from '@/interface/ufo';

export default {
  getListByPage: (mirrorAlgorithmId: number, pageNum: number, pageSize: number) => runApi<IPerformanceList>('/ufo/getListByPage', { mirrorAlgorithmId, pageNum, pageSize }, 'get'),
  getAllListByPage: (taskIds: number[], pageNum: number, pageSize: number, projectName: string) => runApi<IPerformanceList>('/ufo/getAllListByPage', { taskIds: JSON.stringify(taskIds), pageNum, pageSize, projectName }, 'get'),
  getBranchList: (projectName: string) => runApi<IIterBranchResult>('/ufo/getBranchList', { projectName }, 'get'),
}
