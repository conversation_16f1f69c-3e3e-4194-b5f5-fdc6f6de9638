import { runApi } from '@/utils/request';
import { EPubTypeEnv } from '@/const/def';
import { IProject } from '@/interface/project';
import { EDeliverBranchType, EDeliverType } from '@/const/component-deliver';
import { INpm } from '@/interface/base-dev-branch';
import { IDevCreateReq } from '@/api/dev-branch';
export interface IComponentDeliverRes {
  success: boolean;
  data?: any
  errorMsg?: string;
  errorCode?: string;
}

export default {
  get: (params: { deliverId: number; assertExist?: boolean }) =>
    runApi<IComponentDeliverRes>('/component-deliver/get', params, 'post'),
  list: (params: { pageSize: number; pageNum: number; conditions: {
      devId?: number;
      branchType?: EDeliverBranchType;
      projectName?: string;
      defEnvType?: EPubTypeEnv;
      modifier?: string;
    }
  }) =>
    runApi<IComponentDeliverRes>('/component-deliver/list', params, 'post'),
  init: (params: {
    deliverId: number;
    devId: number;
    iterId: number;
    deliverType: EDeliverType;
    branchType: EDeliverBranchType;
    pub_env: EPubTypeEnv;
    projectName: string;
    gitRepo: string;
    gitProjectId: number;
    npmList?: INpm[];
    aoneBinds: string[],
    description: string;
  }) =>
    runApi<IComponentDeliverRes>('/component-deliver/init', params, 'post'),
  createPublish: (params: {
    deliverId: number;
    pub_env: EPubTypeEnv;
    gitRepo: string;
    npmList?: INpm[];
    defIterId: number;
  }) =>
    runApi<IComponentDeliverRes>('/component-deliver/create-publish', params, 'post'),
  getPublishDetail: (params: {
    project: IProject;
    deliverId: number;
    defTaskId: number;
    npmList?: INpm[];
  }) =>
    runApi<IComponentDeliverRes>('/component-deliver/get-publish-detail', params, 'post'),
  initMiniapp: (params: {
      deliverId: number;
      devId: number;
      iterId: number;
      deliverType: EDeliverType;
      branchType: EDeliverBranchType;
      pub_env: EPubTypeEnv;
    } & IDevCreateReq) =>
      runApi<IComponentDeliverRes>('/component-deliver/init-miniapp', params, 'post'),
  updateBetaMiniapp: (params: {
      deliverId: number;
      npmList: INpm[];
    }) =>
      runApi<IComponentDeliverRes>('/component-deliver/update-beta-miniapp', params, 'post'),
  updateProdMiniapp: (params: {
      deliverId: number;
      iterId: number;
      deliverType: EDeliverType;
      npmList: INpm[];
      projectName: string;
    }) =>
      runApi<IComponentDeliverRes>('/component-deliver/update-prod-miniapp', params, 'post'),
};
