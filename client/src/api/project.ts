import { runApi } from '@/utils/request';
import { IClient, IDingtalkRobot, IProject, IWsyConfig } from '@/interface/project';
import { EProjectType } from '@/const/project';

export interface IProjectCreateReq {
  /** 项目名称 */
  name: string;
  /** 项目中文名 */
  cnName: string;
  /** 项目类型 */
  type: EProjectType;
  /** 项目图标 */
  icon: string;
  /** 代码仓库地址 */
  gitRepo: string;
  /** 项目管理员 */
  adminWorkidList: string[];
  /** 投放端 */
  clientList?: IClient[];
  /** 钉钉机器人 */
  dingtalkRobots?: IDingtalkRobot[];
  /** 项目是组件类型时，投放的配置信息 */
  deliverConfig?: { projectName: string; moduleName: string; gitRepo: string; gitProjectId: number; defProjectId: number; deliverList: any };
  /** 打码配置 */
  wsyConfig?: IWsyConfig;
}

interface ICommonRes {
  success: boolean;
  errorMsg?: string;
}

export interface IProjectUpdateReq extends IProjectCreateReq {
  /** 项目id */
  id: number;
}

export interface IProjectCreateRes extends ICommonRes {
  data: IProject;
}

export interface IProjectUpdateRes extends ICommonRes {
  data: IProject;
}

interface IProjectListRes extends ICommonRes {
  data: IProject[];
}

export default {
  list: () => runApi<IProjectListRes>('/project/list'),
  create: (params: IProjectCreateReq) => runApi<IProjectCreateRes>('/project/create', params, 'post'),
  update: (params: IProjectUpdateReq) => runApi<IProjectUpdateRes>('/project/update', params, 'post'),
  delete: (id: number) => runApi<ICommonRes>('/project/delete', { id }, 'get'),
};
