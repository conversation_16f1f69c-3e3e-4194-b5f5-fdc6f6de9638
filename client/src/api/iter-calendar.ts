import { runApi } from '@/utils/request';
import { IIterBranch } from '@/interface/iter-branch';
import { IProject } from '@/interface/project';

interface IListReq {
  year?: number;
  month?: number;
  projectName?: string;
  filteredStatus?: string;
}

interface ICommonRes {
  success: boolean;
  errorMsg?: string;
}

interface IListRes extends ICommonRes {
  data: {
    condition: {
      projectName: IIterBranch['projectName']
    };
    iterList: IIterBranch[],
    project: IProject
  }
}

export default {
  list: (params: IListReq) => runApi<IListRes>('/iter-calendar/list', params)
};