import { runApi } from '@/utils/request';
import { EClient } from '@/const/iter-deliver';

interface ICommonRes {
  success: boolean;
  errorMsg?: string;
}

export interface IIterExperienceGetReq {
  iterId: number;
  clientName: any;
  miniAppId: string;
  commitId?: string;
  rcGitName?: string;
  rcGitUrl?: string;
}

export interface IIterExperienceCreateReq {
  iterId: number;
  clientName: any;
  miniAppId: string;
  reBuild?: boolean;
  commitId?: string;
  projectTmpDirPath?: string; // service 层使用
}

export default {
  createExperience: (params: IIterExperienceCreateReq) => runApi<any>('/iter-experience/create', params, 'post'),
  getExperienceInfo: (params: IIterExperienceGetReq) => runApi<any>('/iter-experience/get', params, 'post'),
  getExperienceQRCode: (params: { hashK: any }) => runApi<any>('/iter-experience/getQRCode', params, 'post'),
  getDeliverList: (params: { iterId: number, clientName?: EClient, miniAppId?: any }) => runApi<any>('/iter-experience/getDeliverList', params, 'post'),
  audit: (params: { id: number }) => runApi<any>('/iter-experience/submit', params, 'post'),
};
