import { IIterBranch } from '@/interface/iter-branch';
import { IDependencies, IDependenciesDiff } from '@/interface/helper';
import { INpm } from '@/interface/base-dev-branch';
import { runApi, run2ndApi } from '@/utils/request';
import { EPubTypeEnv } from '@/const/def';
import { IProject } from '@/interface/project';

export interface IAnalyzeDependenciesReq {
  /** 项目名称 */
  projectName: string;
}

export interface IDiffDependenciesReq {
  /** 迭代分支id */
  iterId: string;
  /** 对照的迭代分支id */
  fromIterId?: string;
}

export interface INoticeMergeReq {
  /** 迭代分支id */
  iterId: number;
}

export interface INoticeRegressionTestingReq {
  /** 迭代分支id */
  iterId: number;
  /** 二维码链接 */
  qrCodeLink?: string;
}

interface ITriggerAutomatedTestReq {
  /** 迭代分支id */
  iterId?: number;
  /** 开发分支id */
  devId?: number;
  /** 是否是游离开发分支，需与 devId 一同传递 */
  isFreeDevBranch?: boolean;
  /** 自定义的二维码链接，不传将自动构建 */
  qrCodeLink?: string;
  /** 自定义的构建产物地址 */
  dist?: string;
  /** 是否更新入库 */
  update?: boolean;
}

interface IGetLatestPublishedByProjectReq {
  /** 项目名称 */
  projectName: string;
}

export interface IMakeAndSaveShrinkwrapReq {
  /** 迭代分支id */
  iterId: number;
}

interface ItriggerComponentAutoTestReq {
  pub_env: EPubTypeEnv;
  gitRepo: string;
  npmList: INpm[];
  defIterId: number;
  branchName: string;
}

interface ICommonRes {
  success: boolean;
  data?: any;
  errorMsg?: string;
}

interface IAnalyzeDependenciesRes extends ICommonRes {
  data: {
    dependencies: IDependencies;
    iterBranch: IIterBranch;
  }
}

interface IDiffDependenciesRes extends ICommonRes {
  data: {
    iterBranch: IIterBranch;
    prevPublishedIterBranch?: IIterBranch;
    dependenciesDiff?: IDependenciesDiff;
    tips: string;
    dependencies?: IDependencies;
  }
}

interface IGetUfoUrlRes extends ICommonRes {
  data: {
    resultList: {
      resultUrl: string;
    }[];
  }
}

interface ITriggerAutomatedTestRes extends ICommonRes {
  data: {
     /** 塔台id */
    towerId: string;
  };
}

interface IMakeAndSaveShrinkwrapRes extends ICommonRes {
  data: boolean;
}

interface IGetLatestPublishedByProjectRes extends ICommonRes {
  data: IIterBranch;
}

interface IPublishReportParams {
  projectName: string;
  iterId: number;
}

export default {
  /** 依赖分析 */
  analyzeDependencies: (params: IAnalyzeDependenciesReq) => runApi<IAnalyzeDependenciesRes>('/helper/analyze-dependencies', params),
  /** 依赖 diff */
  diffDependencies: (params: IDiffDependenciesReq) => runApi<IDiffDependenciesRes>('/helper/diff-dependencies', params),
  /** 发送即将集成通知 */
  noticeMerge: (params: INoticeMergeReq) => runApi<ICommonRes>('/helper/notice-merge', params),
  /** 发送回归测试通知 */
  noticeRegressionTesting: (params: INoticeRegressionTestingReq) => runApi<ICommonRes>('/helper/notice-regression-testing', params),
  /** 触发自动化测试 */
  triggerAutomatedTest: (params: ITriggerAutomatedTestReq) => runApi<ITriggerAutomatedTestRes>('/helper/trigger-automated-test', params, 'post'),
  /** 生成shrinkwrap */
  makeAndSaveShrinkwrap: (params: IMakeAndSaveShrinkwrapReq) => runApi<IMakeAndSaveShrinkwrapRes>('/helper/make-and-save-shrinkwrap', params, 'post'),
  /** 生成report_analyzed.json */
  makeAndSaveReportAnalyzed: (params: IMakeAndSaveShrinkwrapReq) => runApi<IMakeAndSaveShrinkwrapRes>('/helper/make-and-save-reportAnalyzed', params, 'post'),
  /** 获取飞碟测试报告 */
  getUfoUrl: (towerId: string) => run2ndApi<IGetUfoUrlRes>('https://pre-tower.trip.alibaba-inc.com/miniApp/getResultById', { taskId: towerId }),
  triggerComponentAutoTest: (params: ItriggerComponentAutoTestReq) => runApi<ICommonRes>('/helper/trigger-component-auto-test', params, 'post'),
  /** 获取指定项目线上版本 */
  getLatestPublishedByProject: (params: IGetLatestPublishedByProjectReq) => runApi<IGetLatestPublishedByProjectRes>('/helper/get-latest-published-by-project', params),
  /** 依赖变更通知 */
  noticePublishReport: (params: IPublishReportParams) => runApi<ICommonRes>('/helper/notice-publish-report', params, 'post'),
};
