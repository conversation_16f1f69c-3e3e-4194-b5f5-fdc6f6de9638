import { runApi } from '@/utils/request';

interface IWxPromotionReq {
  url: string;
  needShortLink: boolean;
  needQrcode: boolean;
  shortLinkName?: boolean;
  weappCodeWidth?: number;
  isHyaline?: boolean;
  qrcodePath: string;
}

interface IWxPromotionRes {
  success: boolean;
  errorMsg?: string;
  data: {
    weappShortLink: string | null;
    weappCode: string | null;
  };
}

export default {
  getWxPromotionInfo: (params: IWxPromotionReq) => runApi<IWxPromotionRes>('/open-link/wx-promotion-info', params, 'POST'),
};