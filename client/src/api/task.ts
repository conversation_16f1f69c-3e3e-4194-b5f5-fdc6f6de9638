import {runApi} from '@/utils/request';
import {MainTaskDetail, TaskDetail} from "@/interface/task-detail";

export interface ITaskResultRes {
  data: {
    success: boolean;
    errorMsg?: string;
    errorCode: string;
    data: {
      miniAppResultVOList: TaskDetail[];
      totalCount: number;
      successCount: number;
      failCount: number;
    };
  };
  success: boolean;
}

export interface ITotalTaskCountRes {
  data: {
    success: boolean;
    errorMsg?: string;
    errorCode: string;
    data: SubTaskCountDetail[];
  };
  success: boolean;
}

export interface SubTaskCountDetail {
  "taskType": number,
  "taskTypeDesc": string,
  "totalMainCount": number,
  "checkSuccCount": number,
  "taskSuccCount": number,
  "totalCount": number,
  "succCaseCount":number,
  "totalCaseCount":number,
}

export interface IMainTaskDetailRes {
  data: {
    success: boolean;
    errorMsg?: string;
    errorCode: string;
    data: {
      dataList: MainTaskDetail[];
      totalCount: number;
    };
  };
  success: boolean;
}

export default {
  getTaskResult: (taskId: number) => runApi<ITaskResultRes>('/helper/get-task-result', {taskId}, 'get'),
  getTotalTaskCount: (startTime: string, endTime: string) => runApi<ITotalTaskCountRes>('/helper/get-total-task-count', {
    startTime,
    endTime
  }, 'get'),
  getMainTaskDetail: (startTime: string, endTime: string) => runApi<IMainTaskDetailRes>('/helper/get-main-task-detail', {
    startTime, endTime
  }, 'get'),

}
