# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
npm-debug.log*
yarn-error.log
yarn.lock

# production
/dist
/build
/build-cloud.json

# misc
.DS_Store
.vscode
.idea

# umi
/src/.umi
/src/.umi-production
/src/.umi-test
/.env.local

log
logs
.faas_debug_tmp
.history
!.gitkeep

# server
/server/bin/test.pem
/server/mock
/server/.serverless
